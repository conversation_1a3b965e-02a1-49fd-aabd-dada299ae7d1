import{a as br}from"./chunk-FK6H3RFT.js";import{d,e as Z}from"./chunk-CWTPBX7D.js";var _=d((qi,Ds)=>{"use strict";var xr=function(i){return i&&i.Math===Math&&i};Ds.exports=xr(typeof globalThis=="object"&&globalThis)||xr(typeof window=="object"&&window)||xr(typeof self=="object"&&self)||xr(typeof global=="object"&&global)||xr(typeof qi=="object"&&qi)||function(){return this}()||Function("return this")()});var D=d((mO,Ls)=>{"use strict";Ls.exports=function(i){try{return!!i()}catch{return!0}}});var oe=d((bO,ks)=>{"use strict";var cg=D();ks.exports=!cg(function(){return Object.defineProperty({},1,{get:function(){return 7}})[1]!==7})});var Tr=d((xO,Bs)=>{"use strict";var fg=D();Bs.exports=!fg(function(){var i=function(){}.bind();return typeof i!="function"||i.hasOwnProperty("prototype")})});var G=d((TO,Us)=>{"use strict";var vg=Tr(),nt=Function.prototype.call;Us.exports=vg?nt.bind(nt):function(){return nt.apply(nt,arguments)}});var $s=d(zs=>{"use strict";var js={}.propertyIsEnumerable,Fs=Object.getOwnPropertyDescriptor,gg=Fs&&!js.call({1:2},1);zs.f=gg?function(e){var r=Fs(this,e);return!!r&&r.enumerable}:js});var st=d((SO,Hs)=>{"use strict";Hs.exports=function(i,e){return{enumerable:!(i&1),configurable:!(i&2),writable:!(i&4),value:e}}});var L=d((wO,Xs)=>{"use strict";var Gs=Tr(),Ys=Function.prototype,Pi=Ys.call,dg=Gs&&Ys.bind.bind(Pi,Pi);Xs.exports=Gs?dg:function(i){return function(){return Pi.apply(i,arguments)}}});var Pe=d((EO,Qs)=>{"use strict";var Ws=L(),pg=Ws({}.toString),yg=Ws("".slice);Qs.exports=function(i){return yg(pg(i),8,-1)}});var Ri=d((CO,Ks)=>{"use strict";var mg=L(),bg=D(),xg=Pe(),Ai=Object,Tg=mg("".split);Ks.exports=bg(function(){return!Ai("z").propertyIsEnumerable(0)})?function(i){return xg(i)==="String"?Tg(i,""):Ai(i)}:Ai});var Ae=d((qO,Zs)=>{"use strict";Zs.exports=function(i){return i==null}});var ue=d((PO,Js)=>{"use strict";var Og=Ae(),Sg=TypeError;Js.exports=function(i){if(Og(i))throw new Sg("Can't call method on "+i);return i}});var Qe=d((AO,eo)=>{"use strict";var wg=Ri(),Eg=ue();eo.exports=function(i){return wg(Eg(i))}});var k=d((RO,ro)=>{"use strict";var Ni=typeof document=="object"&&document.all;ro.exports=typeof Ni>"u"&&Ni!==void 0?function(i){return typeof i=="function"||i===Ni}:function(i){return typeof i=="function"}});var le=d((NO,to)=>{"use strict";var Cg=k();to.exports=function(i){return typeof i=="object"?i!==null:Cg(i)}});var Re=d((IO,io)=>{"use strict";var Ii=_(),qg=k(),Pg=function(i){return qg(i)?i:void 0};io.exports=function(i,e){return arguments.length<2?Pg(Ii[i]):Ii[i]&&Ii[i][e]}});var Or=d((MO,ao)=>{"use strict";var Ag=L();ao.exports=Ag({}.isPrototypeOf)});var Sr=d((VO,no)=>{"use strict";no.exports=typeof navigator<"u"&&String(navigator.userAgent)||""});var ut=d((_O,co)=>{"use strict";var ho=_(),Mi=Sr(),so=ho.process,oo=ho.Deno,uo=so&&so.versions||oo&&oo.version,lo=uo&&uo.v8,fe,ot;lo&&(fe=lo.split("."),ot=fe[0]>0&&fe[0]<4?1:+(fe[0]+fe[1]));!ot&&Mi&&(fe=Mi.match(/Edge\/(\d+)/),(!fe||fe[1]>=74)&&(fe=Mi.match(/Chrome\/(\d+)/),fe&&(ot=+fe[1])));co.exports=ot});var Vi=d((DO,vo)=>{"use strict";var fo=ut(),Rg=D(),Ng=_(),Ig=Ng.String;vo.exports=!!Object.getOwnPropertySymbols&&!Rg(function(){var i=Symbol("symbol detection");return!Ig(i)||!(Object(i)instanceof Symbol)||!Symbol.sham&&fo&&fo<41})});var _i=d((LO,go)=>{"use strict";var Mg=Vi();go.exports=Mg&&!Symbol.sham&&typeof Symbol.iterator=="symbol"});var Di=d((kO,po)=>{"use strict";var Vg=Re(),_g=k(),Dg=Or(),Lg=_i(),kg=Object;po.exports=Lg?function(i){return typeof i=="symbol"}:function(i){var e=Vg("Symbol");return _g(e)&&Dg(e.prototype,kg(i))}});var wr=d((BO,yo)=>{"use strict";var Bg=String;yo.exports=function(i){try{return Bg(i)}catch{return"Object"}}});var be=d((UO,mo)=>{"use strict";var Ug=k(),jg=wr(),Fg=TypeError;mo.exports=function(i){if(Ug(i))return i;throw new Fg(jg(i)+" is not a function")}});var Be=d((jO,bo)=>{"use strict";var zg=be(),$g=Ae();bo.exports=function(i,e){var r=i[e];return $g(r)?void 0:zg(r)}});var To=d((FO,xo)=>{"use strict";var Li=G(),ki=k(),Bi=le(),Hg=TypeError;xo.exports=function(i,e){var r,t;if(e==="string"&&ki(r=i.toString)&&!Bi(t=Li(r,i))||ki(r=i.valueOf)&&!Bi(t=Li(r,i))||e!=="string"&&ki(r=i.toString)&&!Bi(t=Li(r,i)))return t;throw new Hg("Can't convert object to primitive value")}});var ve=d((zO,Oo)=>{"use strict";Oo.exports=!1});var lt=d(($O,wo)=>{"use strict";var So=_(),Gg=Object.defineProperty;wo.exports=function(i,e){try{Gg(So,i,{value:e,configurable:!0,writable:!0})}catch{So[i]=e}return e}});var ht=d((HO,qo)=>{"use strict";var Yg=ve(),Xg=_(),Wg=lt(),Eo="__core-js_shared__",Co=qo.exports=Xg[Eo]||Wg(Eo,{});(Co.versions||(Co.versions=[])).push({version:"3.37.1",mode:Yg?"pure":"global",copyright:"\xA9 2014-2024 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.37.1/LICENSE",source:"https://github.com/zloirock/core-js"})});var ct=d((GO,Ao)=>{"use strict";var Po=ht();Ao.exports=function(i,e){return Po[i]||(Po[i]=e||{})}});var Er=d((YO,Ro)=>{"use strict";var Qg=ue(),Kg=Object;Ro.exports=function(i){return Kg(Qg(i))}});var he=d((XO,No)=>{"use strict";var Zg=L(),Jg=Er(),ed=Zg({}.hasOwnProperty);No.exports=Object.hasOwn||function(e,r){return ed(Jg(e),r)}});var Ui=d((WO,Io)=>{"use strict";var rd=L(),td=0,id=Math.random(),ad=rd(1 .toString);Io.exports=function(i){return"Symbol("+(i===void 0?"":i)+")_"+ad(++td+id,36)}});var z=d((QO,Vo)=>{"use strict";var nd=_(),sd=ct(),Mo=he(),od=Ui(),ud=Vi(),ld=_i(),Ke=nd.Symbol,ji=sd("wks"),hd=ld?Ke.for||Ke:Ke&&Ke.withoutSetter||od;Vo.exports=function(i){return Mo(ji,i)||(ji[i]=ud&&Mo(Ke,i)?Ke[i]:hd("Symbol."+i)),ji[i]}});var ko=d((KO,Lo)=>{"use strict";var cd=G(),_o=le(),Do=Di(),fd=Be(),vd=To(),gd=z(),dd=TypeError,pd=gd("toPrimitive");Lo.exports=function(i,e){if(!_o(i)||Do(i))return i;var r=fd(i,pd),t;if(r){if(e===void 0&&(e="default"),t=cd(r,i,e),!_o(t)||Do(t))return t;throw new dd("Can't convert object to primitive value")}return e===void 0&&(e="number"),vd(i,e)}});var Fi=d((ZO,Bo)=>{"use strict";var yd=ko(),md=Di();Bo.exports=function(i){var e=yd(i,"string");return md(e)?e:e+""}});var Cr=d((JO,jo)=>{"use strict";var bd=_(),Uo=le(),zi=bd.document,xd=Uo(zi)&&Uo(zi.createElement);jo.exports=function(i){return xd?zi.createElement(i):{}}});var $i=d((eS,Fo)=>{"use strict";var Td=oe(),Od=D(),Sd=Cr();Fo.exports=!Td&&!Od(function(){return Object.defineProperty(Sd("div"),"a",{get:function(){return 7}}).a!==7})});var qr=d($o=>{"use strict";var wd=oe(),Ed=G(),Cd=$s(),qd=st(),Pd=Qe(),Ad=Fi(),Rd=he(),Nd=$i(),zo=Object.getOwnPropertyDescriptor;$o.f=wd?zo:function(e,r){if(e=Pd(e),r=Ad(r),Nd)try{return zo(e,r)}catch{}if(Rd(e,r))return qd(!Ed(Cd.f,e,r),e[r])}});var Hi=d((tS,Ho)=>{"use strict";var Id=oe(),Md=D();Ho.exports=Id&&Md(function(){return Object.defineProperty(function(){},"prototype",{value:42,writable:!1}).prototype!==42})});var J=d((iS,Go)=>{"use strict";var Vd=le(),_d=String,Dd=TypeError;Go.exports=function(i){if(Vd(i))return i;throw new Dd(_d(i)+" is not an object")}});var Te=d(Xo=>{"use strict";var Ld=oe(),kd=$i(),Bd=Hi(),ft=J(),Yo=Fi(),Ud=TypeError,Gi=Object.defineProperty,jd=Object.getOwnPropertyDescriptor,Yi="enumerable",Xi="configurable",Wi="writable";Xo.f=Ld?Bd?function(e,r,t){if(ft(e),r=Yo(r),ft(t),typeof e=="function"&&r==="prototype"&&"value"in t&&Wi in t&&!t[Wi]){var a=jd(e,r);a&&a[Wi]&&(e[r]=t.value,t={configurable:Xi in t?t[Xi]:a[Xi],enumerable:Yi in t?t[Yi]:a[Yi],writable:!1})}return Gi(e,r,t)}:Gi:function(e,r,t){if(ft(e),r=Yo(r),ft(t),kd)try{return Gi(e,r,t)}catch{}if("get"in t||"set"in t)throw new Ud("Accessors not supported");return"value"in t&&(e[r]=t.value),e}});var Ze=d((nS,Wo)=>{"use strict";var Fd=oe(),zd=Te(),$d=st();Wo.exports=Fd?function(i,e,r){return zd.f(i,e,$d(1,r))}:function(i,e,r){return i[e]=r,i}});var Pr=d((sS,Ko)=>{"use strict";var Qi=oe(),Hd=he(),Qo=Function.prototype,Gd=Qi&&Object.getOwnPropertyDescriptor,Ki=Hd(Qo,"name"),Yd=Ki&&function(){}.name==="something",Xd=Ki&&(!Qi||Qi&&Gd(Qo,"name").configurable);Ko.exports={EXISTS:Ki,PROPER:Yd,CONFIGURABLE:Xd}});var vt=d((oS,Zo)=>{"use strict";var Wd=L(),Qd=k(),Zi=ht(),Kd=Wd(Function.toString);Qd(Zi.inspectSource)||(Zi.inspectSource=function(i){return Kd(i)});Zo.exports=Zi.inspectSource});var ru=d((uS,eu)=>{"use strict";var Zd=_(),Jd=k(),Jo=Zd.WeakMap;eu.exports=Jd(Jo)&&/native code/.test(String(Jo))});var gt=d((lS,iu)=>{"use strict";var ep=ct(),rp=Ui(),tu=ep("keys");iu.exports=function(i){return tu[i]||(tu[i]=rp(i))}});var dt=d((hS,au)=>{"use strict";au.exports={}});var Rr=d((cS,ou)=>{"use strict";var tp=ru(),su=_(),ip=le(),ap=Ze(),Ji=he(),ea=ht(),np=gt(),sp=dt(),nu="Object already initialized",ra=su.TypeError,op=su.WeakMap,pt,Ar,yt,up=function(i){return yt(i)?Ar(i):pt(i,{})},lp=function(i){return function(e){var r;if(!ip(e)||(r=Ar(e)).type!==i)throw new ra("Incompatible receiver, "+i+" required");return r}};tp||ea.state?(ge=ea.state||(ea.state=new op),ge.get=ge.get,ge.has=ge.has,ge.set=ge.set,pt=function(i,e){if(ge.has(i))throw new ra(nu);return e.facade=i,ge.set(i,e),e},Ar=function(i){return ge.get(i)||{}},yt=function(i){return ge.has(i)}):(Ue=np("state"),sp[Ue]=!0,pt=function(i,e){if(Ji(i,Ue))throw new ra(nu);return e.facade=i,ap(i,Ue,e),e},Ar=function(i){return Ji(i,Ue)?i[Ue]:{}},yt=function(i){return Ji(i,Ue)});var ge,Ue;ou.exports={set:pt,get:Ar,has:yt,enforce:up,getterFor:lp}});var aa=d((fS,hu)=>{"use strict";var ia=L(),hp=D(),cp=k(),mt=he(),ta=oe(),fp=Pr().CONFIGURABLE,vp=vt(),lu=Rr(),gp=lu.enforce,dp=lu.get,uu=String,bt=Object.defineProperty,pp=ia("".slice),yp=ia("".replace),mp=ia([].join),bp=ta&&!hp(function(){return bt(function(){},"length",{value:8}).length!==8}),xp=String(String).split("String"),Tp=hu.exports=function(i,e,r){pp(uu(e),0,7)==="Symbol("&&(e="["+yp(uu(e),/^Symbol\(([^)]*)\).*$/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!mt(i,"name")||fp&&i.name!==e)&&(ta?bt(i,"name",{value:e,configurable:!0}):i.name=e),bp&&r&&mt(r,"arity")&&i.length!==r.arity&&bt(i,"length",{value:r.arity});try{r&&mt(r,"constructor")&&r.constructor?ta&&bt(i,"prototype",{writable:!1}):i.prototype&&(i.prototype=void 0)}catch{}var t=gp(i);return mt(t,"source")||(t.source=mp(xp,typeof e=="string"?e:"")),i};Function.prototype.toString=Tp(function(){return cp(this)&&dp(this).source||vp(this)},"toString")});var Ne=d((vS,cu)=>{"use strict";var Op=k(),Sp=Te(),wp=aa(),Ep=lt();cu.exports=function(i,e,r,t){t||(t={});var a=t.enumerable,n=t.name!==void 0?t.name:e;if(Op(r)&&wp(r,n,t),t.global)a?i[e]=r:Ep(e,r);else{try{t.unsafe?i[e]&&(a=!0):delete i[e]}catch{}a?i[e]=r:Sp.f(i,e,{value:r,enumerable:!1,configurable:!t.nonConfigurable,writable:!t.nonWritable})}return i}});var vu=d((gS,fu)=>{"use strict";var Cp=Math.ceil,qp=Math.floor;fu.exports=Math.trunc||function(e){var r=+e;return(r>0?qp:Cp)(r)}});var Nr=d((dS,gu)=>{"use strict";var Pp=vu();gu.exports=function(i){var e=+i;return e!==e||e===0?0:Pp(e)}});var pu=d((pS,du)=>{"use strict";var Ap=Nr(),Rp=Math.max,Np=Math.min;du.exports=function(i,e){var r=Ap(i);return r<0?Rp(r+e,0):Np(r,e)}});var je=d((yS,yu)=>{"use strict";var Ip=Nr(),Mp=Math.min;yu.exports=function(i){var e=Ip(i);return e>0?Mp(e,9007199254740991):0}});var xt=d((mS,mu)=>{"use strict";var Vp=je();mu.exports=function(i){return Vp(i.length)}});var na=d((bS,xu)=>{"use strict";var _p=Qe(),Dp=pu(),Lp=xt(),bu=function(i){return function(e,r,t){var a=_p(e),n=Lp(a);if(n===0)return!i&&-1;var o=Dp(t,n),s;if(i&&r!==r){for(;n>o;)if(s=a[o++],s!==s)return!0}else for(;n>o;o++)if((i||o in a)&&a[o]===r)return i||o||0;return!i&&-1}};xu.exports={includes:bu(!0),indexOf:bu(!1)}});var oa=d((xS,Ou)=>{"use strict";var kp=L(),sa=he(),Bp=Qe(),Up=na().indexOf,jp=dt(),Tu=kp([].push);Ou.exports=function(i,e){var r=Bp(i),t=0,a=[],n;for(n in r)!sa(jp,n)&&sa(r,n)&&Tu(a,n);for(;e.length>t;)sa(r,n=e[t++])&&(~Up(a,n)||Tu(a,n));return a}});var Tt=d((TS,Su)=>{"use strict";Su.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]});var Eu=d(wu=>{"use strict";var Fp=oa(),zp=Tt(),$p=zp.concat("length","prototype");wu.f=Object.getOwnPropertyNames||function(e){return Fp(e,$p)}});var qu=d(Cu=>{"use strict";Cu.f=Object.getOwnPropertySymbols});var Au=d((wS,Pu)=>{"use strict";var Hp=Re(),Gp=L(),Yp=Eu(),Xp=qu(),Wp=J(),Qp=Gp([].concat);Pu.exports=Hp("Reflect","ownKeys")||function(e){var r=Yp.f(Wp(e)),t=Xp.f;return t?Qp(r,t(e)):r}});var Iu=d((ES,Nu)=>{"use strict";var Ru=he(),Kp=Au(),Zp=qr(),Jp=Te();Nu.exports=function(i,e,r){for(var t=Kp(e),a=Jp.f,n=Zp.f,o=0;o<t.length;o++){var s=t[o];!Ru(i,s)&&!(r&&Ru(r,s))&&a(i,s,n(e,s))}}});var ua=d((CS,Mu)=>{"use strict";var ey=D(),ry=k(),ty=/#|\.prototype\./,Ir=function(i,e){var r=ay[iy(i)];return r===sy?!0:r===ny?!1:ry(e)?ey(e):!!e},iy=Ir.normalize=function(i){return String(i).replace(ty,".").toLowerCase()},ay=Ir.data={},ny=Ir.NATIVE="N",sy=Ir.POLYFILL="P";Mu.exports=Ir});var ee=d((qS,Vu)=>{"use strict";var Ot=_(),oy=qr().f,uy=Ze(),ly=Ne(),hy=lt(),cy=Iu(),fy=ua();Vu.exports=function(i,e){var r=i.target,t=i.global,a=i.stat,n,o,s,u,l,h;if(t?o=Ot:a?o=Ot[r]||hy(r,{}):o=Ot[r]&&Ot[r].prototype,o)for(s in e){if(l=e[s],i.dontCallGetSet?(h=oy(o,s),u=h&&h.value):u=o[s],n=fy(t?s:r+(a?".":"#")+s,i.forced),!n&&u!==void 0){if(typeof l==typeof u)continue;cy(l,u)}(i.sham||u&&u.sham)&&uy(l,"sham",!0),ly(o,s,l,i)}}});var Je=d((PS,_u)=>{"use strict";var vy=_(),gy=Pe();_u.exports=gy(vy.process)==="process"});var Lu=d((AS,Du)=>{"use strict";var dy=L(),py=be();Du.exports=function(i,e,r){try{return dy(py(Object.getOwnPropertyDescriptor(i,e)[r]))}catch{}}});var Bu=d((RS,ku)=>{"use strict";var yy=le();ku.exports=function(i){return yy(i)||i===null}});var ju=d((NS,Uu)=>{"use strict";var my=Bu(),by=String,xy=TypeError;Uu.exports=function(i){if(my(i))return i;throw new xy("Can't set "+by(i)+" as a prototype")}});var la=d((IS,Fu)=>{"use strict";var Ty=Lu(),Oy=le(),Sy=ue(),wy=ju();Fu.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var i=!1,e={},r;try{r=Ty(Object.prototype,"__proto__","set"),r(e,[]),i=e instanceof Array}catch{}return function(a,n){return Sy(a),wy(n),Oy(a)&&(i?r(a,n):a.__proto__=n),a}}():void 0)});var Mr=d((MS,$u)=>{"use strict";var Ey=Te().f,Cy=he(),qy=z(),zu=qy("toStringTag");$u.exports=function(i,e,r){i&&!r&&(i=i.prototype),i&&!Cy(i,zu)&&Ey(i,zu,{configurable:!0,value:e})}});var Yu=d((VS,Gu)=>{"use strict";var Hu=aa(),Py=Te();Gu.exports=function(i,e,r){return r.get&&Hu(r.get,e,{getter:!0}),r.set&&Hu(r.set,e,{setter:!0}),Py.f(i,e,r)}});var Qu=d((_S,Wu)=>{"use strict";var Ay=Re(),Ry=Yu(),Ny=z(),Iy=oe(),Xu=Ny("species");Wu.exports=function(i){var e=Ay(i);Iy&&e&&!e[Xu]&&Ry(e,Xu,{configurable:!0,get:function(){return this}})}});var Zu=d((DS,Ku)=>{"use strict";var My=Or(),Vy=TypeError;Ku.exports=function(i,e){if(My(e,i))return i;throw new Vy("Incorrect invocation")}});var rl=d((LS,el)=>{"use strict";var _y=z(),Dy=_y("toStringTag"),Ju={};Ju[Dy]="z";el.exports=String(Ju)==="[object z]"});var wt=d((kS,tl)=>{"use strict";var Ly=rl(),ky=k(),St=Pe(),By=z(),Uy=By("toStringTag"),jy=Object,Fy=St(function(){return arguments}())==="Arguments",zy=function(i,e){try{return i[e]}catch{}};tl.exports=Ly?St:function(i){var e,r,t;return i===void 0?"Undefined":i===null?"Null":typeof(r=zy(e=jy(i),Uy))=="string"?r:Fy?St(e):(t=St(e))==="Object"&&ky(e.callee)?"Arguments":t}});var ul=d((BS,ol)=>{"use strict";var $y=L(),Hy=D(),il=k(),Gy=wt(),Yy=Re(),Xy=vt(),al=function(){},nl=Yy("Reflect","construct"),ha=/^\s*(?:class|function)\b/,Wy=$y(ha.exec),Qy=!ha.test(al),Vr=function(e){if(!il(e))return!1;try{return nl(al,[],e),!0}catch{return!1}},sl=function(e){if(!il(e))return!1;switch(Gy(e)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return Qy||!!Wy(ha,Xy(e))}catch{return!0}};sl.sham=!0;ol.exports=!nl||Hy(function(){var i;return Vr(Vr.call)||!Vr(Object)||!Vr(function(){i=!0})||i})?sl:Vr});var hl=d((US,ll)=>{"use strict";var Ky=ul(),Zy=wr(),Jy=TypeError;ll.exports=function(i){if(Ky(i))return i;throw new Jy(Zy(i)+" is not a constructor")}});var ca=d((jS,fl)=>{"use strict";var cl=J(),e0=hl(),r0=Ae(),t0=z(),i0=t0("species");fl.exports=function(i,e){var r=cl(i).constructor,t;return r===void 0||r0(t=cl(r)[i0])?e:e0(t)}});var fa=d((FS,pl)=>{"use strict";var a0=Tr(),dl=Function.prototype,vl=dl.apply,gl=dl.call;pl.exports=typeof Reflect=="object"&&Reflect.apply||(a0?gl.bind(vl):function(){return gl.apply(vl,arguments)})});var _r=d((zS,yl)=>{"use strict";var n0=Pe(),s0=L();yl.exports=function(i){if(n0(i)==="Function")return s0(i)}});var Et=d(($S,bl)=>{"use strict";var ml=_r(),o0=be(),u0=Tr(),l0=ml(ml.bind);bl.exports=function(i,e){return o0(i),e===void 0?i:u0?l0(i,e):function(){return i.apply(e,arguments)}}});var va=d((HS,xl)=>{"use strict";var h0=Re();xl.exports=h0("document","documentElement")});var Ol=d((GS,Tl)=>{"use strict";var c0=L();Tl.exports=c0([].slice)});var wl=d((YS,Sl)=>{"use strict";var f0=TypeError;Sl.exports=function(i,e){if(i<e)throw new f0("Not enough arguments");return i}});var ga=d((XS,El)=>{"use strict";var v0=Sr();El.exports=/(?:ipad|iphone|ipod).*applewebkit/i.test(v0)});var Sa=d((WS,Vl)=>{"use strict";var ae=_(),g0=fa(),d0=Et(),Cl=k(),p0=he(),Ml=D(),ql=va(),y0=Ol(),Pl=Cr(),m0=wl(),b0=ga(),x0=Je(),xa=ae.setImmediate,Ta=ae.clearImmediate,T0=ae.process,da=ae.Dispatch,O0=ae.Function,Al=ae.MessageChannel,S0=ae.String,pa=0,Dr={},Rl="onreadystatechange",Lr,Fe,ya,ma;Ml(function(){Lr=ae.location});var Oa=function(i){if(p0(Dr,i)){var e=Dr[i];delete Dr[i],e()}},ba=function(i){return function(){Oa(i)}},Nl=function(i){Oa(i.data)},Il=function(i){ae.postMessage(S0(i),Lr.protocol+"//"+Lr.host)};(!xa||!Ta)&&(xa=function(e){m0(arguments.length,1);var r=Cl(e)?e:O0(e),t=y0(arguments,1);return Dr[++pa]=function(){g0(r,void 0,t)},Fe(pa),pa},Ta=function(e){delete Dr[e]},x0?Fe=function(i){T0.nextTick(ba(i))}:da&&da.now?Fe=function(i){da.now(ba(i))}:Al&&!b0?(ya=new Al,ma=ya.port2,ya.port1.onmessage=Nl,Fe=d0(ma.postMessage,ma)):ae.addEventListener&&Cl(ae.postMessage)&&!ae.importScripts&&Lr&&Lr.protocol!=="file:"&&!Ml(Il)?(Fe=Il,ae.addEventListener("message",Nl,!1)):Rl in Pl("script")?Fe=function(i){ql.appendChild(Pl("script"))[Rl]=function(){ql.removeChild(this),Oa(i)}}:Fe=function(i){setTimeout(ba(i),0)});Vl.exports={set:xa,clear:Ta}});var Ll=d((QS,Dl)=>{"use strict";var _l=_(),w0=oe(),E0=Object.getOwnPropertyDescriptor;Dl.exports=function(i){if(!w0)return _l[i];var e=E0(_l,i);return e&&e.value}});var wa=d((KS,Bl)=>{"use strict";var kl=function(){this.head=null,this.tail=null};kl.prototype={add:function(i){var e={item:i,next:null},r=this.tail;r?r.next=e:this.head=e,this.tail=e},get:function(){var i=this.head;if(i){var e=this.head=i.next;return e===null&&(this.tail=null),i.item}}};Bl.exports=kl});var jl=d((ZS,Ul)=>{"use strict";var C0=Sr();Ul.exports=/ipad|iphone|ipod/i.test(C0)&&typeof Pebble<"u"});var zl=d((JS,Fl)=>{"use strict";var q0=Sr();Fl.exports=/web0s(?!.*chrome)/i.test(q0)});var Ql=d((ew,Wl)=>{"use strict";var rr=_(),P0=Ll(),$l=Et(),Ea=Sa().set,A0=wa(),R0=ga(),N0=jl(),I0=zl(),Ca=Je(),Hl=rr.MutationObserver||rr.WebKitMutationObserver,Gl=rr.document,Yl=rr.process,Ct=rr.Promise,Aa=P0("queueMicrotask"),er,qa,Pa,qt,Xl;Aa||(kr=new A0,Br=function(){var i,e;for(Ca&&(i=Yl.domain)&&i.exit();e=kr.get();)try{e()}catch(r){throw kr.head&&er(),r}i&&i.enter()},!R0&&!Ca&&!I0&&Hl&&Gl?(qa=!0,Pa=Gl.createTextNode(""),new Hl(Br).observe(Pa,{characterData:!0}),er=function(){Pa.data=qa=!qa}):!N0&&Ct&&Ct.resolve?(qt=Ct.resolve(void 0),qt.constructor=Ct,Xl=$l(qt.then,qt),er=function(){Xl(Br)}):Ca?er=function(){Yl.nextTick(Br)}:(Ea=$l(Ea,rr),er=function(){Ea(Br)}),Aa=function(i){kr.head||er(),kr.add(i)});var kr,Br;Wl.exports=Aa});var Zl=d((rw,Kl)=>{"use strict";Kl.exports=function(i,e){try{arguments.length===1?console.error(i):console.error(i,e)}catch{}}});var Pt=d((tw,Jl)=>{"use strict";Jl.exports=function(i){try{return{error:!1,value:i()}}catch(e){return{error:!0,value:e}}}});var tr=d((iw,eh)=>{"use strict";var M0=_();eh.exports=M0.Promise});var Ra=d((aw,rh)=>{"use strict";rh.exports=typeof Deno=="object"&&Deno&&typeof Deno.version=="object"});var ih=d((nw,th)=>{"use strict";var V0=Ra(),_0=Je();th.exports=!V0&&!_0&&typeof window=="object"&&typeof document=="object"});var ir=d((sw,sh)=>{"use strict";var D0=_(),Ur=tr(),L0=k(),k0=ua(),B0=vt(),U0=z(),j0=ih(),F0=Ra(),z0=ve(),Na=ut(),ah=Ur&&Ur.prototype,$0=U0("species"),Ia=!1,nh=L0(D0.PromiseRejectionEvent),H0=k0("Promise",function(){var i=B0(Ur),e=i!==String(Ur);if(!e&&Na===66||z0&&!(ah.catch&&ah.finally))return!0;if(!Na||Na<51||!/native code/.test(i)){var r=new Ur(function(n){n(1)}),t=function(n){n(function(){},function(){})},a=r.constructor={};if(a[$0]=t,Ia=r.then(function(){})instanceof t,!Ia)return!0}return!e&&(j0||F0)&&!nh});sh.exports={CONSTRUCTOR:H0,REJECTION_EVENT:nh,SUBCLASSING:Ia}});var ar=d((ow,uh)=>{"use strict";var oh=be(),G0=TypeError,Y0=function(i){var e,r;this.promise=new i(function(t,a){if(e!==void 0||r!==void 0)throw new G0("Bad Promise constructor");e=t,r=a}),this.resolve=oh(e),this.reject=oh(r)};uh.exports.f=function(i){return new Y0(i)}});var qh=d(()=>{"use strict";var X0=ee(),W0=ve(),It=Je(),Ie=_(),ur=G(),lh=Ne(),hh=la(),Q0=Mr(),K0=Qu(),Z0=be(),Nt=k(),J0=le(),em=Zu(),rm=ca(),dh=Sa().set,La=Ql(),tm=Zl(),im=Pt(),am=wa(),ph=Rr(),Mt=tr(),ka=ir(),yh=ar(),Vt="Promise",mh=ka.CONSTRUCTOR,nm=ka.REJECTION_EVENT,sm=ka.SUBCLASSING,Ma=ph.getterFor(Vt),om=ph.set,nr=Mt&&Mt.prototype,ze=Mt,At=nr,bh=Ie.TypeError,Va=Ie.document,Ba=Ie.process,_a=yh.f,um=_a,lm=!!(Va&&Va.createEvent&&Ie.dispatchEvent),xh="unhandledrejection",hm="rejectionhandled",ch=0,Th=1,cm=2,Ua=1,Oh=2,Rt,fh,fm,vh,Sh=function(i){var e;return J0(i)&&Nt(e=i.then)?e:!1},wh=function(i,e){var r=e.value,t=e.state===Th,a=t?i.ok:i.fail,n=i.resolve,o=i.reject,s=i.domain,u,l,h;try{a?(t||(e.rejection===Oh&&gm(e),e.rejection=Ua),a===!0?u=r:(s&&s.enter(),u=a(r),s&&(s.exit(),h=!0)),u===i.promise?o(new bh("Promise-chain cycle")):(l=Sh(u))?ur(l,u,n,o):n(u)):o(r)}catch(f){s&&!h&&s.exit(),o(f)}},Eh=function(i,e){i.notified||(i.notified=!0,La(function(){for(var r=i.reactions,t;t=r.get();)wh(t,i);i.notified=!1,e&&!i.rejection&&vm(i)}))},Ch=function(i,e,r){var t,a;lm?(t=Va.createEvent("Event"),t.promise=e,t.reason=r,t.initEvent(i,!1,!0),Ie.dispatchEvent(t)):t={promise:e,reason:r},!nm&&(a=Ie["on"+i])?a(t):i===xh&&tm("Unhandled promise rejection",r)},vm=function(i){ur(dh,Ie,function(){var e=i.facade,r=i.value,t=gh(i),a;if(t&&(a=im(function(){It?Ba.emit("unhandledRejection",r,e):Ch(xh,e,r)}),i.rejection=It||gh(i)?Oh:Ua,a.error))throw a.value})},gh=function(i){return i.rejection!==Ua&&!i.parent},gm=function(i){ur(dh,Ie,function(){var e=i.facade;It?Ba.emit("rejectionHandled",e):Ch(hm,e,i.value)})},sr=function(i,e,r){return function(t){i(e,t,r)}},or=function(i,e,r){i.done||(i.done=!0,r&&(i=r),i.value=e,i.state=cm,Eh(i,!0))},Da=function(i,e,r){if(!i.done){i.done=!0,r&&(i=r);try{if(i.facade===e)throw new bh("Promise can't be resolved itself");var t=Sh(e);t?La(function(){var a={done:!1};try{ur(t,e,sr(Da,a,i),sr(or,a,i))}catch(n){or(a,n,i)}}):(i.value=e,i.state=Th,Eh(i,!1))}catch(a){or({done:!1},a,i)}}};if(mh&&(ze=function(e){em(this,At),Z0(e),ur(Rt,this);var r=Ma(this);try{e(sr(Da,r),sr(or,r))}catch(t){or(r,t)}},At=ze.prototype,Rt=function(e){om(this,{type:Vt,done:!1,notified:!1,parent:!1,reactions:new am,rejection:!1,state:ch,value:void 0})},Rt.prototype=lh(At,"then",function(e,r){var t=Ma(this),a=_a(rm(this,ze));return t.parent=!0,a.ok=Nt(e)?e:!0,a.fail=Nt(r)&&r,a.domain=It?Ba.domain:void 0,t.state===ch?t.reactions.add(a):La(function(){wh(a,t)}),a.promise}),fh=function(){var i=new Rt,e=Ma(i);this.promise=i,this.resolve=sr(Da,e),this.reject=sr(or,e)},yh.f=_a=function(i){return i===ze||i===fm?new fh(i):um(i)},!W0&&Nt(Mt)&&nr!==Object.prototype)){vh=nr.then,sm||lh(nr,"then",function(e,r){var t=this;return new ze(function(a,n){ur(vh,t,a,n)}).then(e,r)},{unsafe:!0});try{delete nr.constructor}catch{}hh&&hh(nr,At)}X0({global:!0,constructor:!0,wrap:!0,forced:mh},{Promise:ze});Q0(ze,Vt,!1,!0);K0(Vt)});var lr=d((hw,Ph)=>{"use strict";Ph.exports={}});var Rh=d((cw,Ah)=>{"use strict";var dm=z(),pm=lr(),ym=dm("iterator"),mm=Array.prototype;Ah.exports=function(i){return i!==void 0&&(pm.Array===i||mm[ym]===i)}});var ja=d((fw,Ih)=>{"use strict";var bm=wt(),Nh=Be(),xm=Ae(),Tm=lr(),Om=z(),Sm=Om("iterator");Ih.exports=function(i){if(!xm(i))return Nh(i,Sm)||Nh(i,"@@iterator")||Tm[bm(i)]}});var Vh=d((vw,Mh)=>{"use strict";var wm=G(),Em=be(),Cm=J(),qm=wr(),Pm=ja(),Am=TypeError;Mh.exports=function(i,e){var r=arguments.length<2?Pm(i):e;if(Em(r))return Cm(wm(r,i));throw new Am(qm(i)+" is not iterable")}});var Lh=d((gw,Dh)=>{"use strict";var Rm=G(),_h=J(),Nm=Be();Dh.exports=function(i,e,r){var t,a;_h(i);try{if(t=Nm(i,"return"),!t){if(e==="throw")throw r;return r}t=Rm(t,i)}catch(n){a=!0,t=n}if(e==="throw")throw r;if(a)throw t;return _h(t),r}});var Fa=d((dw,jh)=>{"use strict";var Im=Et(),Mm=G(),Vm=J(),_m=wr(),Dm=Rh(),Lm=xt(),kh=Or(),km=Vh(),Bm=ja(),Bh=Lh(),Um=TypeError,_t=function(i,e){this.stopped=i,this.result=e},Uh=_t.prototype;jh.exports=function(i,e,r){var t=r&&r.that,a=!!(r&&r.AS_ENTRIES),n=!!(r&&r.IS_RECORD),o=!!(r&&r.IS_ITERATOR),s=!!(r&&r.INTERRUPTED),u=Im(e,t),l,h,f,v,c,g,p,y=function(T){return l&&Bh(l,"normal",T),new _t(!0,T)},m=function(T){return a?(Vm(T),s?u(T[0],T[1],y):u(T[0],T[1])):s?u(T,y):u(T)};if(n)l=i.iterator;else if(o)l=i;else{if(h=Bm(i),!h)throw new Um(_m(i)+" is not iterable");if(Dm(h)){for(f=0,v=Lm(i);v>f;f++)if(c=m(i[f]),c&&kh(Uh,c))return c;return new _t(!1)}l=km(i,h)}for(g=n?i.next:l.next;!(p=Mm(g,l)).done;){try{c=m(p.value)}catch(T){Bh(l,"throw",T)}if(typeof c=="object"&&c&&kh(Uh,c))return c}return new _t(!1)}});var Gh=d((pw,Hh)=>{"use strict";var jm=z(),zh=jm("iterator"),$h=!1;try{Fh=0,za={next:function(){return{done:!!Fh++}},return:function(){$h=!0}},za[zh]=function(){return this},Array.from(za,function(){throw 2})}catch{}var Fh,za;Hh.exports=function(i,e){try{if(!e&&!$h)return!1}catch{return!1}var r=!1;try{var t={};t[zh]=function(){return{next:function(){return{done:r=!0}}}},i(t)}catch{}return r}});var $a=d((yw,Yh)=>{"use strict";var Fm=tr(),zm=Gh(),$m=ir().CONSTRUCTOR;Yh.exports=$m||!zm(function(i){Fm.all(i).then(void 0,function(){})})});var Xh=d(()=>{"use strict";var Hm=ee(),Gm=G(),Ym=be(),Xm=ar(),Wm=Pt(),Qm=Fa(),Km=$a();Hm({target:"Promise",stat:!0,forced:Km},{all:function(e){var r=this,t=Xm.f(r),a=t.resolve,n=t.reject,o=Wm(function(){var s=Ym(r.resolve),u=[],l=0,h=1;Qm(e,function(f){var v=l++,c=!1;h++,Gm(s,r,f).then(function(g){c||(c=!0,u[v]=g,--h||a(u))},n)}),--h||a(u)});return o.error&&n(o.value),t.promise}})});var Qh=d(()=>{"use strict";var Zm=ee(),Jm=ve(),eb=ir().CONSTRUCTOR,Ga=tr(),rb=Re(),tb=k(),ib=Ne(),Wh=Ga&&Ga.prototype;Zm({target:"Promise",proto:!0,forced:eb,real:!0},{catch:function(i){return this.then(void 0,i)}});!Jm&&tb(Ga)&&(Ha=rb("Promise").prototype.catch,Wh.catch!==Ha&&ib(Wh,"catch",Ha,{unsafe:!0}));var Ha});var Kh=d(()=>{"use strict";var ab=ee(),nb=G(),sb=be(),ob=ar(),ub=Pt(),lb=Fa(),hb=$a();ab({target:"Promise",stat:!0,forced:hb},{race:function(e){var r=this,t=ob.f(r),a=t.reject,n=ub(function(){var o=sb(r.resolve);lb(e,function(s){nb(o,r,s).then(t.resolve,a)})});return n.error&&a(n.value),t.promise}})});var Zh=d(()=>{"use strict";var cb=ee(),fb=ar(),vb=ir().CONSTRUCTOR;cb({target:"Promise",stat:!0,forced:vb},{reject:function(e){var r=fb.f(this),t=r.reject;return t(e),r.promise}})});var ec=d((Cw,Jh)=>{"use strict";var gb=J(),db=le(),pb=ar();Jh.exports=function(i,e){if(gb(i),db(e)&&e.constructor===i)return e;var r=pb.f(i),t=r.resolve;return t(e),r.promise}});var ic=d(()=>{"use strict";var yb=ee(),mb=Re(),rc=ve(),bb=tr(),tc=ir().CONSTRUCTOR,xb=ec(),Tb=mb("Promise"),Ob=rc&&!tc;yb({target:"Promise",stat:!0,forced:rc||tc},{resolve:function(e){return xb(Ob&&this===Tb?bb:this,e)}})});var ac=d(()=>{"use strict";qh();Xh();Qh();Kh();Zh();ic()});var de=d((Iw,sc)=>{"use strict";var Sb=wt(),wb=String;sc.exports=function(i){if(Sb(i)==="Symbol")throw new TypeError("Cannot convert a Symbol value to a string");return wb(i)}});var Ya=d((Mw,oc)=>{"use strict";var Eb=J();oc.exports=function(){var i=Eb(this),e="";return i.hasIndices&&(e+="d"),i.global&&(e+="g"),i.ignoreCase&&(e+="i"),i.multiline&&(e+="m"),i.dotAll&&(e+="s"),i.unicode&&(e+="u"),i.unicodeSets&&(e+="v"),i.sticky&&(e+="y"),e}});var Ka=d((Vw,uc)=>{"use strict";var Xa=D(),Cb=_(),Wa=Cb.RegExp,Qa=Xa(function(){var i=Wa("a","y");return i.lastIndex=2,i.exec("abcd")!==null}),qb=Qa||Xa(function(){return!Wa("a","y").sticky}),Pb=Qa||Xa(function(){var i=Wa("^r","gy");return i.lastIndex=2,i.exec("str")!==null});uc.exports={BROKEN_CARET:Pb,MISSED_STICKY:qb,UNSUPPORTED_Y:Qa}});var hc=d((_w,lc)=>{"use strict";var Ab=oa(),Rb=Tt();lc.exports=Object.keys||function(e){return Ab(e,Rb)}});var fc=d(cc=>{"use strict";var Nb=oe(),Ib=Hi(),Mb=Te(),Vb=J(),_b=Qe(),Db=hc();cc.f=Nb&&!Ib?Object.defineProperties:function(e,r){Vb(e);for(var t=_b(r),a=Db(r),n=a.length,o=0,s;n>o;)Mb.f(e,s=a[o++],t[s]);return e}});var jr=d((Lw,bc)=>{"use strict";var Lb=J(),kb=fc(),vc=Tt(),Bb=dt(),Ub=va(),jb=Cr(),Fb=gt(),gc=">",dc="<",Ja="prototype",en="script",yc=Fb("IE_PROTO"),Za=function(){},mc=function(i){return dc+en+gc+i+dc+"/"+en+gc},pc=function(i){i.write(mc("")),i.close();var e=i.parentWindow.Object;return i=null,e},zb=function(){var i=jb("iframe"),e="java"+en+":",r;return i.style.display="none",Ub.appendChild(i),i.src=String(e),r=i.contentWindow.document,r.open(),r.write(mc("document.F=Object")),r.close(),r.F},Dt,Lt=function(){try{Dt=new ActiveXObject("htmlfile")}catch{}Lt=typeof document<"u"?document.domain&&Dt?pc(Dt):zb():pc(Dt);for(var i=vc.length;i--;)delete Lt[Ja][vc[i]];return Lt()};Bb[yc]=!0;bc.exports=Object.create||function(e,r){var t;return e!==null?(Za[Ja]=Lb(e),t=new Za,Za[Ja]=null,t[yc]=e):t=Lt(),r===void 0?t:kb.f(t,r)}});var Tc=d((kw,xc)=>{"use strict";var $b=D(),Hb=_(),Gb=Hb.RegExp;xc.exports=$b(function(){var i=Gb(".","s");return!(i.dotAll&&i.test(`
`)&&i.flags==="s")})});var Sc=d((Bw,Oc)=>{"use strict";var Yb=D(),Xb=_(),Wb=Xb.RegExp;Oc.exports=Yb(function(){var i=Wb("(?<a>b)","g");return i.exec("b").groups.a!=="b"||"b".replace(i,"$<a>c")!=="bc"})});var Ut=d((Uw,Ec)=>{"use strict";var hr=G(),Bt=L(),Qb=de(),Kb=Ya(),Zb=Ka(),Jb=ct(),ex=jr(),rx=Rr().get,tx=Tc(),ix=Sc(),ax=Jb("native-string-replace",String.prototype.replace),kt=RegExp.prototype.exec,tn=kt,nx=Bt("".charAt),sx=Bt("".indexOf),ox=Bt("".replace),rn=Bt("".slice),an=function(){var i=/a/,e=/b*/g;return hr(kt,i,"a"),hr(kt,e,"a"),i.lastIndex!==0||e.lastIndex!==0}(),wc=Zb.BROKEN_CARET,nn=/()??/.exec("")[1]!==void 0,ux=an||nn||wc||tx||ix;ux&&(tn=function(e){var r=this,t=rx(r),a=Qb(e),n=t.raw,o,s,u,l,h,f,v;if(n)return n.lastIndex=r.lastIndex,o=hr(tn,n,a),r.lastIndex=n.lastIndex,o;var c=t.groups,g=wc&&r.sticky,p=hr(Kb,r),y=r.source,m=0,T=a;if(g&&(p=ox(p,"y",""),sx(p,"g")===-1&&(p+="g"),T=rn(a,r.lastIndex),r.lastIndex>0&&(!r.multiline||r.multiline&&nx(a,r.lastIndex-1)!==`
`)&&(y="(?: "+y+")",T=" "+T,m++),s=new RegExp("^(?:"+y+")",p)),nn&&(s=new RegExp("^"+y+"$(?!\\s)",p)),an&&(u=r.lastIndex),l=hr(kt,g?s:r,T),g?l?(l.input=rn(l.input,m),l[0]=rn(l[0],m),l.index=r.lastIndex,r.lastIndex+=l[0].length):r.lastIndex=0:an&&l&&(r.lastIndex=r.global?l.index+l[0].length:u),nn&&l&&l.length>1&&hr(ax,l[0],s,function(){for(h=1;h<arguments.length-2;h++)arguments[h]===void 0&&(l[h]=void 0)}),l&&c)for(l.groups=f=ex(null),h=0;h<c.length;h++)v=c[h],f[v[0]]=l[v[1]];return l});Ec.exports=tn});var qc=d(()=>{"use strict";var lx=ee(),Cc=Ut();lx({target:"RegExp",proto:!0,forced:/./.exec!==Cc},{exec:Cc})});var jt=d((zw,Ic)=>{"use strict";qc();var Pc=G(),Ac=Ne(),hx=Ut(),Rc=D(),Nc=z(),cx=Ze(),fx=Nc("species"),sn=RegExp.prototype;Ic.exports=function(i,e,r,t){var a=Nc(i),n=!Rc(function(){var l={};return l[a]=function(){return 7},""[i](l)!==7}),o=n&&!Rc(function(){var l=!1,h=/a/;return i==="split"&&(h={},h.constructor={},h.constructor[fx]=function(){return h},h.flags="",h[a]=/./[a]),h.exec=function(){return l=!0,null},h[a](""),!l});if(!n||!o||r){var s=/./[a],u=e(a,""[i],function(l,h,f,v,c){var g=h.exec;return g===hx||g===sn.exec?n&&!c?{done:!0,value:Pc(s,h,f,v)}:{done:!0,value:Pc(l,f,h,v)}:{done:!1}});Ac(String.prototype,i,u[0]),Ac(sn,a,u[1])}t&&cx(sn[a],"sham",!0)}});var Dc=d(($w,_c)=>{"use strict";var on=L(),vx=Nr(),gx=de(),dx=ue(),px=on("".charAt),Mc=on("".charCodeAt),yx=on("".slice),Vc=function(i){return function(e,r){var t=gx(dx(e)),a=vx(r),n=t.length,o,s;return a<0||a>=n?i?"":void 0:(o=Mc(t,a),o<55296||o>56319||a+1===n||(s=Mc(t,a+1))<56320||s>57343?i?px(t,a):o:i?yx(t,a,a+2):(o-55296<<10)+(s-56320)+65536)}};_c.exports={codeAt:Vc(!1),charAt:Vc(!0)}});var Ft=d((Hw,Lc)=>{"use strict";var mx=Dc().charAt;Lc.exports=function(i,e,r){return e+(r?mx(i,e).length:1)}});var zt=d((Gw,Bc)=>{"use strict";var kc=G(),bx=J(),xx=k(),Tx=Pe(),Ox=Ut(),Sx=TypeError;Bc.exports=function(i,e){var r=i.exec;if(xx(r)){var t=kc(r,i,e);return t!==null&&bx(t),t}if(Tx(i)==="RegExp")return kc(Ox,i,e);throw new Sx("RegExp#exec called on incompatible receiver")}});var jc=d(()=>{"use strict";var wx=G(),Ex=jt(),Cx=J(),qx=Ae(),Px=je(),un=de(),Ax=ue(),Rx=Be(),Nx=Ft(),Uc=zt();Ex("match",function(i,e,r){return[function(a){var n=Ax(this),o=qx(a)?void 0:Rx(a,i);return o?wx(o,a,n):new RegExp(a)[i](un(n))},function(t){var a=Cx(this),n=un(t),o=r(e,a,n);if(o.done)return o.value;if(!a.global)return Uc(a,n);var s=a.unicode;a.lastIndex=0;for(var u=[],l=0,h;(h=Uc(a,n))!==null;){var f=un(h[0]);u[l]=f,f===""&&(a.lastIndex=Nx(n,Px(a.lastIndex),s)),l++}return l===0?null:u}]})});var zc=d((Ww,Fc)=>{"use strict";var cn=L(),Ix=Er(),Mx=Math.floor,ln=cn("".charAt),Vx=cn("".replace),hn=cn("".slice),_x=/\$([$&'`]|\d{1,2}|<[^>]*>)/g,Dx=/\$([$&'`]|\d{1,2})/g;Fc.exports=function(i,e,r,t,a,n){var o=r+i.length,s=t.length,u=Dx;return a!==void 0&&(a=Ix(a),u=_x),Vx(n,u,function(l,h){var f;switch(ln(h,0)){case"$":return"$";case"&":return i;case"`":return hn(e,0,r);case"'":return hn(e,o);case"<":f=a[hn(h,1,-1)];break;default:var v=+h;if(v===0)return l;if(v>s){var c=Mx(v/10);return c===0?l:c<=s?t[c-1]===void 0?ln(h,1):t[c-1]+ln(h,1):l}f=t[v-1]}return f===void 0?"":f})}});var Xc=d(()=>{"use strict";var Lx=fa(),$c=G(),$t=L(),kx=jt(),Bx=D(),Ux=J(),jx=k(),Fx=Ae(),zx=Nr(),$x=je(),cr=de(),Hx=ue(),Gx=Ft(),Yx=Be(),Xx=zc(),Wx=zt(),Qx=z(),vn=Qx("replace"),Kx=Math.max,Zx=Math.min,Jx=$t([].concat),fn=$t([].push),Hc=$t("".indexOf),Gc=$t("".slice),e1=function(i){return i===void 0?i:String(i)},r1=function(){return"a".replace(/./,"$0")==="$0"}(),Yc=function(){return/./[vn]?/./[vn]("a","$0")==="":!1}(),t1=!Bx(function(){var i=/./;return i.exec=function(){var e=[];return e.groups={a:"7"},e},"".replace(i,"$<a>")!=="7"});kx("replace",function(i,e,r){var t=Yc?"$":"$0";return[function(n,o){var s=Hx(this),u=Fx(n)?void 0:Yx(n,vn);return u?$c(u,n,s,o):$c(e,cr(s),n,o)},function(a,n){var o=Ux(this),s=cr(a);if(typeof n=="string"&&Hc(n,t)===-1&&Hc(n,"$<")===-1){var u=r(e,o,s,n);if(u.done)return u.value}var l=jx(n);l||(n=cr(n));var h=o.global,f;h&&(f=o.unicode,o.lastIndex=0);for(var v=[],c;c=Wx(o,s),!(c===null||(fn(v,c),!h));){var g=cr(c[0]);g===""&&(o.lastIndex=Gx(s,$x(o.lastIndex),f))}for(var p="",y=0,m=0;m<v.length;m++){c=v[m];for(var T=cr(c[0]),x=Kx(Zx(zx(c.index),s.length),0),O=[],w,C=1;C<c.length;C++)fn(O,e1(c[C]));var S=c.groups;if(l){var P=Jx([T],O,x,s);S!==void 0&&fn(P,S),w=cr(Lx(n,void 0,P))}else w=Xx(T,s,x,O,S,n);x>=y&&(p+=Gc(s,y,x)+w,y=x+T.length)}return p+Gc(s,y)}]},!t1||!r1||Yc)});var Qc=d((Zw,Wc)=>{"use strict";var i1=le(),a1=Pe(),n1=z(),s1=n1("match");Wc.exports=function(i){var e;return i1(i)&&((e=i[s1])!==void 0?!!e:a1(i)==="RegExp")}});var Ht=d((Jw,Kc)=>{"use strict";var o1=Qc(),u1=TypeError;Kc.exports=function(i){if(o1(i))throw new u1("The method doesn't accept regular expressions");return i}});var Gt=d((eE,Zc)=>{"use strict";var l1=z(),h1=l1("match");Zc.exports=function(i){var e=/./;try{"/./"[i](e)}catch{try{return e[h1]=!1,"/./"[i](e)}catch{}}return!1}});var rf=d(()=>{"use strict";var c1=ee(),f1=_r(),v1=qr().f,g1=je(),Jc=de(),d1=Ht(),p1=ue(),y1=Gt(),m1=ve(),b1=f1("".slice),x1=Math.min,ef=y1("startsWith"),T1=!m1&&!ef&&!!function(){var i=v1(String.prototype,"startsWith");return i&&!i.writable}();c1({target:"String",proto:!0,forced:!T1&&!ef},{startsWith:function(e){var r=Jc(p1(this));d1(e);var t=g1(x1(arguments.length>1?arguments[1]:void 0,r.length)),a=Jc(e);return b1(r,t,t+a.length)===a}})});var af=d((iE,tf)=>{"use strict";var O1=z(),S1=jr(),w1=Te().f,gn=O1("unscopables"),dn=Array.prototype;dn[gn]===void 0&&w1(dn,gn,{configurable:!0,value:S1(null)});tf.exports=function(i){dn[gn][i]=!0}});var sf=d((aE,nf)=>{"use strict";var E1=D();nf.exports=!E1(function(){function i(){}return i.prototype.constructor=null,Object.getPrototypeOf(new i)!==i.prototype})});var yn=d((nE,uf)=>{"use strict";var C1=he(),q1=k(),P1=Er(),A1=gt(),R1=sf(),of=A1("IE_PROTO"),pn=Object,N1=pn.prototype;uf.exports=R1?pn.getPrototypeOf:function(i){var e=P1(i);if(C1(e,of))return e[of];var r=e.constructor;return q1(r)&&e instanceof r?r.prototype:e instanceof pn?N1:null}});var Tn=d((sE,cf)=>{"use strict";var I1=D(),M1=k(),V1=le(),_1=jr(),lf=yn(),D1=Ne(),L1=z(),k1=ve(),xn=L1("iterator"),hf=!1,Oe,mn,bn;[].keys&&(bn=[].keys(),"next"in bn?(mn=lf(lf(bn)),mn!==Object.prototype&&(Oe=mn)):hf=!0);var B1=!V1(Oe)||I1(function(){var i={};return Oe[xn].call(i)!==i});B1?Oe={}:k1&&(Oe=_1(Oe));M1(Oe[xn])||D1(Oe,xn,function(){return this});cf.exports={IteratorPrototype:Oe,BUGGY_SAFARI_ITERATORS:hf}});var vf=d((oE,ff)=>{"use strict";var U1=Tn().IteratorPrototype,j1=jr(),F1=st(),z1=Mr(),$1=lr(),H1=function(){return this};ff.exports=function(i,e,r,t){var a=e+" Iterator";return i.prototype=j1(U1,{next:F1(+!t,r)}),z1(i,a,!1,!0),$1[a]=H1,i}});var wf=d((uE,Sf)=>{"use strict";var G1=ee(),Y1=G(),Yt=ve(),Tf=Pr(),X1=k(),W1=vf(),gf=yn(),df=la(),Q1=Mr(),K1=Ze(),On=Ne(),Z1=z(),pf=lr(),Of=Tn(),J1=Tf.PROPER,e2=Tf.CONFIGURABLE,yf=Of.IteratorPrototype,Xt=Of.BUGGY_SAFARI_ITERATORS,Fr=Z1("iterator"),mf="keys",zr="values",bf="entries",xf=function(){return this};Sf.exports=function(i,e,r,t,a,n,o){W1(r,e,t);var s=function(m){if(m===a&&v)return v;if(!Xt&&m&&m in h)return h[m];switch(m){case mf:return function(){return new r(this,m)};case zr:return function(){return new r(this,m)};case bf:return function(){return new r(this,m)}}return function(){return new r(this)}},u=e+" Iterator",l=!1,h=i.prototype,f=h[Fr]||h["@@iterator"]||a&&h[a],v=!Xt&&f||s(a),c=e==="Array"&&h.entries||f,g,p,y;if(c&&(g=gf(c.call(new i)),g!==Object.prototype&&g.next&&(!Yt&&gf(g)!==yf&&(df?df(g,yf):X1(g[Fr])||On(g,Fr,xf)),Q1(g,u,!0,!0),Yt&&(pf[u]=xf))),J1&&a===zr&&f&&f.name!==zr&&(!Yt&&e2?K1(h,"name",zr):(l=!0,v=function(){return Y1(f,this)})),a)if(p={values:s(zr),keys:n?v:s(mf),entries:s(bf)},o)for(y in p)(Xt||l||!(y in h))&&On(h,y,p[y]);else G1({target:e,proto:!0,forced:Xt||l},p);return(!Yt||o)&&h[Fr]!==v&&On(h,Fr,v,{name:a}),pf[e]=v,p}});var Cf=d((lE,Ef)=>{"use strict";Ef.exports=function(i,e){return{value:i,done:e}}});var wn=d((hE,Nf)=>{"use strict";var r2=Qe(),Sn=af(),qf=lr(),Af=Rr(),t2=Te().f,i2=wf(),Wt=Cf(),a2=ve(),n2=oe(),Rf="Array Iterator",s2=Af.set,o2=Af.getterFor(Rf);Nf.exports=i2(Array,"Array",function(i,e){s2(this,{type:Rf,target:r2(i),index:0,kind:e})},function(){var i=o2(this),e=i.target,r=i.index++;if(!e||r>=e.length)return i.target=void 0,Wt(void 0,!0);switch(i.kind){case"keys":return Wt(r,!1);case"values":return Wt(e[r],!1)}return Wt([r,e[r]],!1)},"values");var Pf=qf.Arguments=qf.Array;Sn("keys");Sn("values");Sn("entries");if(!a2&&n2&&Pf.name!=="values")try{t2(Pf,"name",{value:"values"})}catch{}});var Mf=d((cE,If)=>{"use strict";If.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}});var Df=d((fE,_f)=>{"use strict";var u2=Cr(),En=u2("span").classList,Vf=En&&En.constructor&&En.constructor.prototype;_f.exports=Vf===Object.prototype?void 0:Vf});var jf=d(()=>{"use strict";var Lf=_(),Bf=Mf(),l2=Df(),$r=wn(),kf=Ze(),h2=Mr(),c2=z(),Cn=c2("iterator"),qn=$r.values,Uf=function(i,e){if(i){if(i[Cn]!==qn)try{kf(i,Cn,qn)}catch{i[Cn]=qn}if(h2(i,e,!0),Bf[e]){for(var r in $r)if(i[r]!==$r[r])try{kf(i,r,$r[r])}catch{i[r]=$r[r]}}}};for(Qt in Bf)Uf(Lf[Qt]&&Lf[Qt].prototype,Qt);var Qt;Uf(l2,"DOMTokenList")});var Xf=d((OE,Yf)=>{"use strict";var f2=be(),v2=Er(),g2=Ri(),d2=xt(),$f=TypeError,Hf="Reduce of empty array with no initial value",Gf=function(i){return function(e,r,t,a){var n=v2(e),o=g2(n),s=d2(n);if(f2(r),s===0&&t<2)throw new $f(Hf);var u=i?s-1:0,l=i?-1:1;if(t<2)for(;;){if(u in o){a=o[u],u+=l;break}if(u+=l,i?u<0:s<=u)throw new $f(Hf)}for(;i?u>=0:s>u;u+=l)u in o&&(a=r(a,o[u],u,n));return a}};Yf.exports={left:Gf(!1),right:Gf(!0)}});var Pn=d((SE,Wf)=>{"use strict";var p2=D();Wf.exports=function(i,e){var r=[][i];return!!r&&p2(function(){r.call(null,e||function(){return 1},1)})}});var Kf=d(()=>{"use strict";var y2=ee(),m2=Xf().left,b2=Pn(),Qf=ut(),x2=Je(),T2=!x2&&Qf>79&&Qf<83,O2=T2||!b2("reduce");y2({target:"Array",proto:!0,forced:O2},{reduce:function(e){var r=arguments.length;return m2(this,e,r,r>1?arguments[1]:void 0)}})});var ev=d(()=>{"use strict";var S2=ee(),w2=_r(),E2=qr().f,C2=je(),Zf=de(),q2=Ht(),P2=ue(),A2=Gt(),R2=ve(),N2=w2("".slice),I2=Math.min,Jf=A2("endsWith"),M2=!R2&&!Jf&&!!function(){var i=E2(String.prototype,"endsWith");return i&&!i.writable}();S2({target:"String",proto:!0,forced:!M2&&!Jf},{endsWith:function(e){var r=Zf(P2(this));q2(e);var t=arguments.length>1?arguments[1]:void 0,a=r.length,n=t===void 0?a:I2(C2(t),a),o=Zf(e);return N2(r,n-o.length,n)===o}})});var nv=d(()=>{"use strict";var An=G(),av=L(),V2=jt(),_2=J(),D2=Ae(),L2=ue(),k2=ca(),B2=Ft(),U2=je(),rv=de(),j2=Be(),tv=zt(),F2=Ka(),z2=D(),fr=F2.UNSUPPORTED_Y,$2=4294967295,H2=Math.min,Rn=av([].push),Nn=av("".slice),G2=!z2(function(){var i=/(?:)/,e=i.exec;i.exec=function(){return e.apply(this,arguments)};var r="ab".split(i);return r.length!==2||r[0]!=="a"||r[1]!=="b"}),iv="abbc".split(/(b)*/)[1]==="c"||"test".split(/(?:)/,-1).length!==4||"ab".split(/(?:ab)*/).length!==2||".".split(/(.?)(.?)/).length!==4||".".split(/()()/).length>1||"".split(/.?/).length;V2("split",function(i,e,r){var t="0".split(void 0,0).length?function(a,n){return a===void 0&&n===0?[]:An(e,this,a,n)}:e;return[function(n,o){var s=L2(this),u=D2(n)?void 0:j2(n,i);return u?An(u,n,s,o):An(t,rv(s),n,o)},function(a,n){var o=_2(this),s=rv(a);if(!iv){var u=r(t,o,s,n,t!==e);if(u.done)return u.value}var l=k2(o,RegExp),h=o.unicode,f=(o.ignoreCase?"i":"")+(o.multiline?"m":"")+(o.unicode?"u":"")+(fr?"g":"y"),v=new l(fr?"^(?:"+o.source+")":o,f),c=n===void 0?$2:n>>>0;if(c===0)return[];if(s.length===0)return tv(v,s)===null?[s]:[];for(var g=0,p=0,y=[];p<s.length;){v.lastIndex=fr?0:p;var m=tv(v,fr?Nn(s,p):s),T;if(m===null||(T=H2(U2(v.lastIndex+(fr?p:0)),s.length))===g)p=B2(s,p,h);else{if(Rn(y,Nn(s,g,p)),y.length===c)return y;for(var x=1;x<=m.length-1;x++)if(Rn(y,m[x]),y.length===c)return y;p=g=T}}return Rn(y,Nn(s,g)),y}]},iv||!G2,fr)});var ov=d((sv,Hr)=>{"use strict";(function(){var i,e,r,t,a,n;typeof performance<"u"&&performance!==null&&performance.now?Hr.exports=function(){return performance.now()}:typeof process<"u"&&process!==null&&process.hrtime?(Hr.exports=function(){return(i()-a)/1e6},e=process.hrtime,i=function(){var o;return o=e(),o[0]*1e9+o[1]},t=i(),n=process.uptime()*1e9,a=t-n):Date.now?(Hr.exports=function(){return Date.now()-r},r=Date.now()):(Hr.exports=function(){return new Date().getTime()-r},r=new Date().getTime())}).call(sv)});var lv=d((RE,ei)=>{"use strict";var Y2=ov(),Se=typeof window>"u"?global:window,Zt=["moz","webkit"],gr="AnimationFrame",dr=Se["request"+gr],Gr=Se["cancel"+gr]||Se["cancelRequest"+gr];for(vr=0;!dr&&vr<Zt.length;vr++)dr=Se[Zt[vr]+"Request"+gr],Gr=Se[Zt[vr]+"Cancel"+gr]||Se[Zt[vr]+"CancelRequest"+gr];var vr;(!dr||!Gr)&&(Jt=0,In=0,Me=[],uv=1e3/60,dr=function(i){if(Me.length===0){var e=Y2(),r=Math.max(0,uv-(e-Jt));Jt=r+e,setTimeout(function(){var t=Me.slice(0);Me.length=0;for(var a=0;a<t.length;a++)if(!t[a].cancelled)try{t[a].callback(Jt)}catch(n){setTimeout(function(){throw n},0)}},Math.round(r))}return Me.push({handle:++In,callback:i,cancelled:!1}),In},Gr=function(i){for(var e=0;e<Me.length;e++)Me[e].handle===i&&(Me[e].cancelled=!0)});var Jt,In,Me,uv;ei.exports=function(i){return dr.call(Se,i)};ei.exports.cancel=function(){Gr.apply(Se,arguments)};ei.exports.polyfill=function(i){i||(i=Se),i.requestAnimationFrame=dr,i.cancelAnimationFrame=Gr}});var Mn=d((NE,hv)=>{"use strict";hv.exports=`	
\v\f\r \xA0\u1680\u2000\u2001\u2002\u2003\u2004\u2005\u2006\u2007\u2008\u2009\u200A\u202F\u205F\u3000\u2028\u2029\uFEFF`});var vv=d((IE,fv)=>{"use strict";var X2=L(),W2=ue(),Q2=de(),_n=Mn(),cv=X2("".replace),K2=RegExp("^["+_n+"]+"),Z2=RegExp("(^|[^"+_n+"])["+_n+"]+$"),Vn=function(i){return function(e){var r=Q2(W2(e));return i&1&&(r=cv(r,K2,"")),i&2&&(r=cv(r,Z2,"$1")),r}};fv.exports={start:Vn(1),end:Vn(2),trim:Vn(3)}});var yv=d((ME,pv)=>{"use strict";var J2=Pr().PROPER,eT=D(),gv=Mn(),dv="\u200B\x85\u180E";pv.exports=function(i){return eT(function(){return!!gv[i]()||dv[i]()!==dv||J2&&gv[i].name!==i})}});var mv=d(()=>{"use strict";var rT=ee(),tT=vv().trim,iT=yv();rT({target:"String",proto:!0,forced:iT("trim")},{trim:function(){return tT(this)}})});var xv=d((DE,bv)=>{"use strict";bv.exports=function(i){this.ok=!1,this.alpha=1,i.charAt(0)=="#"&&(i=i.substr(1,6)),i=i.replace(/ /g,""),i=i.toLowerCase();var e={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"};i=e[i]||i;for(var r=[{re:/^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*((?:\d?\.)?\d)\)$/,example:["rgba(123, 234, 45, 0.8)","rgba(255,234,245,1.0)"],process:function(u){return[parseInt(u[1]),parseInt(u[2]),parseInt(u[3]),parseFloat(u[4])]}},{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(u){return[parseInt(u[1]),parseInt(u[2]),parseInt(u[3])]}},{re:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,example:["#00ff00","336699"],process:function(u){return[parseInt(u[1],16),parseInt(u[2],16),parseInt(u[3],16)]}},{re:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,example:["#fb0","f0f"],process:function(u){return[parseInt(u[1]+u[1],16),parseInt(u[2]+u[2],16),parseInt(u[3]+u[3],16)]}}],t=0;t<r.length;t++){var a=r[t].re,n=r[t].process,o=a.exec(i);if(o){var s=n(o);this.r=s[0],this.g=s[1],this.b=s[2],s.length>3&&(this.alpha=s[3]),this.ok=!0}}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.alpha=this.alpha<0?0:this.alpha>1||isNaN(this.alpha)?1:this.alpha,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toRGBA=function(){return"rgba("+this.r+", "+this.g+", "+this.b+", "+this.alpha+")"},this.toHex=function(){var u=this.r.toString(16),l=this.g.toString(16),h=this.b.toString(16);return u.length==1&&(u="0"+u),l.length==1&&(l="0"+l),h.length==1&&(h="0"+h),"#"+u+l+h},this.getHelpXML=function(){for(var u=new Array,l=0;l<r.length;l++)for(var h=r[l].example,f=0;f<h.length;f++)u[u.length]=h[f];for(var v in e)u[u.length]=v;var c=document.createElement("ul");c.setAttribute("id","rgbcolor-examples");for(var l=0;l<u.length;l++)try{var g=document.createElement("li"),p=new RGBColor(u[l]),y=document.createElement("div");y.style.cssText="margin: 3px; border: 1px solid black; background:"+p.toHex()+"; color:"+p.toHex(),y.appendChild(document.createTextNode("test"));var m=document.createTextNode(" "+u[l]+" -> "+p.toRGB()+" -> "+p.toHex());g.appendChild(y),g.appendChild(m),c.appendChild(g)}catch{}return c}}});var Ov=d(()=>{"use strict";var aT=ee(),nT=_r(),sT=na().indexOf,oT=Pn(),Dn=nT([].indexOf),Tv=!!Dn&&1/Dn([1],1,-0)<0,uT=Tv||!oT("indexOf");aT({target:"Array",proto:!0,forced:uT},{indexOf:function(e){var r=arguments.length>1?arguments[1]:void 0;return Tv?Dn(this,e,r)||0:sT(this,e,r)}})});var wv=d(()=>{"use strict";var lT=ee(),hT=L(),cT=Ht(),fT=ue(),Sv=de(),vT=Gt(),gT=hT("".indexOf);lT({target:"String",proto:!0,forced:!vT("includes")},{includes:function(e){return!!~gT(Sv(fT(this)),Sv(cT(e)),arguments.length>1?arguments[1]:void 0)}})});var Cv=d((jE,Ev)=>{"use strict";var dT=Pe();Ev.exports=Array.isArray||function(e){return dT(e)==="Array"}});var Pv=d(()=>{"use strict";var pT=ee(),yT=L(),mT=Cv(),bT=yT([].reverse),qv=[1,2];pT({target:"Array",proto:!0,forced:String(qv)===String(qv.reverse())},{reverse:function(){return mT(this)&&(this.length=this.length),bT(this)}})});var Uv=d((HE,Bv)=>{"use strict";var wT=G(),ET=he(),CT=Or(),qT=Ya(),kv=RegExp.prototype;Bv.exports=function(i){var e=i.flags;return e===void 0&&!("flags"in kv)&&!ET(i,"flags")&&CT(kv,i)?wT(qT,i):e}});var $v=d(()=>{"use strict";var PT=Pr().PROPER,AT=Ne(),RT=J(),jv=de(),NT=D(),IT=Uv(),Bn="toString",Fv=RegExp.prototype,zv=Fv[Bn],MT=NT(function(){return zv.call({source:"a",flags:"b"})!=="/a/b"}),VT=PT&&zv.name!==Bn;(MT||VT)&&AT(Fv,Bn,function(){var e=RT(this),r=jv(e.source),t=jv(IT(e));return"/"+r+"/"+t},{unsafe:!0})});var WE=Z(ac(),1);function nc(i,e,r,t,a,n,o){try{var s=i[n](o),u=s.value}catch(l){return void r(l)}s.done?e(u):Promise.resolve(u).then(t,a)}function xe(i){return function(){var e=this,r=arguments;return new Promise(function(t,a){var n=i.apply(e,r);function o(u){nc(n,t,a,o,s,"next",u)}function s(u){nc(n,t,a,o,s,"throw",u)}o(void 0)})}}var KE=Z(jc(),1),ZE=Z(Xc(),1),JE=Z(rf(),1),eC=Z(wn(),1),rC=Z(jf(),1);function Ff(i,e){if(br(i)!="object"||!i)return i;var r=i[Symbol.toPrimitive];if(r!==void 0){var t=r.call(i,e||"default");if(br(t)!="object")return t;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(i)}function zf(i){var e=Ff(i,"string");return br(e)=="symbol"?e:e+""}function Kt(i,e,r){return(e=zf(e))in i?Object.defineProperty(i,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):i[e]=r,i}var iC=Z(Kf(),1),aC=Z(ev(),1),nC=Z(nv(),1),ni=Z(lv(),1),sC=Z(mv(),1),si=Z(xv(),1),oC=Z(Ov(),1),uC=Z(wv(),1),lC=Z(Pv(),1);var _v=function(i,e){return(_v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(r,t){r.__proto__=t}||function(r,t){for(var a in t)Object.prototype.hasOwnProperty.call(t,a)&&(r[a]=t[a])})(i,e)};function Dv(i,e){if(typeof e!="function"&&e!==null)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=i}_v(i,e),i.prototype=e===null?Object.create(e):(r.prototype=e.prototype,new r)}function xT(i){var e="";Array.isArray(i)||(i=[i]);for(var r=0;r<i.length;r++){var t=i[r];if(t.type===b.CLOSE_PATH)e+="z";else if(t.type===b.HORIZ_LINE_TO)e+=(t.relative?"h":"H")+t.x;else if(t.type===b.VERT_LINE_TO)e+=(t.relative?"v":"V")+t.y;else if(t.type===b.MOVE_TO)e+=(t.relative?"m":"M")+t.x+" "+t.y;else if(t.type===b.LINE_TO)e+=(t.relative?"l":"L")+t.x+" "+t.y;else if(t.type===b.CURVE_TO)e+=(t.relative?"c":"C")+t.x1+" "+t.y1+" "+t.x2+" "+t.y2+" "+t.x+" "+t.y;else if(t.type===b.SMOOTH_CURVE_TO)e+=(t.relative?"s":"S")+t.x2+" "+t.y2+" "+t.x+" "+t.y;else if(t.type===b.QUAD_TO)e+=(t.relative?"q":"Q")+t.x1+" "+t.y1+" "+t.x+" "+t.y;else if(t.type===b.SMOOTH_QUAD_TO)e+=(t.relative?"t":"T")+t.x+" "+t.y;else{if(t.type!==b.ARC)throw new Error('Unexpected command type "'+t.type+'" at index '+r+".");e+=(t.relative?"a":"A")+t.rX+" "+t.rY+" "+t.xRot+" "+ +t.lArcFlag+" "+ +t.sweepFlag+" "+t.x+" "+t.y}}return e}function kn(i,e){var r=i[0],t=i[1];return[r*Math.cos(e)-t*Math.sin(e),r*Math.sin(e)+t*Math.cos(e)]}function ye(){for(var i=[],e=0;e<arguments.length;e++)i[e]=arguments[e];for(var r=0;r<i.length;r++)if(typeof i[r]!="number")throw new Error("assertNumbers arguments["+r+"] is not a number. "+typeof i[r]+" == typeof "+i[r]);return!0}var Ve=Math.PI;function Ln(i,e,r){i.lArcFlag=i.lArcFlag===0?0:1,i.sweepFlag=i.sweepFlag===0?0:1;var t=i.rX,a=i.rY,n=i.x,o=i.y;t=Math.abs(i.rX),a=Math.abs(i.rY);var s=kn([(e-n)/2,(r-o)/2],-i.xRot/180*Ve),u=s[0],l=s[1],h=Math.pow(u,2)/Math.pow(t,2)+Math.pow(l,2)/Math.pow(a,2);1<h&&(t*=Math.sqrt(h),a*=Math.sqrt(h)),i.rX=t,i.rY=a;var f=Math.pow(t,2)*Math.pow(l,2)+Math.pow(a,2)*Math.pow(u,2),v=(i.lArcFlag!==i.sweepFlag?1:-1)*Math.sqrt(Math.max(0,(Math.pow(t,2)*Math.pow(a,2)-f)/f)),c=t*l/a*v,g=-a*u/t*v,p=kn([c,g],i.xRot/180*Ve);i.cX=p[0]+(e+n)/2,i.cY=p[1]+(r+o)/2,i.phi1=Math.atan2((l-g)/a,(u-c)/t),i.phi2=Math.atan2((-l-g)/a,(-u-c)/t),i.sweepFlag===0&&i.phi2>i.phi1&&(i.phi2-=2*Ve),i.sweepFlag===1&&i.phi2<i.phi1&&(i.phi2+=2*Ve),i.phi1*=180/Ve,i.phi2*=180/Ve}function Av(i,e,r){ye(i,e,r);var t=i*i+e*e-r*r;if(0>t)return[];if(t===0)return[[i*r/(i*i+e*e),e*r/(i*i+e*e)]];var a=Math.sqrt(t);return[[(i*r+e*a)/(i*i+e*e),(e*r-i*a)/(i*i+e*e)],[(i*r-e*a)/(i*i+e*e),(e*r+i*a)/(i*i+e*e)]]}var U,we=Math.PI/180;function Rv(i,e,r){return(1-r)*i+r*e}function Nv(i,e,r,t){return i+Math.cos(t/180*Ve)*e+Math.sin(t/180*Ve)*r}function Iv(i,e,r,t){var a=1e-6,n=e-i,o=r-e,s=3*n+3*(t-r)-6*o,u=6*(o-n),l=3*n;return Math.abs(s)<a?[-l/u]:function(h,f,v){v===void 0&&(v=1e-6);var c=h*h/4-f;if(c<-v)return[];if(c<=v)return[-h/2];var g=Math.sqrt(c);return[-h/2-g,-h/2+g]}(u/s,l/s,a)}function Mv(i,e,r,t,a){var n=1-a;return i*(n*n*n)+e*(3*n*n*a)+r*(3*n*a*a)+t*(a*a*a)}(function(i){function e(){return a(function(s,u,l){return s.relative&&(s.x1!==void 0&&(s.x1+=u),s.y1!==void 0&&(s.y1+=l),s.x2!==void 0&&(s.x2+=u),s.y2!==void 0&&(s.y2+=l),s.x!==void 0&&(s.x+=u),s.y!==void 0&&(s.y+=l),s.relative=!1),s})}function r(){var s=NaN,u=NaN,l=NaN,h=NaN;return a(function(f,v,c){return f.type&b.SMOOTH_CURVE_TO&&(f.type=b.CURVE_TO,s=isNaN(s)?v:s,u=isNaN(u)?c:u,f.x1=f.relative?v-s:2*v-s,f.y1=f.relative?c-u:2*c-u),f.type&b.CURVE_TO?(s=f.relative?v+f.x2:f.x2,u=f.relative?c+f.y2:f.y2):(s=NaN,u=NaN),f.type&b.SMOOTH_QUAD_TO&&(f.type=b.QUAD_TO,l=isNaN(l)?v:l,h=isNaN(h)?c:h,f.x1=f.relative?v-l:2*v-l,f.y1=f.relative?c-h:2*c-h),f.type&b.QUAD_TO?(l=f.relative?v+f.x1:f.x1,h=f.relative?c+f.y1:f.y1):(l=NaN,h=NaN),f})}function t(){var s=NaN,u=NaN;return a(function(l,h,f){if(l.type&b.SMOOTH_QUAD_TO&&(l.type=b.QUAD_TO,s=isNaN(s)?h:s,u=isNaN(u)?f:u,l.x1=l.relative?h-s:2*h-s,l.y1=l.relative?f-u:2*f-u),l.type&b.QUAD_TO){s=l.relative?h+l.x1:l.x1,u=l.relative?f+l.y1:l.y1;var v=l.x1,c=l.y1;l.type=b.CURVE_TO,l.x1=((l.relative?0:h)+2*v)/3,l.y1=((l.relative?0:f)+2*c)/3,l.x2=(l.x+2*v)/3,l.y2=(l.y+2*c)/3}else s=NaN,u=NaN;return l})}function a(s){var u=0,l=0,h=NaN,f=NaN;return function(v){if(isNaN(h)&&!(v.type&b.MOVE_TO))throw new Error("path must start with moveto");var c=s(v,u,l,h,f);return v.type&b.CLOSE_PATH&&(u=h,l=f),v.x!==void 0&&(u=v.relative?u+v.x:v.x),v.y!==void 0&&(l=v.relative?l+v.y:v.y),v.type&b.MOVE_TO&&(h=u,f=l),c}}function n(s,u,l,h,f,v){return ye(s,u,l,h,f,v),a(function(c,g,p,y){var m=c.x1,T=c.x2,x=c.relative&&!isNaN(y),O=c.x!==void 0?c.x:x?0:g,w=c.y!==void 0?c.y:x?0:p;function C(ce){return ce*ce}c.type&b.HORIZ_LINE_TO&&u!==0&&(c.type=b.LINE_TO,c.y=c.relative?0:p),c.type&b.VERT_LINE_TO&&l!==0&&(c.type=b.LINE_TO,c.x=c.relative?0:g),c.x!==void 0&&(c.x=c.x*s+w*l+(x?0:f)),c.y!==void 0&&(c.y=O*u+c.y*h+(x?0:v)),c.x1!==void 0&&(c.x1=c.x1*s+c.y1*l+(x?0:f)),c.y1!==void 0&&(c.y1=m*u+c.y1*h+(x?0:v)),c.x2!==void 0&&(c.x2=c.x2*s+c.y2*l+(x?0:f)),c.y2!==void 0&&(c.y2=T*u+c.y2*h+(x?0:v));var S=s*h-u*l;if(c.xRot!==void 0&&(s!==1||u!==0||l!==0||h!==1))if(S===0)delete c.rX,delete c.rY,delete c.xRot,delete c.lArcFlag,delete c.sweepFlag,c.type=b.LINE_TO;else{var P=c.xRot*Math.PI/180,R=Math.sin(P),M=Math.cos(P),V=1/C(c.rX),N=1/C(c.rY),X=C(M)*V+C(R)*N,W=2*R*M*(V-N),H=C(R)*V+C(M)*N,Q=X*h*h-W*u*h+H*u*u,Y=W*(s*h+u*l)-2*(X*l*h+H*s*u),K=X*l*l-W*s*l+H*s*s,I=(Math.atan2(Y,Q-K)+Math.PI)%Math.PI/2,A=Math.sin(I),re=Math.cos(I);c.rX=Math.abs(S)/Math.sqrt(Q*C(re)+Y*A*re+K*C(A)),c.rY=Math.abs(S)/Math.sqrt(Q*C(A)-Y*A*re+K*C(re)),c.xRot=180*I/Math.PI}return c.sweepFlag!==void 0&&0>S&&(c.sweepFlag=+!c.sweepFlag),c})}function o(){return function(s){var u={};for(var l in s)u[l]=s[l];return u}}i.ROUND=function(s){function u(l){return Math.round(l*s)/s}return s===void 0&&(s=1e13),ye(s),function(l){return l.x1!==void 0&&(l.x1=u(l.x1)),l.y1!==void 0&&(l.y1=u(l.y1)),l.x2!==void 0&&(l.x2=u(l.x2)),l.y2!==void 0&&(l.y2=u(l.y2)),l.x!==void 0&&(l.x=u(l.x)),l.y!==void 0&&(l.y=u(l.y)),l.rX!==void 0&&(l.rX=u(l.rX)),l.rY!==void 0&&(l.rY=u(l.rY)),l}},i.TO_ABS=e,i.TO_REL=function(){return a(function(s,u,l){return s.relative||(s.x1!==void 0&&(s.x1-=u),s.y1!==void 0&&(s.y1-=l),s.x2!==void 0&&(s.x2-=u),s.y2!==void 0&&(s.y2-=l),s.x!==void 0&&(s.x-=u),s.y!==void 0&&(s.y-=l),s.relative=!0),s})},i.NORMALIZE_HVZ=function(s,u,l){return s===void 0&&(s=!0),u===void 0&&(u=!0),l===void 0&&(l=!0),a(function(h,f,v,c,g){if(isNaN(c)&&!(h.type&b.MOVE_TO))throw new Error("path must start with moveto");return u&&h.type&b.HORIZ_LINE_TO&&(h.type=b.LINE_TO,h.y=h.relative?0:v),l&&h.type&b.VERT_LINE_TO&&(h.type=b.LINE_TO,h.x=h.relative?0:f),s&&h.type&b.CLOSE_PATH&&(h.type=b.LINE_TO,h.x=h.relative?c-f:c,h.y=h.relative?g-v:g),h.type&b.ARC&&(h.rX===0||h.rY===0)&&(h.type=b.LINE_TO,delete h.rX,delete h.rY,delete h.xRot,delete h.lArcFlag,delete h.sweepFlag),h})},i.NORMALIZE_ST=r,i.QT_TO_C=t,i.INFO=a,i.SANITIZE=function(s){s===void 0&&(s=0),ye(s);var u=NaN,l=NaN,h=NaN,f=NaN;return a(function(v,c,g,p,y){var m=Math.abs,T=!1,x=0,O=0;if(v.type&b.SMOOTH_CURVE_TO&&(x=isNaN(u)?0:c-u,O=isNaN(l)?0:g-l),v.type&(b.CURVE_TO|b.SMOOTH_CURVE_TO)?(u=v.relative?c+v.x2:v.x2,l=v.relative?g+v.y2:v.y2):(u=NaN,l=NaN),v.type&b.SMOOTH_QUAD_TO?(h=isNaN(h)?c:2*c-h,f=isNaN(f)?g:2*g-f):v.type&b.QUAD_TO?(h=v.relative?c+v.x1:v.x1,f=v.relative?g+v.y1:v.y2):(h=NaN,f=NaN),v.type&b.LINE_COMMANDS||v.type&b.ARC&&(v.rX===0||v.rY===0||!v.lArcFlag)||v.type&b.CURVE_TO||v.type&b.SMOOTH_CURVE_TO||v.type&b.QUAD_TO||v.type&b.SMOOTH_QUAD_TO){var w=v.x===void 0?0:v.relative?v.x:v.x-c,C=v.y===void 0?0:v.relative?v.y:v.y-g;x=isNaN(h)?v.x1===void 0?x:v.relative?v.x:v.x1-c:h-c,O=isNaN(f)?v.y1===void 0?O:v.relative?v.y:v.y1-g:f-g;var S=v.x2===void 0?0:v.relative?v.x:v.x2-c,P=v.y2===void 0?0:v.relative?v.y:v.y2-g;m(w)<=s&&m(C)<=s&&m(x)<=s&&m(O)<=s&&m(S)<=s&&m(P)<=s&&(T=!0)}return v.type&b.CLOSE_PATH&&m(c-p)<=s&&m(g-y)<=s&&(T=!0),T?[]:v})},i.MATRIX=n,i.ROTATE=function(s,u,l){u===void 0&&(u=0),l===void 0&&(l=0),ye(s,u,l);var h=Math.sin(s),f=Math.cos(s);return n(f,h,-h,f,u-u*f+l*h,l-u*h-l*f)},i.TRANSLATE=function(s,u){return u===void 0&&(u=0),ye(s,u),n(1,0,0,1,s,u)},i.SCALE=function(s,u){return u===void 0&&(u=s),ye(s,u),n(s,0,0,u,0,0)},i.SKEW_X=function(s){return ye(s),n(1,0,Math.atan(s),1,0,0)},i.SKEW_Y=function(s){return ye(s),n(1,Math.atan(s),0,1,0,0)},i.X_AXIS_SYMMETRY=function(s){return s===void 0&&(s=0),ye(s),n(-1,0,0,1,s,0)},i.Y_AXIS_SYMMETRY=function(s){return s===void 0&&(s=0),ye(s),n(1,0,0,-1,0,s)},i.A_TO_C=function(){return a(function(s,u,l){return b.ARC===s.type?function(h,f,v){var c,g,p,y;h.cX||Ln(h,f,v);for(var m=Math.min(h.phi1,h.phi2),T=Math.max(h.phi1,h.phi2)-m,x=Math.ceil(T/90),O=new Array(x),w=f,C=v,S=0;S<x;S++){var P=Rv(h.phi1,h.phi2,S/x),R=Rv(h.phi1,h.phi2,(S+1)/x),M=R-P,V=4/3*Math.tan(M*we/4),N=[Math.cos(P*we)-V*Math.sin(P*we),Math.sin(P*we)+V*Math.cos(P*we)],X=N[0],W=N[1],H=[Math.cos(R*we),Math.sin(R*we)],Q=H[0],Y=H[1],K=[Q+V*Math.sin(R*we),Y-V*Math.cos(R*we)],I=K[0],A=K[1];O[S]={relative:h.relative,type:b.CURVE_TO};var re=function(ce,me){var qe=kn([ce*h.rX,me*h.rY],h.xRot),Xe=qe[0],Qr=qe[1];return[h.cX+Xe,h.cY+Qr]};c=re(X,W),O[S].x1=c[0],O[S].y1=c[1],g=re(I,A),O[S].x2=g[0],O[S].y2=g[1],p=re(Q,Y),O[S].x=p[0],O[S].y=p[1],h.relative&&(O[S].x1-=w,O[S].y1-=C,O[S].x2-=w,O[S].y2-=C,O[S].x-=w,O[S].y-=C),w=(y=[O[S].x,O[S].y])[0],C=y[1]}return O}(s,s.relative?0:u,s.relative?0:l):s})},i.ANNOTATE_ARCS=function(){return a(function(s,u,l){return s.relative&&(u=0,l=0),b.ARC===s.type&&Ln(s,u,l),s})},i.CLONE=o,i.CALCULATE_BOUNDS=function(){var s=function(v){var c={};for(var g in v)c[g]=v[g];return c},u=e(),l=t(),h=r(),f=a(function(v,c,g){var p=h(l(u(s(v))));function y(A){A>f.maxX&&(f.maxX=A),A<f.minX&&(f.minX=A)}function m(A){A>f.maxY&&(f.maxY=A),A<f.minY&&(f.minY=A)}if(p.type&b.DRAWING_COMMANDS&&(y(c),m(g)),p.type&b.HORIZ_LINE_TO&&y(p.x),p.type&b.VERT_LINE_TO&&m(p.y),p.type&b.LINE_TO&&(y(p.x),m(p.y)),p.type&b.CURVE_TO){y(p.x),m(p.y);for(var T=0,x=Iv(c,p.x1,p.x2,p.x);T<x.length;T++)0<(I=x[T])&&1>I&&y(Mv(c,p.x1,p.x2,p.x,I));for(var O=0,w=Iv(g,p.y1,p.y2,p.y);O<w.length;O++)0<(I=w[O])&&1>I&&m(Mv(g,p.y1,p.y2,p.y,I))}if(p.type&b.ARC){y(p.x),m(p.y),Ln(p,c,g);for(var C=p.xRot/180*Math.PI,S=Math.cos(C)*p.rX,P=Math.sin(C)*p.rX,R=-Math.sin(C)*p.rY,M=Math.cos(C)*p.rY,V=p.phi1<p.phi2?[p.phi1,p.phi2]:-180>p.phi2?[p.phi2+360,p.phi1+360]:[p.phi2,p.phi1],N=V[0],X=V[1],W=function(A){var re=A[0],ce=A[1],me=180*Math.atan2(ce,re)/Math.PI;return me<N?me+360:me},H=0,Q=Av(R,-S,0).map(W);H<Q.length;H++)(I=Q[H])>N&&I<X&&y(Nv(p.cX,S,R,I));for(var Y=0,K=Av(M,-P,0).map(W);Y<K.length;Y++){var I;(I=K[Y])>N&&I<X&&m(Nv(p.cY,P,M,I))}}return v});return f.minX=1/0,f.maxX=-1/0,f.minY=1/0,f.maxY=-1/0,f}})(U||(U={}));var pe,Lv=function(){function i(){}return i.prototype.round=function(e){return this.transform(U.ROUND(e))},i.prototype.toAbs=function(){return this.transform(U.TO_ABS())},i.prototype.toRel=function(){return this.transform(U.TO_REL())},i.prototype.normalizeHVZ=function(e,r,t){return this.transform(U.NORMALIZE_HVZ(e,r,t))},i.prototype.normalizeST=function(){return this.transform(U.NORMALIZE_ST())},i.prototype.qtToC=function(){return this.transform(U.QT_TO_C())},i.prototype.aToC=function(){return this.transform(U.A_TO_C())},i.prototype.sanitize=function(e){return this.transform(U.SANITIZE(e))},i.prototype.translate=function(e,r){return this.transform(U.TRANSLATE(e,r))},i.prototype.scale=function(e,r){return this.transform(U.SCALE(e,r))},i.prototype.rotate=function(e,r,t){return this.transform(U.ROTATE(e,r,t))},i.prototype.matrix=function(e,r,t,a,n,o){return this.transform(U.MATRIX(e,r,t,a,n,o))},i.prototype.skewX=function(e){return this.transform(U.SKEW_X(e))},i.prototype.skewY=function(e){return this.transform(U.SKEW_Y(e))},i.prototype.xSymmetry=function(e){return this.transform(U.X_AXIS_SYMMETRY(e))},i.prototype.ySymmetry=function(e){return this.transform(U.Y_AXIS_SYMMETRY(e))},i.prototype.annotateArcs=function(){return this.transform(U.ANNOTATE_ARCS())},i}(),TT=function(i){return i===" "||i==="	"||i==="\r"||i===`
`},Vv=function(i){return 48<=i.charCodeAt(0)&&i.charCodeAt(0)<=57},OT=function(i){function e(){var r=i.call(this)||this;return r.curNumber="",r.curCommandType=-1,r.curCommandRelative=!1,r.canParseCommandOrComma=!0,r.curNumberHasExp=!1,r.curNumberHasExpDigits=!1,r.curNumberHasDecimal=!1,r.curArgs=[],r}return Dv(e,i),e.prototype.finish=function(r){if(r===void 0&&(r=[]),this.parse(" ",r),this.curArgs.length!==0||!this.canParseCommandOrComma)throw new SyntaxError("Unterminated command at the path end.");return r},e.prototype.parse=function(r,t){var a=this;t===void 0&&(t=[]);for(var n=function(f){t.push(f),a.curArgs.length=0,a.canParseCommandOrComma=!0},o=0;o<r.length;o++){var s=r[o],u=!(this.curCommandType!==b.ARC||this.curArgs.length!==3&&this.curArgs.length!==4||this.curNumber.length!==1||this.curNumber!=="0"&&this.curNumber!=="1"),l=Vv(s)&&(this.curNumber==="0"&&s==="0"||u);if(!Vv(s)||l)if(s!=="e"&&s!=="E")if(s!=="-"&&s!=="+"||!this.curNumberHasExp||this.curNumberHasExpDigits)if(s!=="."||this.curNumberHasExp||this.curNumberHasDecimal||u){if(this.curNumber&&this.curCommandType!==-1){var h=Number(this.curNumber);if(isNaN(h))throw new SyntaxError("Invalid number ending at "+o);if(this.curCommandType===b.ARC){if(this.curArgs.length===0||this.curArgs.length===1){if(0>h)throw new SyntaxError('Expected positive number, got "'+h+'" at index "'+o+'"')}else if((this.curArgs.length===3||this.curArgs.length===4)&&this.curNumber!=="0"&&this.curNumber!=="1")throw new SyntaxError('Expected a flag, got "'+this.curNumber+'" at index "'+o+'"')}this.curArgs.push(h),this.curArgs.length===ST[this.curCommandType]&&(b.HORIZ_LINE_TO===this.curCommandType?n({type:b.HORIZ_LINE_TO,relative:this.curCommandRelative,x:h}):b.VERT_LINE_TO===this.curCommandType?n({type:b.VERT_LINE_TO,relative:this.curCommandRelative,y:h}):this.curCommandType===b.MOVE_TO||this.curCommandType===b.LINE_TO||this.curCommandType===b.SMOOTH_QUAD_TO?(n({type:this.curCommandType,relative:this.curCommandRelative,x:this.curArgs[0],y:this.curArgs[1]}),b.MOVE_TO===this.curCommandType&&(this.curCommandType=b.LINE_TO)):this.curCommandType===b.CURVE_TO?n({type:b.CURVE_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x2:this.curArgs[2],y2:this.curArgs[3],x:this.curArgs[4],y:this.curArgs[5]}):this.curCommandType===b.SMOOTH_CURVE_TO?n({type:b.SMOOTH_CURVE_TO,relative:this.curCommandRelative,x2:this.curArgs[0],y2:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===b.QUAD_TO?n({type:b.QUAD_TO,relative:this.curCommandRelative,x1:this.curArgs[0],y1:this.curArgs[1],x:this.curArgs[2],y:this.curArgs[3]}):this.curCommandType===b.ARC&&n({type:b.ARC,relative:this.curCommandRelative,rX:this.curArgs[0],rY:this.curArgs[1],xRot:this.curArgs[2],lArcFlag:this.curArgs[3],sweepFlag:this.curArgs[4],x:this.curArgs[5],y:this.curArgs[6]})),this.curNumber="",this.curNumberHasExpDigits=!1,this.curNumberHasExp=!1,this.curNumberHasDecimal=!1,this.canParseCommandOrComma=!0}if(!TT(s))if(s===","&&this.canParseCommandOrComma)this.canParseCommandOrComma=!1;else if(s!=="+"&&s!=="-"&&s!==".")if(l)this.curNumber=s,this.curNumberHasDecimal=!1;else{if(this.curArgs.length!==0)throw new SyntaxError("Unterminated command at index "+o+".");if(!this.canParseCommandOrComma)throw new SyntaxError('Unexpected character "'+s+'" at index '+o+". Command cannot follow comma");if(this.canParseCommandOrComma=!1,s!=="z"&&s!=="Z")if(s==="h"||s==="H")this.curCommandType=b.HORIZ_LINE_TO,this.curCommandRelative=s==="h";else if(s==="v"||s==="V")this.curCommandType=b.VERT_LINE_TO,this.curCommandRelative=s==="v";else if(s==="m"||s==="M")this.curCommandType=b.MOVE_TO,this.curCommandRelative=s==="m";else if(s==="l"||s==="L")this.curCommandType=b.LINE_TO,this.curCommandRelative=s==="l";else if(s==="c"||s==="C")this.curCommandType=b.CURVE_TO,this.curCommandRelative=s==="c";else if(s==="s"||s==="S")this.curCommandType=b.SMOOTH_CURVE_TO,this.curCommandRelative=s==="s";else if(s==="q"||s==="Q")this.curCommandType=b.QUAD_TO,this.curCommandRelative=s==="q";else if(s==="t"||s==="T")this.curCommandType=b.SMOOTH_QUAD_TO,this.curCommandRelative=s==="t";else{if(s!=="a"&&s!=="A")throw new SyntaxError('Unexpected character "'+s+'" at index '+o+".");this.curCommandType=b.ARC,this.curCommandRelative=s==="a"}else t.push({type:b.CLOSE_PATH}),this.canParseCommandOrComma=!0,this.curCommandType=-1}else this.curNumber=s,this.curNumberHasDecimal=s==="."}else this.curNumber+=s,this.curNumberHasDecimal=!0;else this.curNumber+=s;else this.curNumber+=s,this.curNumberHasExp=!0;else this.curNumber+=s,this.curNumberHasExpDigits=this.curNumberHasExp}return t},e.prototype.transform=function(r){return Object.create(this,{parse:{value:function(t,a){a===void 0&&(a=[]);for(var n=0,o=Object.getPrototypeOf(this).parse.call(this,t);n<o.length;n++){var s=o[n],u=r(s);Array.isArray(u)?a.push.apply(a,u):a.push(u)}return a}}})},e}(Lv),b=function(i){function e(r){var t=i.call(this)||this;return t.commands=typeof r=="string"?e.parse(r):r,t}return Dv(e,i),e.prototype.encode=function(){return e.encode(this.commands)},e.prototype.getBounds=function(){var r=U.CALCULATE_BOUNDS();return this.transform(r),r},e.prototype.transform=function(r){for(var t=[],a=0,n=this.commands;a<n.length;a++){var o=r(n[a]);Array.isArray(o)?t.push.apply(t,o):t.push(o)}return this.commands=t,this},e.encode=function(r){return xT(r)},e.parse=function(r){var t=new OT,a=[];return t.parse(r,a),t.finish(a),a},e.CLOSE_PATH=1,e.MOVE_TO=2,e.HORIZ_LINE_TO=4,e.VERT_LINE_TO=8,e.LINE_TO=16,e.CURVE_TO=32,e.SMOOTH_CURVE_TO=64,e.QUAD_TO=128,e.SMOOTH_QUAD_TO=256,e.ARC=512,e.LINE_COMMANDS=e.LINE_TO|e.HORIZ_LINE_TO|e.VERT_LINE_TO,e.DRAWING_COMMANDS=e.HORIZ_LINE_TO|e.VERT_LINE_TO|e.LINE_TO|e.CURVE_TO|e.SMOOTH_CURVE_TO|e.QUAD_TO|e.SMOOTH_QUAD_TO|e.ARC,e}(Lv),ST=((pe={})[b.MOVE_TO]=2,pe[b.LINE_TO]=2,pe[b.HORIZ_LINE_TO]=1,pe[b.VERT_LINE_TO]=1,pe[b.CLOSE_PATH]=0,pe[b.QUAD_TO]=4,pe[b.SMOOTH_QUAD_TO]=2,pe[b.CURVE_TO]=6,pe[b.SMOOTH_CURVE_TO]=4,pe[b.ARC]=7,pe);var cC=Z($v(),1);function ri(i){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?ri=function(e){return typeof e}:ri=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ri(i)}function _T(i,e){if(!(i instanceof e))throw new TypeError("Cannot call a class as a function")}var DT=[512,512,456,512,328,456,335,512,405,328,271,456,388,335,292,512,454,405,364,328,298,271,496,456,420,388,360,335,312,292,273,512,482,454,428,405,383,364,345,328,312,298,284,271,259,496,475,456,437,420,404,388,374,360,347,335,323,312,302,292,282,273,265,512,497,482,468,454,441,428,417,405,394,383,373,364,354,345,337,328,320,312,305,298,291,284,278,271,265,259,507,496,485,475,465,456,446,437,428,420,412,404,396,388,381,374,367,360,354,347,341,335,329,323,318,312,307,302,297,292,287,282,278,273,269,265,261,512,505,497,489,482,475,468,461,454,447,441,435,428,422,417,411,405,399,394,389,383,378,373,368,364,359,354,350,345,341,337,332,328,324,320,316,312,309,305,301,298,294,291,287,284,281,278,274,271,268,265,262,259,257,507,501,496,491,485,480,475,470,465,460,456,451,446,442,437,433,428,424,420,416,412,408,404,400,396,392,388,385,381,377,374,370,367,363,360,357,354,350,347,344,341,338,335,332,329,326,323,320,318,315,312,310,307,304,302,299,297,294,292,289,287,285,282,280,278,275,273,271,269,267,265,263,261,259],LT=[9,11,12,13,13,14,14,15,15,15,15,16,16,16,16,17,17,17,17,17,17,17,18,18,18,18,18,18,18,18,18,19,19,19,19,19,19,19,19,19,19,19,19,19,19,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,20,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,21,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,22,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,23,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24];function kT(i,e,r,t,a){if(typeof i=="string"&&(i=document.getElementById(i)),!i||ri(i)!=="object"||!("getContext"in i))throw new TypeError("Expecting canvas with `getContext` method in processCanvasRGB(A) calls!");var n=i.getContext("2d");try{return n.getImageData(e,r,t,a)}catch(o){throw new Error("unable to access image data: "+o)}}function Gv(i,e,r,t,a,n){if(!(isNaN(n)||n<1)){n|=0;var o=kT(i,e,r,t,a);o=BT(o,e,r,t,a,n),i.getContext("2d").putImageData(o,e,r)}}function BT(i,e,r,t,a,n){for(var o=i.data,s=2*n+1,u=t-1,l=a-1,h=n+1,f=h*(h+1)/2,v=new Hv,c=v,g,p=1;p<s;p++)c=c.next=new Hv,p===h&&(g=c);c.next=v;for(var y=null,m=null,T=0,x=0,O=DT[n],w=LT[n],C=0;C<a;C++){c=v;for(var S=o[x],P=o[x+1],R=o[x+2],M=o[x+3],V=0;V<h;V++)c.r=S,c.g=P,c.b=R,c.a=M,c=c.next;for(var N=0,X=0,W=0,H=0,Q=h*S,Y=h*P,K=h*R,I=h*M,A=f*S,re=f*P,ce=f*R,me=f*M,qe=1;qe<h;qe++){var Xe=x+((u<qe?u:qe)<<2),Qr=o[Xe],qs=o[Xe+1],Ps=o[Xe+2],As=o[Xe+3],Kr=h-qe;A+=(c.r=Qr)*Kr,re+=(c.g=qs)*Kr,ce+=(c.b=Ps)*Kr,me+=(c.a=As)*Kr,N+=Qr,X+=qs,W+=Ps,H+=As,c=c.next}y=v,m=g;for(var di=0;di<t;di++){var pi=me*O>>>w;if(o[x+3]=pi,pi!==0){var yi=255/pi;o[x]=(A*O>>>w)*yi,o[x+1]=(re*O>>>w)*yi,o[x+2]=(ce*O>>>w)*yi}else o[x]=o[x+1]=o[x+2]=0;A-=Q,re-=Y,ce-=K,me-=I,Q-=y.r,Y-=y.g,K-=y.b,I-=y.a;var _e=di+n+1;_e=T+(_e<u?_e:u)<<2,N+=y.r=o[_e],X+=y.g=o[_e+1],W+=y.b=o[_e+2],H+=y.a=o[_e+3],A+=N,re+=X,ce+=W,me+=H,y=y.next;var Zr=m,Rs=Zr.r,Ns=Zr.g,Is=Zr.b,Ms=Zr.a;Q+=Rs,Y+=Ns,K+=Is,I+=Ms,N-=Rs,X-=Ns,W-=Is,H-=Ms,m=m.next,x+=4}T+=t}for(var We=0;We<t;We++){x=We<<2;var De=o[x],Le=o[x+1],ke=o[x+2],te=o[x+3],mi=h*De,bi=h*Le,xi=h*ke,Ti=h*te,Jr=f*De,et=f*Le,rt=f*ke,tt=f*te;c=v;for(var Vs=0;Vs<h;Vs++)c.r=De,c.g=Le,c.b=ke,c.a=te,c=c.next;for(var _s=t,Oi=0,Si=0,wi=0,Ei=0,it=1;it<=n;it++){x=_s+We<<2;var at=h-it;Jr+=(c.r=De=o[x])*at,et+=(c.g=Le=o[x+1])*at,rt+=(c.b=ke=o[x+2])*at,tt+=(c.a=te=o[x+3])*at,Ei+=De,Oi+=Le,Si+=ke,wi+=te,c=c.next,it<l&&(_s+=t)}x=We,y=v,m=g;for(var Ci=0;Ci<a;Ci++){var ie=x<<2;o[ie+3]=te=tt*O>>>w,te>0?(te=255/te,o[ie]=(Jr*O>>>w)*te,o[ie+1]=(et*O>>>w)*te,o[ie+2]=(rt*O>>>w)*te):o[ie]=o[ie+1]=o[ie+2]=0,Jr-=mi,et-=bi,rt-=xi,tt-=Ti,mi-=y.r,bi-=y.g,xi-=y.b,Ti-=y.a,ie=We+((ie=Ci+h)<l?ie:l)*t<<2,Jr+=Ei+=y.r=o[ie],et+=Oi+=y.g=o[ie+1],rt+=Si+=y.b=o[ie+2],tt+=wi+=y.a=o[ie+3],y=y.next,mi+=De=m.r,bi+=Le=m.g,xi+=ke=m.b,Ti+=te=m.a,Ei-=De,Oi-=Le,Si-=ke,wi-=te,m=m.next,x+=t}}return i}var Hv=function i(){_T(this,i),this.r=0,this.g=0,this.b=0,this.a=0,this.next=null};function UT(){var{DOMParser:i}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},e={window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:i,createCanvas(r,t){return new OffscreenCanvas(r,t)},createImage(r){return xe(function*(){var t=yield fetch(r),a=yield t.blob(),n=yield createImageBitmap(a);return n})()}};return(typeof DOMParser<"u"||typeof i>"u")&&Reflect.deleteProperty(e,"DOMParser"),e}function jT(i){var{DOMParser:e,canvas:r,fetch:t}=i;return{window:null,ignoreAnimation:!0,ignoreMouse:!0,DOMParser:e,fetch:t,createCanvas:r.createCanvas,createImage:r.loadImage}}var vC=Object.freeze({__proto__:null,offscreen:UT,node:jT});function mr(i){return i.replace(/(?!\u3000)\s+/gm," ")}function FT(i){return i.replace(/^[\n \t]+/,"")}function zT(i){return i.replace(/[\n \t]+$/,"")}function ne(i){var e=(i||"").match(/-?(\d+(?:\.\d*(?:[eE][+-]?\d+)?)?|\.\d+)(?=\D|$)/gm)||[];return e.map(parseFloat)}var $T=/^[A-Z-]+$/;function HT(i){return $T.test(i)?i.toLowerCase():i}function Yv(i){var e=/url\(('([^']+)'|"([^"]+)"|([^'")]+))\)/.exec(i)||[];return e[2]||e[3]||e[4]}function GT(i){if(!i.startsWith("rgb"))return i;var e=3,r=i.replace(/\d+(\.\d+)?/g,(t,a)=>e--&&a?String(Math.round(parseFloat(t))):t);return r}var YT=/(\[[^\]]+\])/g,XT=/(#[^\s+>~.[:]+)/g,WT=/(\.[^\s+>~.[:]+)/g,QT=/(::[^\s+>~.[:]+|:first-line|:first-letter|:before|:after)/gi,KT=/(:[\w-]+\([^)]*\))/gi,ZT=/(:[^\s+>~.[:]+)/g,JT=/([^\s+>~.[:]+)/g;function $e(i,e){var r=e.exec(i);return r?[i.replace(e," "),r.length]:[i,0]}function eO(i){var e=[0,0,0],r=i.replace(/:not\(([^)]*)\)/g,"     $1 ").replace(/{[\s\S]*/gm," "),t=0;return[r,t]=$e(r,YT),e[1]+=t,[r,t]=$e(r,XT),e[0]+=t,[r,t]=$e(r,WT),e[1]+=t,[r,t]=$e(r,QT),e[2]+=t,[r,t]=$e(r,KT),e[1]+=t,[r,t]=$e(r,ZT),e[1]+=t,r=r.replace(/[*\s+>~]/g," ").replace(/[#.]/g," "),[r,t]=$e(r,JT),e[2]+=t,e.join("")}var pr=1e-8;function Xv(i){return Math.sqrt(Math.pow(i[0],2)+Math.pow(i[1],2))}function Un(i,e){return(i[0]*e[0]+i[1]*e[1])/(Xv(i)*Xv(e))}function Wv(i,e){return(i[0]*e[1]<i[1]*e[0]?-1:1)*Math.acos(Un(i,e))}function Qv(i){return i*i*i}function Kv(i){return 3*i*i*(1-i)}function Zv(i){return 3*i*(1-i)*(1-i)}function Jv(i){return(1-i)*(1-i)*(1-i)}function eg(i){return i*i}function rg(i){return 2*i*(1-i)}function tg(i){return(1-i)*(1-i)}var E=(()=>{class i{constructor(r,t,a){this.document=r,this.name=t,this.value=a,this.isNormalizedColor=!1}static empty(r){return new i(r,"EMPTY","")}split(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:" ",{document:t,name:a}=this;return mr(this.getString()).trim().split(r).map(n=>new i(t,a,n))}hasValue(r){var{value:t}=this;return t!==null&&t!==""&&(r||t!==0)&&typeof t<"u"}isString(r){var{value:t}=this,a=typeof t=="string";return!a||!r?a:r.test(t)}isUrlDefinition(){return this.isString(/^url\(/)}isPixels(){if(!this.hasValue())return!1;var r=this.getString();switch(!0){case r.endsWith("px"):case/^[0-9]+$/.test(r):return!0;default:return!1}}setValue(r){return this.value=r,this}getValue(r){return typeof r>"u"||this.hasValue()?this.value:r}getNumber(r){if(!this.hasValue())return typeof r>"u"?0:parseFloat(r);var{value:t}=this,a=parseFloat(t);return this.isString(/%$/)&&(a/=100),a}getString(r){return typeof r>"u"||this.hasValue()?typeof this.value>"u"?"":String(this.value):String(r)}getColor(r){var t=this.getString(r);return this.isNormalizedColor||(this.isNormalizedColor=!0,t=GT(t),this.value=t),t}getDpi(){return 96}getRem(){return this.document.rootEmSize}getEm(){return this.document.emSize}getUnits(){return this.getString().replace(/[0-9.-]/g,"")}getPixels(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!this.hasValue())return 0;var[a,n]=typeof r=="boolean"?[void 0,r]:[r],{viewPort:o}=this.document.screen;switch(!0){case this.isString(/vmin$/):return this.getNumber()/100*Math.min(o.computeSize("x"),o.computeSize("y"));case this.isString(/vmax$/):return this.getNumber()/100*Math.max(o.computeSize("x"),o.computeSize("y"));case this.isString(/vw$/):return this.getNumber()/100*o.computeSize("x");case this.isString(/vh$/):return this.getNumber()/100*o.computeSize("y");case this.isString(/rem$/):return this.getNumber()*this.getRem();case this.isString(/em$/):return this.getNumber()*this.getEm();case this.isString(/ex$/):return this.getNumber()*this.getEm()/2;case this.isString(/px$/):return this.getNumber();case this.isString(/pt$/):return this.getNumber()*this.getDpi()*(1/72);case this.isString(/pc$/):return this.getNumber()*15;case this.isString(/cm$/):return this.getNumber()*this.getDpi()/2.54;case this.isString(/mm$/):return this.getNumber()*this.getDpi()/25.4;case this.isString(/in$/):return this.getNumber()*this.getDpi();case(this.isString(/%$/)&&n):return this.getNumber()*this.getEm();case this.isString(/%$/):return this.getNumber()*o.computeSize(a);default:{var s=this.getNumber();return t&&s<1?s*o.computeSize(a):s}}}getMilliseconds(){return this.hasValue()?this.isString(/ms$/)?this.getNumber():this.getNumber()*1e3:0}getRadians(){if(!this.hasValue())return 0;switch(!0){case this.isString(/deg$/):return this.getNumber()*(Math.PI/180);case this.isString(/grad$/):return this.getNumber()*(Math.PI/200);case this.isString(/rad$/):return this.getNumber();default:return this.getNumber()*(Math.PI/180)}}getDefinition(){var r=this.getString(),t=/#([^)'"]+)/.exec(r);return t&&(t=t[1]),t||(t=r),this.document.definitions[t]}getFillStyleDefinition(r,t){var a=this.getDefinition();if(!a)return null;if(typeof a.createGradient=="function")return a.createGradient(this.document.ctx,r,t);if(typeof a.createPattern=="function"){if(a.getHrefAttribute().hasValue()){var n=a.getAttribute("patternTransform");a=a.getHrefAttribute().getDefinition(),n.hasValue()&&a.getAttribute("patternTransform",!0).setValue(n.value)}return a.createPattern(this.document.ctx,r,t)}return null}getTextBaseline(){return this.hasValue()?i.textBaselineMapping[this.getString()]:null}addOpacity(r){for(var t=this.getColor(),a=t.length,n=0,o=0;o<a&&(t[o]===","&&n++,n!==3);o++);if(r.hasValue()&&this.isString()&&n!==3){var s=new si.default(t);s.ok&&(s.alpha=r.getNumber(),t=s.toRGBA())}return new i(this.document,this.name,t)}}return i.textBaselineMapping={baseline:"alphabetic","before-edge":"top","text-before-edge":"top",middle:"middle",central:"middle","after-edge":"bottom","text-after-edge":"bottom",ideographic:"ideographic",alphabetic:"alphabetic",hanging:"hanging",mathematical:"alphabetic"},i})(),jn=class{constructor(){this.viewPorts=[]}clear(){this.viewPorts=[]}setCurrent(e,r){this.viewPorts.push({width:e,height:r})}removeCurrent(){this.viewPorts.pop()}getCurrent(){var{viewPorts:e}=this;return e[e.length-1]}get width(){return this.getCurrent().width}get height(){return this.getCurrent().height}computeSize(e){return typeof e=="number"?e:e==="x"?this.width:e==="y"?this.height:Math.sqrt(Math.pow(this.width,2)+Math.pow(this.height,2))/Math.sqrt(2)}},$=class i{constructor(e,r){this.x=e,this.y=r}static parse(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,[t=r,a=r]=ne(e);return new i(t,a)}static parseScale(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:1,[t=r,a=t]=ne(e);return new i(t,a)}static parsePath(e){for(var r=ne(e),t=r.length,a=[],n=0;n<t;n+=2)a.push(new i(r[n],r[n+1]));return a}angleTo(e){return Math.atan2(e.y-this.y,e.x-this.x)}applyTransform(e){var{x:r,y:t}=this,a=r*e[0]+t*e[2]+e[4],n=r*e[1]+t*e[3]+e[5];this.x=a,this.y=n}},Fn=class{constructor(e){this.screen=e,this.working=!1,this.events=[],this.eventElements=[],this.onClick=this.onClick.bind(this),this.onMouseMove=this.onMouseMove.bind(this)}isWorking(){return this.working}start(){if(!this.working){var{screen:e,onClick:r,onMouseMove:t}=this,a=e.ctx.canvas;a.onclick=r,a.onmousemove=t,this.working=!0}}stop(){if(this.working){var e=this.screen.ctx.canvas;this.working=!1,e.onclick=null,e.onmousemove=null}}hasEvents(){return this.working&&this.events.length>0}runEvents(){if(this.working){var{screen:e,events:r,eventElements:t}=this,{style:a}=e.ctx.canvas;a&&(a.cursor=""),r.forEach((n,o)=>{for(var{run:s}=n,u=t[o];u;)s(u),u=u.parent}),this.events=[],this.eventElements=[]}}checkPath(e,r){if(!(!this.working||!r)){var{events:t,eventElements:a}=this;t.forEach((n,o)=>{var{x:s,y:u}=n;!a[o]&&r.isPointInPath&&r.isPointInPath(s,u)&&(a[o]=e)})}}checkBoundingBox(e,r){if(!(!this.working||!r)){var{events:t,eventElements:a}=this;t.forEach((n,o)=>{var{x:s,y:u}=n;!a[o]&&r.isPointInBox(s,u)&&(a[o]=e)})}}mapXY(e,r){for(var{window:t,ctx:a}=this.screen,n=new $(e,r),o=a.canvas;o;)n.x-=o.offsetLeft,n.y-=o.offsetTop,o=o.offsetParent;return t.scrollX&&(n.x+=t.scrollX),t.scrollY&&(n.y+=t.scrollY),n}onClick(e){var{x:r,y:t}=this.mapXY(e.clientX,e.clientY);this.events.push({type:"onclick",x:r,y:t,run(a){a.onClick&&a.onClick()}})}onMouseMove(e){var{x:r,y:t}=this.mapXY(e.clientX,e.clientY);this.events.push({type:"onmousemove",x:r,y:t,run(a){a.onMouseMove&&a.onMouseMove()}})}},ig=typeof window<"u"?window:null,ag=typeof fetch<"u"?fetch.bind(void 0):null,hg=(()=>{class i{constructor(r){var{fetch:t=ag,window:a=ig}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.ctx=r,this.FRAMERATE=30,this.MAX_VIRTUAL_PIXELS=3e4,this.CLIENT_WIDTH=800,this.CLIENT_HEIGHT=600,this.viewPort=new jn,this.mouse=new Fn(this),this.animations=[],this.waits=[],this.frameDuration=0,this.isReadyLock=!1,this.isFirstRender=!0,this.intervalId=null,this.window=a,this.fetch=t}wait(r){this.waits.push(r)}ready(){return this.readyPromise?this.readyPromise:Promise.resolve()}isReady(){if(this.isReadyLock)return!0;var r=this.waits.every(t=>t());return r&&(this.waits=[],this.resolveReady&&this.resolveReady()),this.isReadyLock=r,r}setDefaults(r){r.strokeStyle="rgba(0,0,0,0)",r.lineCap="butt",r.lineJoin="miter",r.miterLimit=4}setViewBox(r){var{document:t,ctx:a,aspectRatio:n,width:o,desiredWidth:s,height:u,desiredHeight:l,minX:h=0,minY:f=0,refX:v,refY:c,clip:g=!1,clipX:p=0,clipY:y=0}=r,m=mr(n).replace(/^defer\s/,""),[T,x]=m.split(" "),O=T||"xMidYMid",w=x||"meet",C=o/s,S=u/l,P=Math.min(C,S),R=Math.max(C,S),M=s,V=l;w==="meet"&&(M*=P,V*=P),w==="slice"&&(M*=R,V*=R);var N=new E(t,"refX",v),X=new E(t,"refY",c),W=N.hasValue()&&X.hasValue();if(W&&a.translate(-P*N.getPixels("x"),-P*X.getPixels("y")),g){var H=P*p,Q=P*y;a.beginPath(),a.moveTo(H,Q),a.lineTo(o,Q),a.lineTo(o,u),a.lineTo(H,u),a.closePath(),a.clip()}if(!W){var Y=w==="meet"&&P===S,K=w==="slice"&&R===S,I=w==="meet"&&P===C,A=w==="slice"&&R===C;O.startsWith("xMid")&&(Y||K)&&a.translate(o/2-M/2,0),O.endsWith("YMid")&&(I||A)&&a.translate(0,u/2-V/2),O.startsWith("xMax")&&(Y||K)&&a.translate(o-M,0),O.endsWith("YMax")&&(I||A)&&a.translate(0,u-V)}switch(!0){case O==="none":a.scale(C,S);break;case w==="meet":a.scale(P,P);break;case w==="slice":a.scale(R,R);break}a.translate(-h,-f)}start(r){var{enableRedraw:t=!1,ignoreMouse:a=!1,ignoreAnimation:n=!1,ignoreDimensions:o=!1,ignoreClear:s=!1,forceRedraw:u,scaleWidth:l,scaleHeight:h,offsetX:f,offsetY:v}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},{FRAMERATE:c,mouse:g}=this,p=1e3/c;if(this.frameDuration=p,this.readyPromise=new Promise(O=>{this.resolveReady=O}),this.isReady()&&this.render(r,o,s,l,h,f,v),!!t){var y=Date.now(),m=y,T=0,x=()=>{y=Date.now(),T=y-m,T>=p&&(m=y-T%p,this.shouldUpdate(n,u)&&(this.render(r,o,s,l,h,f,v),g.runEvents())),this.intervalId=(0,ni.default)(x)};a||g.start(),this.intervalId=(0,ni.default)(x)}}stop(){this.intervalId&&(ni.default.cancel(this.intervalId),this.intervalId=null),this.mouse.stop()}shouldUpdate(r,t){if(!r){var{frameDuration:a}=this,n=this.animations.reduce((o,s)=>s.update(a)||o,!1);if(n)return!0}return!!(typeof t=="function"&&t()||!this.isReadyLock&&this.isReady()||this.mouse.hasEvents())}render(r,t,a,n,o,s,u){var{CLIENT_WIDTH:l,CLIENT_HEIGHT:h,viewPort:f,ctx:v,isFirstRender:c}=this,g=v.canvas;f.clear(),g.width&&g.height?f.setCurrent(g.width,g.height):f.setCurrent(l,h);var p=r.getStyle("width"),y=r.getStyle("height");!t&&(c||typeof n!="number"&&typeof o!="number")&&(p.hasValue()&&(g.width=p.getPixels("x"),g.style&&(g.style.width="".concat(g.width,"px"))),y.hasValue()&&(g.height=y.getPixels("y"),g.style&&(g.style.height="".concat(g.height,"px"))));var m=g.clientWidth||g.width,T=g.clientHeight||g.height;if(t&&p.hasValue()&&y.hasValue()&&(m=p.getPixels("x"),T=y.getPixels("y")),f.setCurrent(m,T),typeof s=="number"&&r.getAttribute("x",!0).setValue(s),typeof u=="number"&&r.getAttribute("y",!0).setValue(u),typeof n=="number"||typeof o=="number"){var x=ne(r.getAttribute("viewBox").getString()),O=0,w=0;if(typeof n=="number"){var C=r.getStyle("width");C.hasValue()?O=C.getPixels("x")/n:isNaN(x[2])||(O=x[2]/n)}if(typeof o=="number"){var S=r.getStyle("height");S.hasValue()?w=S.getPixels("y")/o:isNaN(x[3])||(w=x[3]/o)}O||(O=w),w||(w=O),r.getAttribute("width",!0).setValue(n),r.getAttribute("height",!0).setValue(o);var P=r.getStyle("transform",!0,!0);P.setValue("".concat(P.getString()," scale(").concat(1/O,", ").concat(1/w,")"))}a||v.clearRect(0,0,m,T),r.render(v),c&&(this.isFirstRender=!1)}}return i.defaultWindow=ig,i.defaultFetch=ag,i})(),{defaultFetch:rO}=hg,tO=typeof DOMParser<"u"?DOMParser:null,Yr=class{constructor(){var{fetch:e=rO,DOMParser:r=tO}=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.fetch=e,this.DOMParser=r}parse(e){var r=this;return xe(function*(){return e.startsWith("<")?r.parseFromString(e):r.load(e)})()}parseFromString(e){var r=new this.DOMParser;try{return this.checkDocument(r.parseFromString(e,"image/svg+xml"))}catch{return this.checkDocument(r.parseFromString(e,"text/xml"))}}checkDocument(e){var r=e.getElementsByTagName("parsererror")[0];if(r)throw new Error(r.textContent);return e}load(e){var r=this;return xe(function*(){var t=yield r.fetch(e),a=yield t.text();return r.parseFromString(a)})()}},zn=class{constructor(e,r){this.type="translate",this.point=null,this.point=$.parse(r)}apply(e){var{x:r,y:t}=this.point;e.translate(r||0,t||0)}unapply(e){var{x:r,y:t}=this.point;e.translate(-1*r||0,-1*t||0)}applyToPoint(e){var{x:r,y:t}=this.point;e.applyTransform([1,0,0,1,r||0,t||0])}},$n=class{constructor(e,r,t){this.type="rotate",this.angle=null,this.originX=null,this.originY=null,this.cx=0,this.cy=0;var a=ne(r);this.angle=new E(e,"angle",a[0]),this.originX=t[0],this.originY=t[1],this.cx=a[1]||0,this.cy=a[2]||0}apply(e){var{cx:r,cy:t,originX:a,originY:n,angle:o}=this,s=r+a.getPixels("x"),u=t+n.getPixels("y");e.translate(s,u),e.rotate(o.getRadians()),e.translate(-s,-u)}unapply(e){var{cx:r,cy:t,originX:a,originY:n,angle:o}=this,s=r+a.getPixels("x"),u=t+n.getPixels("y");e.translate(s,u),e.rotate(-1*o.getRadians()),e.translate(-s,-u)}applyToPoint(e){var{cx:r,cy:t,angle:a}=this,n=a.getRadians();e.applyTransform([1,0,0,1,r||0,t||0]),e.applyTransform([Math.cos(n),Math.sin(n),-Math.sin(n),Math.cos(n),0,0]),e.applyTransform([1,0,0,1,-r||0,-t||0])}},Hn=class{constructor(e,r,t){this.type="scale",this.scale=null,this.originX=null,this.originY=null;var a=$.parseScale(r);(a.x===0||a.y===0)&&(a.x=pr,a.y=pr),this.scale=a,this.originX=t[0],this.originY=t[1]}apply(e){var{scale:{x:r,y:t},originX:a,originY:n}=this,o=a.getPixels("x"),s=n.getPixels("y");e.translate(o,s),e.scale(r,t||r),e.translate(-o,-s)}unapply(e){var{scale:{x:r,y:t},originX:a,originY:n}=this,o=a.getPixels("x"),s=n.getPixels("y");e.translate(o,s),e.scale(1/r,1/t||r),e.translate(-o,-s)}applyToPoint(e){var{x:r,y:t}=this.scale;e.applyTransform([r||0,0,0,t||0,0,0])}},oi=class{constructor(e,r,t){this.type="matrix",this.matrix=[],this.originX=null,this.originY=null,this.matrix=ne(r),this.originX=t[0],this.originY=t[1]}apply(e){var{originX:r,originY:t,matrix:a}=this,n=r.getPixels("x"),o=t.getPixels("y");e.translate(n,o),e.transform(a[0],a[1],a[2],a[3],a[4],a[5]),e.translate(-n,-o)}unapply(e){var{originX:r,originY:t,matrix:a}=this,n=a[0],o=a[2],s=a[4],u=a[1],l=a[3],h=a[5],f=0,v=0,c=1,g=1/(n*(l*c-h*v)-o*(u*c-h*f)+s*(u*v-l*f)),p=r.getPixels("x"),y=t.getPixels("y");e.translate(p,y),e.transform(g*(l*c-h*v),g*(h*f-u*c),g*(s*v-o*c),g*(n*c-s*f),g*(o*h-s*l),g*(s*u-n*h)),e.translate(-p,-y)}applyToPoint(e){e.applyTransform(this.matrix)}},ui=class extends oi{constructor(e,r,t){super(e,r,t),this.type="skew",this.angle=null,this.angle=new E(e,"angle",r)}},Gn=class extends ui{constructor(e,r,t){super(e,r,t),this.type="skewX",this.matrix=[1,0,Math.tan(this.angle.getRadians()),1,0,0]}},Yn=class extends ui{constructor(e,r,t){super(e,r,t),this.type="skewY",this.matrix=[1,Math.tan(this.angle.getRadians()),0,1,0,0]}};function iO(i){return mr(i).trim().replace(/\)([a-zA-Z])/g,") $1").replace(/\)(\s?,\s?)/g,") ").split(/\s(?=[a-z])/)}function aO(i){var[e,r]=i.split("(");return[e.trim(),r.trim().replace(")","")]}var Cs=(()=>{class i{constructor(r,t,a){this.document=r,this.transforms=[];var n=iO(t);n.forEach(o=>{if(o!=="none"){var[s,u]=aO(o),l=i.transformTypes[s];typeof l<"u"&&this.transforms.push(new l(this.document,u,a))}})}static fromElement(r,t){var a=t.getStyle("transform",!1,!0),[n,o=n]=t.getStyle("transform-origin",!1,!0).split(),s=[n,o];return a.hasValue()?new i(r,a.getString(),s):null}apply(r){for(var{transforms:t}=this,a=t.length,n=0;n<a;n++)t[n].apply(r)}unapply(r){for(var{transforms:t}=this,a=t.length,n=a-1;n>=0;n--)t[n].unapply(r)}applyToPoint(r){for(var{transforms:t}=this,a=t.length,n=0;n<a;n++)t[n].applyToPoint(r)}}return i.transformTypes={translate:zn,rotate:$n,scale:Hn,matrix:oi,skewX:Gn,skewY:Yn},i})(),B=(()=>{class i{constructor(r,t){var a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;if(this.document=r,this.node=t,this.captureTextNodes=a,this.attributes={},this.styles={},this.stylesSpecificity={},this.animationFrozen=!1,this.animationFrozenValue="",this.parent=null,this.children=[],!(!t||t.nodeType!==1)){if(Array.from(t.attributes).forEach(u=>{var l=HT(u.nodeName);this.attributes[l]=new E(r,l,u.value)}),this.addStylesFromStyleDefinition(),this.getAttribute("style").hasValue()){var n=this.getAttribute("style").getString().split(";").map(u=>u.trim());n.forEach(u=>{if(u){var[l,h]=u.split(":").map(f=>f.trim());this.styles[l]=new E(r,l,h)}})}var{definitions:o}=r,s=this.getAttribute("id");s.hasValue()&&(o[s.getString()]||(o[s.getString()]=this)),Array.from(t.childNodes).forEach(u=>{if(u.nodeType===1)this.addChild(u);else if(a&&(u.nodeType===3||u.nodeType===4)){var l=r.createTextNode(u);l.getText().length>0&&this.addChild(l)}})}}getAttribute(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,a=this.attributes[r];if(!a&&t){var n=new E(this.document,r,"");return this.attributes[r]=n,n}return a||E.empty(this.document)}getHrefAttribute(){for(var r in this.attributes)if(r==="href"||r.endsWith(":href"))return this.attributes[r];return E.empty(this.document)}getStyle(r){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,a=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,n=this.styles[r];if(n)return n;var o=this.getAttribute(r);if(o!=null&&o.hasValue())return this.styles[r]=o,o;if(!a){var{parent:s}=this;if(s){var u=s.getStyle(r);if(u!=null&&u.hasValue())return u}}if(t){var l=new E(this.document,r,"");return this.styles[r]=l,l}return n||E.empty(this.document)}render(r){if(!(this.getStyle("display").getString()==="none"||this.getStyle("visibility").getString()==="hidden")){if(r.save(),this.getStyle("mask").hasValue()){var t=this.getStyle("mask").getDefinition();t&&(this.applyEffects(r),t.apply(r,this))}else if(this.getStyle("filter").getValue("none")!=="none"){var a=this.getStyle("filter").getDefinition();a&&(this.applyEffects(r),a.apply(r,this))}else this.setContext(r),this.renderChildren(r),this.clearContext(r);r.restore()}}setContext(r){}applyEffects(r){var t=Cs.fromElement(this.document,this);t&&t.apply(r);var a=this.getStyle("clip-path",!1,!0);if(a.hasValue()){var n=a.getDefinition();n&&n.apply(r)}}clearContext(r){}renderChildren(r){this.children.forEach(t=>{t.render(r)})}addChild(r){var t=r instanceof i?r:this.document.createElement(r);t.parent=this,i.ignoreChildTypes.includes(t.type)||this.children.push(t)}matchesSelector(r){var t,{node:a}=this;if(typeof a.matches=="function")return a.matches(r);var n=(t=a.getAttribute)===null||t===void 0?void 0:t.call(a,"class");return!n||n===""?!1:n.split(" ").some(o=>".".concat(o)===r)}addStylesFromStyleDefinition(){var{styles:r,stylesSpecificity:t}=this.document;for(var a in r)if(!a.startsWith("@")&&this.matchesSelector(a)){var n=r[a],o=t[a];if(n)for(var s in n){var u=this.stylesSpecificity[s];typeof u>"u"&&(u="000"),o>=u&&(this.styles[s]=n[s],this.stylesSpecificity[s]=o)}}}removeStyles(r,t){var a=t.reduce((n,o)=>{var s=r.getStyle(o);if(!s.hasValue())return n;var u=s.getString();return s.setValue(""),[...n,[o,u]]},[]);return a}restoreStyles(r,t){t.forEach(a=>{var[n,o]=a;r.getStyle(n,!0).setValue(o)})}isFirstChild(){var r;return((r=this.parent)===null||r===void 0?void 0:r.children.indexOf(this))===0}}return i.ignoreChildTypes=["title"],i})(),Xn=class extends B{constructor(e,r,t){super(e,r,t)}};function nO(i){var e=i.trim();return/^('|")/.test(e)?e:'"'.concat(e,'"')}function sO(i){return typeof process>"u"?i:i.trim().split(",").map(nO).join(",")}function oO(i){if(!i)return"";var e=i.trim().toLowerCase();switch(e){case"normal":case"italic":case"oblique":case"inherit":case"initial":case"unset":return e;default:return/^oblique\s+(-|)\d+deg$/.test(e)?e:""}}function uO(i){if(!i)return"";var e=i.trim().toLowerCase();switch(e){case"normal":case"bold":case"lighter":case"bolder":case"inherit":case"initial":case"unset":return e;default:return/^[\d.]+$/.test(e)?e:""}}var yr=(()=>{class i{constructor(r,t,a,n,o,s){var u=s?typeof s=="string"?i.parse(s):s:{};this.fontFamily=o||u.fontFamily,this.fontSize=n||u.fontSize,this.fontStyle=r||u.fontStyle,this.fontWeight=a||u.fontWeight,this.fontVariant=t||u.fontVariant}static parse(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"",t=arguments.length>1?arguments[1]:void 0,a="",n="",o="",s="",u="",l=mr(r).trim().split(" "),h={fontSize:!1,fontStyle:!1,fontWeight:!1,fontVariant:!1};return l.forEach(f=>{switch(!0){case(!h.fontStyle&&i.styles.includes(f)):f!=="inherit"&&(a=f),h.fontStyle=!0;break;case(!h.fontVariant&&i.variants.includes(f)):f!=="inherit"&&(n=f),h.fontStyle=!0,h.fontVariant=!0;break;case(!h.fontWeight&&i.weights.includes(f)):f!=="inherit"&&(o=f),h.fontStyle=!0,h.fontVariant=!0,h.fontWeight=!0;break;case!h.fontSize:f!=="inherit"&&([s]=f.split("/")),h.fontStyle=!0,h.fontVariant=!0,h.fontWeight=!0,h.fontSize=!0;break;default:f!=="inherit"&&(u+=f)}}),new i(a,n,o,s,u,t)}toString(){return[oO(this.fontStyle),this.fontVariant,uO(this.fontWeight),this.fontSize,sO(this.fontFamily)].join(" ").trim()}}return i.styles="normal|italic|oblique|inherit",i.variants="normal|small-caps|inherit",i.weights="normal|bold|bolder|lighter|100|200|300|400|500|600|700|800|900|inherit",i})(),se=class{constructor(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:Number.NaN,r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:Number.NaN,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:Number.NaN,a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:Number.NaN;this.x1=e,this.y1=r,this.x2=t,this.y2=a,this.addPoint(e,r),this.addPoint(t,a)}get x(){return this.x1}get y(){return this.y1}get width(){return this.x2-this.x1}get height(){return this.y2-this.y1}addPoint(e,r){typeof e<"u"&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=e,this.x2=e),e<this.x1&&(this.x1=e),e>this.x2&&(this.x2=e)),typeof r<"u"&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=r,this.y2=r),r<this.y1&&(this.y1=r),r>this.y2&&(this.y2=r))}addX(e){this.addPoint(e,null)}addY(e){this.addPoint(null,e)}addBoundingBox(e){if(e){var{x1:r,y1:t,x2:a,y2:n}=e;this.addPoint(r,t),this.addPoint(a,n)}}sumCubic(e,r,t,a,n){return Math.pow(1-e,3)*r+3*Math.pow(1-e,2)*e*t+3*(1-e)*Math.pow(e,2)*a+Math.pow(e,3)*n}bezierCurveAdd(e,r,t,a,n){var o=6*r-12*t+6*a,s=-3*r+9*t-9*a+3*n,u=3*t-3*r;if(s===0){if(o===0)return;var l=-u/o;0<l&&l<1&&(e?this.addX(this.sumCubic(l,r,t,a,n)):this.addY(this.sumCubic(l,r,t,a,n)));return}var h=Math.pow(o,2)-4*u*s;if(!(h<0)){var f=(-o+Math.sqrt(h))/(2*s);0<f&&f<1&&(e?this.addX(this.sumCubic(f,r,t,a,n)):this.addY(this.sumCubic(f,r,t,a,n)));var v=(-o-Math.sqrt(h))/(2*s);0<v&&v<1&&(e?this.addX(this.sumCubic(v,r,t,a,n)):this.addY(this.sumCubic(v,r,t,a,n)))}}addBezierCurve(e,r,t,a,n,o,s,u){this.addPoint(e,r),this.addPoint(s,u),this.bezierCurveAdd(!0,e,t,n,s),this.bezierCurveAdd(!1,r,a,o,u)}addQuadraticCurve(e,r,t,a,n,o){var s=e+.6666666666666666*(t-e),u=r+2/3*(a-r),l=s+1/3*(n-e),h=u+1/3*(o-r);this.addBezierCurve(e,r,s,l,u,h,n,o)}isPointInBox(e,r){var{x1:t,y1:a,x2:n,y2:o}=this;return t<=e&&e<=n&&a<=r&&r<=o}},q=class extends b{constructor(e){super(e.replace(/([+\-.])\s+/gm,"$1").replace(/[^MmZzLlHhVvCcSsQqTtAae\d\s.,+-].*/g,"")),this.control=null,this.start=null,this.current=null,this.command=null,this.commands=this.commands,this.i=-1,this.previousCommand=null,this.points=[],this.angles=[]}reset(){this.i=-1,this.command=null,this.previousCommand=null,this.start=new $(0,0),this.control=new $(0,0),this.current=new $(0,0),this.points=[],this.angles=[]}isEnd(){var{i:e,commands:r}=this;return e>=r.length-1}next(){var e=this.commands[++this.i];return this.previousCommand=this.command,this.command=e,e}getPoint(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"x",r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"y",t=new $(this.command[e],this.command[r]);return this.makeAbsolute(t)}getAsControlPoint(e,r){var t=this.getPoint(e,r);return this.control=t,t}getAsCurrentPoint(e,r){var t=this.getPoint(e,r);return this.current=t,t}getReflectedControlPoint(){var e=this.previousCommand.type;if(e!==b.CURVE_TO&&e!==b.SMOOTH_CURVE_TO&&e!==b.QUAD_TO&&e!==b.SMOOTH_QUAD_TO)return this.current;var{current:{x:r,y:t},control:{x:a,y:n}}=this,o=new $(2*r-a,2*t-n);return o}makeAbsolute(e){if(this.command.relative){var{x:r,y:t}=this.current;e.x+=r,e.y+=t}return e}addMarker(e,r,t){var{points:a,angles:n}=this;t&&n.length>0&&!n[n.length-1]&&(n[n.length-1]=a[a.length-1].angleTo(t)),this.addMarkerAngle(e,r?r.angleTo(e):null)}addMarkerAngle(e,r){this.points.push(e),this.angles.push(r)}getMarkerPoints(){return this.points}getMarkerAngles(){for(var{angles:e}=this,r=e.length,t=0;t<r;t++)if(!e[t]){for(var a=t+1;a<r;a++)if(e[a]){e[t]=e[a];break}}return e}},Ce=class extends B{constructor(){super(...arguments),this.modifiedEmSizeStack=!1}calculateOpacity(){for(var e=1,r=this;r;){var t=r.getStyle("opacity",!1,!0);t.hasValue(!0)&&(e*=t.getNumber()),r=r.parent}return e}setContext(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;if(!r){var t=this.getStyle("fill"),a=this.getStyle("fill-opacity"),n=this.getStyle("stroke"),o=this.getStyle("stroke-opacity");if(t.isUrlDefinition()){var s=t.getFillStyleDefinition(this,a);s&&(e.fillStyle=s)}else if(t.hasValue()){t.getString()==="currentColor"&&t.setValue(this.getStyle("color").getColor());var u=t.getColor();u!=="inherit"&&(e.fillStyle=u==="none"?"rgba(0,0,0,0)":u)}if(a.hasValue()){var l=new E(this.document,"fill",e.fillStyle).addOpacity(a).getColor();e.fillStyle=l}if(n.isUrlDefinition()){var h=n.getFillStyleDefinition(this,o);h&&(e.strokeStyle=h)}else if(n.hasValue()){n.getString()==="currentColor"&&n.setValue(this.getStyle("color").getColor());var f=n.getString();f!=="inherit"&&(e.strokeStyle=f==="none"?"rgba(0,0,0,0)":f)}if(o.hasValue()){var v=new E(this.document,"stroke",e.strokeStyle).addOpacity(o).getString();e.strokeStyle=v}var c=this.getStyle("stroke-width");if(c.hasValue()){var g=c.getPixels();e.lineWidth=g||pr}var p=this.getStyle("stroke-linecap"),y=this.getStyle("stroke-linejoin"),m=this.getStyle("stroke-miterlimit"),T=this.getStyle("stroke-dasharray"),x=this.getStyle("stroke-dashoffset");if(p.hasValue()&&(e.lineCap=p.getString()),y.hasValue()&&(e.lineJoin=y.getString()),m.hasValue()&&(e.miterLimit=m.getNumber()),T.hasValue()&&T.getString()!=="none"){var O=ne(T.getString());typeof e.setLineDash<"u"?e.setLineDash(O):typeof e.webkitLineDash<"u"?e.webkitLineDash=O:typeof e.mozDash<"u"&&!(O.length===1&&O[0]===0)&&(e.mozDash=O);var w=x.getPixels();typeof e.lineDashOffset<"u"?e.lineDashOffset=w:typeof e.webkitLineDashOffset<"u"?e.webkitLineDashOffset=w:typeof e.mozDashOffset<"u"&&(e.mozDashOffset=w)}}if(this.modifiedEmSizeStack=!1,typeof e.font<"u"){var C=this.getStyle("font"),S=this.getStyle("font-style"),P=this.getStyle("font-variant"),R=this.getStyle("font-weight"),M=this.getStyle("font-size"),V=this.getStyle("font-family"),N=new yr(S.getString(),P.getString(),R.getString(),M.hasValue()?"".concat(M.getPixels(!0),"px"):"",V.getString(),yr.parse(C.getString(),e.font));S.setValue(N.fontStyle),P.setValue(N.fontVariant),R.setValue(N.fontWeight),M.setValue(N.fontSize),V.setValue(N.fontFamily),e.font=N.toString(),M.isPixels()&&(this.document.emSize=M.getPixels(),this.modifiedEmSizeStack=!0)}r||(this.applyEffects(e),e.globalAlpha=this.calculateOpacity())}clearContext(e){super.clearContext(e),this.modifiedEmSizeStack&&this.document.popEmSize()}},F=class i extends Ce{constructor(e,r,t){super(e,r,t),this.type="path",this.pathParser=null,this.pathParser=new q(this.getAttribute("d").getString())}path(e){var{pathParser:r}=this,t=new se;for(r.reset(),e&&e.beginPath();!r.isEnd();)switch(r.next().type){case q.MOVE_TO:this.pathM(e,t);break;case q.LINE_TO:this.pathL(e,t);break;case q.HORIZ_LINE_TO:this.pathH(e,t);break;case q.VERT_LINE_TO:this.pathV(e,t);break;case q.CURVE_TO:this.pathC(e,t);break;case q.SMOOTH_CURVE_TO:this.pathS(e,t);break;case q.QUAD_TO:this.pathQ(e,t);break;case q.SMOOTH_QUAD_TO:this.pathT(e,t);break;case q.ARC:this.pathA(e,t);break;case q.CLOSE_PATH:this.pathZ(e,t);break}return t}getBoundingBox(e){return this.path()}getMarkers(){var{pathParser:e}=this,r=e.getMarkerPoints(),t=e.getMarkerAngles(),a=r.map((n,o)=>[n,t[o]]);return a}renderChildren(e){this.path(e),this.document.screen.mouse.checkPath(this,e);var r=this.getStyle("fill-rule");e.fillStyle!==""&&(r.getString("inherit")!=="inherit"?e.fill(r.getString()):e.fill()),e.strokeStyle!==""&&(this.getAttribute("vector-effect").getString()==="non-scaling-stroke"?(e.save(),e.setTransform(1,0,0,1,0,0),e.stroke(),e.restore()):e.stroke());var t=this.getMarkers();if(t){var a=t.length-1,n=this.getStyle("marker-start"),o=this.getStyle("marker-mid"),s=this.getStyle("marker-end");if(n.isUrlDefinition()){var u=n.getDefinition(),[l,h]=t[0];u.render(e,l,h)}if(o.isUrlDefinition())for(var f=o.getDefinition(),v=1;v<a;v++){var[c,g]=t[v];f.render(e,c,g)}if(s.isUrlDefinition()){var p=s.getDefinition(),[y,m]=t[a];p.render(e,y,m)}}}static pathM(e){var r=e.getAsCurrentPoint();return e.start=e.current,{point:r}}pathM(e,r){var{pathParser:t}=this,{point:a}=i.pathM(t),{x:n,y:o}=a;t.addMarker(a),r.addPoint(n,o),e&&e.moveTo(n,o)}static pathL(e){var{current:r}=e,t=e.getAsCurrentPoint();return{current:r,point:t}}pathL(e,r){var{pathParser:t}=this,{current:a,point:n}=i.pathL(t),{x:o,y:s}=n;t.addMarker(n,a),r.addPoint(o,s),e&&e.lineTo(o,s)}static pathH(e){var{current:r,command:t}=e,a=new $((t.relative?r.x:0)+t.x,r.y);return e.current=a,{current:r,point:a}}pathH(e,r){var{pathParser:t}=this,{current:a,point:n}=i.pathH(t),{x:o,y:s}=n;t.addMarker(n,a),r.addPoint(o,s),e&&e.lineTo(o,s)}static pathV(e){var{current:r,command:t}=e,a=new $(r.x,(t.relative?r.y:0)+t.y);return e.current=a,{current:r,point:a}}pathV(e,r){var{pathParser:t}=this,{current:a,point:n}=i.pathV(t),{x:o,y:s}=n;t.addMarker(n,a),r.addPoint(o,s),e&&e.lineTo(o,s)}static pathC(e){var{current:r}=e,t=e.getPoint("x1","y1"),a=e.getAsControlPoint("x2","y2"),n=e.getAsCurrentPoint();return{current:r,point:t,controlPoint:a,currentPoint:n}}pathC(e,r){var{pathParser:t}=this,{current:a,point:n,controlPoint:o,currentPoint:s}=i.pathC(t);t.addMarker(s,o,n),r.addBezierCurve(a.x,a.y,n.x,n.y,o.x,o.y,s.x,s.y),e&&e.bezierCurveTo(n.x,n.y,o.x,o.y,s.x,s.y)}static pathS(e){var{current:r}=e,t=e.getReflectedControlPoint(),a=e.getAsControlPoint("x2","y2"),n=e.getAsCurrentPoint();return{current:r,point:t,controlPoint:a,currentPoint:n}}pathS(e,r){var{pathParser:t}=this,{current:a,point:n,controlPoint:o,currentPoint:s}=i.pathS(t);t.addMarker(s,o,n),r.addBezierCurve(a.x,a.y,n.x,n.y,o.x,o.y,s.x,s.y),e&&e.bezierCurveTo(n.x,n.y,o.x,o.y,s.x,s.y)}static pathQ(e){var{current:r}=e,t=e.getAsControlPoint("x1","y1"),a=e.getAsCurrentPoint();return{current:r,controlPoint:t,currentPoint:a}}pathQ(e,r){var{pathParser:t}=this,{current:a,controlPoint:n,currentPoint:o}=i.pathQ(t);t.addMarker(o,n,n),r.addQuadraticCurve(a.x,a.y,n.x,n.y,o.x,o.y),e&&e.quadraticCurveTo(n.x,n.y,o.x,o.y)}static pathT(e){var{current:r}=e,t=e.getReflectedControlPoint();e.control=t;var a=e.getAsCurrentPoint();return{current:r,controlPoint:t,currentPoint:a}}pathT(e,r){var{pathParser:t}=this,{current:a,controlPoint:n,currentPoint:o}=i.pathT(t);t.addMarker(o,n,n),r.addQuadraticCurve(a.x,a.y,n.x,n.y,o.x,o.y),e&&e.quadraticCurveTo(n.x,n.y,o.x,o.y)}static pathA(e){var{current:r,command:t}=e,{rX:a,rY:n,xRot:o,lArcFlag:s,sweepFlag:u}=t,l=o*(Math.PI/180),h=e.getAsCurrentPoint(),f=new $(Math.cos(l)*(r.x-h.x)/2+Math.sin(l)*(r.y-h.y)/2,-Math.sin(l)*(r.x-h.x)/2+Math.cos(l)*(r.y-h.y)/2),v=Math.pow(f.x,2)/Math.pow(a,2)+Math.pow(f.y,2)/Math.pow(n,2);v>1&&(a*=Math.sqrt(v),n*=Math.sqrt(v));var c=(s===u?-1:1)*Math.sqrt((Math.pow(a,2)*Math.pow(n,2)-Math.pow(a,2)*Math.pow(f.y,2)-Math.pow(n,2)*Math.pow(f.x,2))/(Math.pow(a,2)*Math.pow(f.y,2)+Math.pow(n,2)*Math.pow(f.x,2)));isNaN(c)&&(c=0);var g=new $(c*a*f.y/n,c*-n*f.x/a),p=new $((r.x+h.x)/2+Math.cos(l)*g.x-Math.sin(l)*g.y,(r.y+h.y)/2+Math.sin(l)*g.x+Math.cos(l)*g.y),y=Wv([1,0],[(f.x-g.x)/a,(f.y-g.y)/n]),m=[(f.x-g.x)/a,(f.y-g.y)/n],T=[(-f.x-g.x)/a,(-f.y-g.y)/n],x=Wv(m,T);return Un(m,T)<=-1&&(x=Math.PI),Un(m,T)>=1&&(x=0),{currentPoint:h,rX:a,rY:n,sweepFlag:u,xAxisRotation:l,centp:p,a1:y,ad:x}}pathA(e,r){var{pathParser:t}=this,{currentPoint:a,rX:n,rY:o,sweepFlag:s,xAxisRotation:u,centp:l,a1:h,ad:f}=i.pathA(t),v=1-s?1:-1,c=h+v*(f/2),g=new $(l.x+n*Math.cos(c),l.y+o*Math.sin(c));if(t.addMarkerAngle(g,c-v*Math.PI/2),t.addMarkerAngle(a,c-v*Math.PI),r.addPoint(a.x,a.y),e&&!isNaN(h)&&!isNaN(f)){var p=n>o?n:o,y=n>o?1:n/o,m=n>o?o/n:1;e.translate(l.x,l.y),e.rotate(u),e.scale(y,m),e.arc(0,0,p,h,h+f,!!(1-s)),e.scale(1/y,1/m),e.rotate(-u),e.translate(-l.x,-l.y)}}static pathZ(e){e.current=e.start}pathZ(e,r){i.pathZ(this.pathParser),e&&r.x1!==r.x2&&r.y1!==r.y2&&e.closePath()}},li=class extends F{constructor(e,r,t){super(e,r,t),this.type="glyph",this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber(),this.unicode=this.getAttribute("unicode").getString(),this.arabicForm=this.getAttribute("arabic-form").getString()}},Ge=class i extends Ce{constructor(e,r,t){super(e,r,new.target===i?!0:t),this.type="text",this.x=0,this.y=0,this.measureCache=-1}setContext(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1;super.setContext(e,r);var t=this.getStyle("dominant-baseline").getTextBaseline()||this.getStyle("alignment-baseline").getTextBaseline();t&&(e.textBaseline=t)}initializeCoordinates(){this.x=0,this.y=0,this.leafTexts=[],this.textChunkStart=0,this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY}getBoundingBox(e){if(this.type!=="text")return this.getTElementBoundingBox(e);this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(e);var r=null;return this.children.forEach((t,a)=>{var n=this.getChildBoundingBox(e,this,this,a);r?r.addBoundingBox(n):r=n}),r}getFontSize(){var{document:e,parent:r}=this,t=yr.parse(e.ctx.font).fontSize,a=r.getStyle("font-size").getNumber(t);return a}getTElementBoundingBox(e){var r=this.getFontSize();return new se(this.x,this.y-r,this.x+this.measureText(e),this.y)}getGlyph(e,r,t){var a=r[t],n=null;if(e.isArabic){var o=r.length,s=r[t-1],u=r[t+1],l="isolated";if((t===0||s===" ")&&t<o-1&&u!==" "&&(l="terminal"),t>0&&s!==" "&&t<o-1&&u!==" "&&(l="medial"),t>0&&s!==" "&&(t===o-1||u===" ")&&(l="initial"),typeof e.glyphs[a]<"u"){var h=e.glyphs[a];n=h instanceof li?h:h[l]}}else n=e.glyphs[a];return n||(n=e.missingGlyph),n}getText(){return""}getTextFromNode(e){var r=e||this.node,t=Array.from(r.parentNode.childNodes),a=t.indexOf(r),n=t.length-1,o=mr(r.textContent||"");return a===0&&(o=FT(o)),a===n&&(o=zT(o)),o}renderChildren(e){if(this.type!=="text"){this.renderTElementChildren(e);return}this.initializeCoordinates(),this.adjustChildCoordinatesRecursive(e),this.children.forEach((t,a)=>{this.renderChild(e,this,this,a)});var{mouse:r}=this.document.screen;r.isWorking()&&r.checkBoundingBox(this,this.getBoundingBox(e))}renderTElementChildren(e){var{document:r,parent:t}=this,a=this.getText(),n=t.getStyle("font-family").getDefinition();if(n){for(var{unitsPerEm:o}=n.fontFace,s=yr.parse(r.ctx.font),u=t.getStyle("font-size").getNumber(s.fontSize),l=t.getStyle("font-style").getString(s.fontStyle),h=u/o,f=n.isRTL?a.split("").reverse().join(""):a,v=ne(t.getAttribute("dx").getString()),c=f.length,g=0;g<c;g++){var p=this.getGlyph(n,f,g);e.translate(this.x,this.y),e.scale(h,-h);var y=e.lineWidth;e.lineWidth=e.lineWidth*o/u,l==="italic"&&e.transform(1,0,.4,1,0,0),p.render(e),l==="italic"&&e.transform(1,0,-.4,1,0,0),e.lineWidth=y,e.scale(1/h,-1/h),e.translate(-this.x,-this.y),this.x+=u*(p.horizAdvX||n.horizAdvX)/o,typeof v[g]<"u"&&!isNaN(v[g])&&(this.x+=v[g])}return}var{x:m,y:T}=this;e.fillStyle&&e.fillText(a,m,T),e.strokeStyle&&e.strokeText(a,m,T)}applyAnchoring(){if(!(this.textChunkStart>=this.leafTexts.length)){var e=this.leafTexts[this.textChunkStart],r=e.getStyle("text-anchor").getString("start"),t=!1,a=0;r==="start"&&!t||r==="end"&&t?a=e.x-this.minX:r==="end"&&!t||r==="start"&&t?a=e.x-this.maxX:a=e.x-(this.minX+this.maxX)/2;for(var n=this.textChunkStart;n<this.leafTexts.length;n++)this.leafTexts[n].x+=a;this.minX=Number.POSITIVE_INFINITY,this.maxX=Number.NEGATIVE_INFINITY,this.textChunkStart=this.leafTexts.length}}adjustChildCoordinatesRecursive(e){this.children.forEach((r,t)=>{this.adjustChildCoordinatesRecursiveCore(e,this,this,t)}),this.applyAnchoring()}adjustChildCoordinatesRecursiveCore(e,r,t,a){var n=t.children[a];n.children.length>0?n.children.forEach((o,s)=>{r.adjustChildCoordinatesRecursiveCore(e,r,n,s)}):this.adjustChildCoordinates(e,r,t,a)}adjustChildCoordinates(e,r,t,a){var n=t.children[a];if(typeof n.measureText!="function")return n;e.save(),n.setContext(e,!0);var o=n.getAttribute("x"),s=n.getAttribute("y"),u=n.getAttribute("dx"),l=n.getAttribute("dy"),h=n.getStyle("font-family").getDefinition(),f=!!h&&h.isRTL;a===0&&(o.hasValue()||o.setValue(n.getInheritedAttribute("x")),s.hasValue()||s.setValue(n.getInheritedAttribute("y")),u.hasValue()||u.setValue(n.getInheritedAttribute("dx")),l.hasValue()||l.setValue(n.getInheritedAttribute("dy")));var v=n.measureText(e);return f&&(r.x-=v),o.hasValue()?(r.applyAnchoring(),n.x=o.getPixels("x"),u.hasValue()&&(n.x+=u.getPixels("x"))):(u.hasValue()&&(r.x+=u.getPixels("x")),n.x=r.x),r.x=n.x,f||(r.x+=v),s.hasValue()?(n.y=s.getPixels("y"),l.hasValue()&&(n.y+=l.getPixels("y"))):(l.hasValue()&&(r.y+=l.getPixels("y")),n.y=r.y),r.y=n.y,r.leafTexts.push(n),r.minX=Math.min(r.minX,n.x,n.x+v),r.maxX=Math.max(r.maxX,n.x,n.x+v),n.clearContext(e),e.restore(),n}getChildBoundingBox(e,r,t,a){var n=t.children[a];if(typeof n.getBoundingBox!="function")return null;var o=n.getBoundingBox(e);return o?(n.children.forEach((s,u)=>{var l=r.getChildBoundingBox(e,r,n,u);o.addBoundingBox(l)}),o):null}renderChild(e,r,t,a){var n=t.children[a];n.render(e),n.children.forEach((o,s)=>{r.renderChild(e,r,n,s)})}measureText(e){var{measureCache:r}=this;if(~r)return r;var t=this.getText(),a=this.measureTargetText(e,t);return this.measureCache=a,a}measureTargetText(e,r){if(!r.length)return 0;var{parent:t}=this,a=t.getStyle("font-family").getDefinition();if(a){for(var n=this.getFontSize(),o=a.isRTL?r.split("").reverse().join(""):r,s=ne(t.getAttribute("dx").getString()),u=o.length,l=0,h=0;h<u;h++){var f=this.getGlyph(a,o,h);l+=(f.horizAdvX||a.horizAdvX)*n/a.fontFace.unitsPerEm,typeof s[h]<"u"&&!isNaN(s[h])&&(l+=s[h])}return l}if(!e.measureText)return r.length*10;e.save(),this.setContext(e,!0);var{width:v}=e.measureText(r);return this.clearContext(e),e.restore(),v}getInheritedAttribute(e){for(var r=this;r instanceof i&&r.isFirstChild();){var t=r.parent.getAttribute(e);if(t.hasValue(!0))return t.getValue("0");r=r.parent}return null}},hi=class i extends Ge{constructor(e,r,t){super(e,r,new.target===i?!0:t),this.type="tspan",this.text=this.children.length>0?"":this.getTextFromNode()}getText(){return this.text}},Wn=class extends hi{constructor(){super(...arguments),this.type="textNode"}},Ye=class extends Ce{constructor(){super(...arguments),this.type="svg",this.root=!1}setContext(e){var r,{document:t}=this,{screen:a,window:n}=t,o=e.canvas;if(a.setDefaults(e),o.style&&typeof e.font<"u"&&n&&typeof n.getComputedStyle<"u"){e.font=n.getComputedStyle(o).getPropertyValue("font");var s=new E(t,"fontSize",yr.parse(e.font).fontSize);s.hasValue()&&(t.rootEmSize=s.getPixels("y"),t.emSize=t.rootEmSize)}this.getAttribute("x").hasValue()||this.getAttribute("x",!0).setValue(0),this.getAttribute("y").hasValue()||this.getAttribute("y",!0).setValue(0);var{width:u,height:l}=a.viewPort;this.getStyle("width").hasValue()||this.getStyle("width",!0).setValue("100%"),this.getStyle("height").hasValue()||this.getStyle("height",!0).setValue("100%"),this.getStyle("color").hasValue()||this.getStyle("color",!0).setValue("black");var h=this.getAttribute("refX"),f=this.getAttribute("refY"),v=this.getAttribute("viewBox"),c=v.hasValue()?ne(v.getString()):null,g=!this.root&&this.getStyle("overflow").getValue("hidden")!=="visible",p=0,y=0,m=0,T=0;c&&(p=c[0],y=c[1]),this.root||(u=this.getStyle("width").getPixels("x"),l=this.getStyle("height").getPixels("y"),this.type==="marker"&&(m=p,T=y,p=0,y=0)),a.viewPort.setCurrent(u,l),this.node&&(!this.parent||((r=this.node.parentNode)===null||r===void 0?void 0:r.nodeName)==="foreignObject")&&this.getStyle("transform",!1,!0).hasValue()&&!this.getStyle("transform-origin",!1,!0).hasValue()&&this.getStyle("transform-origin",!0,!0).setValue("50% 50%"),super.setContext(e),e.translate(this.getAttribute("x").getPixels("x"),this.getAttribute("y").getPixels("y")),c&&(u=c[2],l=c[3]),t.setViewBox({ctx:e,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:a.viewPort.width,desiredWidth:u,height:a.viewPort.height,desiredHeight:l,minX:p,minY:y,refX:h.getValue(),refY:f.getValue(),clip:g,clipX:m,clipY:T}),c&&(a.viewPort.removeCurrent(),a.viewPort.setCurrent(u,l))}clearContext(e){super.clearContext(e),this.document.screen.viewPort.removeCurrent()}resize(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1,a=this.getAttribute("width",!0),n=this.getAttribute("height",!0),o=this.getAttribute("viewBox"),s=this.getAttribute("style"),u=a.getNumber(0),l=n.getNumber(0);if(t)if(typeof t=="string")this.getAttribute("preserveAspectRatio",!0).setValue(t);else{var h=this.getAttribute("preserveAspectRatio");h.hasValue()&&h.setValue(h.getString().replace(/^\s*(\S.*\S)\s*$/,"$1"))}if(a.setValue(e),n.setValue(r),o.hasValue()||o.setValue("0 0 ".concat(u||e," ").concat(l||r)),s.hasValue()){var f=this.getStyle("width"),v=this.getStyle("height");f.hasValue()&&f.setValue("".concat(e,"px")),v.hasValue()&&v.setValue("".concat(r,"px"))}}},ci=class extends F{constructor(){super(...arguments),this.type="rect"}path(e){var r=this.getAttribute("x").getPixels("x"),t=this.getAttribute("y").getPixels("y"),a=this.getStyle("width",!1,!0).getPixels("x"),n=this.getStyle("height",!1,!0).getPixels("y"),o=this.getAttribute("rx"),s=this.getAttribute("ry"),u=o.getPixels("x"),l=s.getPixels("y");if(o.hasValue()&&!s.hasValue()&&(l=u),s.hasValue()&&!o.hasValue()&&(u=l),u=Math.min(u,a/2),l=Math.min(l,n/2),e){var h=4*((Math.sqrt(2)-1)/3);e.beginPath(),n>0&&a>0&&(e.moveTo(r+u,t),e.lineTo(r+a-u,t),e.bezierCurveTo(r+a-u+h*u,t,r+a,t+l-h*l,r+a,t+l),e.lineTo(r+a,t+n-l),e.bezierCurveTo(r+a,t+n-l+h*l,r+a-u+h*u,t+n,r+a-u,t+n),e.lineTo(r+u,t+n),e.bezierCurveTo(r+u-h*u,t+n,r,t+n-l+h*l,r,t+n-l),e.lineTo(r,t+l),e.bezierCurveTo(r,t+l-h*l,r+u-h*u,t,r+u,t),e.closePath())}return new se(r,t,r+a,t+n)}getMarkers(){return null}},Qn=class extends F{constructor(){super(...arguments),this.type="circle"}path(e){var r=this.getAttribute("cx").getPixels("x"),t=this.getAttribute("cy").getPixels("y"),a=this.getAttribute("r").getPixels();return e&&a>0&&(e.beginPath(),e.arc(r,t,a,0,Math.PI*2,!1),e.closePath()),new se(r-a,t-a,r+a,t+a)}getMarkers(){return null}},Kn=class extends F{constructor(){super(...arguments),this.type="ellipse"}path(e){var r=4*((Math.sqrt(2)-1)/3),t=this.getAttribute("rx").getPixels("x"),a=this.getAttribute("ry").getPixels("y"),n=this.getAttribute("cx").getPixels("x"),o=this.getAttribute("cy").getPixels("y");return e&&t>0&&a>0&&(e.beginPath(),e.moveTo(n+t,o),e.bezierCurveTo(n+t,o+r*a,n+r*t,o+a,n,o+a),e.bezierCurveTo(n-r*t,o+a,n-t,o+r*a,n-t,o),e.bezierCurveTo(n-t,o-r*a,n-r*t,o-a,n,o-a),e.bezierCurveTo(n+r*t,o-a,n+t,o-r*a,n+t,o),e.closePath()),new se(n-t,o-a,n+t,o+a)}getMarkers(){return null}},Zn=class extends F{constructor(){super(...arguments),this.type="line"}getPoints(){return[new $(this.getAttribute("x1").getPixels("x"),this.getAttribute("y1").getPixels("y")),new $(this.getAttribute("x2").getPixels("x"),this.getAttribute("y2").getPixels("y"))]}path(e){var[{x:r,y:t},{x:a,y:n}]=this.getPoints();return e&&(e.beginPath(),e.moveTo(r,t),e.lineTo(a,n)),new se(r,t,a,n)}getMarkers(){var[e,r]=this.getPoints(),t=e.angleTo(r);return[[e,t],[r,t]]}},fi=class extends F{constructor(e,r,t){super(e,r,t),this.type="polyline",this.points=[],this.points=$.parsePath(this.getAttribute("points").getString())}path(e){var{points:r}=this,[{x:t,y:a}]=r,n=new se(t,a);return e&&(e.beginPath(),e.moveTo(t,a)),r.forEach(o=>{var{x:s,y:u}=o;n.addPoint(s,u),e&&e.lineTo(s,u)}),n}getMarkers(){var{points:e}=this,r=e.length-1,t=[];return e.forEach((a,n)=>{n!==r&&t.push([a,a.angleTo(e[n+1])])}),t.length>0&&t.push([e[e.length-1],t[t.length-1][1]]),t}},Jn=class extends fi{constructor(){super(...arguments),this.type="polygon"}path(e){var r=super.path(e),[{x:t,y:a}]=this.points;return e&&(e.lineTo(t,a),e.closePath()),r}},es=class extends B{constructor(){super(...arguments),this.type="pattern"}createPattern(e,r,t){var a=this.getStyle("width").getPixels("x",!0),n=this.getStyle("height").getPixels("y",!0),o=new Ye(this.document,null);o.attributes.viewBox=new E(this.document,"viewBox",this.getAttribute("viewBox").getValue()),o.attributes.width=new E(this.document,"width","".concat(a,"px")),o.attributes.height=new E(this.document,"height","".concat(n,"px")),o.attributes.transform=new E(this.document,"transform",this.getAttribute("patternTransform").getValue()),o.children=this.children;var s=this.document.createCanvas(a,n),u=s.getContext("2d"),l=this.getAttribute("x"),h=this.getAttribute("y");l.hasValue()&&h.hasValue()&&u.translate(l.getPixels("x",!0),h.getPixels("y",!0)),t.hasValue()?this.styles["fill-opacity"]=t:Reflect.deleteProperty(this.styles,"fill-opacity");for(var f=-1;f<=1;f++)for(var v=-1;v<=1;v++)u.save(),o.attributes.x=new E(this.document,"x",f*s.width),o.attributes.y=new E(this.document,"y",v*s.height),o.render(u),u.restore();var c=e.createPattern(s,"repeat");return c}},rs=class extends B{constructor(){super(...arguments),this.type="marker"}render(e,r,t){if(r){var{x:a,y:n}=r,o=this.getAttribute("orient").getString("auto"),s=this.getAttribute("markerUnits").getString("strokeWidth");e.translate(a,n),o==="auto"&&e.rotate(t),s==="strokeWidth"&&e.scale(e.lineWidth,e.lineWidth),e.save();var u=new Ye(this.document,null);u.type=this.type,u.attributes.viewBox=new E(this.document,"viewBox",this.getAttribute("viewBox").getValue()),u.attributes.refX=new E(this.document,"refX",this.getAttribute("refX").getValue()),u.attributes.refY=new E(this.document,"refY",this.getAttribute("refY").getValue()),u.attributes.width=new E(this.document,"width",this.getAttribute("markerWidth").getValue()),u.attributes.height=new E(this.document,"height",this.getAttribute("markerHeight").getValue()),u.attributes.overflow=new E(this.document,"overflow",this.getAttribute("overflow").getValue()),u.attributes.fill=new E(this.document,"fill",this.getAttribute("fill").getColor("black")),u.attributes.stroke=new E(this.document,"stroke",this.getAttribute("stroke").getValue("none")),u.children=this.children,u.render(e),e.restore(),s==="strokeWidth"&&e.scale(1/e.lineWidth,1/e.lineWidth),o==="auto"&&e.rotate(-t),e.translate(-a,-n)}}},ts=class extends B{constructor(){super(...arguments),this.type="defs"}render(){}},Xr=class extends Ce{constructor(){super(...arguments),this.type="g"}getBoundingBox(e){var r=new se;return this.children.forEach(t=>{r.addBoundingBox(t.getBoundingBox(e))}),r}},vi=class extends B{constructor(e,r,t){super(e,r,t),this.attributesToInherit=["gradientUnits"],this.stops=[];var{stops:a,children:n}=this;n.forEach(o=>{o.type==="stop"&&a.push(o)})}getGradientUnits(){return this.getAttribute("gradientUnits").getString("objectBoundingBox")}createGradient(e,r,t){var a=this;this.getHrefAttribute().hasValue()&&(a=this.getHrefAttribute().getDefinition(),this.inheritStopContainer(a));var{stops:n}=a,o=this.getGradient(e,r);if(!o)return this.addParentOpacity(t,n[n.length-1].color);if(n.forEach(y=>{o.addColorStop(y.offset,this.addParentOpacity(t,y.color))}),this.getAttribute("gradientTransform").hasValue()){var{document:s}=this,{MAX_VIRTUAL_PIXELS:u,viewPort:l}=s.screen,[h]=l.viewPorts,f=new ci(s,null);f.attributes.x=new E(s,"x",-u/3),f.attributes.y=new E(s,"y",-u/3),f.attributes.width=new E(s,"width",u),f.attributes.height=new E(s,"height",u);var v=new Xr(s,null);v.attributes.transform=new E(s,"transform",this.getAttribute("gradientTransform").getValue()),v.children=[f];var c=new Ye(s,null);c.attributes.x=new E(s,"x",0),c.attributes.y=new E(s,"y",0),c.attributes.width=new E(s,"width",h.width),c.attributes.height=new E(s,"height",h.height),c.children=[v];var g=s.createCanvas(h.width,h.height),p=g.getContext("2d");return p.fillStyle=o,c.render(p),p.createPattern(g,"no-repeat")}return o}inheritStopContainer(e){this.attributesToInherit.forEach(r=>{!this.getAttribute(r).hasValue()&&e.getAttribute(r).hasValue()&&this.getAttribute(r,!0).setValue(e.getAttribute(r).getValue())})}addParentOpacity(e,r){if(e.hasValue()){var t=new E(this.document,"color",r);return t.addOpacity(e).getColor()}return r}},is=class extends vi{constructor(e,r,t){super(e,r,t),this.type="linearGradient",this.attributesToInherit.push("x1","y1","x2","y2")}getGradient(e,r){var t=this.getGradientUnits()==="objectBoundingBox",a=t?r.getBoundingBox(e):null;if(t&&!a)return null;!this.getAttribute("x1").hasValue()&&!this.getAttribute("y1").hasValue()&&!this.getAttribute("x2").hasValue()&&!this.getAttribute("y2").hasValue()&&(this.getAttribute("x1",!0).setValue(0),this.getAttribute("y1",!0).setValue(0),this.getAttribute("x2",!0).setValue(1),this.getAttribute("y2",!0).setValue(0));var n=t?a.x+a.width*this.getAttribute("x1").getNumber():this.getAttribute("x1").getPixels("x"),o=t?a.y+a.height*this.getAttribute("y1").getNumber():this.getAttribute("y1").getPixels("y"),s=t?a.x+a.width*this.getAttribute("x2").getNumber():this.getAttribute("x2").getPixels("x"),u=t?a.y+a.height*this.getAttribute("y2").getNumber():this.getAttribute("y2").getPixels("y");return n===s&&o===u?null:e.createLinearGradient(n,o,s,u)}},as=class extends vi{constructor(e,r,t){super(e,r,t),this.type="radialGradient",this.attributesToInherit.push("cx","cy","r","fx","fy","fr")}getGradient(e,r){var t=this.getGradientUnits()==="objectBoundingBox",a=r.getBoundingBox(e);if(t&&!a)return null;this.getAttribute("cx").hasValue()||this.getAttribute("cx",!0).setValue("50%"),this.getAttribute("cy").hasValue()||this.getAttribute("cy",!0).setValue("50%"),this.getAttribute("r").hasValue()||this.getAttribute("r",!0).setValue("50%");var n=t?a.x+a.width*this.getAttribute("cx").getNumber():this.getAttribute("cx").getPixels("x"),o=t?a.y+a.height*this.getAttribute("cy").getNumber():this.getAttribute("cy").getPixels("y"),s=n,u=o;this.getAttribute("fx").hasValue()&&(s=t?a.x+a.width*this.getAttribute("fx").getNumber():this.getAttribute("fx").getPixels("x")),this.getAttribute("fy").hasValue()&&(u=t?a.y+a.height*this.getAttribute("fy").getNumber():this.getAttribute("fy").getPixels("y"));var l=t?(a.width+a.height)/2*this.getAttribute("r").getNumber():this.getAttribute("r").getPixels(),h=this.getAttribute("fr").getPixels();return e.createRadialGradient(s,u,h,n,o,l)}},ns=class extends B{constructor(e,r,t){super(e,r,t),this.type="stop";var a=Math.max(0,Math.min(1,this.getAttribute("offset").getNumber())),n=this.getStyle("stop-opacity"),o=this.getStyle("stop-color",!0);o.getString()===""&&o.setValue("#000"),n.hasValue()&&(o=o.addOpacity(n)),this.offset=a,this.color=o.getColor()}},Wr=class extends B{constructor(e,r,t){super(e,r,t),this.type="animate",this.duration=0,this.initialValue=null,this.initialUnits="",this.removed=!1,this.frozen=!1,e.screen.animations.push(this),this.begin=this.getAttribute("begin").getMilliseconds(),this.maxDuration=this.begin+this.getAttribute("dur").getMilliseconds(),this.from=this.getAttribute("from"),this.to=this.getAttribute("to"),this.values=new E(e,"values",null);var a=this.getAttribute("values");a.hasValue()&&this.values.setValue(a.getString().split(";"))}getProperty(){var e=this.getAttribute("attributeType").getString(),r=this.getAttribute("attributeName").getString();return e==="CSS"?this.parent.getStyle(r,!0):this.parent.getAttribute(r,!0)}calcValue(){var{initialUnits:e}=this,{progress:r,from:t,to:a}=this.getProgress(),n=t.getNumber()+(a.getNumber()-t.getNumber())*r;return e==="%"&&(n*=100),"".concat(n).concat(e)}update(e){var{parent:r}=this,t=this.getProperty();if(this.initialValue||(this.initialValue=t.getString(),this.initialUnits=t.getUnits()),this.duration>this.maxDuration){var a=this.getAttribute("fill").getString("remove");if(this.getAttribute("repeatCount").getString()==="indefinite"||this.getAttribute("repeatDur").getString()==="indefinite")this.duration=0;else if(a==="freeze"&&!this.frozen)this.frozen=!0,r.animationFrozen=!0,r.animationFrozenValue=t.getString();else if(a==="remove"&&!this.removed)return this.removed=!0,t.setValue(r.animationFrozen?r.animationFrozenValue:this.initialValue),!0;return!1}this.duration+=e;var n=!1;if(this.begin<this.duration){var o=this.calcValue(),s=this.getAttribute("type");if(s.hasValue()){var u=s.getString();o="".concat(u,"(").concat(o,")")}t.setValue(o),n=!0}return n}getProgress(){var{document:e,values:r}=this,t={progress:(this.duration-this.begin)/(this.maxDuration-this.begin)};if(r.hasValue()){var a=t.progress*(r.getValue().length-1),n=Math.floor(a),o=Math.ceil(a);t.from=new E(e,"from",parseFloat(r.getValue()[n])),t.to=new E(e,"to",parseFloat(r.getValue()[o])),t.progress=(a-n)/(o-n)}else t.from=this.from,t.to=this.to;return t}},ss=class extends Wr{constructor(){super(...arguments),this.type="animateColor"}calcValue(){var{progress:e,from:r,to:t}=this.getProgress(),a=new si.default(r.getColor()),n=new si.default(t.getColor());if(a.ok&&n.ok){var o=a.r+(n.r-a.r)*e,s=a.g+(n.g-a.g)*e,u=a.b+(n.b-a.b)*e;return"rgb(".concat(Math.floor(o),", ").concat(Math.floor(s),", ").concat(Math.floor(u),")")}return this.getAttribute("from").getColor()}},os=class extends Wr{constructor(){super(...arguments),this.type="animateTransform"}calcValue(){var{progress:e,from:r,to:t}=this.getProgress(),a=ne(r.getString()),n=ne(t.getString()),o=a.map((s,u)=>{var l=n[u];return s+(l-s)*e}).join(" ");return o}},us=class extends B{constructor(e,r,t){super(e,r,t),this.type="font",this.glyphs={},this.horizAdvX=this.getAttribute("horiz-adv-x").getNumber();var{definitions:a}=e,{children:n}=this;for(var o of n)switch(o.type){case"font-face":{this.fontFace=o;var s=o.getStyle("font-family");s.hasValue()&&(a[s.getString()]=this);break}case"missing-glyph":this.missingGlyph=o;break;case"glyph":{var u=o;u.arabicForm?(this.isRTL=!0,this.isArabic=!0,typeof this.glyphs[u.unicode]>"u"&&(this.glyphs[u.unicode]={}),this.glyphs[u.unicode][u.arabicForm]=u):this.glyphs[u.unicode]=u;break}}}render(){}},ls=class extends B{constructor(e,r,t){super(e,r,t),this.type="font-face",this.ascent=this.getAttribute("ascent").getNumber(),this.descent=this.getAttribute("descent").getNumber(),this.unitsPerEm=this.getAttribute("units-per-em").getNumber()}},hs=class extends F{constructor(){super(...arguments),this.type="missing-glyph",this.horizAdvX=0}},cs=class extends Ge{constructor(){super(...arguments),this.type="tref"}getText(){var e=this.getHrefAttribute().getDefinition();if(e){var r=e.children[0];if(r)return r.getText()}return""}},fs=class extends Ge{constructor(e,r,t){super(e,r,t),this.type="a";var{childNodes:a}=r,n=a[0],o=a.length>0&&Array.from(a).every(s=>s.nodeType===3);this.hasText=o,this.text=o?this.getTextFromNode(n):""}getText(){return this.text}renderChildren(e){if(this.hasText){super.renderChildren(e);var{document:r,x:t,y:a}=this,{mouse:n}=r.screen,o=new E(r,"fontSize",yr.parse(r.ctx.font).fontSize);n.isWorking()&&n.checkBoundingBox(this,new se(t,a-o.getPixels("y"),t+this.measureText(e),a))}else if(this.children.length>0){var s=new Xr(this.document,null);s.children=this.children,s.parent=this,s.render(e)}}onClick(){var{window:e}=this.document;e&&e.open(this.getHrefAttribute().getString())}onMouseMove(){var e=this.document.ctx;e.canvas.style.cursor="pointer"}};function ng(i,e){var r=Object.keys(i);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(i);e&&(t=t.filter(function(a){return Object.getOwnPropertyDescriptor(i,a).enumerable})),r.push.apply(r,t)}return r}function ti(i){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?ng(Object(r),!0).forEach(function(t){Kt(i,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(r)):ng(Object(r)).forEach(function(t){Object.defineProperty(i,t,Object.getOwnPropertyDescriptor(r,t))})}return i}var vs=class extends Ge{constructor(e,r,t){super(e,r,t),this.type="textPath",this.textWidth=0,this.textHeight=0,this.pathLength=-1,this.glyphInfo=null,this.letterSpacingCache=[],this.measuresCache=new Map([["",0]]);var a=this.getHrefAttribute().getDefinition();this.text=this.getTextFromNode(),this.dataArray=this.parsePathData(a)}getText(){return this.text}path(e){var{dataArray:r}=this;e&&e.beginPath(),r.forEach(t=>{var{type:a,points:n}=t;switch(a){case q.LINE_TO:e&&e.lineTo(n[0],n[1]);break;case q.MOVE_TO:e&&e.moveTo(n[0],n[1]);break;case q.CURVE_TO:e&&e.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5]);break;case q.QUAD_TO:e&&e.quadraticCurveTo(n[0],n[1],n[2],n[3]);break;case q.ARC:{var[o,s,u,l,h,f,v,c]=n,g=u>l?u:l,p=u>l?1:u/l,y=u>l?l/u:1;e&&(e.translate(o,s),e.rotate(v),e.scale(p,y),e.arc(0,0,g,h,h+f,!!(1-c)),e.scale(1/p,1/y),e.rotate(-v),e.translate(-o,-s));break}case q.CLOSE_PATH:e&&e.closePath();break}})}renderChildren(e){this.setTextData(e),e.save();var r=this.parent.getStyle("text-decoration").getString(),t=this.getFontSize(),{glyphInfo:a}=this,n=e.fillStyle;r==="underline"&&e.beginPath(),a.forEach((o,s)=>{var{p0:u,p1:l,rotation:h,text:f}=o;e.save(),e.translate(u.x,u.y),e.rotate(h),e.fillStyle&&e.fillText(f,0,0),e.strokeStyle&&e.strokeText(f,0,0),e.restore(),r==="underline"&&(s===0&&e.moveTo(u.x,u.y+t/8),e.lineTo(l.x,l.y+t/5))}),r==="underline"&&(e.lineWidth=t/20,e.strokeStyle=n,e.stroke(),e.closePath()),e.restore()}getLetterSpacingAt(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;return this.letterSpacingCache[e]||0}findSegmentToFitChar(e,r,t,a,n,o,s,u,l){var h=o,f=this.measureText(e,u);u===" "&&r==="justify"&&t<a&&(f+=(a-t)/n),l>-1&&(h+=this.getLetterSpacingAt(l));var v=this.textHeight/20,c=this.getEquidistantPointOnPath(h,v,0),g=this.getEquidistantPointOnPath(h+f,v,0),p={p0:c,p1:g},y=c&&g?Math.atan2(g.y-c.y,g.x-c.x):0;if(s){var m=Math.cos(Math.PI/2+y)*s,T=Math.cos(-y)*s;p.p0=ti(ti({},c),{},{x:c.x+m,y:c.y+T}),p.p1=ti(ti({},g),{},{x:g.x+m,y:g.y+T})}return h+=f,{offset:h,segment:p,rotation:y}}measureText(e,r){var{measuresCache:t}=this,a=r||this.getText();if(t.has(a))return t.get(a);var n=this.measureTargetText(e,a);return t.set(a,n),n}setTextData(e){if(!this.glyphInfo){var r=this.getText(),t=r.split(""),a=r.split(" ").length-1,n=this.parent.getAttribute("dx").split().map(O=>O.getPixels("x")),o=this.parent.getAttribute("dy").getPixels("y"),s=this.parent.getStyle("text-anchor").getString("start"),u=this.getStyle("letter-spacing"),l=this.parent.getStyle("letter-spacing"),h=0;!u.hasValue()||u.getValue()==="inherit"?h=l.getPixels():u.hasValue()&&u.getValue()!=="initial"&&u.getValue()!=="unset"&&(h=u.getPixels());var f=[],v=r.length;this.letterSpacingCache=f;for(var c=0;c<v;c++)f.push(typeof n[c]<"u"?n[c]:h);var g=f.reduce((O,w,C)=>C===0?0:O+w||0,0),p=this.measureText(e),y=Math.max(p+g,0);this.textWidth=p,this.textHeight=this.getFontSize(),this.glyphInfo=[];var m=this.getPathLength(),T=this.getStyle("startOffset").getNumber(0)*m,x=0;(s==="middle"||s==="center")&&(x=-y/2),(s==="end"||s==="right")&&(x=-y),x+=T,t.forEach((O,w)=>{var{offset:C,segment:S,rotation:P}=this.findSegmentToFitChar(e,s,y,m,a,x,o,O,w);x=C,!(!S.p0||!S.p1)&&this.glyphInfo.push({text:t[w],p0:S.p0,p1:S.p1,rotation:P})})}}parsePathData(e){if(this.pathLength=-1,!e)return[];var r=[],{pathParser:t}=e;for(t.reset();!t.isEnd();){var{current:a}=t,n=a?a.x:0,o=a?a.y:0,s=t.next(),u=s.type,l=[];switch(s.type){case q.MOVE_TO:this.pathM(t,l);break;case q.LINE_TO:u=this.pathL(t,l);break;case q.HORIZ_LINE_TO:u=this.pathH(t,l);break;case q.VERT_LINE_TO:u=this.pathV(t,l);break;case q.CURVE_TO:this.pathC(t,l);break;case q.SMOOTH_CURVE_TO:u=this.pathS(t,l);break;case q.QUAD_TO:this.pathQ(t,l);break;case q.SMOOTH_QUAD_TO:u=this.pathT(t,l);break;case q.ARC:l=this.pathA(t);break;case q.CLOSE_PATH:F.pathZ(t);break}s.type!==q.CLOSE_PATH?r.push({type:u,points:l,start:{x:n,y:o},pathLength:this.calcLength(n,o,u,l)}):r.push({type:q.CLOSE_PATH,points:[],pathLength:0})}return r}pathM(e,r){var{x:t,y:a}=F.pathM(e).point;r.push(t,a)}pathL(e,r){var{x:t,y:a}=F.pathL(e).point;return r.push(t,a),q.LINE_TO}pathH(e,r){var{x:t,y:a}=F.pathH(e).point;return r.push(t,a),q.LINE_TO}pathV(e,r){var{x:t,y:a}=F.pathV(e).point;return r.push(t,a),q.LINE_TO}pathC(e,r){var{point:t,controlPoint:a,currentPoint:n}=F.pathC(e);r.push(t.x,t.y,a.x,a.y,n.x,n.y)}pathS(e,r){var{point:t,controlPoint:a,currentPoint:n}=F.pathS(e);return r.push(t.x,t.y,a.x,a.y,n.x,n.y),q.CURVE_TO}pathQ(e,r){var{controlPoint:t,currentPoint:a}=F.pathQ(e);r.push(t.x,t.y,a.x,a.y)}pathT(e,r){var{controlPoint:t,currentPoint:a}=F.pathT(e);return r.push(t.x,t.y,a.x,a.y),q.QUAD_TO}pathA(e){var{rX:r,rY:t,sweepFlag:a,xAxisRotation:n,centp:o,a1:s,ad:u}=F.pathA(e);return a===0&&u>0&&(u-=2*Math.PI),a===1&&u<0&&(u+=2*Math.PI),[o.x,o.y,r,t,s,u,n,a]}calcLength(e,r,t,a){var n=0,o=null,s=null,u=0;switch(t){case q.LINE_TO:return this.getLineLength(e,r,a[0],a[1]);case q.CURVE_TO:for(n=0,o=this.getPointOnCubicBezier(0,e,r,a[0],a[1],a[2],a[3],a[4],a[5]),u=.01;u<=1;u+=.01)s=this.getPointOnCubicBezier(u,e,r,a[0],a[1],a[2],a[3],a[4],a[5]),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return n;case q.QUAD_TO:for(n=0,o=this.getPointOnQuadraticBezier(0,e,r,a[0],a[1],a[2],a[3]),u=.01;u<=1;u+=.01)s=this.getPointOnQuadraticBezier(u,e,r,a[0],a[1],a[2],a[3]),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return n;case q.ARC:{n=0;var l=a[4],h=a[5],f=a[4]+h,v=Math.PI/180;if(Math.abs(l-f)<v&&(v=Math.abs(l-f)),o=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],l,0),h<0)for(u=l-v;u>f;u-=v)s=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],u,0),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;else for(u=l+v;u<f;u+=v)s=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],u,0),n+=this.getLineLength(o.x,o.y,s.x,s.y),o=s;return s=this.getPointOnEllipticalArc(a[0],a[1],a[2],a[3],f,0),n+=this.getLineLength(o.x,o.y,s.x,s.y),n}}return 0}getPointOnLine(e,r,t,a,n){var o=arguments.length>5&&arguments[5]!==void 0?arguments[5]:r,s=arguments.length>6&&arguments[6]!==void 0?arguments[6]:t,u=(n-t)/(a-r+pr),l=Math.sqrt(e*e/(1+u*u));a<r&&(l*=-1);var h=u*l,f=null;if(a===r)f={x:o,y:s+h};else if((s-t)/(o-r+pr)===u)f={x:o+l,y:s+h};else{var v=0,c=0,g=this.getLineLength(r,t,a,n);if(g<pr)return null;var p=(o-r)*(a-r)+(s-t)*(n-t);p/=g*g,v=r+p*(a-r),c=t+p*(n-t);var y=this.getLineLength(o,s,v,c),m=Math.sqrt(e*e-y*y);l=Math.sqrt(m*m/(1+u*u)),a<r&&(l*=-1),h=u*l,f={x:v+l,y:c+h}}return f}getPointOnPath(e){var r=this.getPathLength(),t=0,a=null;if(e<-5e-5||e-5e-5>r)return null;var{dataArray:n}=this;for(var o of n){if(o&&(o.pathLength<5e-5||t+o.pathLength+5e-5<e)){t+=o.pathLength;continue}var s=e-t,u=0;switch(o.type){case q.LINE_TO:a=this.getPointOnLine(s,o.start.x,o.start.y,o.points[0],o.points[1],o.start.x,o.start.y);break;case q.ARC:{var l=o.points[4],h=o.points[5],f=o.points[4]+h;if(u=l+s/o.pathLength*h,h<0&&u<f||h>=0&&u>f)break;a=this.getPointOnEllipticalArc(o.points[0],o.points[1],o.points[2],o.points[3],u,o.points[6]);break}case q.CURVE_TO:u=s/o.pathLength,u>1&&(u=1),a=this.getPointOnCubicBezier(u,o.start.x,o.start.y,o.points[0],o.points[1],o.points[2],o.points[3],o.points[4],o.points[5]);break;case q.QUAD_TO:u=s/o.pathLength,u>1&&(u=1),a=this.getPointOnQuadraticBezier(u,o.start.x,o.start.y,o.points[0],o.points[1],o.points[2],o.points[3]);break}if(a)return a;break}return null}getLineLength(e,r,t,a){return Math.sqrt((t-e)*(t-e)+(a-r)*(a-r))}getPathLength(){return this.pathLength===-1&&(this.pathLength=this.dataArray.reduce((e,r)=>r.pathLength>0?e+r.pathLength:e,0)),this.pathLength}getPointOnCubicBezier(e,r,t,a,n,o,s,u,l){var h=u*Qv(e)+o*Kv(e)+a*Zv(e)+r*Jv(e),f=l*Qv(e)+s*Kv(e)+n*Zv(e)+t*Jv(e);return{x:h,y:f}}getPointOnQuadraticBezier(e,r,t,a,n,o,s){var u=o*eg(e)+a*rg(e)+r*tg(e),l=s*eg(e)+n*rg(e)+t*tg(e);return{x:u,y:l}}getPointOnEllipticalArc(e,r,t,a,n,o){var s=Math.cos(o),u=Math.sin(o),l={x:t*Math.cos(n),y:a*Math.sin(n)};return{x:e+(l.x*s-l.y*u),y:r+(l.x*u+l.y*s)}}buildEquidistantCache(e,r){var t=this.getPathLength(),a=r||.25,n=e||t/100;if(!this.equidistantCache||this.equidistantCache.step!==n||this.equidistantCache.precision!==a){this.equidistantCache={step:n,precision:a,points:[]};for(var o=0,s=0;s<=t;s+=a){var u=this.getPointOnPath(s),l=this.getPointOnPath(s+a);!u||!l||(o+=this.getLineLength(u.x,u.y,l.x,l.y),o>=n&&(this.equidistantCache.points.push({x:u.x,y:u.y,distance:s}),o-=n))}}}getEquidistantPointOnPath(e,r,t){if(this.buildEquidistantCache(r,t),e<0||e-this.getPathLength()>5e-5)return null;var a=Math.round(e/this.getPathLength()*(this.equidistantCache.points.length-1));return this.equidistantCache.points[a]||null}},lO=/^\s*data:(([^/,;]+\/[^/,;]+)(?:;([^,;=]+=[^,;=]+))?)?(?:;(base64))?,(.*)$/i,gs=class extends Ce{constructor(e,r,t){super(e,r,t),this.type="image",this.loaded=!1;var a=this.getHrefAttribute().getString();if(a){var n=a.endsWith(".svg")||/^\s*data:image\/svg\+xml/i.test(a);e.images.push(this),n?this.loadSvg(a):this.loadImage(a),this.isSvg=n}}loadImage(e){var r=this;return xe(function*(){try{var t=yield r.document.createImage(e);r.image=t}catch(a){console.error('Error while loading image "'.concat(e,'":'),a)}r.loaded=!0})()}loadSvg(e){var r=this;return xe(function*(){var t=lO.exec(e);if(t){var a=t[5];t[4]==="base64"?r.image=atob(a):r.image=decodeURIComponent(a)}else try{var n=yield r.document.fetch(e),o=yield n.text();r.image=o}catch(s){console.error('Error while loading image "'.concat(e,'":'),s)}r.loaded=!0})()}renderChildren(e){var{document:r,image:t,loaded:a}=this,n=this.getAttribute("x").getPixels("x"),o=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(!(!a||!t||!s||!u)){if(e.save(),e.translate(n,o),this.isSvg){var l=r.canvg.forkString(e,this.image,{ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0,ignoreClear:!0,offsetX:0,offsetY:0,scaleWidth:s,scaleHeight:u});l.document.documentElement.parent=this,l.render()}else{var h=this.image;r.setViewBox({ctx:e,aspectRatio:this.getAttribute("preserveAspectRatio").getString(),width:s,desiredWidth:h.width,height:u,desiredHeight:h.height}),this.loaded&&(typeof h.complete>"u"||h.complete)&&e.drawImage(h,0,0)}e.restore()}}getBoundingBox(){var e=this.getAttribute("x").getPixels("x"),r=this.getAttribute("y").getPixels("y"),t=this.getStyle("width").getPixels("x"),a=this.getStyle("height").getPixels("y");return new se(e,r,e+t,r+a)}},ds=class extends Ce{constructor(){super(...arguments),this.type="symbol"}render(e){}},ps=class{constructor(e){this.document=e,this.loaded=!1,e.fonts.push(this)}load(e,r){var t=this;return xe(function*(){try{var{document:a}=t,n=yield a.canvg.parser.load(r),o=n.getElementsByTagName("font");Array.from(o).forEach(s=>{var u=a.createElement(s);a.definitions[e]=u})}catch(s){console.error('Error while loading font "'.concat(r,'":'),s)}t.loaded=!0})()}},hO=(()=>{class i extends B{constructor(r,t,a){super(r,t,a),this.type="style";var n=mr(Array.from(t.childNodes).map(s=>s.textContent).join("").replace(/(\/\*([^*]|[\r\n]|(\*+([^*/]|[\r\n])))*\*+\/)|(^[\s]*\/\/.*)/gm,"").replace(/@import.*;/g,"")),o=n.split("}");o.forEach(s=>{var u=s.trim();if(u){var l=u.split("{"),h=l[0].split(","),f=l[1].split(";");h.forEach(v=>{var c=v.trim();if(c){var g=r.styles[c]||{};if(f.forEach(m=>{var T=m.indexOf(":"),x=m.substr(0,T).trim(),O=m.substr(T+1,m.length-T).trim();x&&O&&(g[x]=new E(r,x,O))}),r.styles[c]=g,r.stylesSpecificity[c]=eO(c),c==="@font-face"){var p=g["font-family"].getString().replace(/"|'/g,""),y=g.src.getString().split(",");y.forEach(m=>{if(m.indexOf('format("svg")')>0){var T=Yv(m);T&&new ps(r).load(p,T)}})}}})}})}}return i.parseExternalUrl=Yv,i})(),ys=class extends Ce{constructor(){super(...arguments),this.type="use"}setContext(e){super.setContext(e);var r=this.getAttribute("x"),t=this.getAttribute("y");r.hasValue()&&e.translate(r.getPixels("x"),0),t.hasValue()&&e.translate(0,t.getPixels("y"))}path(e){var{element:r}=this;r&&r.path(e)}renderChildren(e){var{document:r,element:t}=this;if(t){var a=t;if(t.type==="symbol"&&(a=new Ye(r,null),a.attributes.viewBox=new E(r,"viewBox",t.getAttribute("viewBox").getString()),a.attributes.preserveAspectRatio=new E(r,"preserveAspectRatio",t.getAttribute("preserveAspectRatio").getString()),a.attributes.overflow=new E(r,"overflow",t.getAttribute("overflow").getString()),a.children=t.children,t.styles.opacity=new E(r,"opacity",this.calculateOpacity())),a.type==="svg"){var n=this.getStyle("width",!1,!0),o=this.getStyle("height",!1,!0);n.hasValue()&&(a.attributes.width=new E(r,"width",n.getString())),o.hasValue()&&(a.attributes.height=new E(r,"height",o.getString()))}var s=a.parent;a.parent=this,a.render(e),a.parent=s}}getBoundingBox(e){var{element:r}=this;return r?r.getBoundingBox(e):null}elementTransform(){var{document:e,element:r}=this;return Cs.fromElement(e,r)}get element(){return this.cachedElement||(this.cachedElement=this.getHrefAttribute().getDefinition()),this.cachedElement}};function ii(i,e,r,t,a,n){return i[r*t*4+e*4+n]}function ai(i,e,r,t,a,n,o){i[r*t*4+e*4+n]=o}function j(i,e,r){var t=i[e];return t*r}function Ee(i,e,r,t){return e+Math.cos(i)*r+Math.sin(i)*t}var gi=class extends B{constructor(e,r,t){super(e,r,t),this.type="feColorMatrix";var a=ne(this.getAttribute("values").getString());switch(this.getAttribute("type").getString("matrix")){case"saturate":{var n=a[0];a=[.213+.787*n,.715-.715*n,.072-.072*n,0,0,.213-.213*n,.715+.285*n,.072-.072*n,0,0,.213-.213*n,.715-.715*n,.072+.928*n,0,0,0,0,0,1,0,0,0,0,0,1];break}case"hueRotate":{var o=a[0]*Math.PI/180;a=[Ee(o,.213,.787,-.213),Ee(o,.715,-.715,-.715),Ee(o,.072,-.072,.928),0,0,Ee(o,.213,-.213,.143),Ee(o,.715,.285,.14),Ee(o,.072,-.072,-.283),0,0,Ee(o,.213,-.213,-.787),Ee(o,.715,-.715,.715),Ee(o,.072,.928,.072),0,0,0,0,0,1,0,0,0,0,0,1];break}case"luminanceToAlpha":a=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,.2125,.7154,.0721,0,0,0,0,0,0,1];break}this.matrix=a,this.includeOpacity=this.getAttribute("includeOpacity").hasValue()}apply(e,r,t,a,n){for(var{includeOpacity:o,matrix:s}=this,u=e.getImageData(0,0,a,n),l=0;l<n;l++)for(var h=0;h<a;h++){var f=ii(u.data,h,l,a,n,0),v=ii(u.data,h,l,a,n,1),c=ii(u.data,h,l,a,n,2),g=ii(u.data,h,l,a,n,3),p=j(s,0,f)+j(s,1,v)+j(s,2,c)+j(s,3,g)+j(s,4,1),y=j(s,5,f)+j(s,6,v)+j(s,7,c)+j(s,8,g)+j(s,9,1),m=j(s,10,f)+j(s,11,v)+j(s,12,c)+j(s,13,g)+j(s,14,1),T=j(s,15,f)+j(s,16,v)+j(s,17,c)+j(s,18,g)+j(s,19,1);o&&(p=0,y=0,m=0,T*=g/255),ai(u.data,h,l,a,n,0,p),ai(u.data,h,l,a,n,1,y),ai(u.data,h,l,a,n,2,m),ai(u.data,h,l,a,n,3,T)}e.clearRect(0,0,a,n),e.putImageData(u,0,0)}},cO=(()=>{class i extends B{constructor(){super(...arguments),this.type="mask"}apply(r,t){var{document:a}=this,n=this.getAttribute("x").getPixels("x"),o=this.getAttribute("y").getPixels("y"),s=this.getStyle("width").getPixels("x"),u=this.getStyle("height").getPixels("y");if(!s&&!u){var l=new se;this.children.forEach(p=>{l.addBoundingBox(p.getBoundingBox(r))}),n=Math.floor(l.x1),o=Math.floor(l.y1),s=Math.floor(l.width),u=Math.floor(l.height)}var h=this.removeStyles(t,i.ignoreStyles),f=a.createCanvas(n+s,o+u),v=f.getContext("2d");a.screen.setDefaults(v),this.renderChildren(v),new gi(a,{nodeType:1,childNodes:[],attributes:[{nodeName:"type",value:"luminanceToAlpha"},{nodeName:"includeOpacity",value:"true"}]}).apply(v,0,0,n+s,o+u);var c=a.createCanvas(n+s,o+u),g=c.getContext("2d");a.screen.setDefaults(g),t.render(g),g.globalCompositeOperation="destination-in",g.fillStyle=v.createPattern(f,"no-repeat"),g.fillRect(0,0,n+s,o+u),r.fillStyle=g.createPattern(c,"no-repeat"),r.fillRect(0,0,n+s,o+u),this.restoreStyles(t,h)}render(r){}}return i.ignoreStyles=["mask","transform","clip-path"],i})(),sg=()=>{},ms=class extends B{constructor(){super(...arguments),this.type="clipPath"}apply(e){var{document:r}=this,t=Reflect.getPrototypeOf(e),{beginPath:a,closePath:n}=e;t&&(t.beginPath=sg,t.closePath=sg),Reflect.apply(a,e,[]),this.children.forEach(o=>{if(!(typeof o.path>"u")){var s=typeof o.elementTransform<"u"?o.elementTransform():null;s||(s=Cs.fromElement(r,o)),s&&s.apply(e),o.path(e),t&&(t.closePath=n),s&&s.unapply(e)}}),Reflect.apply(n,e,[]),e.clip(),t&&(t.beginPath=a,t.closePath=n)}render(e){}},fO=(()=>{class i extends B{constructor(){super(...arguments),this.type="filter"}apply(r,t){var{document:a,children:n}=this,o=t.getBoundingBox(r);if(o){var s=0,u=0;n.forEach(T=>{var x=T.extraFilterDistance||0;s=Math.max(s,x),u=Math.max(u,x)});var l=Math.floor(o.width),h=Math.floor(o.height),f=l+2*s,v=h+2*u;if(!(f<1||v<1)){var c=Math.floor(o.x),g=Math.floor(o.y),p=this.removeStyles(t,i.ignoreStyles),y=a.createCanvas(f,v),m=y.getContext("2d");a.screen.setDefaults(m),m.translate(-c+s,-g+u),t.render(m),n.forEach(T=>{typeof T.apply=="function"&&T.apply(m,0,0,f,v)}),r.drawImage(y,0,0,f,v,c-s,g-u,f,v),this.restoreStyles(t,p)}}}render(r){}}return i.ignoreStyles=["filter","transform","clip-path"],i})(),bs=class extends B{constructor(e,r,t){super(e,r,t),this.type="feDropShadow",this.addStylesFromStyleDefinition()}apply(e,r,t,a,n){}},xs=class extends B{constructor(){super(...arguments),this.type="feMorphology"}apply(e,r,t,a,n){}},Ts=class extends B{constructor(){super(...arguments),this.type="feComposite"}apply(e,r,t,a,n){}},Os=class extends B{constructor(e,r,t){super(e,r,t),this.type="feGaussianBlur",this.blurRadius=Math.floor(this.getAttribute("stdDeviation").getNumber()),this.extraFilterDistance=this.blurRadius}apply(e,r,t,a,n){var{document:o,blurRadius:s}=this,u=o.window?o.window.document.body:null,l=e.canvas;l.id=o.getUniqueId(),u&&(l.style.display="none",u.appendChild(l)),Gv(l,r,t,a,n,s),u&&u.removeChild(l)}},Ss=class extends B{constructor(){super(...arguments),this.type="title"}},ws=class extends B{constructor(){super(...arguments),this.type="desc"}},vO={svg:Ye,rect:ci,circle:Qn,ellipse:Kn,line:Zn,polyline:fi,polygon:Jn,path:F,pattern:es,marker:rs,defs:ts,linearGradient:is,radialGradient:as,stop:ns,animate:Wr,animateColor:ss,animateTransform:os,font:us,"font-face":ls,"missing-glyph":hs,glyph:li,text:Ge,tspan:hi,tref:cs,a:fs,textPath:vs,image:gs,g:Xr,symbol:ds,style:hO,use:ys,mask:cO,clipPath:ms,filter:fO,feDropShadow:bs,feMorphology:xs,feComposite:Ts,feColorMatrix:gi,feGaussianBlur:Os,title:Ss,desc:ws};function og(i,e){var r=Object.keys(i);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(i);e&&(t=t.filter(function(a){return Object.getOwnPropertyDescriptor(i,a).enumerable})),r.push.apply(r,t)}return r}function gO(i){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?og(Object(r),!0).forEach(function(t){Kt(i,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(r)):og(Object(r)).forEach(function(t){Object.defineProperty(i,t,Object.getOwnPropertyDescriptor(r,t))})}return i}function dO(i,e){var r=document.createElement("canvas");return r.width=i,r.height=e,r}function pO(i){return Es.apply(this,arguments)}function Es(){return Es=xe(function*(i){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,r=document.createElement("img");return e&&(r.crossOrigin="Anonymous"),new Promise((t,a)=>{r.onload=()=>{t(r)},r.onerror=(n,o,s,u,l)=>{a(l)},r.src=i})}),Es.apply(this,arguments)}var yO=(()=>{class i{constructor(r){var{rootEmSize:t=12,emSize:a=12,createCanvas:n=i.createCanvas,createImage:o=i.createImage,anonymousCrossOrigin:s}=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.canvg=r,this.definitions={},this.styles={},this.stylesSpecificity={},this.images=[],this.fonts=[],this.emSizeStack=[],this.uniqueId=0,this.screen=r.screen,this.rootEmSize=t,this.emSize=a,this.createCanvas=n,this.createImage=this.bindCreateImage(o,s),this.screen.wait(this.isImagesLoaded.bind(this)),this.screen.wait(this.isFontsLoaded.bind(this))}bindCreateImage(r,t){return typeof t=="boolean"?(a,n)=>r(a,typeof n=="boolean"?n:t):r}get window(){return this.screen.window}get fetch(){return this.screen.fetch}get ctx(){return this.screen.ctx}get emSize(){var{emSizeStack:r}=this;return r[r.length-1]}set emSize(r){var{emSizeStack:t}=this;t.push(r)}popEmSize(){var{emSizeStack:r}=this;r.pop()}getUniqueId(){return"canvg".concat(++this.uniqueId)}isImagesLoaded(){return this.images.every(r=>r.loaded)}isFontsLoaded(){return this.fonts.every(r=>r.loaded)}createDocumentElement(r){var t=this.createElement(r.documentElement);return t.root=!0,t.addStylesFromStyleDefinition(),this.documentElement=t,t}createElement(r){var t=r.nodeName.replace(/^[^:]+:/,""),a=i.elementTypes[t];return typeof a<"u"?new a(this,r):new Xn(this,r)}createTextNode(r){return new Wn(this,r)}setViewBox(r){this.screen.setViewBox(gO({document:this},r))}}return i.createCanvas=dO,i.createImage=pO,i.elementTypes=vO,i})();function ug(i,e){var r=Object.keys(i);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(i);e&&(t=t.filter(function(a){return Object.getOwnPropertyDescriptor(i,a).enumerable})),r.push.apply(r,t)}return r}function He(i){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?ug(Object(r),!0).forEach(function(t){Kt(i,t,r[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(i,Object.getOwnPropertyDescriptors(r)):ug(Object(r)).forEach(function(t){Object.defineProperty(i,t,Object.getOwnPropertyDescriptor(r,t))})}return i}var lg=class i{constructor(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.parser=new Yr(t),this.screen=new hg(e,t),this.options=t;var a=new yO(this,t),n=a.createDocumentElement(r);this.document=a,this.documentElement=n}static from(e,r){var t=arguments;return xe(function*(){var a=t.length>2&&t[2]!==void 0?t[2]:{},n=new Yr(a),o=yield n.parse(r);return new i(e,o,a)})()}static fromString(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},a=new Yr(t),n=a.parseFromString(r);return new i(e,n,t)}fork(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return i.from(e,r,He(He({},this.options),t))}forkString(e,r){var t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return i.fromString(e,r,He(He({},this.options),t))}ready(){return this.screen.ready()}isReady(){return this.screen.isReady()}render(){var e=arguments,r=this;return xe(function*(){var t=e.length>0&&e[0]!==void 0?e[0]:{};r.start(He({enableRedraw:!0,ignoreAnimation:!0,ignoreMouse:!0},t)),yield r.ready(),r.stop()})()}start(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},{documentElement:r,screen:t,options:a}=this;t.start(r,He(He({enableRedraw:!0},a),e))}stop(){this.screen.stop()}resize(e){var r=arguments.length>1&&arguments[1]!==void 0?arguments[1]:e,t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:!1;this.documentElement.resize(e,r,t)}};export{fs as AElement,ss as AnimateColorElement,Wr as AnimateElement,os as AnimateTransformElement,se as BoundingBox,Qv as CB1,Kv as CB2,Zv as CB3,Jv as CB4,lg as Canvg,Qn as CircleElement,ms as ClipPathElement,ts as DefsElement,ws as DescElement,yO as Document,B as Element,Kn as EllipseElement,gi as FeColorMatrixElement,Ts as FeCompositeElement,bs as FeDropShadowElement,Os as FeGaussianBlurElement,xs as FeMorphologyElement,fO as FilterElement,yr as Font,us as FontElement,ls as FontFaceElement,Xr as GElement,li as GlyphElement,vi as GradientElement,gs as ImageElement,Zn as LineElement,is as LinearGradientElement,rs as MarkerElement,cO as MaskElement,oi as Matrix,hs as MissingGlyphElement,Fn as Mouse,pr as PSEUDO_ZERO,Yr as Parser,F as PathElement,q as PathParser,es as PatternElement,$ as Point,Jn as PolygonElement,fi as PolylineElement,E as Property,eg as QB1,rg as QB2,tg as QB3,as as RadialGradientElement,ci as RectElement,Ce as RenderedElement,$n as Rotate,Ye as SVGElement,ps as SVGFontLoader,Hn as Scale,hg as Screen,ui as Skew,Gn as SkewX,Yn as SkewY,ns as StopElement,hO as StyleElement,ds as SymbolElement,cs as TRefElement,hi as TSpanElement,Ge as TextElement,vs as TextPathElement,Ss as TitleElement,Cs as Transform,zn as Translate,Xn as UnknownElement,ys as UseElement,jn as ViewPort,mr as compressSpaces,lg as default,eO as getSelectorSpecificity,HT as normalizeAttributeName,GT as normalizeColor,Yv as parseExternalUrl,vC as presets,ne as toNumbers,FT as trimLeft,zT as trimRight,Xv as vectorMagnitude,Wv as vectorsAngle,Un as vectorsRatio};
