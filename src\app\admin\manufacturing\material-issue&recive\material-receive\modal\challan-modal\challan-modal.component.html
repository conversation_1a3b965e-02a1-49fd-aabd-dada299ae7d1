
<h2 mat-dialog-title class="title mt-2">Cha<PERSON> Issued Details</h2>
<mat-dialog-content>
  <ng-container *ngIf="challanDetails.length > 0; else noData">
    <table mat-table [dataSource]="challanDetails" class="mat-elevation-z8">
         <!-- Issue No -->
      <ng-container matColumnDef="issueNo">
        <th mat-header-cell *matHeaderCellDef>Issue No</th>
        <td mat-cell *matCellDef="let detail">{{detail.issueNo}}</td>
      </ng-container>
      <!-- Challan No Column -->
      <ng-container matColumnDef="challanNo">
        <th mat-header-cell *matHeaderCellDef>Challan No</th>
        <td mat-cell *matCellDef="let detail">{{detail.challanNo}}</td>
      </ng-container>
      <!-- Challan Date Column -->
      <ng-container matColumnDef="cDate">
        <th mat-header-cell *matHeaderCellDef>C Date</th>
        <td mat-cell *matCellDef="let detail">{{detail.cDate | date:'dd-MM-yyyy'}}</td>
      </ng-container>
      <!-- Kati Column -->
      <ng-container matColumnDef="katiIssued">
        <th mat-header-cell *matHeaderCellDef>Kati Issued</th>
        <td mat-cell *matCellDef="let detail">
          <div class="issued-cell">
            <span class="issued-value">{{ detail.katiIssued | number:'1.3-3' }}</span>
            <button mat-icon-button class="mini-info" matTooltip="View Kati details" (click)="toggleKatiDetail(detail)">
              <mat-icon>info</mat-icon>
            </button>
          </div>
        </td>
      </ng-container>

      <!-- Tana Column -->
      <ng-container matColumnDef="tanaIssued">
        <th mat-header-cell *matHeaderCellDef>Tana Issued</th>
        <td mat-cell *matCellDef="let detail">{{detail.tanaIssued}}</td>
      </ng-container>
      <!-- Soot Column -->
      <ng-container matColumnDef="sootIssued">
        <th mat-header-cell *matHeaderCellDef>Soot Issued</th>
        <td mat-cell *matCellDef="let detail">{{detail.sootIssued}}</td>
      </ng-container>
      <!-- Tharri Column -->
      <ng-container matColumnDef="tharriIssued">
        <th mat-header-cell *matHeaderCellDef>Tharri Issued</th>
        <td mat-cell *matCellDef="let detail">{{detail.tharriIssued}}</td>
      </ng-container>
      <!-- Silk Column -->
      <ng-container matColumnDef="silkIssued">
        <th mat-header-cell *matHeaderCellDef>Silk Issued</th>
        <td mat-cell *matCellDef="let detail">{{detail.silkIssued}}</td>
      </ng-container>
      <!-- Other Column -->
      <ng-container matColumnDef="otherIssued">
        <th mat-header-cell *matHeaderCellDef>Other Issued</th>
        <td mat-cell *matCellDef="let detail">{{detail.otherIssued}}</td>
      </ng-container>

      <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
      <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
    </table>

<!-- Draggable floating Kati details panel -->
<div *ngIf="showKatiPanel" class="kati-flyover" cdkDrag>
  <div class="kati-flyover-header" cdkDragHandle>
    <div class="kati-header-title">
      Challan No: {{ activeChallan?.challanNo || '—' }} (Kati Material Details)
    </div>
    <button mat-icon-button (click)="closeKatiPanel()" matTooltip="Close">
      <mat-icon>close</mat-icon>
    </button>
  </div>
  <div class="kati-flyover-body">
    <table class="kati-mini-table" *ngIf="activeKatiRows?.length; else noKatiData">
      <thead>
        <tr>
          <th>Sr.No</th>
          <th>Colour</th>
          <th>Lagat</th>
          <th>Carpet Lagat</th>
          <th>Issued</th>
          <th>To Issue</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let r of activeKatiRows; let i = index">
          <td>{{i+1}}</td>
          <td>{{ r?.colourText }}</td>
          <td>{{ r?.lagat }}</td>
          <td>{{ r?.carpetLagat }}</td>
          <td>{{ r?.issueValue || '0.000' }}</td>
          <td>{{ r?.toIssueValue || '0.000' }}</td>
        </tr>
      </tbody>
      <tfoot>
        <tr>
          <th>Total</th>
          <td></td>
          <td></td>
          <td></td>
          <th>{{ activeTotals.issue | number:'1.3-3' }}</th>
          <th>{{ activeTotals.toIssue | number:'1.3-3' }}</th>
        </tr>
      </tfoot>
    </table>
    <ng-template #noKatiData>
      <div class="empty">No Kati details available</div>
    </ng-template>
  </div>
</div>

  </ng-container>
  <ng-template #noData>
    <p>No Challan Issued Available.</p>
  </ng-template>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button mat-button mat-dialog-close>Close</button>
</mat-dialog-actions>

