const express = require('express');
const router = express.Router();
const materialReceiveController = require('../../controller/manifacturing/materialReceive-controller');

// Create a new material receive
router.post('/materialReceive', materialReceiveController.createMaterialReceive);

// Get all material receives
router.get('/materialReceive', materialReceiveController.getAllMaterialReceives);

// Get material receive by ID
router.get('/materialReceive/:id', materialReceiveController.getMaterialReceiveById);

// Update material receive by ID
router.patch('/materialReceive/:id', materialReceiveController.updateMaterialReceive);

// Delete material receive by ID
router.delete('/materialReceive/:id', materialReceiveController.deleteMaterialReceive);

// Extra queries
router.get('/materialReceive/challan/:challanNo', materialReceiveController.getMaterialReceivesByChallanNo);
router.get('/materialReceive/weaver/:weaver', materialReceiveController.getMaterialReceivesByWeaver);
router.get('/materialReceive/issueNo/:issueNoId', materialReceiveController.getMaterialReceivesByIssueNo);

module.exports = router;

