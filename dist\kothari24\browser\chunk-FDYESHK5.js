import{A as ce,B as de,E as It,G as pi,H as et,I as Bt,L as rt,U as bi,a as B,aa as gi,d as Rt,e as ae,f as St,g as re,h as Fe,i as Me,j as Et,l as se,m as hi,n as Te,o as C,p as fi}from"./chunk-UCMEJ5RL.js";import{$ as M,$b as O,A as it,Aa as H,B as Ee,Cb as N,Eb as Oe,Ec as R,G as ot,Ga as lt,Gb as mi,Jb as ht,Jc as ne,Kb as z,Nb as at,Ob as oe,Pb as $,Qb as Q,S as Nt,U as tt,V as ci,Xa as gt,Ya as c,Za as li,_ as k,_a as j,a as dt,ab as ie,ac as F,ba as S,c as Re,da as h,e as ri,ea as E,eb as w,f as g,g as Ct,gb as Z,hb as b,ia as u,ib as Y,ja as I,k as si,ka as T,l as q,la as p,mb as mt,n as Kt,nb as ut,q as Jt,qa as nt,qb as U,qc as ui,r as te,rb as Ie,tb as Dt,uc as Lt,v as ee,va as D,vc as _t,wa as di,x as Se,xa as K,xb as W,yb as X,yc as x,z as bt,za as v,zb as kt}from"./chunk-TSVGDZRC.js";import{a as V,b as qt}from"./chunk-CWTPBX7D.js";var $i=["mat-button",""],Qi=[[["",8,"material-icons",3,"iconPositionEnd",""],["mat-icon",3,"iconPositionEnd",""],["","matButtonIcon","",3,"iconPositionEnd",""]],"*",[["","iconPositionEnd","",8,"material-icons"],["mat-icon","iconPositionEnd",""],["","matButtonIcon","","iconPositionEnd",""]]],Gi=[".material-icons:not([iconPositionEnd]), mat-icon:not([iconPositionEnd]), [matButtonIcon]:not([iconPositionEnd])","*",".material-icons[iconPositionEnd], mat-icon[iconPositionEnd], [matButtonIcon][iconPositionEnd]"];var qi=".cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-unelevated-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-raised-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-outlined-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-icon-button{outline:solid 1px}";var Ki=["mat-icon-button",""],Ji=["*"];var to=new S("MAT_BUTTON_CONFIG");var eo=[{attribute:"mat-button",mdcClasses:["mdc-button","mat-mdc-button"]},{attribute:"mat-flat-button",mdcClasses:["mdc-button","mdc-button--unelevated","mat-mdc-unelevated-button"]},{attribute:"mat-raised-button",mdcClasses:["mdc-button","mdc-button--raised","mat-mdc-raised-button"]},{attribute:"mat-stroked-button",mdcClasses:["mdc-button","mdc-button--outlined","mat-mdc-outlined-button"]},{attribute:"mat-fab",mdcClasses:["mdc-fab","mat-mdc-fab"]},{attribute:"mat-mini-fab",mdcClasses:["mdc-fab","mdc-fab--mini","mat-mdc-mini-fab"]},{attribute:"mat-icon-button",mdcClasses:["mdc-icon-button","mat-mdc-icon-button"]}],_i=(()=>{class i{get ripple(){return this._rippleLoader?.getRipple(this._elementRef.nativeElement)}set ripple(t){this._rippleLoader?.attachRipple(this._elementRef.nativeElement,t)}get disableRipple(){return this._disableRipple}set disableRipple(t){this._disableRipple=t,this._updateRippleDisabled()}get disabled(){return this._disabled}set disabled(t){this._disabled=t,this._updateRippleDisabled()}constructor(t,e,o,a){this._elementRef=t,this._platform=e,this._ngZone=o,this._animationMode=a,this._focusMonitor=E(It),this._rippleLoader=E(gi),this._isFab=!1,this._disableRipple=!1,this._disabled=!1;let r=E(to,{optional:!0}),s=t.nativeElement,d=s.classList;this.disabledInteractive=r?.disabledInteractive??!1,this._rippleLoader?.configureRipple(s,{className:"mat-mdc-button-ripple"});for(let{attribute:m,mdcClasses:l}of eo)s.hasAttribute(m)&&d.add(...l)}ngAfterViewInit(){this._focusMonitor.monitor(this._elementRef,!0)}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._rippleLoader?.destroyRipple(this._elementRef.nativeElement)}focus(t="program",e){t?this._focusMonitor.focusVia(this._elementRef.nativeElement,t,e):this._elementRef.nativeElement.focus(e)}_getAriaDisabled(){return this.ariaDisabled!=null?this.ariaDisabled:this.disabled&&this.disabledInteractive?!0:null}_getDisabledAttribute(){return this.disabledInteractive||!this.disabled?null:!0}_updateRippleDisabled(){this._rippleLoader?.setDisabled(this._elementRef.nativeElement,this.disableRipple||this.disabled)}static{this.\u0275fac=function(e){li()}}static{this.\u0275dir=p({type:i,inputs:{color:"color",disableRipple:[u.HasDecoratorInputTransform,"disableRipple","disableRipple",x],disabled:[u.HasDecoratorInputTransform,"disabled","disabled",x],ariaDisabled:[u.HasDecoratorInputTransform,"aria-disabled","ariaDisabled",x],disabledInteractive:[u.HasDecoratorInputTransform,"disabledInteractive","disabledInteractive",x]},features:[Y]})}}return i})();var dn=(()=>{class i extends _i{constructor(t,e,o,a){super(t,e,o,a)}static{this.\u0275fac=function(e){return new(e||i)(c(v),c(B),c(w),c(lt,8))}}static{this.\u0275cmp=I({type:i,selectors:[["button","mat-button",""],["button","mat-raised-button",""],["button","mat-flat-button",""],["button","mat-stroked-button",""]],hostVars:14,hostBindings:function(e,o){e&2&&(ut("disabled",o._getDisabledAttribute())("aria-disabled",o._getAriaDisabled()),Ie(o.color?"mat-"+o.color:""),U("mat-mdc-button-disabled",o.disabled)("mat-mdc-button-disabled-interactive",o.disabledInteractive)("_mat-animation-noopable",o._animationMode==="NoopAnimations")("mat-unthemed",!o.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],standalone:!0,features:[b,F],attrs:$i,ngContentSelectors:Gi,decls:7,vars:4,consts:[[1,"mat-mdc-button-persistent-ripple"],[1,"mdc-button__label"],[1,"mat-mdc-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(e,o){e&1&&(ht(Qi),kt(0,"span",0),z(1),W(2,"span",1),z(3,1),X(),z(4,2),kt(5,"span",2)(6,"span",3)),e&2&&U("mdc-button__ripple",!o._isFab)("mdc-fab__ripple",o._isFab)},styles:['.mdc-touch-target-wrapper{display:inline}.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button{position:relative;display:inline-flex;align-items:center;justify-content:center;box-sizing:border-box;min-width:64px;border:none;outline:none;line-height:inherit;user-select:none;-webkit-appearance:none;overflow:visible;vertical-align:middle;background:rgba(0,0,0,0)}.mdc-button .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-button::-moz-focus-inner{padding:0;border:0}.mdc-button:active{outline:none}.mdc-button:hover{cursor:pointer}.mdc-button:disabled{cursor:default;pointer-events:none}.mdc-button[hidden]{display:none}.mdc-button .mdc-button__icon{margin-left:0;margin-right:8px;display:inline-block;position:relative;vertical-align:top}[dir=rtl] .mdc-button .mdc-button__icon,.mdc-button .mdc-button__icon[dir=rtl]{margin-left:8px;margin-right:0}.mdc-button .mdc-button__progress-indicator{font-size:0;position:absolute;transform:translate(-50%, -50%);top:50%;left:50%;line-height:initial}.mdc-button .mdc-button__label{position:relative}.mdc-button .mdc-button__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px);display:none}@media screen and (forced-colors: active){.mdc-button .mdc-button__focus-ring{border-color:CanvasText}}.mdc-button .mdc-button__focus-ring::after{content:"";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-button .mdc-button__focus-ring::after{border-color:CanvasText}}@media screen and (forced-colors: active){.mdc-button.mdc-ripple-upgraded--background-focused .mdc-button__focus-ring,.mdc-button:not(.mdc-ripple-upgraded):focus .mdc-button__focus-ring{display:block}}.mdc-button .mdc-button__touch{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%)}.mdc-button__label+.mdc-button__icon{margin-left:8px;margin-right:0}[dir=rtl] .mdc-button__label+.mdc-button__icon,.mdc-button__label+.mdc-button__icon[dir=rtl]{margin-left:0;margin-right:8px}svg.mdc-button__icon{fill:currentColor}.mdc-button--touch{margin-top:6px;margin-bottom:6px}.mdc-button{padding:0 8px 0 8px}.mdc-button--unelevated{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--unelevated.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--unelevated.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--raised{transition:box-shadow 280ms cubic-bezier(0.4, 0, 0.2, 1);padding:0 16px 0 16px}.mdc-button--raised.mdc-button--icon-trailing{padding:0 12px 0 16px}.mdc-button--raised.mdc-button--icon-leading{padding:0 16px 0 12px}.mdc-button--outlined{border-style:solid;transition:border 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-button--outlined .mdc-button__ripple{border-style:solid;border-color:rgba(0,0,0,0)}.mat-mdc-button{font-family:var(--mdc-text-button-label-text-font);font-size:var(--mdc-text-button-label-text-size);letter-spacing:var(--mdc-text-button-label-text-tracking);font-weight:var(--mdc-text-button-label-text-weight);text-transform:var(--mdc-text-button-label-text-transform);height:var(--mdc-text-button-container-height);border-radius:var(--mdc-text-button-container-shape);padding:0 var(--mat-text-button-horizontal-padding, 8px)}.mat-mdc-button:not(:disabled){color:var(--mdc-text-button-label-text-color)}.mat-mdc-button:disabled{color:var(--mdc-text-button-disabled-label-text-color)}.mat-mdc-button .mdc-button__ripple{border-radius:var(--mdc-text-button-container-shape)}.mat-mdc-button:has(.material-icons,mat-icon,[matButtonIcon]){padding:0 var(--mat-text-button-with-icon-horizontal-padding, 8px)}.mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, 0)}[dir=rtl] .mat-mdc-button>.mat-icon{margin-right:var(--mat-text-button-icon-offset, 0);margin-left:var(--mat-text-button-icon-spacing, 8px)}.mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-offset, 0);margin-left:var(--mat-text-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-button .mdc-button__label+.mat-icon{margin-right:var(--mat-text-button-icon-spacing, 8px);margin-left:var(--mat-text-button-icon-offset, 0)}.mat-mdc-button .mat-ripple-element{background-color:var(--mat-text-button-ripple-color)}.mat-mdc-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-state-layer-color)}.mat-mdc-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-text-button-disabled-state-layer-color)}.mat-mdc-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-hover-state-layer-opacity)}.mat-mdc-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-focus-state-layer-opacity)}.mat-mdc-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-text-button-pressed-state-layer-opacity)}.mat-mdc-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-text-button-touch-target-display)}.mat-mdc-button[disabled],.mat-mdc-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-text-button-disabled-label-text-color)}.mat-mdc-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-unelevated-button{font-family:var(--mdc-filled-button-label-text-font);font-size:var(--mdc-filled-button-label-text-size);letter-spacing:var(--mdc-filled-button-label-text-tracking);font-weight:var(--mdc-filled-button-label-text-weight);text-transform:var(--mdc-filled-button-label-text-transform);height:var(--mdc-filled-button-container-height);border-radius:var(--mdc-filled-button-container-shape);padding:0 var(--mat-filled-button-horizontal-padding, 16px)}.mat-mdc-unelevated-button:not(:disabled){background-color:var(--mdc-filled-button-container-color)}.mat-mdc-unelevated-button:disabled{background-color:var(--mdc-filled-button-disabled-container-color)}.mat-mdc-unelevated-button:not(:disabled){color:var(--mdc-filled-button-label-text-color)}.mat-mdc-unelevated-button:disabled{color:var(--mdc-filled-button-disabled-label-text-color)}.mat-mdc-unelevated-button .mdc-button__ripple{border-radius:var(--mdc-filled-button-container-shape)}.mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-unelevated-button>.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -4px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}.mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-offset, -4px);margin-left:var(--mat-filled-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-unelevated-button .mdc-button__label+.mat-icon{margin-right:var(--mat-filled-button-icon-spacing, 8px);margin-left:var(--mat-filled-button-icon-offset, -4px)}.mat-mdc-unelevated-button .mat-ripple-element{background-color:var(--mat-filled-button-ripple-color)}.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-state-layer-color)}.mat-mdc-unelevated-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-filled-button-disabled-state-layer-color)}.mat-mdc-unelevated-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-hover-state-layer-opacity)}.mat-mdc-unelevated-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-focus-state-layer-opacity)}.mat-mdc-unelevated-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-filled-button-pressed-state-layer-opacity)}.mat-mdc-unelevated-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-filled-button-touch-target-display)}.mat-mdc-unelevated-button[disabled],.mat-mdc-unelevated-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-filled-button-disabled-label-text-color);background-color:var(--mdc-filled-button-disabled-container-color)}.mat-mdc-unelevated-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-raised-button{font-family:var(--mdc-protected-button-label-text-font);font-size:var(--mdc-protected-button-label-text-size);letter-spacing:var(--mdc-protected-button-label-text-tracking);font-weight:var(--mdc-protected-button-label-text-weight);text-transform:var(--mdc-protected-button-label-text-transform);height:var(--mdc-protected-button-container-height);border-radius:var(--mdc-protected-button-container-shape);padding:0 var(--mat-protected-button-horizontal-padding, 16px);box-shadow:var(--mdc-protected-button-container-elevation-shadow)}.mat-mdc-raised-button:not(:disabled){background-color:var(--mdc-protected-button-container-color)}.mat-mdc-raised-button:disabled{background-color:var(--mdc-protected-button-disabled-container-color)}.mat-mdc-raised-button:not(:disabled){color:var(--mdc-protected-button-label-text-color)}.mat-mdc-raised-button:disabled{color:var(--mdc-protected-button-disabled-label-text-color)}.mat-mdc-raised-button .mdc-button__ripple{border-radius:var(--mdc-protected-button-container-shape)}.mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-raised-button>.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -4px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}.mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-offset, -4px);margin-left:var(--mat-protected-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-raised-button .mdc-button__label+.mat-icon{margin-right:var(--mat-protected-button-icon-spacing, 8px);margin-left:var(--mat-protected-button-icon-offset, -4px)}.mat-mdc-raised-button .mat-ripple-element{background-color:var(--mat-protected-button-ripple-color)}.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-state-layer-color)}.mat-mdc-raised-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-protected-button-disabled-state-layer-color)}.mat-mdc-raised-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-hover-state-layer-opacity)}.mat-mdc-raised-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-focus-state-layer-opacity)}.mat-mdc-raised-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-protected-button-pressed-state-layer-opacity)}.mat-mdc-raised-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-protected-button-touch-target-display)}.mat-mdc-raised-button:hover{box-shadow:var(--mdc-protected-button-hover-container-elevation-shadow)}.mat-mdc-raised-button:focus{box-shadow:var(--mdc-protected-button-focus-container-elevation-shadow)}.mat-mdc-raised-button:active,.mat-mdc-raised-button:focus:active{box-shadow:var(--mdc-protected-button-pressed-container-elevation-shadow)}.mat-mdc-raised-button[disabled],.mat-mdc-raised-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-protected-button-disabled-label-text-color);background-color:var(--mdc-protected-button-disabled-container-color)}.mat-mdc-raised-button[disabled].mat-mdc-button-disabled,.mat-mdc-raised-button.mat-mdc-button-disabled.mat-mdc-button-disabled{box-shadow:var(--mdc-protected-button-disabled-container-elevation-shadow)}.mat-mdc-raised-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-outlined-button{font-family:var(--mdc-outlined-button-label-text-font);font-size:var(--mdc-outlined-button-label-text-size);letter-spacing:var(--mdc-outlined-button-label-text-tracking);font-weight:var(--mdc-outlined-button-label-text-weight);text-transform:var(--mdc-outlined-button-label-text-transform);height:var(--mdc-outlined-button-container-height);border-radius:var(--mdc-outlined-button-container-shape);padding:0 15px 0 15px;border-width:var(--mdc-outlined-button-outline-width);padding:0 var(--mat-outlined-button-horizontal-padding, 15px)}.mat-mdc-outlined-button:not(:disabled){color:var(--mdc-outlined-button-label-text-color)}.mat-mdc-outlined-button:disabled{color:var(--mdc-outlined-button-disabled-label-text-color)}.mat-mdc-outlined-button .mdc-button__ripple{border-radius:var(--mdc-outlined-button-container-shape)}.mat-mdc-outlined-button:not(:disabled){border-color:var(--mdc-outlined-button-outline-color)}.mat-mdc-outlined-button:disabled{border-color:var(--mdc-outlined-button-disabled-outline-color)}.mat-mdc-outlined-button.mdc-button--icon-trailing{padding:0 11px 0 15px}.mat-mdc-outlined-button.mdc-button--icon-leading{padding:0 15px 0 11px}.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:var(--mdc-outlined-button-outline-width)}.mat-mdc-outlined-button .mdc-button__touch{left:calc(-1 * var(--mdc-outlined-button-outline-width));width:calc(100% + 2 * var(--mdc-outlined-button-outline-width))}.mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -4px)}[dir=rtl] .mat-mdc-outlined-button>.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -4px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}.mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-offset, -4px);margin-left:var(--mat-outlined-button-icon-spacing, 8px)}[dir=rtl] .mat-mdc-outlined-button .mdc-button__label+.mat-icon{margin-right:var(--mat-outlined-button-icon-spacing, 8px);margin-left:var(--mat-outlined-button-icon-offset, -4px)}.mat-mdc-outlined-button .mat-ripple-element{background-color:var(--mat-outlined-button-ripple-color)}.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-state-layer-color)}.mat-mdc-outlined-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-outlined-button-disabled-state-layer-color)}.mat-mdc-outlined-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-hover-state-layer-opacity)}.mat-mdc-outlined-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-focus-state-layer-opacity)}.mat-mdc-outlined-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-outlined-button-pressed-state-layer-opacity)}.mat-mdc-outlined-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:0;right:0;transform:translateY(-50%);display:var(--mat-outlined-button-touch-target-display)}.mat-mdc-outlined-button[disabled],.mat-mdc-outlined-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-outlined-button-disabled-label-text-color);border-color:var(--mdc-outlined-button-disabled-outline-color)}.mat-mdc-outlined-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-button-base{text-decoration:none}.mat-mdc-button,.mat-mdc-unelevated-button,.mat-mdc-raised-button,.mat-mdc-outlined-button{-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple,.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-button .mat-mdc-button-ripple,.mat-mdc-unelevated-button .mat-mdc-button-ripple,.mat-mdc-raised-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-unelevated-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-raised-button .mat-mdc-button-persistent-ripple::before,.mat-mdc-outlined-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-button .mdc-button__label,.mat-mdc-unelevated-button .mdc-button__label,.mat-mdc-raised-button .mdc-button__label,.mat-mdc-outlined-button .mdc-button__label{z-index:1}.mat-mdc-button .mat-mdc-focus-indicator,.mat-mdc-unelevated-button .mat-mdc-focus-indicator,.mat-mdc-raised-button .mat-mdc-focus-indicator,.mat-mdc-outlined-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-unelevated-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-raised-button:focus .mat-mdc-focus-indicator::before,.mat-mdc-outlined-button:focus .mat-mdc-focus-indicator::before{content:""}.mat-mdc-button._mat-animation-noopable,.mat-mdc-unelevated-button._mat-animation-noopable,.mat-mdc-raised-button._mat-animation-noopable,.mat-mdc-outlined-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-button>.mat-icon,.mat-mdc-unelevated-button>.mat-icon,.mat-mdc-raised-button>.mat-icon,.mat-mdc-outlined-button>.mat-icon{display:inline-block;position:relative;vertical-align:top;font-size:1.125rem;height:1.125rem;width:1.125rem}.mat-mdc-outlined-button .mat-mdc-button-ripple,.mat-mdc-outlined-button .mdc-button__ripple{top:-1px;left:-1px;bottom:-1px;right:-1px;border-width:-1px}.mat-mdc-unelevated-button .mat-mdc-focus-indicator::before,.mat-mdc-raised-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 2px)*-1)}.mat-mdc-outlined-button .mat-mdc-focus-indicator::before{margin:calc(calc(var(--mat-mdc-focus-indicator-border-width, 3px) + 3px)*-1)}',".cdk-high-contrast-active .mat-mdc-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-unelevated-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-raised-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-outlined-button:not(.mdc-button--outlined),.cdk-high-contrast-active .mat-mdc-icon-button{outline:solid 1px}"],encapsulation:2,changeDetection:0})}}return i})();var ln=(()=>{class i extends _i{constructor(t,e,o,a){super(t,e,o,a),this._rippleLoader.configureRipple(this._elementRef.nativeElement,{centered:!0})}static{this.\u0275fac=function(e){return new(e||i)(c(v),c(B),c(w),c(lt,8))}}static{this.\u0275cmp=I({type:i,selectors:[["button","mat-icon-button",""]],hostVars:14,hostBindings:function(e,o){e&2&&(ut("disabled",o._getDisabledAttribute())("aria-disabled",o._getAriaDisabled()),Ie(o.color?"mat-"+o.color:""),U("mat-mdc-button-disabled",o.disabled)("mat-mdc-button-disabled-interactive",o.disabledInteractive)("_mat-animation-noopable",o._animationMode==="NoopAnimations")("mat-unthemed",!o.color)("mat-mdc-button-base",!0))},exportAs:["matButton"],standalone:!0,features:[b,F],attrs:Ki,ngContentSelectors:Ji,decls:4,vars:0,consts:[[1,"mat-mdc-button-persistent-ripple","mdc-icon-button__ripple"],[1,"mat-mdc-focus-indicator"],[1,"mat-mdc-button-touch-target"]],template:function(e,o){e&1&&(ht(),kt(0,"span",0),z(1),kt(2,"span",1)(3,"span",2))},styles:['.mdc-icon-button{display:inline-block;position:relative;box-sizing:border-box;border:none;outline:none;background-color:rgba(0,0,0,0);fill:currentColor;color:inherit;text-decoration:none;cursor:pointer;user-select:none;z-index:0;overflow:visible}.mdc-icon-button .mdc-icon-button__touch{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%)}@media screen and (forced-colors: active){.mdc-icon-button.mdc-ripple-upgraded--background-focused .mdc-icon-button__focus-ring,.mdc-icon-button:not(.mdc-ripple-upgraded):focus .mdc-icon-button__focus-ring{display:block}}.mdc-icon-button:disabled{cursor:default;pointer-events:none}.mdc-icon-button[hidden]{display:none}.mdc-icon-button--display-flex{align-items:center;display:inline-flex;justify-content:center}.mdc-icon-button__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%;display:none}@media screen and (forced-colors: active){.mdc-icon-button__focus-ring{border-color:CanvasText}}.mdc-icon-button__focus-ring::after{content:"";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-icon-button__focus-ring::after{border-color:CanvasText}}.mdc-icon-button__icon{display:inline-block}.mdc-icon-button__icon.mdc-icon-button__icon--on{display:none}.mdc-icon-button--on .mdc-icon-button__icon{display:none}.mdc-icon-button--on .mdc-icon-button__icon.mdc-icon-button__icon--on{display:inline-block}.mdc-icon-button__link{height:100%;left:0;outline:none;position:absolute;top:0;width:100%}.mat-mdc-icon-button{color:var(--mdc-icon-button-icon-color)}.mat-mdc-icon-button .mdc-button__icon{font-size:var(--mdc-icon-button-icon-size)}.mat-mdc-icon-button svg,.mat-mdc-icon-button img{width:var(--mdc-icon-button-icon-size);height:var(--mdc-icon-button-icon-size)}.mat-mdc-icon-button:disabled{color:var(--mdc-icon-button-disabled-icon-color)}.mat-mdc-icon-button{border-radius:50%;flex-shrink:0;text-align:center;width:var(--mdc-icon-button-state-layer-size, 48px);height:var(--mdc-icon-button-state-layer-size, 48px);padding:calc(calc(var(--mdc-icon-button-state-layer-size, 48px) - var(--mdc-icon-button-icon-size, 24px)) / 2);font-size:var(--mdc-icon-button-icon-size);-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-icon-button svg{vertical-align:baseline}.mat-mdc-icon-button[disabled],.mat-mdc-icon-button.mat-mdc-button-disabled{cursor:default;pointer-events:none;color:var(--mdc-icon-button-disabled-icon-color)}.mat-mdc-icon-button.mat-mdc-button-disabled-interactive{pointer-events:auto}.mat-mdc-icon-button .mat-mdc-button-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple,.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none;border-radius:inherit}.mat-mdc-icon-button .mat-mdc-button-ripple{overflow:hidden}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{content:"";opacity:0}.mat-mdc-icon-button .mdc-button__label{z-index:1}.mat-mdc-icon-button .mat-mdc-focus-indicator{top:0;left:0;right:0;bottom:0;position:absolute}.mat-mdc-icon-button:focus .mat-mdc-focus-indicator::before{content:""}.mat-mdc-icon-button .mat-ripple-element{background-color:var(--mat-icon-button-ripple-color)}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-state-layer-color)}.mat-mdc-icon-button.mat-mdc-button-disabled .mat-mdc-button-persistent-ripple::before{background-color:var(--mat-icon-button-disabled-state-layer-color)}.mat-mdc-icon-button:hover .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-hover-state-layer-opacity)}.mat-mdc-icon-button.cdk-program-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.cdk-keyboard-focused .mat-mdc-button-persistent-ripple::before,.mat-mdc-icon-button.mat-mdc-button-disabled-interactive:focus .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-focus-state-layer-opacity)}.mat-mdc-icon-button:active .mat-mdc-button-persistent-ripple::before{opacity:var(--mat-icon-button-pressed-state-layer-opacity)}.mat-mdc-icon-button .mat-mdc-button-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-icon-button-touch-target-display)}.mat-mdc-icon-button._mat-animation-noopable{transition:none !important;animation:none !important}.mat-mdc-icon-button .mat-mdc-button-persistent-ripple{border-radius:50%}.mat-mdc-icon-button.mat-unthemed:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-primary:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-accent:not(.mdc-ripple-upgraded):focus::before,.mat-mdc-icon-button.mat-warn:not(.mdc-ripple-upgraded):focus::before{background:rgba(0,0,0,0);opacity:1}',qi],encapsulation:2,changeDetection:0})}}return i})();var mn=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=T({type:i})}static{this.\u0275inj=M({imports:[rt,bi,rt]})}}return i})();var le=class{};function me(i){return i&&typeof i.connect=="function"&&!(i instanceof ri)}var Ot=function(i){return i[i.REPLACED=0]="REPLACED",i[i.INSERTED=1]="INSERTED",i[i.MOVED=2]="MOVED",i[i.REMOVED=3]="REMOVED",i}(Ot||{}),zt=new S("_ViewRepeater"),Ft=class{applyChanges(n,t,e,o,a){n.forEachOperation((r,s,d)=>{let m,l;if(r.previousIndex==null){let f=e(r,s,d);m=t.createEmbeddedView(f.templateRef,f.context,f.index),l=Ot.INSERTED}else d==null?(t.remove(s),l=Ot.REMOVED):(m=t.get(s),t.move(m,d),l=Ot.MOVED);a&&a({context:m?.context,operation:l,record:r})})}detach(){}};var vi=class{get selected(){return this._selected||(this._selected=Array.from(this._selection.values())),this._selected}constructor(n=!1,t,e=!0,o){this._multiple=n,this._emitChanges=e,this.compareWith=o,this._selection=new Set,this._deselectedToEmit=[],this._selectedToEmit=[],this.changed=new g,t&&t.length&&(n?t.forEach(a=>this._markSelected(a)):this._markSelected(t[0]),this._selectedToEmit.length=0)}select(...n){this._verifyValueAssignment(n),n.forEach(e=>this._markSelected(e));let t=this._hasQueuedChanges();return this._emitChangeEvent(),t}deselect(...n){this._verifyValueAssignment(n),n.forEach(e=>this._unmarkSelected(e));let t=this._hasQueuedChanges();return this._emitChangeEvent(),t}setSelection(...n){this._verifyValueAssignment(n);let t=this.selected,e=new Set(n);n.forEach(a=>this._markSelected(a)),t.filter(a=>!e.has(this._getConcreteValue(a,e))).forEach(a=>this._unmarkSelected(a));let o=this._hasQueuedChanges();return this._emitChangeEvent(),o}toggle(n){return this.isSelected(n)?this.deselect(n):this.select(n)}clear(n=!0){this._unmarkAll();let t=this._hasQueuedChanges();return n&&this._emitChangeEvent(),t}isSelected(n){return this._selection.has(this._getConcreteValue(n))}isEmpty(){return this._selection.size===0}hasValue(){return!this.isEmpty()}sort(n){this._multiple&&this.selected&&this._selected.sort(n)}isMultipleSelection(){return this._multiple}_emitChangeEvent(){this._selected=null,(this._selectedToEmit.length||this._deselectedToEmit.length)&&(this.changed.next({source:this,added:this._selectedToEmit,removed:this._deselectedToEmit}),this._deselectedToEmit=[],this._selectedToEmit=[])}_markSelected(n){n=this._getConcreteValue(n),this.isSelected(n)||(this._multiple||this._unmarkAll(),this.isSelected(n)||this._selection.add(n),this._emitChanges&&this._selectedToEmit.push(n))}_unmarkSelected(n){n=this._getConcreteValue(n),this.isSelected(n)&&(this._selection.delete(n),this._emitChanges&&this._deselectedToEmit.push(n))}_unmarkAll(){this.isEmpty()||this._selection.forEach(n=>this._unmarkSelected(n))}_verifyValueAssignment(n){n.length>1&&this._multiple}_hasQueuedChanges(){return!!(this._deselectedToEmit.length||this._selectedToEmit.length)}_getConcreteValue(n,t){if(this.compareWith){t=t??this._selection;for(let e of t)if(this.compareWith(n,e))return e;return n}else return n}};var bn=(()=>{class i{constructor(){this._listeners=[]}notify(t,e){for(let o of this._listeners)o(t,e)}listen(t){return this._listeners.push(t),()=>{this._listeners=this._listeners.filter(e=>t!==e)}}ngOnDestroy(){this._listeners=[]}static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var io=20,ue=(()=>{class i{constructor(t,e,o){this._ngZone=t,this._platform=e,this._scrolled=new g,this._globalSubscription=null,this._scrolledCount=0,this.scrollContainers=new Map,this._document=o}register(t){this.scrollContainers.has(t)||this.scrollContainers.set(t,t.elementScrolled().subscribe(()=>this._scrolled.next(t)))}deregister(t){let e=this.scrollContainers.get(t);e&&(e.unsubscribe(),this.scrollContainers.delete(t))}scrolled(t=io){return this._platform.isBrowser?new Re(e=>{this._globalSubscription||this._addGlobalListener();let o=t>0?this._scrolled.pipe(Ee(t)).subscribe(e):this._scrolled.subscribe(e);return this._scrolledCount++,()=>{o.unsubscribe(),this._scrolledCount--,this._scrolledCount||this._removeGlobalListener()}}):q()}ngOnDestroy(){this._removeGlobalListener(),this.scrollContainers.forEach((t,e)=>this.deregister(e)),this._scrolled.complete()}ancestorScrolled(t,e){let o=this.getAncestorScrollContainers(t);return this.scrolled(e).pipe(it(a=>!a||o.indexOf(a)>-1))}getAncestorScrollContainers(t){let e=[];return this.scrollContainers.forEach((o,a)=>{this._scrollableContainsElement(a,t)&&e.push(a)}),e}_getWindow(){return this._document.defaultView||window}_scrollableContainsElement(t,e){let o=fi(e),a=t.getElementRef().nativeElement;do if(o==a)return!0;while(o=o.parentElement);return!1}_addGlobalListener(){this._globalSubscription=this._ngZone.runOutsideAngular(()=>{let t=this._getWindow();return Se(t.document,"scroll").subscribe(()=>this._scrolled.next())})}_removeGlobalListener(){this._globalSubscription&&(this._globalSubscription.unsubscribe(),this._globalSubscription=null)}static{this.\u0275fac=function(e){return new(e||i)(h(w),h(B),h(R,8))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),oo=(()=>{class i{constructor(t,e,o,a){this.elementRef=t,this.scrollDispatcher=e,this.ngZone=o,this.dir=a,this._destroyed=new g,this._elementScrolled=new Re(r=>this.ngZone.runOutsideAngular(()=>Se(this.elementRef.nativeElement,"scroll").pipe(tt(this._destroyed)).subscribe(r)))}ngOnInit(){this.scrollDispatcher.register(this)}ngOnDestroy(){this.scrollDispatcher.deregister(this),this._destroyed.next(),this._destroyed.complete()}elementScrolled(){return this._elementScrolled}getElementRef(){return this.elementRef}scrollTo(t){let e=this.elementRef.nativeElement,o=this.dir&&this.dir.value=="rtl";t.left==null&&(t.left=o?t.end:t.start),t.right==null&&(t.right=o?t.start:t.end),t.bottom!=null&&(t.top=e.scrollHeight-e.clientHeight-t.bottom),o&&St()!=Rt.NORMAL?(t.left!=null&&(t.right=e.scrollWidth-e.clientWidth-t.left),St()==Rt.INVERTED?t.left=t.right:St()==Rt.NEGATED&&(t.left=t.right?-t.right:t.right)):t.right!=null&&(t.left=e.scrollWidth-e.clientWidth-t.right),this._applyScrollToOptions(t)}_applyScrollToOptions(t){let e=this.elementRef.nativeElement;ae()?e.scrollTo(t):(t.top!=null&&(e.scrollTop=t.top),t.left!=null&&(e.scrollLeft=t.left))}measureScrollOffset(t){let e="left",o="right",a=this.elementRef.nativeElement;if(t=="top")return a.scrollTop;if(t=="bottom")return a.scrollHeight-a.clientHeight-a.scrollTop;let r=this.dir&&this.dir.value=="rtl";return t=="start"?t=r?o:e:t=="end"&&(t=r?e:o),r&&St()==Rt.INVERTED?t==e?a.scrollWidth-a.clientWidth-a.scrollLeft:a.scrollLeft:r&&St()==Rt.NEGATED?t==e?a.scrollLeft+a.scrollWidth-a.clientWidth:-a.scrollLeft:t==e?a.scrollLeft:a.scrollWidth-a.clientWidth-a.scrollLeft}static{this.\u0275fac=function(e){return new(e||i)(c(v),c(ue),c(w),c(et,8))}}static{this.\u0275dir=p({type:i,selectors:[["","cdk-scrollable",""],["","cdkScrollable",""]],standalone:!0})}}return i})(),no=20,Mt=(()=>{class i{constructor(t,e,o){this._platform=t,this._change=new g,this._changeListener=a=>{this._change.next(a)},this._document=o,e.runOutsideAngular(()=>{if(t.isBrowser){let a=this._getWindow();a.addEventListener("resize",this._changeListener),a.addEventListener("orientationchange",this._changeListener)}this.change().subscribe(()=>this._viewportSize=null)})}ngOnDestroy(){if(this._platform.isBrowser){let t=this._getWindow();t.removeEventListener("resize",this._changeListener),t.removeEventListener("orientationchange",this._changeListener)}this._change.complete()}getViewportSize(){this._viewportSize||this._updateViewportSize();let t={width:this._viewportSize.width,height:this._viewportSize.height};return this._platform.isBrowser||(this._viewportSize=null),t}getViewportRect(){let t=this.getViewportScrollPosition(),{width:e,height:o}=this.getViewportSize();return{top:t.top,left:t.left,bottom:t.top+o,right:t.left+e,height:o,width:e}}getViewportScrollPosition(){if(!this._platform.isBrowser)return{top:0,left:0};let t=this._document,e=this._getWindow(),o=t.documentElement,a=o.getBoundingClientRect(),r=-a.top||t.body.scrollTop||e.scrollY||o.scrollTop||0,s=-a.left||t.body.scrollLeft||e.scrollX||o.scrollLeft||0;return{top:r,left:s}}change(t=no){return t>0?this._change.pipe(Ee(t)):this._change}_getWindow(){return this._document.defaultView||window}_updateViewportSize(){let t=this._getWindow();this._viewportSize=this._platform.isBrowser?{width:t.innerWidth,height:t.innerHeight}:{width:0,height:0}}static{this.\u0275fac=function(e){return new(e||i)(h(B),h(w),h(R,8))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var yi=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=T({type:i})}static{this.\u0275inj=M({})}}return i})(),Vt=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=T({type:i})}static{this.\u0275inj=M({imports:[Bt,yi,Bt,yi]})}}return i})();var uo=[[["caption"]],[["colgroup"],["col"]],"*"],ho=["caption","colgroup, col","*"];function fo(i,n){i&1&&z(0,2)}function po(i,n){i&1&&(W(0,"thead",0),N(1,1),X(),W(2,"tbody",0),N(3,2)(4,3),X(),W(5,"tfoot",0),N(6,4),X())}function bo(i,n){i&1&&N(0,1)(1,2)(2,3)(3,4)}var J=new S("CDK_TABLE");var ge=(()=>{class i{constructor(t){this.template=t}static{this.\u0275fac=function(e){return new(e||i)(c(j))}}static{this.\u0275dir=p({type:i,selectors:[["","cdkCellDef",""]],standalone:!0})}}return i})(),_e=(()=>{class i{constructor(t){this.template=t}static{this.\u0275fac=function(e){return new(e||i)(c(j))}}static{this.\u0275dir=p({type:i,selectors:[["","cdkHeaderCellDef",""]],standalone:!0})}}return i})(),ve=(()=>{class i{constructor(t){this.template=t}static{this.\u0275fac=function(e){return new(e||i)(c(j))}}static{this.\u0275dir=p({type:i,selectors:[["","cdkFooterCellDef",""]],standalone:!0})}}return i})(),vt=(()=>{class i{get name(){return this._name}set name(t){this._setNameInput(t)}get sticky(){return this._sticky}set sticky(t){t!==this._sticky&&(this._sticky=t,this._hasStickyChanged=!0)}get stickyEnd(){return this._stickyEnd}set stickyEnd(t){t!==this._stickyEnd&&(this._stickyEnd=t,this._hasStickyChanged=!0)}constructor(t){this._table=t,this._hasStickyChanged=!1,this._sticky=!1,this._stickyEnd=!1}hasStickyChanged(){let t=this._hasStickyChanged;return this.resetStickyChanged(),t}resetStickyChanged(){this._hasStickyChanged=!1}_updateColumnCssClassName(){this._columnCssClassName=[`cdk-column-${this.cssClassFriendlyName}`]}_setNameInput(t){t&&(this._name=t,this.cssClassFriendlyName=t.replace(/[^a-z0-9_-]/gi,"-"),this._updateColumnCssClassName())}static{this.\u0275fac=function(e){return new(e||i)(c(J,8))}}static{this.\u0275dir=p({type:i,selectors:[["","cdkColumnDef",""]],contentQueries:function(e,o,a){if(e&1&&(at(a,ge,5),at(a,_e,5),at(a,ve,5)),e&2){let r;$(r=Q())&&(o.cell=r.first),$(r=Q())&&(o.headerCell=r.first),$(r=Q())&&(o.footerCell=r.first)}},inputs:{name:[u.None,"cdkColumnDef","name"],sticky:[u.HasDecoratorInputTransform,"sticky","sticky",x],stickyEnd:[u.HasDecoratorInputTransform,"stickyEnd","stickyEnd",x]},standalone:!0,features:[O([{provide:"MAT_SORT_HEADER_COLUMN_DEF",useExisting:i}]),Y]})}}return i})(),Ht=class{constructor(n,t){t.nativeElement.classList.add(...n._columnCssClassName)}},Ri=(()=>{class i extends Ht{constructor(t,e){super(t,e)}static{this.\u0275fac=function(e){return new(e||i)(c(vt),c(v))}}static{this.\u0275dir=p({type:i,selectors:[["cdk-header-cell"],["th","cdk-header-cell",""]],hostAttrs:["role","columnheader",1,"cdk-header-cell"],standalone:!0,features:[b]})}}return i})(),Si=(()=>{class i extends Ht{constructor(t,e){super(t,e);let o=t._table?._getCellRole();o&&e.nativeElement.setAttribute("role",o)}static{this.\u0275fac=function(e){return new(e||i)(c(vt),c(v))}}static{this.\u0275dir=p({type:i,selectors:[["cdk-footer-cell"],["td","cdk-footer-cell",""]],hostAttrs:[1,"cdk-footer-cell"],standalone:!0,features:[b]})}}return i})(),Ei=(()=>{class i extends Ht{constructor(t,e){super(t,e);let o=t._table?._getCellRole();o&&e.nativeElement.setAttribute("role",o)}static{this.\u0275fac=function(e){return new(e||i)(c(vt),c(v))}}static{this.\u0275dir=p({type:i,selectors:[["cdk-cell"],["td","cdk-cell",""]],hostAttrs:[1,"cdk-cell"],standalone:!0,features:[b]})}}return i})(),fe=class{constructor(){this.tasks=[],this.endTasks=[]}},pe=new S("_COALESCED_STYLE_SCHEDULER"),je=(()=>{class i{constructor(t){this._ngZone=t,this._currentSchedule=null,this._destroyed=new g}schedule(t){this._createScheduleIfNeeded(),this._currentSchedule.tasks.push(t)}scheduleEnd(t){this._createScheduleIfNeeded(),this._currentSchedule.endTasks.push(t)}ngOnDestroy(){this._destroyed.next(),this._destroyed.complete()}_createScheduleIfNeeded(){this._currentSchedule||(this._currentSchedule=new fe,this._getScheduleObservable().pipe(tt(this._destroyed)).subscribe(()=>{for(;this._currentSchedule.tasks.length||this._currentSchedule.endTasks.length;){let t=this._currentSchedule;this._currentSchedule=new fe;for(let e of t.tasks)e();for(let e of t.endTasks)e()}this._currentSchedule=null}))}_getScheduleObservable(){return this._ngZone.isStable?si(Promise.resolve(void 0)):this._ngZone.onStable.pipe(ot(1))}static{this.\u0275fac=function(e){return new(e||i)(h(w))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac})}}return i})();var Ne=(()=>{class i{constructor(t,e){this.template=t,this._differs=e}ngOnChanges(t){if(!this._columnsDiffer){let e=t.columns&&t.columns.currentValue||[];this._columnsDiffer=this._differs.find(e).create(),this._columnsDiffer.diff(e)}}getColumnsDiff(){return this._columnsDiffer.diff(this.columns)}extractCellTemplate(t){return this instanceof Yt?t.headerCell.template:this instanceof Wt?t.footerCell.template:t.cell.template}static{this.\u0275fac=function(e){return new(e||i)(c(j),c(_t))}}static{this.\u0275dir=p({type:i,features:[nt]})}}return i})(),Yt=(()=>{class i extends Ne{get sticky(){return this._sticky}set sticky(t){t!==this._sticky&&(this._sticky=t,this._hasStickyChanged=!0)}constructor(t,e,o){super(t,e),this._table=o,this._hasStickyChanged=!1,this._sticky=!1}ngOnChanges(t){super.ngOnChanges(t)}hasStickyChanged(){let t=this._hasStickyChanged;return this.resetStickyChanged(),t}resetStickyChanged(){this._hasStickyChanged=!1}static{this.\u0275fac=function(e){return new(e||i)(c(j),c(_t),c(J,8))}}static{this.\u0275dir=p({type:i,selectors:[["","cdkHeaderRowDef",""]],inputs:{columns:[u.None,"cdkHeaderRowDef","columns"],sticky:[u.HasDecoratorInputTransform,"cdkHeaderRowDefSticky","sticky",x]},standalone:!0,features:[Y,b,nt]})}}return i})(),Wt=(()=>{class i extends Ne{get sticky(){return this._sticky}set sticky(t){t!==this._sticky&&(this._sticky=t,this._hasStickyChanged=!0)}constructor(t,e,o){super(t,e),this._table=o,this._hasStickyChanged=!1,this._sticky=!1}ngOnChanges(t){super.ngOnChanges(t)}hasStickyChanged(){let t=this._hasStickyChanged;return this.resetStickyChanged(),t}resetStickyChanged(){this._hasStickyChanged=!1}static{this.\u0275fac=function(e){return new(e||i)(c(j),c(_t),c(J,8))}}static{this.\u0275dir=p({type:i,selectors:[["","cdkFooterRowDef",""]],inputs:{columns:[u.None,"cdkFooterRowDef","columns"],sticky:[u.HasDecoratorInputTransform,"cdkFooterRowDefSticky","sticky",x]},standalone:!0,features:[Y,b,nt]})}}return i})(),ye=(()=>{class i extends Ne{constructor(t,e,o){super(t,e),this._table=o}static{this.\u0275fac=function(e){return new(e||i)(c(j),c(_t),c(J,8))}}static{this.\u0275dir=p({type:i,selectors:[["","cdkRowDef",""]],inputs:{columns:[u.None,"cdkRowDefColumns","columns"],when:[u.None,"cdkRowDefWhen","when"]},standalone:!0,features:[b]})}}return i})(),st=(()=>{class i{static{this.mostRecentCellOutlet=null}constructor(t){this._viewContainer=t,i.mostRecentCellOutlet=this}ngOnDestroy(){i.mostRecentCellOutlet===this&&(i.mostRecentCellOutlet=null)}static{this.\u0275fac=function(e){return new(e||i)(c(Z))}}static{this.\u0275dir=p({type:i,selectors:[["","cdkCellOutlet",""]],standalone:!0})}}return i})(),Le=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275cmp=I({type:i,selectors:[["cdk-header-row"],["tr","cdk-header-row",""]],hostAttrs:["role","row",1,"cdk-header-row"],standalone:!0,features:[F],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(e,o){e&1&&N(0,0)},dependencies:[st],encapsulation:2})}}return i})(),Be=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275cmp=I({type:i,selectors:[["cdk-footer-row"],["tr","cdk-footer-row",""]],hostAttrs:["role","row",1,"cdk-footer-row"],standalone:!0,features:[F],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(e,o){e&1&&N(0,0)},dependencies:[st],encapsulation:2})}}return i})(),ze=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275cmp=I({type:i,selectors:[["cdk-row"],["tr","cdk-row",""]],hostAttrs:["role","row",1,"cdk-row"],standalone:!0,features:[F],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(e,o){e&1&&N(0,0)},dependencies:[st],encapsulation:2})}}return i})(),we=(()=>{class i{constructor(t){this.templateRef=t,this._contentClassName="cdk-no-data-row"}static{this.\u0275fac=function(e){return new(e||i)(c(j))}}static{this.\u0275dir=p({type:i,selectors:[["ng-template","cdkNoDataRow",""]],standalone:!0})}}return i})(),Di=["top","bottom","left","right"],Pe=class{constructor(n,t,e,o,a=!0,r=!0,s){this._isNativeHtmlTable=n,this._stickCellCss=t,this.direction=e,this._coalescedStyleScheduler=o,this._isBrowser=a,this._needsPositionStickyOnElement=r,this._positionListener=s,this._cachedCellWidths=[],this._borderCellCss={top:`${t}-border-elem-top`,bottom:`${t}-border-elem-bottom`,left:`${t}-border-elem-left`,right:`${t}-border-elem-right`}}clearStickyPositioning(n,t){let e=[];for(let o of n)if(o.nodeType===o.ELEMENT_NODE){e.push(o);for(let a=0;a<o.children.length;a++)e.push(o.children[a])}this._coalescedStyleScheduler.schedule(()=>{for(let o of e)this._removeStickyStyle(o,t)})}updateStickyColumns(n,t,e,o=!0){if(!n.length||!this._isBrowser||!(t.some(a=>a)||e.some(a=>a))){this._positionListener&&(this._positionListener.stickyColumnsUpdated({sizes:[]}),this._positionListener.stickyEndColumnsUpdated({sizes:[]}));return}this._coalescedStyleScheduler.schedule(()=>{let a=n[0],r=a.children.length,s=this._getCellWidths(a,o),d=this._getStickyStartColumnPositions(s,t),m=this._getStickyEndColumnPositions(s,e),l=t.lastIndexOf(!0),f=e.indexOf(!0),_=this.direction==="rtl",y=_?"right":"left",L=_?"left":"right";for(let A of n)for(let P=0;P<r;P++){let ai=A.children[P];t[P]&&this._addStickyStyle(ai,y,d[P],P===l),e[P]&&this._addStickyStyle(ai,L,m[P],P===f)}this._positionListener&&(this._positionListener.stickyColumnsUpdated({sizes:l===-1?[]:s.slice(0,l+1).map((A,P)=>t[P]?A:null)}),this._positionListener.stickyEndColumnsUpdated({sizes:f===-1?[]:s.slice(f).map((A,P)=>e[P+f]?A:null).reverse()}))})}stickRows(n,t,e){this._isBrowser&&this._coalescedStyleScheduler.schedule(()=>{let o=e==="bottom"?n.slice().reverse():n,a=e==="bottom"?t.slice().reverse():t,r=[],s=[],d=[];for(let l=0,f=0;l<o.length;l++){if(!a[l])continue;r[l]=f;let _=o[l];d[l]=this._isNativeHtmlTable?Array.from(_.children):[_];let y=_.getBoundingClientRect().height;f+=y,s[l]=y}let m=a.lastIndexOf(!0);for(let l=0;l<o.length;l++){if(!a[l])continue;let f=r[l],_=l===m;for(let y of d[l])this._addStickyStyle(y,e,f,_)}e==="top"?this._positionListener?.stickyHeaderRowsUpdated({sizes:s,offsets:r,elements:d}):this._positionListener?.stickyFooterRowsUpdated({sizes:s,offsets:r,elements:d})})}updateStickyFooterContainer(n,t){this._isNativeHtmlTable&&this._coalescedStyleScheduler.schedule(()=>{let e=n.querySelector("tfoot");e&&(t.some(o=>!o)?this._removeStickyStyle(e,["bottom"]):this._addStickyStyle(e,"bottom",0,!1))})}_removeStickyStyle(n,t){for(let o of t)n.style[o]="",n.classList.remove(this._borderCellCss[o]);Di.some(o=>t.indexOf(o)===-1&&n.style[o])?n.style.zIndex=this._getCalculatedZIndex(n):(n.style.zIndex="",this._needsPositionStickyOnElement&&(n.style.position=""),n.classList.remove(this._stickCellCss))}_addStickyStyle(n,t,e,o){n.classList.add(this._stickCellCss),o&&n.classList.add(this._borderCellCss[t]),n.style[t]=`${e}px`,n.style.zIndex=this._getCalculatedZIndex(n),this._needsPositionStickyOnElement&&(n.style.cssText+="position: -webkit-sticky; position: sticky; ")}_getCalculatedZIndex(n){let t={top:100,bottom:10,left:1,right:1},e=0;for(let o of Di)n.style[o]&&(e+=t[o]);return e?`${e}`:""}_getCellWidths(n,t=!0){if(!t&&this._cachedCellWidths.length)return this._cachedCellWidths;let e=[],o=n.children;for(let a=0;a<o.length;a++){let r=o[a];e.push(r.getBoundingClientRect().width)}return this._cachedCellWidths=e,e}_getStickyStartColumnPositions(n,t){let e=[],o=0;for(let a=0;a<n.length;a++)t[a]&&(e[a]=o,o+=n[a]);return e}_getStickyEndColumnPositions(n,t){let e=[],o=0;for(let a=n.length;a>0;a--)t[a]&&(e[a]=o,o+=n[a]);return e}};var be=new S("CDK_SPL");var Ve=(()=>{class i{constructor(t,e){this.viewContainer=t,this.elementRef=e;let o=E(J);o._rowOutlet=this,o._outletAssigned()}static{this.\u0275fac=function(e){return new(e||i)(c(Z),c(v))}}static{this.\u0275dir=p({type:i,selectors:[["","rowOutlet",""]],standalone:!0})}}return i})(),He=(()=>{class i{constructor(t,e){this.viewContainer=t,this.elementRef=e;let o=E(J);o._headerRowOutlet=this,o._outletAssigned()}static{this.\u0275fac=function(e){return new(e||i)(c(Z),c(v))}}static{this.\u0275dir=p({type:i,selectors:[["","headerRowOutlet",""]],standalone:!0})}}return i})(),Ye=(()=>{class i{constructor(t,e){this.viewContainer=t,this.elementRef=e;let o=E(J);o._footerRowOutlet=this,o._outletAssigned()}static{this.\u0275fac=function(e){return new(e||i)(c(Z),c(v))}}static{this.\u0275dir=p({type:i,selectors:[["","footerRowOutlet",""]],standalone:!0})}}return i})(),We=(()=>{class i{constructor(t,e){this.viewContainer=t,this.elementRef=e;let o=E(J);o._noDataRowOutlet=this,o._outletAssigned()}static{this.\u0275fac=function(e){return new(e||i)(c(Z),c(v))}}static{this.\u0275dir=p({type:i,selectors:[["","noDataRowOutlet",""]],standalone:!0})}}return i})();var Xe=(()=>{class i{_getCellRole(){if(this._cellRoleInternal===void 0){let t=this._elementRef.nativeElement.getAttribute("role"),e=t==="grid"||t==="treegrid"?"gridcell":"cell";this._cellRoleInternal=this._isNativeHtmlTable&&e==="cell"?null:e}return this._cellRoleInternal}get trackBy(){return this._trackByFn}set trackBy(t){this._trackByFn=t}get dataSource(){return this._dataSource}set dataSource(t){this._dataSource!==t&&this._switchDataSource(t)}get multiTemplateDataRows(){return this._multiTemplateDataRows}set multiTemplateDataRows(t){this._multiTemplateDataRows=t,this._rowOutlet&&this._rowOutlet.viewContainer.length&&(this._forceRenderDataRows(),this.updateStickyColumnStyles())}get fixedLayout(){return this._fixedLayout}set fixedLayout(t){this._fixedLayout=t,this._forceRecalculateCellWidths=!0,this._stickyColumnStylesNeedReset=!0}constructor(t,e,o,a,r,s,d,m,l,f,_,y){this._differs=t,this._changeDetectorRef=e,this._elementRef=o,this._dir=r,this._platform=d,this._viewRepeater=m,this._coalescedStyleScheduler=l,this._viewportRuler=f,this._stickyPositioningListener=_,this._ngZone=y,this._onDestroy=new g,this._columnDefsByName=new Map,this._customColumnDefs=new Set,this._customRowDefs=new Set,this._customHeaderRowDefs=new Set,this._customFooterRowDefs=new Set,this._headerRowDefChanged=!0,this._footerRowDefChanged=!0,this._stickyColumnStylesNeedReset=!0,this._forceRecalculateCellWidths=!0,this._cachedRenderRowsMap=new Map,this.stickyCssClass="cdk-table-sticky",this.needsPositionStickyOnElement=!0,this._isShowingNoDataRow=!1,this._hasAllOutlets=!1,this._hasInitialized=!1,this._cellRoleInternal=void 0,this._multiTemplateDataRows=!1,this._fixedLayout=!1,this.contentChanged=new H,this.viewChange=new Ct({start:0,end:Number.MAX_VALUE}),a||o.nativeElement.setAttribute("role","table"),this._document=s,this._isServer=!d.isBrowser,this._isNativeHtmlTable=o.nativeElement.nodeName==="TABLE"}ngOnInit(){this._setupStickyStyler(),this._dataDiffer=this._differs.find([]).create((t,e)=>this.trackBy?this.trackBy(e.dataIndex,e.data):e),this._viewportRuler.change().pipe(tt(this._onDestroy)).subscribe(()=>{this._forceRecalculateCellWidths=!0})}ngAfterContentInit(){this._hasInitialized=!0}ngAfterContentChecked(){this._canRender()&&this._render()}ngOnDestroy(){[this._rowOutlet?.viewContainer,this._headerRowOutlet?.viewContainer,this._footerRowOutlet?.viewContainer,this._cachedRenderRowsMap,this._customColumnDefs,this._customRowDefs,this._customHeaderRowDefs,this._customFooterRowDefs,this._columnDefsByName].forEach(t=>{t?.clear()}),this._headerRowDefs=[],this._footerRowDefs=[],this._defaultRowDef=null,this._onDestroy.next(),this._onDestroy.complete(),me(this.dataSource)&&this.dataSource.disconnect(this)}renderRows(){this._renderRows=this._getAllRenderRows();let t=this._dataDiffer.diff(this._renderRows);if(!t){this._updateNoDataRow(),this.contentChanged.next();return}let e=this._rowOutlet.viewContainer;this._viewRepeater.applyChanges(t,e,(o,a,r)=>this._getEmbeddedViewArgs(o.item,r),o=>o.item.data,o=>{o.operation===Ot.INSERTED&&o.context&&this._renderCellTemplateForItem(o.record.item.rowDef,o.context)}),this._updateRowIndexContext(),t.forEachIdentityChange(o=>{let a=e.get(o.currentIndex);a.context.$implicit=o.item.data}),this._updateNoDataRow(),this._ngZone&&w.isInAngularZone()?this._ngZone.onStable.pipe(ot(1),tt(this._onDestroy)).subscribe(()=>{this.updateStickyColumnStyles()}):this.updateStickyColumnStyles(),this.contentChanged.next()}addColumnDef(t){this._customColumnDefs.add(t)}removeColumnDef(t){this._customColumnDefs.delete(t)}addRowDef(t){this._customRowDefs.add(t)}removeRowDef(t){this._customRowDefs.delete(t)}addHeaderRowDef(t){this._customHeaderRowDefs.add(t),this._headerRowDefChanged=!0}removeHeaderRowDef(t){this._customHeaderRowDefs.delete(t),this._headerRowDefChanged=!0}addFooterRowDef(t){this._customFooterRowDefs.add(t),this._footerRowDefChanged=!0}removeFooterRowDef(t){this._customFooterRowDefs.delete(t),this._footerRowDefChanged=!0}setNoDataRow(t){this._customNoDataRow=t}updateStickyHeaderRowStyles(){let t=this._getRenderedRows(this._headerRowOutlet);if(this._isNativeHtmlTable){let o=ki(this._headerRowOutlet,"thead");o&&(o.style.display=t.length?"":"none")}let e=this._headerRowDefs.map(o=>o.sticky);this._stickyStyler.clearStickyPositioning(t,["top"]),this._stickyStyler.stickRows(t,e,"top"),this._headerRowDefs.forEach(o=>o.resetStickyChanged())}updateStickyFooterRowStyles(){let t=this._getRenderedRows(this._footerRowOutlet);if(this._isNativeHtmlTable){let o=ki(this._footerRowOutlet,"tfoot");o&&(o.style.display=t.length?"":"none")}let e=this._footerRowDefs.map(o=>o.sticky);this._stickyStyler.clearStickyPositioning(t,["bottom"]),this._stickyStyler.stickRows(t,e,"bottom"),this._stickyStyler.updateStickyFooterContainer(this._elementRef.nativeElement,e),this._footerRowDefs.forEach(o=>o.resetStickyChanged())}updateStickyColumnStyles(){let t=this._getRenderedRows(this._headerRowOutlet),e=this._getRenderedRows(this._rowOutlet),o=this._getRenderedRows(this._footerRowOutlet);(this._isNativeHtmlTable&&!this._fixedLayout||this._stickyColumnStylesNeedReset)&&(this._stickyStyler.clearStickyPositioning([...t,...e,...o],["left","right"]),this._stickyColumnStylesNeedReset=!1),t.forEach((a,r)=>{this._addStickyColumnStyles([a],this._headerRowDefs[r])}),this._rowDefs.forEach(a=>{let r=[];for(let s=0;s<e.length;s++)this._renderRows[s].rowDef===a&&r.push(e[s]);this._addStickyColumnStyles(r,a)}),o.forEach((a,r)=>{this._addStickyColumnStyles([a],this._footerRowDefs[r])}),Array.from(this._columnDefsByName.values()).forEach(a=>a.resetStickyChanged())}_outletAssigned(){!this._hasAllOutlets&&this._rowOutlet&&this._headerRowOutlet&&this._footerRowOutlet&&this._noDataRowOutlet&&(this._hasAllOutlets=!0,this._canRender()&&this._render())}_canRender(){return this._hasAllOutlets&&this._hasInitialized}_render(){this._cacheRowDefs(),this._cacheColumnDefs(),!this._headerRowDefs.length&&!this._footerRowDefs.length&&this._rowDefs.length;let e=this._renderUpdatedColumns()||this._headerRowDefChanged||this._footerRowDefChanged;this._stickyColumnStylesNeedReset=this._stickyColumnStylesNeedReset||e,this._forceRecalculateCellWidths=e,this._headerRowDefChanged&&(this._forceRenderHeaderRows(),this._headerRowDefChanged=!1),this._footerRowDefChanged&&(this._forceRenderFooterRows(),this._footerRowDefChanged=!1),this.dataSource&&this._rowDefs.length>0&&!this._renderChangeSubscription?this._observeRenderChanges():this._stickyColumnStylesNeedReset&&this.updateStickyColumnStyles(),this._checkStickyStates()}_getAllRenderRows(){let t=[],e=this._cachedRenderRowsMap;this._cachedRenderRowsMap=new Map;for(let o=0;o<this._data.length;o++){let a=this._data[o],r=this._getRenderRowsForData(a,o,e.get(a));this._cachedRenderRowsMap.has(a)||this._cachedRenderRowsMap.set(a,new WeakMap);for(let s=0;s<r.length;s++){let d=r[s],m=this._cachedRenderRowsMap.get(d.data);m.has(d.rowDef)?m.get(d.rowDef).push(d):m.set(d.rowDef,[d]),t.push(d)}}return t}_getRenderRowsForData(t,e,o){return this._getRowDefs(t,e).map(r=>{let s=o&&o.has(r)?o.get(r):[];if(s.length){let d=s.shift();return d.dataIndex=e,d}else return{data:t,rowDef:r,dataIndex:e}})}_cacheColumnDefs(){this._columnDefsByName.clear(),he(this._getOwnDefs(this._contentColumnDefs),this._customColumnDefs).forEach(e=>{this._columnDefsByName.has(e.name),this._columnDefsByName.set(e.name,e)})}_cacheRowDefs(){this._headerRowDefs=he(this._getOwnDefs(this._contentHeaderRowDefs),this._customHeaderRowDefs),this._footerRowDefs=he(this._getOwnDefs(this._contentFooterRowDefs),this._customFooterRowDefs),this._rowDefs=he(this._getOwnDefs(this._contentRowDefs),this._customRowDefs);let t=this._rowDefs.filter(e=>!e.when);!this.multiTemplateDataRows&&t.length>1,this._defaultRowDef=t[0]}_renderUpdatedColumns(){let t=(r,s)=>r||!!s.getColumnsDiff(),e=this._rowDefs.reduce(t,!1);e&&this._forceRenderDataRows();let o=this._headerRowDefs.reduce(t,!1);o&&this._forceRenderHeaderRows();let a=this._footerRowDefs.reduce(t,!1);return a&&this._forceRenderFooterRows(),e||o||a}_switchDataSource(t){this._data=[],me(this.dataSource)&&this.dataSource.disconnect(this),this._renderChangeSubscription&&(this._renderChangeSubscription.unsubscribe(),this._renderChangeSubscription=null),t||(this._dataDiffer&&this._dataDiffer.diff([]),this._rowOutlet&&this._rowOutlet.viewContainer.clear()),this._dataSource=t}_observeRenderChanges(){if(!this.dataSource)return;let t;me(this.dataSource)?t=this.dataSource.connect(this):Kt(this.dataSource)?t=this.dataSource:Array.isArray(this.dataSource)&&(t=q(this.dataSource)),this._renderChangeSubscription=t.pipe(tt(this._onDestroy)).subscribe(e=>{this._data=e||[],this.renderRows()})}_forceRenderHeaderRows(){this._headerRowOutlet.viewContainer.length>0&&this._headerRowOutlet.viewContainer.clear(),this._headerRowDefs.forEach((t,e)=>this._renderRow(this._headerRowOutlet,t,e)),this.updateStickyHeaderRowStyles()}_forceRenderFooterRows(){this._footerRowOutlet.viewContainer.length>0&&this._footerRowOutlet.viewContainer.clear(),this._footerRowDefs.forEach((t,e)=>this._renderRow(this._footerRowOutlet,t,e)),this.updateStickyFooterRowStyles()}_addStickyColumnStyles(t,e){let o=Array.from(e.columns||[]).map(s=>{let d=this._columnDefsByName.get(s);return d}),a=o.map(s=>s.sticky),r=o.map(s=>s.stickyEnd);this._stickyStyler.updateStickyColumns(t,a,r,!this._fixedLayout||this._forceRecalculateCellWidths)}_getRenderedRows(t){let e=[];for(let o=0;o<t.viewContainer.length;o++){let a=t.viewContainer.get(o);e.push(a.rootNodes[0])}return e}_getRowDefs(t,e){if(this._rowDefs.length==1)return[this._rowDefs[0]];let o=[];if(this.multiTemplateDataRows)o=this._rowDefs.filter(a=>!a.when||a.when(e,t));else{let a=this._rowDefs.find(r=>r.when&&r.when(e,t))||this._defaultRowDef;a&&o.push(a)}return o.length,o}_getEmbeddedViewArgs(t,e){let o=t.rowDef,a={$implicit:t.data};return{templateRef:o.template,context:a,index:e}}_renderRow(t,e,o,a={}){let r=t.viewContainer.createEmbeddedView(e.template,a,o);return this._renderCellTemplateForItem(e,a),r}_renderCellTemplateForItem(t,e){for(let o of this._getCellTemplates(t))st.mostRecentCellOutlet&&st.mostRecentCellOutlet._viewContainer.createEmbeddedView(o,e);this._changeDetectorRef.markForCheck()}_updateRowIndexContext(){let t=this._rowOutlet.viewContainer;for(let e=0,o=t.length;e<o;e++){let r=t.get(e).context;r.count=o,r.first=e===0,r.last=e===o-1,r.even=e%2===0,r.odd=!r.even,this.multiTemplateDataRows?(r.dataIndex=this._renderRows[e].dataIndex,r.renderIndex=e):r.index=this._renderRows[e].dataIndex}}_getCellTemplates(t){return!t||!t.columns?[]:Array.from(t.columns,e=>{let o=this._columnDefsByName.get(e);return t.extractCellTemplate(o)})}_forceRenderDataRows(){this._dataDiffer.diff([]),this._rowOutlet.viewContainer.clear(),this.renderRows()}_checkStickyStates(){let t=(e,o)=>e||o.hasStickyChanged();this._headerRowDefs.reduce(t,!1)&&this.updateStickyHeaderRowStyles(),this._footerRowDefs.reduce(t,!1)&&this.updateStickyFooterRowStyles(),Array.from(this._columnDefsByName.values()).reduce(t,!1)&&(this._stickyColumnStylesNeedReset=!0,this.updateStickyColumnStyles())}_setupStickyStyler(){let t=this._dir?this._dir.value:"ltr";this._stickyStyler=new Pe(this._isNativeHtmlTable,this.stickyCssClass,t,this._coalescedStyleScheduler,this._platform.isBrowser,this.needsPositionStickyOnElement,this._stickyPositioningListener),(this._dir?this._dir.change:q()).pipe(tt(this._onDestroy)).subscribe(e=>{this._stickyStyler.direction=e,this.updateStickyColumnStyles()})}_getOwnDefs(t){return t.filter(e=>!e._table||e._table===this)}_updateNoDataRow(){let t=this._customNoDataRow||this._noDataRow;if(!t)return;let e=this._rowOutlet.viewContainer.length===0;if(e===this._isShowingNoDataRow)return;let o=this._noDataRowOutlet.viewContainer;if(e){let a=o.createEmbeddedView(t.templateRef),r=a.rootNodes[0];a.rootNodes.length===1&&r?.nodeType===this._document.ELEMENT_NODE&&(r.setAttribute("role","row"),r.classList.add(t._contentClassName))}else o.clear();this._isShowingNoDataRow=e,this._changeDetectorRef.markForCheck()}static{this.\u0275fac=function(e){return new(e||i)(c(_t),c(Lt),c(v),di("role"),c(et,8),c(R),c(B),c(zt),c(pe),c(Mt),c(be,12),c(w,8))}}static{this.\u0275cmp=I({type:i,selectors:[["cdk-table"],["table","cdk-table",""]],contentQueries:function(e,o,a){if(e&1&&(at(a,we,5),at(a,vt,5),at(a,ye,5),at(a,Yt,5),at(a,Wt,5)),e&2){let r;$(r=Q())&&(o._noDataRow=r.first),$(r=Q())&&(o._contentColumnDefs=r),$(r=Q())&&(o._contentRowDefs=r),$(r=Q())&&(o._contentHeaderRowDefs=r),$(r=Q())&&(o._contentFooterRowDefs=r)}},hostAttrs:[1,"cdk-table"],hostVars:2,hostBindings:function(e,o){e&2&&U("cdk-table-fixed-layout",o.fixedLayout)},inputs:{trackBy:"trackBy",dataSource:"dataSource",multiTemplateDataRows:[u.HasDecoratorInputTransform,"multiTemplateDataRows","multiTemplateDataRows",x],fixedLayout:[u.HasDecoratorInputTransform,"fixedLayout","fixedLayout",x]},outputs:{contentChanged:"contentChanged"},exportAs:["cdkTable"],standalone:!0,features:[O([{provide:J,useExisting:i},{provide:zt,useClass:Ft},{provide:pe,useClass:je},{provide:be,useValue:null}]),Y,F],ngContentSelectors:ho,decls:5,vars:2,consts:[["role","rowgroup"],["headerRowOutlet",""],["rowOutlet",""],["noDataRowOutlet",""],["footerRowOutlet",""]],template:function(e,o){e&1&&(ht(uo),z(0),z(1,1),mt(2,fo,1,0)(3,po,7,0)(4,bo,4,0)),e&2&&(gt(2),Dt(2,o._isServer?2:-1),gt(),Dt(3,o._isNativeHtmlTable?3:4))},dependencies:[He,Ve,We,Ye],styles:[".cdk-table-fixed-layout{table-layout:fixed}"],encapsulation:2})}}return i})();function he(i,n){return i.concat(Array.from(n))}function ki(i,n){let t=n.toUpperCase(),e=i.viewContainer.element.nativeElement;for(;e;){let o=e.nodeType===1?e.nodeName:null;if(o===t)return e;if(o==="TABLE")break;e=e.parentNode}return null}var Ii=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=T({type:i})}static{this.\u0275inj=M({imports:[Vt]})}}return i})();var go=[[["caption"]],[["colgroup"],["col"]],"*"],_o=["caption","colgroup, col","*"];function vo(i,n){i&1&&z(0,2)}function yo(i,n){i&1&&(W(0,"thead",0),N(1,1),X(),W(2,"tbody",2),N(3,3)(4,4),X(),W(5,"tfoot",0),N(6,5),X())}function wo(i,n){i&1&&N(0,1)(1,3)(2,4)(3,5)}var ha=(()=>{class i extends Xe{constructor(){super(...arguments),this.stickyCssClass="mat-mdc-table-sticky",this.needsPositionStickyOnElement=!1}static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275cmp=I({type:i,selectors:[["mat-table"],["table","mat-table",""]],hostAttrs:[1,"mat-mdc-table","mdc-data-table__table"],hostVars:2,hostBindings:function(e,o){e&2&&U("mdc-table-fixed-layout",o.fixedLayout)},exportAs:["matTable"],standalone:!0,features:[O([{provide:Xe,useExisting:i},{provide:J,useExisting:i},{provide:pe,useClass:je},{provide:zt,useClass:Ft},{provide:be,useValue:null}]),b,F],ngContentSelectors:_o,decls:5,vars:2,consts:[["role","rowgroup"],["headerRowOutlet",""],["role","rowgroup",1,"mdc-data-table__content"],["rowOutlet",""],["noDataRowOutlet",""],["footerRowOutlet",""]],template:function(e,o){e&1&&(ht(go),z(0),z(1,1),mt(2,vo,1,0)(3,yo,7,0)(4,wo,4,0)),e&2&&(gt(2),Dt(2,o._isServer?2:-1),gt(),Dt(3,o._isNativeHtmlTable?3:4))},dependencies:[He,Ve,We,Ye],styles:[".mat-mdc-table-sticky{position:sticky !important}.mdc-data-table{-webkit-overflow-scrolling:touch;display:inline-flex;flex-direction:column;box-sizing:border-box;position:relative}.mdc-data-table__table-container{-webkit-overflow-scrolling:touch;overflow-x:auto;width:100%}.mdc-data-table__table{min-width:100%;border:0;white-space:nowrap;border-spacing:0;table-layout:fixed}.mdc-data-table__cell{box-sizing:border-box;overflow:hidden;text-align:left;text-overflow:ellipsis}[dir=rtl] .mdc-data-table__cell,.mdc-data-table__cell[dir=rtl]{text-align:right}.mdc-data-table__cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__cell--numeric,.mdc-data-table__cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell{box-sizing:border-box;text-overflow:ellipsis;overflow:hidden;outline:none;text-align:left}[dir=rtl] .mdc-data-table__header-cell,.mdc-data-table__header-cell[dir=rtl]{text-align:right}.mdc-data-table__header-cell--numeric{text-align:right}[dir=rtl] .mdc-data-table__header-cell--numeric,.mdc-data-table__header-cell--numeric[dir=rtl]{text-align:left}.mdc-data-table__header-cell-wrapper{align-items:center;display:inline-flex;vertical-align:middle}.mdc-data-table__cell,.mdc-data-table__header-cell{padding:0 16px 0 16px}.mdc-data-table__header-cell--checkbox,.mdc-data-table__cell--checkbox{padding-left:4px;padding-right:0}[dir=rtl] .mdc-data-table__header-cell--checkbox,[dir=rtl] .mdc-data-table__cell--checkbox,.mdc-data-table__header-cell--checkbox[dir=rtl],.mdc-data-table__cell--checkbox[dir=rtl]{padding-left:0;padding-right:4px}mat-table{display:block}mat-header-row{min-height:56px}mat-row,mat-footer-row{min-height:48px}mat-row,mat-header-row,mat-footer-row{display:flex;border-width:0;border-bottom-width:1px;border-style:solid;align-items:center;box-sizing:border-box}mat-cell:first-of-type,mat-header-cell:first-of-type,mat-footer-cell:first-of-type{padding-left:24px}[dir=rtl] mat-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:first-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:first-of-type:not(:only-of-type){padding-left:0;padding-right:24px}mat-cell:last-of-type,mat-header-cell:last-of-type,mat-footer-cell:last-of-type{padding-right:24px}[dir=rtl] mat-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-header-cell:last-of-type:not(:only-of-type),[dir=rtl] mat-footer-cell:last-of-type:not(:only-of-type){padding-right:0;padding-left:24px}mat-cell,mat-header-cell,mat-footer-cell{flex:1;display:flex;align-items:center;overflow:hidden;word-wrap:break-word;min-height:inherit}.mat-mdc-table{table-layout:auto;white-space:normal;background-color:var(--mat-table-background-color)}.mat-mdc-header-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-header-container-height, 56px);color:var(--mat-table-header-headline-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-header-headline-font, Roboto, sans-serif);line-height:var(--mat-table-header-headline-line-height);font-size:var(--mat-table-header-headline-size, 14px);font-weight:var(--mat-table-header-headline-weight, 500)}.mat-mdc-row{height:var(--mat-table-row-item-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87))}.mat-mdc-row,.mdc-data-table__content{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-table-row-item-label-text-font, Roboto, sans-serif);line-height:var(--mat-table-row-item-label-text-line-height);font-size:var(--mat-table-row-item-label-text-size, 14px);font-weight:var(--mat-table-row-item-label-text-weight)}.mat-mdc-footer-row{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;height:var(--mat-table-footer-container-height, 52px);color:var(--mat-table-row-item-label-text-color, rgba(0, 0, 0, 0.87));font-family:var(--mat-table-footer-supporting-text-font, Roboto, sans-serif);line-height:var(--mat-table-footer-supporting-text-line-height);font-size:var(--mat-table-footer-supporting-text-size, 14px);font-weight:var(--mat-table-footer-supporting-text-weight);letter-spacing:var(--mat-table-footer-supporting-text-tracking)}.mat-mdc-header-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-header-headline-tracking);font-weight:inherit;line-height:inherit}.mat-mdc-cell{border-bottom-color:var(--mat-table-row-item-outline-color, rgba(0, 0, 0, 0.12));border-bottom-width:var(--mat-table-row-item-outline-width, 1px);border-bottom-style:solid;letter-spacing:var(--mat-table-row-item-label-text-tracking);line-height:inherit}.mdc-data-table__row:last-child .mat-mdc-cell{border-bottom:none}.mat-mdc-footer-cell{letter-spacing:var(--mat-table-row-item-label-text-tracking)}mat-row.mat-mdc-row,mat-header-row.mat-mdc-header-row,mat-footer-row.mat-mdc-footer-row{border-bottom:none}.mat-mdc-table tbody,.mat-mdc-table tfoot,.mat-mdc-table thead,.mat-mdc-cell,.mat-mdc-footer-cell,.mat-mdc-header-row,.mat-mdc-row,.mat-mdc-footer-row,.mat-mdc-table .mat-mdc-header-cell{background:inherit}.mat-mdc-table mat-header-row.mat-mdc-header-row,.mat-mdc-table mat-row.mat-mdc-row,.mat-mdc-table mat-footer-row.mat-mdc-footer-cell{height:unset}mat-header-cell.mat-mdc-header-cell,mat-cell.mat-mdc-cell,mat-footer-cell.mat-mdc-footer-cell{align-self:stretch}"],encapsulation:2})}}return i})(),fa=(()=>{class i extends ge{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["","matCellDef",""]],standalone:!0,features:[O([{provide:ge,useExisting:i}]),b]})}}return i})(),pa=(()=>{class i extends _e{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["","matHeaderCellDef",""]],standalone:!0,features:[O([{provide:_e,useExisting:i}]),b]})}}return i})(),ba=(()=>{class i extends ve{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["","matFooterCellDef",""]],standalone:!0,features:[O([{provide:ve,useExisting:i}]),b]})}}return i})(),ga=(()=>{class i extends vt{get name(){return this._name}set name(t){this._setNameInput(t)}_updateColumnCssClassName(){super._updateColumnCssClassName(),this._columnCssClassName.push(`mat-column-${this.cssClassFriendlyName}`)}static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["","matColumnDef",""]],inputs:{name:[u.None,"matColumnDef","name"]},standalone:!0,features:[O([{provide:vt,useExisting:i},{provide:"MAT_SORT_HEADER_COLUMN_DEF",useExisting:i}]),b]})}}return i})(),_a=(()=>{class i extends Ri{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["mat-header-cell"],["th","mat-header-cell",""]],hostAttrs:["role","columnheader",1,"mat-mdc-header-cell","mdc-data-table__header-cell"],standalone:!0,features:[b]})}}return i})(),va=(()=>{class i extends Si{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["mat-footer-cell"],["td","mat-footer-cell",""]],hostAttrs:[1,"mat-mdc-footer-cell","mdc-data-table__cell"],standalone:!0,features:[b]})}}return i})(),ya=(()=>{class i extends Ei{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["mat-cell"],["td","mat-cell",""]],hostAttrs:[1,"mat-mdc-cell","mdc-data-table__cell"],standalone:!0,features:[b]})}}return i})();var wa=(()=>{class i extends Yt{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["","matHeaderRowDef",""]],inputs:{columns:[u.None,"matHeaderRowDef","columns"],sticky:[u.HasDecoratorInputTransform,"matHeaderRowDefSticky","sticky",x]},standalone:!0,features:[O([{provide:Yt,useExisting:i}]),Y,b]})}}return i})(),xa=(()=>{class i extends Wt{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["","matFooterRowDef",""]],inputs:{columns:[u.None,"matFooterRowDef","columns"],sticky:[u.HasDecoratorInputTransform,"matFooterRowDefSticky","sticky",x]},standalone:!0,features:[O([{provide:Wt,useExisting:i}]),Y,b]})}}return i})(),Ca=(()=>{class i extends ye{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["","matRowDef",""]],inputs:{columns:[u.None,"matRowDefColumns","columns"],when:[u.None,"matRowDefWhen","when"]},standalone:!0,features:[O([{provide:ye,useExisting:i}]),b]})}}return i})(),Da=(()=>{class i extends Le{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275cmp=I({type:i,selectors:[["mat-header-row"],["tr","mat-header-row",""]],hostAttrs:["role","row",1,"mat-mdc-header-row","mdc-data-table__header-row"],exportAs:["matHeaderRow"],standalone:!0,features:[O([{provide:Le,useExisting:i}]),b,F],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(e,o){e&1&&N(0,0)},dependencies:[st],encapsulation:2})}}return i})(),ka=(()=>{class i extends Be{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275cmp=I({type:i,selectors:[["mat-footer-row"],["tr","mat-footer-row",""]],hostAttrs:["role","row",1,"mat-mdc-footer-row","mdc-data-table__row"],exportAs:["matFooterRow"],standalone:!0,features:[O([{provide:Be,useExisting:i}]),b,F],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(e,o){e&1&&N(0,0)},dependencies:[st],encapsulation:2})}}return i})(),Ra=(()=>{class i extends ze{static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275cmp=I({type:i,selectors:[["mat-row"],["tr","mat-row",""]],hostAttrs:["role","row",1,"mat-mdc-row","mdc-data-table__row"],exportAs:["matRow"],standalone:!0,features:[O([{provide:ze,useExisting:i}]),b,F],decls:1,vars:0,consts:[["cdkCellOutlet",""]],template:function(e,o){e&1&&N(0,0)},dependencies:[st],encapsulation:2})}}return i})(),Sa=(()=>{class i extends we{constructor(){super(...arguments),this._contentClassName="mat-mdc-no-data-row"}static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["ng-template","matNoDataRow",""]],standalone:!0,features:[O([{provide:we,useExisting:i}]),b]})}}return i})();var Ea=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=T({type:i})}static{this.\u0275inj=M({imports:[rt,Ii,rt]})}}return i})(),xo=9007199254740991,Oi=class extends le{get data(){return this._data.value}set data(n){n=Array.isArray(n)?n:[],this._data.next(n),this._renderChangesSubscription||this._filterData(n)}get filter(){return this._filter.value}set filter(n){this._filter.next(n),this._renderChangesSubscription||this._filterData(this.data)}get sort(){return this._sort}set sort(n){this._sort=n,this._updateChangeSubscription()}get paginator(){return this._paginator}set paginator(n){this._paginator=n,this._updateChangeSubscription()}constructor(n=[]){super(),this._renderData=new Ct([]),this._filter=new Ct(""),this._internalPageChanges=new g,this._renderChangesSubscription=null,this.sortingDataAccessor=(t,e)=>{let o=t[e];if(hi(o)){let a=Number(o);return a<xo?a:o}return o},this.sortData=(t,e)=>{let o=e.active,a=e.direction;return!o||a==""?t:t.sort((r,s)=>{let d=this.sortingDataAccessor(r,o),m=this.sortingDataAccessor(s,o),l=typeof d,f=typeof m;l!==f&&(l==="number"&&(d+=""),f==="number"&&(m+=""));let _=0;return d!=null&&m!=null?d>m?_=1:d<m&&(_=-1):d!=null?_=1:m!=null&&(_=-1),_*(a=="asc"?1:-1)})},this.filterPredicate=(t,e)=>{let o=Object.keys(t).reduce((r,s)=>r+t[s]+"\u25EC","").toLowerCase(),a=e.trim().toLowerCase();return o.indexOf(a)!=-1},this._data=new Ct(n),this._updateChangeSubscription()}_updateChangeSubscription(){let n=this._sort?bt(this._sort.sortChange,this._sort.initialized):q(null),t=this._paginator?bt(this._paginator.page,this._internalPageChanges,this._paginator.initialized):q(null),e=this._data,o=te([e,this._filter]).pipe(Jt(([s])=>this._filterData(s))),a=te([o,n]).pipe(Jt(([s])=>this._orderData(s))),r=te([a,t]).pipe(Jt(([s])=>this._pageData(s)));this._renderChangesSubscription?.unsubscribe(),this._renderChangesSubscription=r.subscribe(s=>this._renderData.next(s))}_filterData(n){return this.filteredData=this.filter==null||this.filter===""?n:n.filter(t=>this.filterPredicate(t,this.filter)),this.paginator&&this._updatePaginator(this.filteredData.length),this.filteredData}_orderData(n){return this.sort?this.sortData(n.slice(),this.sort):n}_pageData(n){if(!this.paginator)return n;let t=this.paginator.pageIndex*this.paginator.pageSize;return n.slice(t,t+this.paginator.pageSize)}_updatePaginator(n){Promise.resolve().then(()=>{let t=this.paginator;if(t&&(t.length=n,t.pageIndex>0)){let e=Math.ceil(t.length/t.pageSize)-1||0,o=Math.min(t.pageIndex,e);o!==t.pageIndex&&(t.pageIndex=o,this._internalPageChanges.next())}})}connect(){return this._renderChangesSubscription||this._updateChangeSubscription(),this._renderData}disconnect(){this._renderChangesSubscription?.unsubscribe(),this._renderChangesSubscription=null}};var Xt=class{attach(n){return this._attachedHost=n,n.attach(this)}detach(){let n=this._attachedHost;n!=null&&(this._attachedHost=null,n.detach())}get isAttached(){return this._attachedHost!=null}setAttachedHost(n){this._attachedHost=n}},Tt=class extends Xt{constructor(n,t,e,o,a){super(),this.component=n,this.viewContainerRef=t,this.injector=e,this.componentFactoryResolver=o,this.projectableNodes=a}},pt=class extends Xt{constructor(n,t,e,o){super(),this.templateRef=n,this.viewContainerRef=t,this.context=e,this.injector=o}get origin(){return this.templateRef.elementRef}attach(n,t=this.context){return this.context=t,super.attach(n)}detach(){return this.context=void 0,super.detach()}},Ze=class extends Xt{constructor(n){super(),this.element=n instanceof v?n.nativeElement:n}},At=class{constructor(){this._isDisposed=!1,this.attachDomPortal=null}hasAttached(){return!!this._attachedPortal}attach(n){if(n instanceof Tt)return this._attachedPortal=n,this.attachComponentPortal(n);if(n instanceof pt)return this._attachedPortal=n,this.attachTemplatePortal(n);if(this.attachDomPortal&&n instanceof Ze)return this._attachedPortal=n,this.attachDomPortal(n)}detach(){this._attachedPortal&&(this._attachedPortal.setAttachedHost(null),this._attachedPortal=null),this._invokeDisposeFn()}dispose(){this.hasAttached()&&this.detach(),this._invokeDisposeFn(),this._isDisposed=!0}setDisposeFn(n){this._disposeFn=n}_invokeDisposeFn(){this._disposeFn&&(this._disposeFn(),this._disposeFn=null)}};var xe=class extends At{constructor(n,t,e,o,a){super(),this.outletElement=n,this._componentFactoryResolver=t,this._appRef=e,this._defaultInjector=o,this.attachDomPortal=r=>{this._document;let s=r.element;s.parentNode;let d=this._document.createComment("dom-portal");s.parentNode.insertBefore(d,s),this.outletElement.appendChild(s),this._attachedPortal=r,super.setDisposeFn(()=>{d.parentNode&&d.parentNode.replaceChild(s,d)})},this._document=a}attachComponentPortal(n){let e=(n.componentFactoryResolver||this._componentFactoryResolver).resolveComponentFactory(n.component),o;return n.viewContainerRef?(o=n.viewContainerRef.createComponent(e,n.viewContainerRef.length,n.injector||n.viewContainerRef.injector,n.projectableNodes||void 0),this.setDisposeFn(()=>o.destroy())):(o=e.create(n.injector||this._defaultInjector||K.NULL),this._appRef.attachView(o.hostView),this.setDisposeFn(()=>{this._appRef.viewCount>0&&this._appRef.detachView(o.hostView),o.destroy()})),this.outletElement.appendChild(this._getComponentRootNode(o)),this._attachedPortal=n,o}attachTemplatePortal(n){let t=n.viewContainerRef,e=t.createEmbeddedView(n.templateRef,n.context,{injector:n.injector});return e.rootNodes.forEach(o=>this.outletElement.appendChild(o)),e.detectChanges(),this.setDisposeFn(()=>{let o=t.indexOf(e);o!==-1&&t.remove(o)}),this._attachedPortal=n,e}dispose(){super.dispose(),this.outletElement.remove()}_getComponentRootNode(n){return n.hostView.rootNodes[0]}};var Na=(()=>{class i extends pt{constructor(t,e){super(t,e)}static{this.\u0275fac=function(e){return new(e||i)(c(j),c(Z))}}static{this.\u0275dir=p({type:i,selectors:[["","cdkPortal",""]],exportAs:["cdkPortal"],standalone:!0,features:[b]})}}return i})();var Zt=(()=>{class i extends At{constructor(t,e,o){super(),this._componentFactoryResolver=t,this._viewContainerRef=e,this._isInitialized=!1,this.attached=new H,this.attachDomPortal=a=>{this._document;let r=a.element;r.parentNode;let s=this._document.createComment("dom-portal");a.setAttachedHost(this),r.parentNode.insertBefore(s,r),this._getRootNode().appendChild(r),this._attachedPortal=a,super.setDisposeFn(()=>{s.parentNode&&s.parentNode.replaceChild(r,s)})},this._document=o}get portal(){return this._attachedPortal}set portal(t){this.hasAttached()&&!t&&!this._isInitialized||(this.hasAttached()&&super.detach(),t&&super.attach(t),this._attachedPortal=t||null)}get attachedRef(){return this._attachedRef}ngOnInit(){this._isInitialized=!0}ngOnDestroy(){super.dispose(),this._attachedRef=this._attachedPortal=null}attachComponentPortal(t){t.setAttachedHost(this);let e=t.viewContainerRef!=null?t.viewContainerRef:this._viewContainerRef,a=(t.componentFactoryResolver||this._componentFactoryResolver).resolveComponentFactory(t.component),r=e.createComponent(a,e.length,t.injector||e.injector,t.projectableNodes||void 0);return e!==this._viewContainerRef&&this._getRootNode().appendChild(r.hostView.rootNodes[0]),super.setDisposeFn(()=>r.destroy()),this._attachedPortal=t,this._attachedRef=r,this.attached.emit(r),r}attachTemplatePortal(t){t.setAttachedHost(this);let e=this._viewContainerRef.createEmbeddedView(t.templateRef,t.context,{injector:t.injector});return super.setDisposeFn(()=>this._viewContainerRef.clear()),this._attachedPortal=t,this._attachedRef=e,this.attached.emit(e),e}_getRootNode(){let t=this._viewContainerRef.element.nativeElement;return t.nodeType===t.ELEMENT_NODE?t:t.parentNode}static{this.\u0275fac=function(e){return new(e||i)(c(ie),c(Z),c(R))}}static{this.\u0275dir=p({type:i,selectors:[["","cdkPortalOutlet",""]],inputs:{portal:[u.None,"cdkPortalOutlet","portal"]},outputs:{attached:"attached"},exportAs:["cdkPortalOutlet"],standalone:!0,features:[b]})}}return i})();var yt=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=T({type:i})}static{this.\u0275inj=M({})}}return i})();var Fi=ae(),Ue=class{constructor(n,t){this._viewportRuler=n,this._previousHTMLStyles={top:"",left:""},this._isEnabled=!1,this._document=t}attach(){}enable(){if(this._canBeEnabled()){let n=this._document.documentElement;this._previousScrollPosition=this._viewportRuler.getViewportScrollPosition(),this._previousHTMLStyles.left=n.style.left||"",this._previousHTMLStyles.top=n.style.top||"",n.style.left=C(-this._previousScrollPosition.left),n.style.top=C(-this._previousScrollPosition.top),n.classList.add("cdk-global-scrollblock"),this._isEnabled=!0}}disable(){if(this._isEnabled){let n=this._document.documentElement,t=this._document.body,e=n.style,o=t.style,a=e.scrollBehavior||"",r=o.scrollBehavior||"";this._isEnabled=!1,e.left=this._previousHTMLStyles.left,e.top=this._previousHTMLStyles.top,n.classList.remove("cdk-global-scrollblock"),Fi&&(e.scrollBehavior=o.scrollBehavior="auto"),window.scroll(this._previousScrollPosition.left,this._previousScrollPosition.top),Fi&&(e.scrollBehavior=a,o.scrollBehavior=r)}}_canBeEnabled(){if(this._document.documentElement.classList.contains("cdk-global-scrollblock")||this._isEnabled)return!1;let t=this._document.body,e=this._viewportRuler.getViewportSize();return t.scrollHeight>e.height||t.scrollWidth>e.width}};var $e=class{constructor(n,t,e,o){this._scrollDispatcher=n,this._ngZone=t,this._viewportRuler=e,this._config=o,this._scrollSubscription=null,this._detach=()=>{this.disable(),this._overlayRef.hasAttached()&&this._ngZone.run(()=>this._overlayRef.detach())}}attach(n){this._overlayRef,this._overlayRef=n}enable(){if(this._scrollSubscription)return;let n=this._scrollDispatcher.scrolled(0).pipe(it(t=>!t||!this._overlayRef.overlayElement.contains(t.getElementRef().nativeElement)));this._config&&this._config.threshold&&this._config.threshold>1?(this._initialScrollPosition=this._viewportRuler.getViewportScrollPosition().top,this._scrollSubscription=n.subscribe(()=>{let t=this._viewportRuler.getViewportScrollPosition().top;Math.abs(t-this._initialScrollPosition)>this._config.threshold?this._detach():this._overlayRef.updatePosition()})):this._scrollSubscription=n.subscribe(this._detach)}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}},Ce=class{enable(){}disable(){}attach(){}};function Qe(i,n){return n.some(t=>{let e=i.bottom<t.top,o=i.top>t.bottom,a=i.right<t.left,r=i.left>t.right;return e||o||a||r})}function Mi(i,n){return n.some(t=>{let e=i.top<t.top,o=i.bottom>t.bottom,a=i.left<t.left,r=i.right>t.right;return e||o||a||r})}var Ge=class{constructor(n,t,e,o){this._scrollDispatcher=n,this._viewportRuler=t,this._ngZone=e,this._config=o,this._scrollSubscription=null}attach(n){this._overlayRef,this._overlayRef=n}enable(){if(!this._scrollSubscription){let n=this._config?this._config.scrollThrottle:0;this._scrollSubscription=this._scrollDispatcher.scrolled(n).subscribe(()=>{if(this._overlayRef.updatePosition(),this._config&&this._config.autoClose){let t=this._overlayRef.overlayElement.getBoundingClientRect(),{width:e,height:o}=this._viewportRuler.getViewportSize();Qe(t,[{width:e,height:o,bottom:o,right:e,top:0,left:0}])&&(this.disable(),this._ngZone.run(()=>this._overlayRef.detach()))}})}}disable(){this._scrollSubscription&&(this._scrollSubscription.unsubscribe(),this._scrollSubscription=null)}detach(){this.disable(),this._overlayRef=null}},Do=(()=>{class i{constructor(t,e,o,a){this._scrollDispatcher=t,this._viewportRuler=e,this._ngZone=o,this.noop=()=>new Ce,this.close=r=>new $e(this._scrollDispatcher,this._ngZone,this._viewportRuler,r),this.block=()=>new Ue(this._viewportRuler,this._document),this.reposition=r=>new Ge(this._scrollDispatcher,this._viewportRuler,this._ngZone,r),this._document=a}static{this.\u0275fac=function(e){return new(e||i)(h(ue),h(Mt),h(w),h(R))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),Pt=class{constructor(n){if(this.scrollStrategy=new Ce,this.panelClass="",this.hasBackdrop=!1,this.backdropClass="cdk-overlay-dark-backdrop",this.disposeOnNavigation=!1,n){let t=Object.keys(n);for(let e of t)n[e]!==void 0&&(this[e]=n[e])}}};var qe=class{constructor(n,t){this.connectionPair=n,this.scrollableViewProperties=t}};var Ni=(()=>{class i{constructor(t){this._attachedOverlays=[],this._document=t}ngOnDestroy(){this.detach()}add(t){this.remove(t),this._attachedOverlays.push(t)}remove(t){let e=this._attachedOverlays.indexOf(t);e>-1&&this._attachedOverlays.splice(e,1),this._attachedOverlays.length===0&&this.detach()}static{this.\u0275fac=function(e){return new(e||i)(h(R))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),ko=(()=>{class i extends Ni{constructor(t,e){super(t),this._ngZone=e,this._keydownListener=o=>{let a=this._attachedOverlays;for(let r=a.length-1;r>-1;r--)if(a[r]._keydownEvents.observers.length>0){let s=a[r]._keydownEvents;this._ngZone?this._ngZone.run(()=>s.next(o)):s.next(o);break}}}add(t){super.add(t),this._isAttached||(this._ngZone?this._ngZone.runOutsideAngular(()=>this._document.body.addEventListener("keydown",this._keydownListener)):this._document.body.addEventListener("keydown",this._keydownListener),this._isAttached=!0)}detach(){this._isAttached&&(this._document.body.removeEventListener("keydown",this._keydownListener),this._isAttached=!1)}static{this.\u0275fac=function(e){return new(e||i)(h(R),h(w,8))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),Ro=(()=>{class i extends Ni{constructor(t,e,o){super(t),this._platform=e,this._ngZone=o,this._cursorStyleIsSet=!1,this._pointerDownListener=a=>{this._pointerDownEventTarget=Fe(a)},this._clickListener=a=>{let r=Fe(a),s=a.type==="click"&&this._pointerDownEventTarget?this._pointerDownEventTarget:r;this._pointerDownEventTarget=null;let d=this._attachedOverlays.slice();for(let m=d.length-1;m>-1;m--){let l=d[m];if(l._outsidePointerEvents.observers.length<1||!l.hasAttached())continue;if(l.overlayElement.contains(r)||l.overlayElement.contains(s))break;let f=l._outsidePointerEvents;this._ngZone?this._ngZone.run(()=>f.next(a)):f.next(a)}}}add(t){if(super.add(t),!this._isAttached){let e=this._document.body;this._ngZone?this._ngZone.runOutsideAngular(()=>this._addEventListeners(e)):this._addEventListeners(e),this._platform.IOS&&!this._cursorStyleIsSet&&(this._cursorOriginalValue=e.style.cursor,e.style.cursor="pointer",this._cursorStyleIsSet=!0),this._isAttached=!0}}detach(){if(this._isAttached){let t=this._document.body;t.removeEventListener("pointerdown",this._pointerDownListener,!0),t.removeEventListener("click",this._clickListener,!0),t.removeEventListener("auxclick",this._clickListener,!0),t.removeEventListener("contextmenu",this._clickListener,!0),this._platform.IOS&&this._cursorStyleIsSet&&(t.style.cursor=this._cursorOriginalValue,this._cursorStyleIsSet=!1),this._isAttached=!1}}_addEventListeners(t){t.addEventListener("pointerdown",this._pointerDownListener,!0),t.addEventListener("click",this._clickListener,!0),t.addEventListener("auxclick",this._clickListener,!0),t.addEventListener("contextmenu",this._clickListener,!0)}static{this.\u0275fac=function(e){return new(e||i)(h(R),h(B),h(w,8))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),jt=(()=>{class i{constructor(t,e){this._platform=e,this._document=t}ngOnDestroy(){this._containerElement?.remove()}getContainerElement(){return this._containerElement||this._createContainer(),this._containerElement}_createContainer(){let t="cdk-overlay-container";if(this._platform.isBrowser||Me()){let o=this._document.querySelectorAll(`.${t}[platform="server"], .${t}[platform="test"]`);for(let a=0;a<o.length;a++)o[a].remove()}let e=this._document.createElement("div");e.classList.add(t),Me()?e.setAttribute("platform","test"):this._platform.isBrowser||e.setAttribute("platform","server"),this._document.body.appendChild(e),this._containerElement=e}static{this.\u0275fac=function(e){return new(e||i)(h(R),h(B))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),ct=class{constructor(n,t,e,o,a,r,s,d,m,l=!1){this._portalOutlet=n,this._host=t,this._pane=e,this._config=o,this._ngZone=a,this._keyboardDispatcher=r,this._document=s,this._location=d,this._outsideClickDispatcher=m,this._animationsDisabled=l,this._backdropElement=null,this._backdropClick=new g,this._attachments=new g,this._detachments=new g,this._locationChanges=dt.EMPTY,this._backdropClickHandler=f=>this._backdropClick.next(f),this._backdropTransitionendHandler=f=>{this._disposeBackdrop(f.target)},this._keydownEvents=new g,this._outsidePointerEvents=new g,o.scrollStrategy&&(this._scrollStrategy=o.scrollStrategy,this._scrollStrategy.attach(this)),this._positionStrategy=o.positionStrategy}get overlayElement(){return this._pane}get backdropElement(){return this._backdropElement}get hostElement(){return this._host}attach(n){!this._host.parentElement&&this._previousHostParent&&this._previousHostParent.appendChild(this._host);let t=this._portalOutlet.attach(n);return this._positionStrategy&&this._positionStrategy.attach(this),this._updateStackingOrder(),this._updateElementSize(),this._updateElementDirection(),this._scrollStrategy&&this._scrollStrategy.enable(),this._ngZone.onStable.pipe(ot(1)).subscribe(()=>{this.hasAttached()&&this.updatePosition()}),this._togglePointerEvents(!0),this._config.hasBackdrop&&this._attachBackdrop(),this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!0),this._attachments.next(),this._keyboardDispatcher.add(this),this._config.disposeOnNavigation&&(this._locationChanges=this._location.subscribe(()=>this.dispose())),this._outsideClickDispatcher.add(this),typeof t?.onDestroy=="function"&&t.onDestroy(()=>{this.hasAttached()&&this._ngZone.runOutsideAngular(()=>Promise.resolve().then(()=>this.detach()))}),t}detach(){if(!this.hasAttached())return;this.detachBackdrop(),this._togglePointerEvents(!1),this._positionStrategy&&this._positionStrategy.detach&&this._positionStrategy.detach(),this._scrollStrategy&&this._scrollStrategy.disable();let n=this._portalOutlet.detach();return this._detachments.next(),this._keyboardDispatcher.remove(this),this._detachContentWhenStable(),this._locationChanges.unsubscribe(),this._outsideClickDispatcher.remove(this),n}dispose(){let n=this.hasAttached();this._positionStrategy&&this._positionStrategy.dispose(),this._disposeScrollStrategy(),this._disposeBackdrop(this._backdropElement),this._locationChanges.unsubscribe(),this._keyboardDispatcher.remove(this),this._portalOutlet.dispose(),this._attachments.complete(),this._backdropClick.complete(),this._keydownEvents.complete(),this._outsidePointerEvents.complete(),this._outsideClickDispatcher.remove(this),this._host?.remove(),this._previousHostParent=this._pane=this._host=null,n&&this._detachments.next(),this._detachments.complete()}hasAttached(){return this._portalOutlet.hasAttached()}backdropClick(){return this._backdropClick}attachments(){return this._attachments}detachments(){return this._detachments}keydownEvents(){return this._keydownEvents}outsidePointerEvents(){return this._outsidePointerEvents}getConfig(){return this._config}updatePosition(){this._positionStrategy&&this._positionStrategy.apply()}updatePositionStrategy(n){n!==this._positionStrategy&&(this._positionStrategy&&this._positionStrategy.dispose(),this._positionStrategy=n,this.hasAttached()&&(n.attach(this),this.updatePosition()))}updateSize(n){this._config=V(V({},this._config),n),this._updateElementSize()}setDirection(n){this._config=qt(V({},this._config),{direction:n}),this._updateElementDirection()}addPanelClass(n){this._pane&&this._toggleClasses(this._pane,n,!0)}removePanelClass(n){this._pane&&this._toggleClasses(this._pane,n,!1)}getDirection(){let n=this._config.direction;return n?typeof n=="string"?n:n.value:"ltr"}updateScrollStrategy(n){n!==this._scrollStrategy&&(this._disposeScrollStrategy(),this._scrollStrategy=n,this.hasAttached()&&(n.attach(this),n.enable()))}_updateElementDirection(){this._host.setAttribute("dir",this.getDirection())}_updateElementSize(){if(!this._pane)return;let n=this._pane.style;n.width=C(this._config.width),n.height=C(this._config.height),n.minWidth=C(this._config.minWidth),n.minHeight=C(this._config.minHeight),n.maxWidth=C(this._config.maxWidth),n.maxHeight=C(this._config.maxHeight)}_togglePointerEvents(n){this._pane.style.pointerEvents=n?"":"none"}_attachBackdrop(){let n="cdk-overlay-backdrop-showing";this._backdropElement=this._document.createElement("div"),this._backdropElement.classList.add("cdk-overlay-backdrop"),this._animationsDisabled&&this._backdropElement.classList.add("cdk-overlay-backdrop-noop-animation"),this._config.backdropClass&&this._toggleClasses(this._backdropElement,this._config.backdropClass,!0),this._host.parentElement.insertBefore(this._backdropElement,this._host),this._backdropElement.addEventListener("click",this._backdropClickHandler),!this._animationsDisabled&&typeof requestAnimationFrame<"u"?this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>{this._backdropElement&&this._backdropElement.classList.add(n)})}):this._backdropElement.classList.add(n)}_updateStackingOrder(){this._host.nextSibling&&this._host.parentNode.appendChild(this._host)}detachBackdrop(){let n=this._backdropElement;if(n){if(this._animationsDisabled){this._disposeBackdrop(n);return}n.classList.remove("cdk-overlay-backdrop-showing"),this._ngZone.runOutsideAngular(()=>{n.addEventListener("transitionend",this._backdropTransitionendHandler)}),n.style.pointerEvents="none",this._backdropTimeout=this._ngZone.runOutsideAngular(()=>setTimeout(()=>{this._disposeBackdrop(n)},500))}}_toggleClasses(n,t,e){let o=Te(t||[]).filter(a=>!!a);o.length&&(e?n.classList.add(...o):n.classList.remove(...o))}_detachContentWhenStable(){this._ngZone.runOutsideAngular(()=>{let n=this._ngZone.onStable.pipe(tt(bt(this._attachments,this._detachments))).subscribe(()=>{(!this._pane||!this._host||this._pane.children.length===0)&&(this._pane&&this._config.panelClass&&this._toggleClasses(this._pane,this._config.panelClass,!1),this._host&&this._host.parentElement&&(this._previousHostParent=this._host.parentElement,this._host.remove()),n.unsubscribe())})})}_disposeScrollStrategy(){let n=this._scrollStrategy;n&&(n.disable(),n.detach&&n.detach())}_disposeBackdrop(n){n&&(n.removeEventListener("click",this._backdropClickHandler),n.removeEventListener("transitionend",this._backdropTransitionendHandler),n.remove(),this._backdropElement===n&&(this._backdropElement=null)),this._backdropTimeout&&(clearTimeout(this._backdropTimeout),this._backdropTimeout=void 0)}},Ti="cdk-overlay-connected-position-bounding-box",So=/([A-Za-z%]+)$/,Ke=class{get positions(){return this._preferredPositions}constructor(n,t,e,o,a){this._viewportRuler=t,this._document=e,this._platform=o,this._overlayContainer=a,this._lastBoundingBoxSize={width:0,height:0},this._isPushed=!1,this._canPush=!0,this._growAfterOpen=!1,this._hasFlexibleDimensions=!0,this._positionLocked=!1,this._viewportMargin=0,this._scrollables=[],this._preferredPositions=[],this._positionChanges=new g,this._resizeSubscription=dt.EMPTY,this._offsetX=0,this._offsetY=0,this._appliedPanelClasses=[],this.positionChanges=this._positionChanges,this.setOrigin(n)}attach(n){this._overlayRef&&this._overlayRef,this._validatePositions(),n.hostElement.classList.add(Ti),this._overlayRef=n,this._boundingBox=n.hostElement,this._pane=n.overlayElement,this._isDisposed=!1,this._isInitialRender=!0,this._lastPosition=null,this._resizeSubscription.unsubscribe(),this._resizeSubscription=this._viewportRuler.change().subscribe(()=>{this._isInitialRender=!0,this.apply()})}apply(){if(this._isDisposed||!this._platform.isBrowser)return;if(!this._isInitialRender&&this._positionLocked&&this._lastPosition){this.reapplyLastPosition();return}this._clearPanelClasses(),this._resetOverlayElementStyles(),this._resetBoundingBoxStyles(),this._viewportRect=this._getNarrowedViewportRect(),this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let n=this._originRect,t=this._overlayRect,e=this._viewportRect,o=this._containerRect,a=[],r;for(let s of this._preferredPositions){let d=this._getOriginPoint(n,o,s),m=this._getOverlayPoint(d,t,s),l=this._getOverlayFit(m,t,e,s);if(l.isCompletelyWithinViewport){this._isPushed=!1,this._applyPosition(s,d);return}if(this._canFitWithFlexibleDimensions(l,m,e)){a.push({position:s,origin:d,overlayRect:t,boundingBoxRect:this._calculateBoundingBoxRect(d,s)});continue}(!r||r.overlayFit.visibleArea<l.visibleArea)&&(r={overlayFit:l,overlayPoint:m,originPoint:d,position:s,overlayRect:t})}if(a.length){let s=null,d=-1;for(let m of a){let l=m.boundingBoxRect.width*m.boundingBoxRect.height*(m.position.weight||1);l>d&&(d=l,s=m)}this._isPushed=!1,this._applyPosition(s.position,s.origin);return}if(this._canPush){this._isPushed=!0,this._applyPosition(r.position,r.originPoint);return}this._applyPosition(r.position,r.originPoint)}detach(){this._clearPanelClasses(),this._lastPosition=null,this._previousPushAmount=null,this._resizeSubscription.unsubscribe()}dispose(){this._isDisposed||(this._boundingBox&&wt(this._boundingBox.style,{top:"",left:"",right:"",bottom:"",height:"",width:"",alignItems:"",justifyContent:""}),this._pane&&this._resetOverlayElementStyles(),this._overlayRef&&this._overlayRef.hostElement.classList.remove(Ti),this.detach(),this._positionChanges.complete(),this._overlayRef=this._boundingBox=null,this._isDisposed=!0)}reapplyLastPosition(){if(this._isDisposed||!this._platform.isBrowser)return;let n=this._lastPosition;if(n){this._originRect=this._getOriginRect(),this._overlayRect=this._pane.getBoundingClientRect(),this._viewportRect=this._getNarrowedViewportRect(),this._containerRect=this._overlayContainer.getContainerElement().getBoundingClientRect();let t=this._getOriginPoint(this._originRect,this._containerRect,n);this._applyPosition(n,t)}else this.apply()}withScrollableContainers(n){return this._scrollables=n,this}withPositions(n){return this._preferredPositions=n,n.indexOf(this._lastPosition)===-1&&(this._lastPosition=null),this._validatePositions(),this}withViewportMargin(n){return this._viewportMargin=n,this}withFlexibleDimensions(n=!0){return this._hasFlexibleDimensions=n,this}withGrowAfterOpen(n=!0){return this._growAfterOpen=n,this}withPush(n=!0){return this._canPush=n,this}withLockedPosition(n=!0){return this._positionLocked=n,this}setOrigin(n){return this._origin=n,this}withDefaultOffsetX(n){return this._offsetX=n,this}withDefaultOffsetY(n){return this._offsetY=n,this}withTransformOriginOn(n){return this._transformOriginSelector=n,this}_getOriginPoint(n,t,e){let o;if(e.originX=="center")o=n.left+n.width/2;else{let r=this._isRtl()?n.right:n.left,s=this._isRtl()?n.left:n.right;o=e.originX=="start"?r:s}t.left<0&&(o-=t.left);let a;return e.originY=="center"?a=n.top+n.height/2:a=e.originY=="top"?n.top:n.bottom,t.top<0&&(a-=t.top),{x:o,y:a}}_getOverlayPoint(n,t,e){let o;e.overlayX=="center"?o=-t.width/2:e.overlayX==="start"?o=this._isRtl()?-t.width:0:o=this._isRtl()?0:-t.width;let a;return e.overlayY=="center"?a=-t.height/2:a=e.overlayY=="top"?0:-t.height,{x:n.x+o,y:n.y+a}}_getOverlayFit(n,t,e,o){let a=Pi(t),{x:r,y:s}=n,d=this._getOffset(o,"x"),m=this._getOffset(o,"y");d&&(r+=d),m&&(s+=m);let l=0-r,f=r+a.width-e.width,_=0-s,y=s+a.height-e.height,L=this._subtractOverflows(a.width,l,f),A=this._subtractOverflows(a.height,_,y),P=L*A;return{visibleArea:P,isCompletelyWithinViewport:a.width*a.height===P,fitsInViewportVertically:A===a.height,fitsInViewportHorizontally:L==a.width}}_canFitWithFlexibleDimensions(n,t,e){if(this._hasFlexibleDimensions){let o=e.bottom-t.y,a=e.right-t.x,r=Ai(this._overlayRef.getConfig().minHeight),s=Ai(this._overlayRef.getConfig().minWidth),d=n.fitsInViewportVertically||r!=null&&r<=o,m=n.fitsInViewportHorizontally||s!=null&&s<=a;return d&&m}return!1}_pushOverlayOnScreen(n,t,e){if(this._previousPushAmount&&this._positionLocked)return{x:n.x+this._previousPushAmount.x,y:n.y+this._previousPushAmount.y};let o=Pi(t),a=this._viewportRect,r=Math.max(n.x+o.width-a.width,0),s=Math.max(n.y+o.height-a.height,0),d=Math.max(a.top-e.top-n.y,0),m=Math.max(a.left-e.left-n.x,0),l=0,f=0;return o.width<=a.width?l=m||-r:l=n.x<this._viewportMargin?a.left-e.left-n.x:0,o.height<=a.height?f=d||-s:f=n.y<this._viewportMargin?a.top-e.top-n.y:0,this._previousPushAmount={x:l,y:f},{x:n.x+l,y:n.y+f}}_applyPosition(n,t){if(this._setTransformOrigin(n),this._setOverlayElementStyles(t,n),this._setBoundingBoxStyles(t,n),n.panelClass&&this._addPanelClasses(n.panelClass),this._positionChanges.observers.length){let e=this._getScrollVisibility();if(n!==this._lastPosition||!this._lastScrollVisibility||!Eo(this._lastScrollVisibility,e)){let o=new qe(n,e);this._positionChanges.next(o)}this._lastScrollVisibility=e}this._lastPosition=n,this._isInitialRender=!1}_setTransformOrigin(n){if(!this._transformOriginSelector)return;let t=this._boundingBox.querySelectorAll(this._transformOriginSelector),e,o=n.overlayY;n.overlayX==="center"?e="center":this._isRtl()?e=n.overlayX==="start"?"right":"left":e=n.overlayX==="start"?"left":"right";for(let a=0;a<t.length;a++)t[a].style.transformOrigin=`${e} ${o}`}_calculateBoundingBoxRect(n,t){let e=this._viewportRect,o=this._isRtl(),a,r,s;if(t.overlayY==="top")r=n.y,a=e.height-r+this._viewportMargin;else if(t.overlayY==="bottom")s=e.height-n.y+this._viewportMargin*2,a=e.height-s+this._viewportMargin;else{let y=Math.min(e.bottom-n.y+e.top,n.y),L=this._lastBoundingBoxSize.height;a=y*2,r=n.y-y,a>L&&!this._isInitialRender&&!this._growAfterOpen&&(r=n.y-L/2)}let d=t.overlayX==="start"&&!o||t.overlayX==="end"&&o,m=t.overlayX==="end"&&!o||t.overlayX==="start"&&o,l,f,_;if(m)_=e.width-n.x+this._viewportMargin*2,l=n.x-this._viewportMargin;else if(d)f=n.x,l=e.right-n.x;else{let y=Math.min(e.right-n.x+e.left,n.x),L=this._lastBoundingBoxSize.width;l=y*2,f=n.x-y,l>L&&!this._isInitialRender&&!this._growAfterOpen&&(f=n.x-L/2)}return{top:r,left:f,bottom:s,right:_,width:l,height:a}}_setBoundingBoxStyles(n,t){let e=this._calculateBoundingBoxRect(n,t);!this._isInitialRender&&!this._growAfterOpen&&(e.height=Math.min(e.height,this._lastBoundingBoxSize.height),e.width=Math.min(e.width,this._lastBoundingBoxSize.width));let o={};if(this._hasExactPosition())o.top=o.left="0",o.bottom=o.right=o.maxHeight=o.maxWidth="",o.width=o.height="100%";else{let a=this._overlayRef.getConfig().maxHeight,r=this._overlayRef.getConfig().maxWidth;o.height=C(e.height),o.top=C(e.top),o.bottom=C(e.bottom),o.width=C(e.width),o.left=C(e.left),o.right=C(e.right),t.overlayX==="center"?o.alignItems="center":o.alignItems=t.overlayX==="end"?"flex-end":"flex-start",t.overlayY==="center"?o.justifyContent="center":o.justifyContent=t.overlayY==="bottom"?"flex-end":"flex-start",a&&(o.maxHeight=C(a)),r&&(o.maxWidth=C(r))}this._lastBoundingBoxSize=e,wt(this._boundingBox.style,o)}_resetBoundingBoxStyles(){wt(this._boundingBox.style,{top:"0",left:"0",right:"0",bottom:"0",height:"",width:"",alignItems:"",justifyContent:""})}_resetOverlayElementStyles(){wt(this._pane.style,{top:"",left:"",bottom:"",right:"",position:"",transform:""})}_setOverlayElementStyles(n,t){let e={},o=this._hasExactPosition(),a=this._hasFlexibleDimensions,r=this._overlayRef.getConfig();if(o){let l=this._viewportRuler.getViewportScrollPosition();wt(e,this._getExactOverlayY(t,n,l)),wt(e,this._getExactOverlayX(t,n,l))}else e.position="static";let s="",d=this._getOffset(t,"x"),m=this._getOffset(t,"y");d&&(s+=`translateX(${d}px) `),m&&(s+=`translateY(${m}px)`),e.transform=s.trim(),r.maxHeight&&(o?e.maxHeight=C(r.maxHeight):a&&(e.maxHeight="")),r.maxWidth&&(o?e.maxWidth=C(r.maxWidth):a&&(e.maxWidth="")),wt(this._pane.style,e)}_getExactOverlayY(n,t,e){let o={top:"",bottom:""},a=this._getOverlayPoint(t,this._overlayRect,n);if(this._isPushed&&(a=this._pushOverlayOnScreen(a,this._overlayRect,e)),n.overlayY==="bottom"){let r=this._document.documentElement.clientHeight;o.bottom=`${r-(a.y+this._overlayRect.height)}px`}else o.top=C(a.y);return o}_getExactOverlayX(n,t,e){let o={left:"",right:""},a=this._getOverlayPoint(t,this._overlayRect,n);this._isPushed&&(a=this._pushOverlayOnScreen(a,this._overlayRect,e));let r;if(this._isRtl()?r=n.overlayX==="end"?"left":"right":r=n.overlayX==="end"?"right":"left",r==="right"){let s=this._document.documentElement.clientWidth;o.right=`${s-(a.x+this._overlayRect.width)}px`}else o.left=C(a.x);return o}_getScrollVisibility(){let n=this._getOriginRect(),t=this._pane.getBoundingClientRect(),e=this._scrollables.map(o=>o.getElementRef().nativeElement.getBoundingClientRect());return{isOriginClipped:Mi(n,e),isOriginOutsideView:Qe(n,e),isOverlayClipped:Mi(t,e),isOverlayOutsideView:Qe(t,e)}}_subtractOverflows(n,...t){return t.reduce((e,o)=>e-Math.max(o,0),n)}_getNarrowedViewportRect(){let n=this._document.documentElement.clientWidth,t=this._document.documentElement.clientHeight,e=this._viewportRuler.getViewportScrollPosition();return{top:e.top+this._viewportMargin,left:e.left+this._viewportMargin,right:e.left+n-this._viewportMargin,bottom:e.top+t-this._viewportMargin,width:n-2*this._viewportMargin,height:t-2*this._viewportMargin}}_isRtl(){return this._overlayRef.getDirection()==="rtl"}_hasExactPosition(){return!this._hasFlexibleDimensions||this._isPushed}_getOffset(n,t){return t==="x"?n.offsetX==null?this._offsetX:n.offsetX:n.offsetY==null?this._offsetY:n.offsetY}_validatePositions(){}_addPanelClasses(n){this._pane&&Te(n).forEach(t=>{t!==""&&this._appliedPanelClasses.indexOf(t)===-1&&(this._appliedPanelClasses.push(t),this._pane.classList.add(t))})}_clearPanelClasses(){this._pane&&(this._appliedPanelClasses.forEach(n=>{this._pane.classList.remove(n)}),this._appliedPanelClasses=[])}_getOriginRect(){let n=this._origin;if(n instanceof v)return n.nativeElement.getBoundingClientRect();if(n instanceof Element)return n.getBoundingClientRect();let t=n.width||0,e=n.height||0;return{top:n.y,bottom:n.y+e,left:n.x,right:n.x+t,height:e,width:t}}};function wt(i,n){for(let t in n)n.hasOwnProperty(t)&&(i[t]=n[t]);return i}function Ai(i){if(typeof i!="number"&&i!=null){let[n,t]=i.split(So);return!t||t==="px"?parseFloat(n):null}return i||null}function Pi(i){return{top:Math.floor(i.top),right:Math.floor(i.right),bottom:Math.floor(i.bottom),left:Math.floor(i.left),width:Math.floor(i.width),height:Math.floor(i.height)}}function Eo(i,n){return i===n?!0:i.isOriginClipped===n.isOriginClipped&&i.isOriginOutsideView===n.isOriginOutsideView&&i.isOverlayClipped===n.isOverlayClipped&&i.isOverlayOutsideView===n.isOverlayOutsideView}var ji="cdk-global-overlay-wrapper",Je=class{constructor(){this._cssPosition="static",this._topOffset="",this._bottomOffset="",this._alignItems="",this._xPosition="",this._xOffset="",this._width="",this._height="",this._isDisposed=!1}attach(n){let t=n.getConfig();this._overlayRef=n,this._width&&!t.width&&n.updateSize({width:this._width}),this._height&&!t.height&&n.updateSize({height:this._height}),n.hostElement.classList.add(ji),this._isDisposed=!1}top(n=""){return this._bottomOffset="",this._topOffset=n,this._alignItems="flex-start",this}left(n=""){return this._xOffset=n,this._xPosition="left",this}bottom(n=""){return this._topOffset="",this._bottomOffset=n,this._alignItems="flex-end",this}right(n=""){return this._xOffset=n,this._xPosition="right",this}start(n=""){return this._xOffset=n,this._xPosition="start",this}end(n=""){return this._xOffset=n,this._xPosition="end",this}width(n=""){return this._overlayRef?this._overlayRef.updateSize({width:n}):this._width=n,this}height(n=""){return this._overlayRef?this._overlayRef.updateSize({height:n}):this._height=n,this}centerHorizontally(n=""){return this.left(n),this._xPosition="center",this}centerVertically(n=""){return this.top(n),this._alignItems="center",this}apply(){if(!this._overlayRef||!this._overlayRef.hasAttached())return;let n=this._overlayRef.overlayElement.style,t=this._overlayRef.hostElement.style,e=this._overlayRef.getConfig(),{width:o,height:a,maxWidth:r,maxHeight:s}=e,d=(o==="100%"||o==="100vw")&&(!r||r==="100%"||r==="100vw"),m=(a==="100%"||a==="100vh")&&(!s||s==="100%"||s==="100vh"),l=this._xPosition,f=this._xOffset,_=this._overlayRef.getConfig().direction==="rtl",y="",L="",A="";d?A="flex-start":l==="center"?(A="center",_?L=f:y=f):_?l==="left"||l==="end"?(A="flex-end",y=f):(l==="right"||l==="start")&&(A="flex-start",L=f):l==="left"||l==="start"?(A="flex-start",y=f):(l==="right"||l==="end")&&(A="flex-end",L=f),n.position=this._cssPosition,n.marginLeft=d?"0":y,n.marginTop=m?"0":this._topOffset,n.marginBottom=this._bottomOffset,n.marginRight=d?"0":L,t.justifyContent=A,t.alignItems=m?"flex-start":this._alignItems}dispose(){if(this._isDisposed||!this._overlayRef)return;let n=this._overlayRef.overlayElement.style,t=this._overlayRef.hostElement,e=t.style;t.classList.remove(ji),e.justifyContent=e.alignItems=n.marginTop=n.marginBottom=n.marginLeft=n.marginRight=n.position="",this._overlayRef=null,this._isDisposed=!0}},Io=(()=>{class i{constructor(t,e,o,a){this._viewportRuler=t,this._document=e,this._platform=o,this._overlayContainer=a}global(){return new Je}flexibleConnectedTo(t){return new Ke(t,this._viewportRuler,this._document,this._platform,this._overlayContainer)}static{this.\u0275fac=function(e){return new(e||i)(h(Mt),h(R),h(B),h(jt))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),Oo=0,G=(()=>{class i{constructor(t,e,o,a,r,s,d,m,l,f,_,y){this.scrollStrategies=t,this._overlayContainer=e,this._componentFactoryResolver=o,this._positionBuilder=a,this._keyboardDispatcher=r,this._injector=s,this._ngZone=d,this._document=m,this._directionality=l,this._location=f,this._outsideClickDispatcher=_,this._animationsModuleType=y}create(t){let e=this._createHostElement(),o=this._createPaneElement(e),a=this._createPortalOutlet(o),r=new Pt(t);return r.direction=r.direction||this._directionality.value,new ct(a,e,o,r,this._ngZone,this._keyboardDispatcher,this._document,this._location,this._outsideClickDispatcher,this._animationsModuleType==="NoopAnimations")}position(){return this._positionBuilder}_createPaneElement(t){let e=this._document.createElement("div");return e.id=`cdk-overlay-${Oo++}`,e.classList.add("cdk-overlay-pane"),t.appendChild(e),e}_createHostElement(){let t=this._document.createElement("div");return this._overlayContainer.getContainerElement().appendChild(t),t}_createPortalOutlet(t){return this._appRef||(this._appRef=this._injector.get(ui)),new xe(t,this._componentFactoryResolver,this._appRef,this._injector,this._document)}static{this.\u0275fac=function(e){return new(e||i)(h(Do),h(jt),h(ie),h(Io),h(ko),h(K),h(w),h(R),h(et),h(ne),h(Ro),h(lt,8))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),Fo=[{originX:"start",originY:"bottom",overlayX:"start",overlayY:"top"},{originX:"start",originY:"top",overlayX:"start",overlayY:"bottom"},{originX:"end",originY:"top",overlayX:"end",overlayY:"bottom"},{originX:"end",originY:"bottom",overlayX:"end",overlayY:"top"}],Li=new S("cdk-connected-overlay-scroll-strategy",{providedIn:"root",factory:()=>{let i=E(G);return()=>i.scrollStrategies.reposition()}}),Mo=(()=>{class i{constructor(t){this.elementRef=t}static{this.\u0275fac=function(e){return new(e||i)(c(v))}}static{this.\u0275dir=p({type:i,selectors:[["","cdk-overlay-origin",""],["","overlay-origin",""],["","cdkOverlayOrigin",""]],exportAs:["cdkOverlayOrigin"],standalone:!0})}}return i})(),ir=(()=>{class i{get offsetX(){return this._offsetX}set offsetX(t){this._offsetX=t,this._position&&this._updatePositionStrategy(this._position)}get offsetY(){return this._offsetY}set offsetY(t){this._offsetY=t,this._position&&this._updatePositionStrategy(this._position)}get disposeOnNavigation(){return this._disposeOnNavigation}set disposeOnNavigation(t){this._disposeOnNavigation=t}constructor(t,e,o,a,r){this._overlay=t,this._dir=r,this._backdropSubscription=dt.EMPTY,this._attachSubscription=dt.EMPTY,this._detachSubscription=dt.EMPTY,this._positionSubscription=dt.EMPTY,this._disposeOnNavigation=!1,this._ngZone=E(w),this.viewportMargin=0,this.open=!1,this.disableClose=!1,this.hasBackdrop=!1,this.lockPosition=!1,this.flexibleDimensions=!1,this.growAfterOpen=!1,this.push=!1,this.backdropClick=new H,this.positionChange=new H,this.attach=new H,this.detach=new H,this.overlayKeydown=new H,this.overlayOutsideClick=new H,this._templatePortal=new pt(e,o),this._scrollStrategyFactory=a,this.scrollStrategy=this._scrollStrategyFactory()}get overlayRef(){return this._overlayRef}get dir(){return this._dir?this._dir.value:"ltr"}ngOnDestroy(){this._attachSubscription.unsubscribe(),this._detachSubscription.unsubscribe(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this._overlayRef&&this._overlayRef.dispose()}ngOnChanges(t){this._position&&(this._updatePositionStrategy(this._position),this._overlayRef.updateSize({width:this.width,minWidth:this.minWidth,height:this.height,minHeight:this.minHeight}),t.origin&&this.open&&this._position.apply()),t.open&&(this.open?this._attachOverlay():this._detachOverlay())}_createOverlay(){(!this.positions||!this.positions.length)&&(this.positions=Fo);let t=this._overlayRef=this._overlay.create(this._buildConfig());this._attachSubscription=t.attachments().subscribe(()=>this.attach.emit()),this._detachSubscription=t.detachments().subscribe(()=>this.detach.emit()),t.keydownEvents().subscribe(e=>{this.overlayKeydown.next(e),e.keyCode===27&&!this.disableClose&&!Et(e)&&(e.preventDefault(),this._detachOverlay())}),this._overlayRef.outsidePointerEvents().subscribe(e=>{this.overlayOutsideClick.next(e)})}_buildConfig(){let t=this._position=this.positionStrategy||this._createPositionStrategy(),e=new Pt({direction:this._dir,positionStrategy:t,scrollStrategy:this.scrollStrategy,hasBackdrop:this.hasBackdrop,disposeOnNavigation:this.disposeOnNavigation});return(this.width||this.width===0)&&(e.width=this.width),(this.height||this.height===0)&&(e.height=this.height),(this.minWidth||this.minWidth===0)&&(e.minWidth=this.minWidth),(this.minHeight||this.minHeight===0)&&(e.minHeight=this.minHeight),this.backdropClass&&(e.backdropClass=this.backdropClass),this.panelClass&&(e.panelClass=this.panelClass),e}_updatePositionStrategy(t){let e=this.positions.map(o=>({originX:o.originX,originY:o.originY,overlayX:o.overlayX,overlayY:o.overlayY,offsetX:o.offsetX||this.offsetX,offsetY:o.offsetY||this.offsetY,panelClass:o.panelClass||void 0}));return t.setOrigin(this._getFlexibleConnectedPositionStrategyOrigin()).withPositions(e).withFlexibleDimensions(this.flexibleDimensions).withPush(this.push).withGrowAfterOpen(this.growAfterOpen).withViewportMargin(this.viewportMargin).withLockedPosition(this.lockPosition).withTransformOriginOn(this.transformOriginSelector)}_createPositionStrategy(){let t=this._overlay.position().flexibleConnectedTo(this._getFlexibleConnectedPositionStrategyOrigin());return this._updatePositionStrategy(t),t}_getFlexibleConnectedPositionStrategyOrigin(){return this.origin instanceof Mo?this.origin.elementRef:this.origin}_attachOverlay(){this._overlayRef?this._overlayRef.getConfig().hasBackdrop=this.hasBackdrop:this._createOverlay(),this._overlayRef.hasAttached()||this._overlayRef.attach(this._templatePortal),this.hasBackdrop?this._backdropSubscription=this._overlayRef.backdropClick().subscribe(t=>{this.backdropClick.emit(t)}):this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe(),this.positionChange.observers.length>0&&(this._positionSubscription=this._position.positionChanges.pipe(ci(()=>this.positionChange.observers.length>0)).subscribe(t=>{this._ngZone.run(()=>this.positionChange.emit(t)),this.positionChange.observers.length===0&&this._positionSubscription.unsubscribe()}))}_detachOverlay(){this._overlayRef&&this._overlayRef.detach(),this._backdropSubscription.unsubscribe(),this._positionSubscription.unsubscribe()}static{this.\u0275fac=function(e){return new(e||i)(c(G),c(j),c(Z),c(Li),c(et,8))}}static{this.\u0275dir=p({type:i,selectors:[["","cdk-connected-overlay",""],["","connected-overlay",""],["","cdkConnectedOverlay",""]],inputs:{origin:[u.None,"cdkConnectedOverlayOrigin","origin"],positions:[u.None,"cdkConnectedOverlayPositions","positions"],positionStrategy:[u.None,"cdkConnectedOverlayPositionStrategy","positionStrategy"],offsetX:[u.None,"cdkConnectedOverlayOffsetX","offsetX"],offsetY:[u.None,"cdkConnectedOverlayOffsetY","offsetY"],width:[u.None,"cdkConnectedOverlayWidth","width"],height:[u.None,"cdkConnectedOverlayHeight","height"],minWidth:[u.None,"cdkConnectedOverlayMinWidth","minWidth"],minHeight:[u.None,"cdkConnectedOverlayMinHeight","minHeight"],backdropClass:[u.None,"cdkConnectedOverlayBackdropClass","backdropClass"],panelClass:[u.None,"cdkConnectedOverlayPanelClass","panelClass"],viewportMargin:[u.None,"cdkConnectedOverlayViewportMargin","viewportMargin"],scrollStrategy:[u.None,"cdkConnectedOverlayScrollStrategy","scrollStrategy"],open:[u.None,"cdkConnectedOverlayOpen","open"],disableClose:[u.None,"cdkConnectedOverlayDisableClose","disableClose"],transformOriginSelector:[u.None,"cdkConnectedOverlayTransformOriginOn","transformOriginSelector"],hasBackdrop:[u.HasDecoratorInputTransform,"cdkConnectedOverlayHasBackdrop","hasBackdrop",x],lockPosition:[u.HasDecoratorInputTransform,"cdkConnectedOverlayLockPosition","lockPosition",x],flexibleDimensions:[u.HasDecoratorInputTransform,"cdkConnectedOverlayFlexibleDimensions","flexibleDimensions",x],growAfterOpen:[u.HasDecoratorInputTransform,"cdkConnectedOverlayGrowAfterOpen","growAfterOpen",x],push:[u.HasDecoratorInputTransform,"cdkConnectedOverlayPush","push",x],disposeOnNavigation:[u.HasDecoratorInputTransform,"cdkConnectedOverlayDisposeOnNavigation","disposeOnNavigation",x]},outputs:{backdropClick:"backdropClick",positionChange:"positionChange",attach:"attach",detach:"detach",overlayKeydown:"overlayKeydown",overlayOutsideClick:"overlayOutsideClick"},exportAs:["cdkConnectedOverlay"],standalone:!0,features:[Y,nt]})}}return i})();function To(i){return()=>i.scrollStrategies.reposition()}var Ao={provide:Li,deps:[G],useFactory:To},De=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=T({type:i})}static{this.\u0275inj=M({providers:[G,Ao],imports:[Bt,yt,Vt,Vt]})}}return i})();function jo(i,n){}var xt=class{constructor(){this.role="dialog",this.panelClass="",this.hasBackdrop=!0,this.backdropClass="",this.disableClose=!1,this.width="",this.height="",this.data=null,this.ariaDescribedBy=null,this.ariaLabelledBy=null,this.ariaLabel=null,this.ariaModal=!0,this.autoFocus="first-tabbable",this.restoreFocus=!0,this.closeOnNavigation=!0,this.closeOnDestroy=!0,this.closeOnOverlayDetachments=!0}};var ei=(()=>{class i extends At{constructor(t,e,o,a,r,s,d,m){super(),this._elementRef=t,this._focusTrapFactory=e,this._config=a,this._interactivityChecker=r,this._ngZone=s,this._overlayRef=d,this._focusMonitor=m,this._platform=E(B),this._focusTrap=null,this._elementFocusedBeforeDialogWasOpened=null,this._closeInteractionType=null,this._ariaLabelledByQueue=[],this._changeDetectorRef=E(Lt),this.attachDomPortal=l=>{this._portalOutlet.hasAttached();let f=this._portalOutlet.attachDomPortal(l);return this._contentAttached(),f},this._document=o,this._config.ariaLabelledBy&&this._ariaLabelledByQueue.push(this._config.ariaLabelledBy)}_addAriaLabelledBy(t){this._ariaLabelledByQueue.push(t),this._changeDetectorRef.markForCheck()}_removeAriaLabelledBy(t){let e=this._ariaLabelledByQueue.indexOf(t);e>-1&&(this._ariaLabelledByQueue.splice(e,1),this._changeDetectorRef.markForCheck())}_contentAttached(){this._initializeFocusTrap(),this._handleBackdropClicks(),this._captureInitialFocus()}_captureInitialFocus(){this._trapFocus()}ngOnDestroy(){this._restoreFocus()}attachComponentPortal(t){this._portalOutlet.hasAttached();let e=this._portalOutlet.attachComponentPortal(t);return this._contentAttached(),e}attachTemplatePortal(t){this._portalOutlet.hasAttached();let e=this._portalOutlet.attachTemplatePortal(t);return this._contentAttached(),e}_recaptureFocus(){this._containsFocus()||this._trapFocus()}_forceFocus(t,e){this._interactivityChecker.isFocusable(t)||(t.tabIndex=-1,this._ngZone.runOutsideAngular(()=>{let o=()=>{t.removeEventListener("blur",o),t.removeEventListener("mousedown",o),t.removeAttribute("tabindex")};t.addEventListener("blur",o),t.addEventListener("mousedown",o)})),t.focus(e)}_focusByCssSelector(t,e){let o=this._elementRef.nativeElement.querySelector(t);o&&this._forceFocus(o,e)}_trapFocus(){let t=this._elementRef.nativeElement;switch(this._config.autoFocus){case!1:case"dialog":this._containsFocus()||t.focus();break;case!0:case"first-tabbable":this._focusTrap?.focusInitialElementWhenReady().then(e=>{e||this._focusDialogContainer()});break;case"first-heading":this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role="heading"]');break;default:this._focusByCssSelector(this._config.autoFocus);break}}_restoreFocus(){let t=this._config.restoreFocus,e=null;if(typeof t=="string"?e=this._document.querySelector(t):typeof t=="boolean"?e=t?this._elementFocusedBeforeDialogWasOpened:null:t&&(e=t),this._config.restoreFocus&&e&&typeof e.focus=="function"){let o=re(),a=this._elementRef.nativeElement;(!o||o===this._document.body||o===a||a.contains(o))&&(this._focusMonitor?(this._focusMonitor.focusVia(e,this._closeInteractionType),this._closeInteractionType=null):e.focus())}this._focusTrap&&this._focusTrap.destroy()}_focusDialogContainer(){this._elementRef.nativeElement.focus&&this._elementRef.nativeElement.focus()}_containsFocus(){let t=this._elementRef.nativeElement,e=re();return t===e||t.contains(e)}_initializeFocusTrap(){this._platform.isBrowser&&(this._focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement),this._document&&(this._elementFocusedBeforeDialogWasOpened=re()))}_handleBackdropClicks(){this._overlayRef.backdropClick().subscribe(()=>{this._config.disableClose&&this._recaptureFocus()})}static{this.\u0275fac=function(e){return new(e||i)(c(v),c(de),c(R,8),c(xt),c(ce),c(w),c(ct),c(It))}}static{this.\u0275cmp=I({type:i,selectors:[["cdk-dialog-container"]],viewQuery:function(e,o){if(e&1&&oe(Zt,7),e&2){let a;$(a=Q())&&(o._portalOutlet=a.first)}},hostAttrs:["tabindex","-1",1,"cdk-dialog-container"],hostVars:6,hostBindings:function(e,o){e&2&&ut("id",o._config.id||null)("role",o._config.role)("aria-modal",o._config.ariaModal)("aria-labelledby",o._config.ariaLabel?null:o._ariaLabelledByQueue[0])("aria-label",o._config.ariaLabel)("aria-describedby",o._config.ariaDescribedBy||null)},standalone:!0,features:[b,F],decls:1,vars:0,consts:[["cdkPortalOutlet",""]],template:function(e,o){e&1&&mt(0,jo,0,0,"ng-template",0)},dependencies:[Zt],styles:[".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}"],encapsulation:2})}}return i})(),$t=class{constructor(n,t){this.overlayRef=n,this.config=t,this.closed=new g,this.disableClose=t.disableClose,this.backdropClick=n.backdropClick(),this.keydownEvents=n.keydownEvents(),this.outsidePointerEvents=n.outsidePointerEvents(),this.id=t.id,this.keydownEvents.subscribe(e=>{e.keyCode===27&&!this.disableClose&&!Et(e)&&(e.preventDefault(),this.close(void 0,{focusOrigin:"keyboard"}))}),this.backdropClick.subscribe(()=>{this.disableClose||this.close(void 0,{focusOrigin:"mouse"})}),this._detachSubscription=n.detachments().subscribe(()=>{t.closeOnOverlayDetachments!==!1&&this.close()})}close(n,t){if(this.containerInstance){let e=this.closed;this.containerInstance._closeInteractionType=t?.focusOrigin||"program",this._detachSubscription.unsubscribe(),this.overlayRef.dispose(),e.next(n),e.complete(),this.componentInstance=this.containerInstance=null}}updatePosition(){return this.overlayRef.updatePosition(),this}updateSize(n="",t=""){return this.overlayRef.updateSize({width:n,height:t}),this}addPanelClass(n){return this.overlayRef.addPanelClass(n),this}removePanelClass(n){return this.overlayRef.removePanelClass(n),this}},No=new S("DialogScrollStrategy",{providedIn:"root",factory:()=>{let i=E(G);return()=>i.scrollStrategies.block()}}),Lo=new S("DialogData"),Bo=new S("DefaultDialogConfig");var zo=0,ii=(()=>{class i{get openDialogs(){return this._parentDialog?this._parentDialog.openDialogs:this._openDialogsAtThisLevel}get afterOpened(){return this._parentDialog?this._parentDialog.afterOpened:this._afterOpenedAtThisLevel}constructor(t,e,o,a,r,s){this._overlay=t,this._injector=e,this._defaultOptions=o,this._parentDialog=a,this._overlayContainer=r,this._openDialogsAtThisLevel=[],this._afterAllClosedAtThisLevel=new g,this._afterOpenedAtThisLevel=new g,this._ariaHiddenElements=new Map,this.afterAllClosed=ee(()=>this.openDialogs.length?this._getAfterAllClosed():this._getAfterAllClosed().pipe(Nt(void 0))),this._scrollStrategy=s}open(t,e){let o=this._defaultOptions||new xt;e=V(V({},o),e),e.id=e.id||`cdk-dialog-${zo++}`,e.id&&this.getDialogById(e.id);let a=this._getOverlayConfig(e),r=this._overlay.create(a),s=new $t(r,e),d=this._attachContainer(r,s,e);return s.containerInstance=d,this._attachDialogContent(t,s,d,e),this.openDialogs.length||this._hideNonDialogContentFromAssistiveTechnology(),this.openDialogs.push(s),s.closed.subscribe(()=>this._removeOpenDialog(s,!0)),this.afterOpened.next(s),s}closeAll(){ti(this.openDialogs,t=>t.close())}getDialogById(t){return this.openDialogs.find(e=>e.id===t)}ngOnDestroy(){ti(this._openDialogsAtThisLevel,t=>{t.config.closeOnDestroy===!1&&this._removeOpenDialog(t,!1)}),ti(this._openDialogsAtThisLevel,t=>t.close()),this._afterAllClosedAtThisLevel.complete(),this._afterOpenedAtThisLevel.complete(),this._openDialogsAtThisLevel=[]}_getOverlayConfig(t){let e=new Pt({positionStrategy:t.positionStrategy||this._overlay.position().global().centerHorizontally().centerVertically(),scrollStrategy:t.scrollStrategy||this._scrollStrategy(),panelClass:t.panelClass,hasBackdrop:t.hasBackdrop,direction:t.direction,minWidth:t.minWidth,minHeight:t.minHeight,maxWidth:t.maxWidth,maxHeight:t.maxHeight,width:t.width,height:t.height,disposeOnNavigation:t.closeOnNavigation});return t.backdropClass&&(e.backdropClass=t.backdropClass),e}_attachContainer(t,e,o){let a=o.injector||o.viewContainerRef?.injector,r=[{provide:xt,useValue:o},{provide:$t,useValue:e},{provide:ct,useValue:t}],s;o.container?typeof o.container=="function"?s=o.container:(s=o.container.type,r.push(...o.container.providers(o))):s=ei;let d=new Tt(s,o.viewContainerRef,K.create({parent:a||this._injector,providers:r}),o.componentFactoryResolver);return t.attach(d).instance}_attachDialogContent(t,e,o,a){if(t instanceof j){let r=this._createInjector(a,e,o,void 0),s={$implicit:a.data,dialogRef:e};a.templateContext&&(s=V(V({},s),typeof a.templateContext=="function"?a.templateContext():a.templateContext)),o.attachTemplatePortal(new pt(t,null,s,r))}else{let r=this._createInjector(a,e,o,this._injector),s=o.attachComponentPortal(new Tt(t,a.viewContainerRef,r,a.componentFactoryResolver));e.componentRef=s,e.componentInstance=s.instance}}_createInjector(t,e,o,a){let r=t.injector||t.viewContainerRef?.injector,s=[{provide:Lo,useValue:t.data},{provide:$t,useValue:e}];return t.providers&&(typeof t.providers=="function"?s.push(...t.providers(e,t,o)):s.push(...t.providers)),t.direction&&(!r||!r.get(et,null,{optional:!0}))&&s.push({provide:et,useValue:{value:t.direction,change:q()}}),K.create({parent:r||a,providers:s})}_removeOpenDialog(t,e){let o=this.openDialogs.indexOf(t);o>-1&&(this.openDialogs.splice(o,1),this.openDialogs.length||(this._ariaHiddenElements.forEach((a,r)=>{a?r.setAttribute("aria-hidden",a):r.removeAttribute("aria-hidden")}),this._ariaHiddenElements.clear(),e&&this._getAfterAllClosed().next()))}_hideNonDialogContentFromAssistiveTechnology(){let t=this._overlayContainer.getContainerElement();if(t.parentElement){let e=t.parentElement.children;for(let o=e.length-1;o>-1;o--){let a=e[o];a!==t&&a.nodeName!=="SCRIPT"&&a.nodeName!=="STYLE"&&!a.hasAttribute("aria-live")&&(this._ariaHiddenElements.set(a,a.getAttribute("aria-hidden")),a.setAttribute("aria-hidden","true"))}}}_getAfterAllClosed(){let t=this._parentDialog;return t?t._getAfterAllClosed():this._afterAllClosedAtThisLevel}static{this.\u0275fac=function(e){return new(e||i)(h(G),h(K),h(Bo,8),h(i,12),h(jt),h(No))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();function ti(i,n){let t=i.length;for(;t--;)n(i[t])}var zi=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=T({type:i})}static{this.\u0275inj=M({providers:[ii],imports:[De,yt,pi,yt]})}}return i})();function Vo(i,n){}var Qt=class{constructor(){this.role="dialog",this.panelClass="",this.hasBackdrop=!0,this.backdropClass="",this.disableClose=!1,this.width="",this.height="",this.data=null,this.ariaDescribedBy=null,this.ariaLabelledBy=null,this.ariaLabel=null,this.ariaModal=!0,this.autoFocus="first-tabbable",this.restoreFocus=!0,this.delayFocusTrap=!0,this.closeOnNavigation=!0}},oi="mdc-dialog--open",Vi="mdc-dialog--opening",Hi="mdc-dialog--closing",Ho=150,Yo=75,Wo=(()=>{class i extends ei{constructor(t,e,o,a,r,s,d,m,l){super(t,e,o,a,r,s,d,l),this._animationMode=m,this._animationStateChanged=new H,this._animationsEnabled=this._animationMode!=="NoopAnimations",this._actionSectionCount=0,this._hostElement=this._elementRef.nativeElement,this._enterAnimationDuration=this._animationsEnabled?Wi(this._config.enterAnimationDuration)??Ho:0,this._exitAnimationDuration=this._animationsEnabled?Wi(this._config.exitAnimationDuration)??Yo:0,this._animationTimer=null,this._finishDialogOpen=()=>{this._clearAnimationClasses(),this._openAnimationDone(this._enterAnimationDuration)},this._finishDialogClose=()=>{this._clearAnimationClasses(),this._animationStateChanged.emit({state:"closed",totalTime:this._exitAnimationDuration})}}_contentAttached(){super._contentAttached(),this._startOpenAnimation()}_startOpenAnimation(){this._animationStateChanged.emit({state:"opening",totalTime:this._enterAnimationDuration}),this._animationsEnabled?(this._hostElement.style.setProperty(Yi,`${this._enterAnimationDuration}ms`),this._requestAnimationFrame(()=>this._hostElement.classList.add(Vi,oi)),this._waitForAnimationToComplete(this._enterAnimationDuration,this._finishDialogOpen)):(this._hostElement.classList.add(oi),Promise.resolve().then(()=>this._finishDialogOpen()))}_startExitAnimation(){this._animationStateChanged.emit({state:"closing",totalTime:this._exitAnimationDuration}),this._hostElement.classList.remove(oi),this._animationsEnabled?(this._hostElement.style.setProperty(Yi,`${this._exitAnimationDuration}ms`),this._requestAnimationFrame(()=>this._hostElement.classList.add(Hi)),this._waitForAnimationToComplete(this._exitAnimationDuration,this._finishDialogClose)):Promise.resolve().then(()=>this._finishDialogClose())}_updateActionSectionCount(t){this._actionSectionCount+=t,this._changeDetectorRef.markForCheck()}_clearAnimationClasses(){this._hostElement.classList.remove(Vi,Hi)}_waitForAnimationToComplete(t,e){this._animationTimer!==null&&clearTimeout(this._animationTimer),this._animationTimer=setTimeout(e,t)}_requestAnimationFrame(t){this._ngZone.runOutsideAngular(()=>{typeof requestAnimationFrame=="function"?requestAnimationFrame(t):t()})}_captureInitialFocus(){this._config.delayFocusTrap||this._trapFocus()}_openAnimationDone(t){this._config.delayFocusTrap&&this._trapFocus(),this._animationStateChanged.next({state:"opened",totalTime:t})}ngOnDestroy(){super.ngOnDestroy(),this._animationTimer!==null&&clearTimeout(this._animationTimer)}attachComponentPortal(t){let e=super.attachComponentPortal(t);return e.location.nativeElement.classList.add("mat-mdc-dialog-component-host"),e}static{this.\u0275fac=function(e){return new(e||i)(c(v),c(de),c(R,8),c(Qt),c(ce),c(w),c(ct),c(lt,8),c(It))}}static{this.\u0275cmp=I({type:i,selectors:[["mat-dialog-container"]],hostAttrs:["tabindex","-1",1,"mat-mdc-dialog-container","mdc-dialog"],hostVars:10,hostBindings:function(e,o){e&2&&(Oe("id",o._config.id),ut("aria-modal",o._config.ariaModal)("role",o._config.role)("aria-labelledby",o._config.ariaLabel?null:o._ariaLabelledByQueue[0])("aria-label",o._config.ariaLabel)("aria-describedby",o._config.ariaDescribedBy||null),U("_mat-animation-noopable",!o._animationsEnabled)("mat-mdc-dialog-container-with-actions",o._actionSectionCount>0))},standalone:!0,features:[b,F],decls:3,vars:0,consts:[[1,"mdc-dialog__container"],[1,"mat-mdc-dialog-surface","mdc-dialog__surface"],["cdkPortalOutlet",""]],template:function(e,o){e&1&&(W(0,"div",0)(1,"div",1),mt(2,Vo,0,0,"ng-template",2),X()())},dependencies:[Zt],styles:['.mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity, 0);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-dialog,.mdc-dialog__scrim{position:fixed;top:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;width:100%;height:100%}.mdc-dialog{display:none;z-index:var(--mdc-dialog-z-index, 7)}.mdc-dialog .mdc-dialog__content{padding:20px 24px 20px 24px}.mdc-dialog .mdc-dialog__surface{min-width:280px}@media(max-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:calc(100vw - 32px)}}@media(min-width: 592px){.mdc-dialog .mdc-dialog__surface{max-width:560px}}.mdc-dialog .mdc-dialog__surface{max-height:calc(100% - 32px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-width:none}@media(max-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px;width:560px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 112px)}}@media(max-width: 720px)and (min-width: 672px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:560px}}@media(max-width: 720px)and (max-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:calc(100vh - 160px)}}@media(max-width: 720px)and (min-height: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{max-height:560px}}@media(max-width: 720px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}@media(max-width: 720px)and (max-height: 400px),(max-width: 600px),(min-width: 720px)and (max-height: 400px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{height:100%;max-height:100vh;max-width:100vw;width:100vw;border-radius:0}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{order:-1;left:-12px}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__header{padding:0 16px 9px;justify-content:flex-start}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__title{margin-left:calc(16px - 2 * 12px)}}@media(min-width: 960px){.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface{width:calc(100vw - 400px)}.mdc-dialog.mdc-dialog--fullscreen .mdc-dialog__surface .mdc-dialog__close{right:-12px}}.mdc-dialog.mdc-dialog__scrim--hidden .mdc-dialog__scrim{opacity:0}.mdc-dialog__scrim{opacity:0;z-index:-1}.mdc-dialog__container{display:flex;flex-direction:row;align-items:center;justify-content:space-around;box-sizing:border-box;height:100%;opacity:0;pointer-events:none}.mdc-dialog__surface{position:relative;display:flex;flex-direction:column;flex-grow:0;flex-shrink:0;box-sizing:border-box;max-width:100%;max-height:100%;pointer-events:auto;overflow-y:auto;outline:0;transform:scale(0.8)}.mdc-dialog__surface .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}[dir=rtl] .mdc-dialog__surface,.mdc-dialog__surface[dir=rtl]{text-align:right}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-dialog__surface{outline:2px solid windowText}}.mdc-dialog__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:2px solid rgba(0,0,0,0);border-radius:inherit;content:"";pointer-events:none}@media screen and (forced-colors: active){.mdc-dialog__surface::before{border-color:CanvasText}}@media screen and (-ms-high-contrast: active),screen and (-ms-high-contrast: none){.mdc-dialog__surface::before{content:none}}.mdc-dialog__title{display:block;margin-top:0;position:relative;flex-shrink:0;box-sizing:border-box;margin:0 0 1px;padding:0 24px 9px}.mdc-dialog__title::before{display:inline-block;width:0;height:40px;content:"";vertical-align:0}[dir=rtl] .mdc-dialog__title,.mdc-dialog__title[dir=rtl]{text-align:right}.mdc-dialog--scrollable .mdc-dialog__title{margin-bottom:1px;padding-bottom:15px}.mdc-dialog--fullscreen .mdc-dialog__header{align-items:baseline;border-bottom:1px solid rgba(0,0,0,0);display:inline-flex;justify-content:space-between;padding:0 24px 9px;z-index:1}@media screen and (forced-colors: active){.mdc-dialog--fullscreen .mdc-dialog__header{border-bottom-color:CanvasText}}.mdc-dialog--fullscreen .mdc-dialog__header .mdc-dialog__close{right:-12px}.mdc-dialog--fullscreen .mdc-dialog__title{margin-bottom:0;padding:0;border-bottom:0}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__title{border-bottom:0;margin-bottom:0}.mdc-dialog--fullscreen .mdc-dialog__close{top:5px}.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog--fullscreen.mdc-dialog--scrollable .mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--fullscreen--titleless .mdc-dialog__close{margin-top:4px}.mdc-dialog--fullscreen--titleless.mdc-dialog--scrollable .mdc-dialog__close{margin-top:0}.mdc-dialog__content{flex-grow:1;box-sizing:border-box;margin:0;overflow:auto}.mdc-dialog__content>:first-child{margin-top:0}.mdc-dialog__content>:last-child{margin-bottom:0}.mdc-dialog__title+.mdc-dialog__content,.mdc-dialog__header+.mdc-dialog__content{padding-top:0}.mdc-dialog--scrollable .mdc-dialog__title+.mdc-dialog__content{padding-top:8px;padding-bottom:8px}.mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:6px 0 0}.mdc-dialog--scrollable .mdc-dialog__content .mdc-deprecated-list:first-child:last-child{padding:0}.mdc-dialog__actions{display:flex;position:relative;flex-shrink:0;flex-wrap:wrap;align-items:center;justify-content:flex-end;box-sizing:border-box;min-height:52px;margin:0;padding:8px;border-top:1px solid rgba(0,0,0,0)}@media screen and (forced-colors: active){.mdc-dialog__actions{border-top-color:CanvasText}}.mdc-dialog--stacked .mdc-dialog__actions{flex-direction:column;align-items:flex-end}.mdc-dialog__button{margin-left:8px;margin-right:0;max-width:100%;text-align:right}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{margin-left:0;margin-right:8px}.mdc-dialog__button:first-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button:first-child,.mdc-dialog__button:first-child[dir=rtl]{margin-left:0;margin-right:0}[dir=rtl] .mdc-dialog__button,.mdc-dialog__button[dir=rtl]{text-align:left}.mdc-dialog--stacked .mdc-dialog__button:not(:first-child){margin-top:12px}.mdc-dialog--open,.mdc-dialog--opening,.mdc-dialog--closing{display:flex}.mdc-dialog--opening .mdc-dialog__scrim{transition:opacity 150ms linear}.mdc-dialog--opening .mdc-dialog__container{transition:opacity 75ms linear,transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-dialog--closing .mdc-dialog__scrim,.mdc-dialog--closing .mdc-dialog__container{transition:opacity 75ms linear}.mdc-dialog--closing .mdc-dialog__container{transform:none}.mdc-dialog--closing .mdc-dialog__surface{transform:none}.mdc-dialog--open .mdc-dialog__scrim{opacity:1}.mdc-dialog--open .mdc-dialog__container{opacity:1}.mdc-dialog--open .mdc-dialog__surface{transform:none}.mdc-dialog--open.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim{opacity:1}.mdc-dialog--open.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{transition:opacity 75ms linear}.mdc-dialog--open.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim{transition:opacity 150ms linear}.mdc-dialog__surface-scrim{display:none;opacity:0;position:absolute;width:100%;height:100%;z-index:1}.mdc-dialog__surface-scrim--shown .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--showing .mdc-dialog__surface-scrim,.mdc-dialog__surface-scrim--hiding .mdc-dialog__surface-scrim{display:block}.mdc-dialog-scroll-lock{overflow:hidden}.mdc-dialog--no-content-padding .mdc-dialog__content{padding:0}.mdc-dialog--sheet .mdc-dialog__container .mdc-dialog__close{right:12px;top:9px;position:absolute;z-index:1}.mdc-dialog__scrim--removed{pointer-events:none}.mdc-dialog__scrim--removed .mdc-dialog__scrim,.mdc-dialog__scrim--removed .mdc-dialog__surface-scrim{display:none}.mat-mdc-dialog-content{max-height:65vh}.mat-mdc-dialog-container{position:static;display:block}.mat-mdc-dialog-container,.mat-mdc-dialog-container .mdc-dialog__container,.mat-mdc-dialog-container .mdc-dialog__surface{max-height:inherit;min-height:inherit;min-width:inherit;max-width:inherit}.mat-mdc-dialog-container .mdc-dialog__surface{width:100%;height:100%}.mat-mdc-dialog-component-host{display:contents}.mat-mdc-dialog-container{--mdc-dialog-container-elevation: var(--mdc-dialog-container-elevation-shadow);outline:0}.mat-mdc-dialog-container .mdc-dialog__surface{background-color:var(--mdc-dialog-container-color, white)}.mat-mdc-dialog-container .mdc-dialog__surface{box-shadow:var(--mdc-dialog-container-elevation, 0px 11px 15px -7px rgba(0, 0, 0, 0.2), 0px 24px 38px 3px rgba(0, 0, 0, 0.14), 0px 9px 46px 8px rgba(0, 0, 0, 0.12))}.mat-mdc-dialog-container .mdc-dialog__surface{border-radius:var(--mdc-dialog-container-shape, 4px)}.mat-mdc-dialog-container .mdc-dialog__title{font-family:var(--mdc-dialog-subhead-font, Roboto, sans-serif);line-height:var(--mdc-dialog-subhead-line-height, 1.5rem);font-size:var(--mdc-dialog-subhead-size, 1rem);font-weight:var(--mdc-dialog-subhead-weight, 400);letter-spacing:var(--mdc-dialog-subhead-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__title{color:var(--mdc-dialog-subhead-color, rgba(0, 0, 0, 0.87))}.mat-mdc-dialog-container .mdc-dialog__content{font-family:var(--mdc-dialog-supporting-text-font, Roboto, sans-serif);line-height:var(--mdc-dialog-supporting-text-line-height, 1.5rem);font-size:var(--mdc-dialog-supporting-text-size, 1rem);font-weight:var(--mdc-dialog-supporting-text-weight, 400);letter-spacing:var(--mdc-dialog-supporting-text-tracking, 0.03125em)}.mat-mdc-dialog-container .mdc-dialog__content{color:var(--mdc-dialog-supporting-text-color, rgba(0, 0, 0, 0.6))}.mat-mdc-dialog-container .mdc-dialog__container{transition:opacity linear var(--mat-dialog-transition-duration, 0ms)}.mat-mdc-dialog-container .mdc-dialog__surface{transition:transform var(--mat-dialog-transition-duration, 0ms) 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__container,.mat-mdc-dialog-container._mat-animation-noopable .mdc-dialog__surface{transition:none}.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-max-width, 80vw);min-width:var(--mat-dialog-container-min-width, 0)}@media(max-width: 599px){.cdk-overlay-pane.mat-mdc-dialog-panel{max-width:var(--mat-dialog-container-small-max-width, 80vw)}}.mat-mdc-dialog-title{padding:var(--mat-dialog-headline-padding, 0 24px 9px)}.mat-mdc-dialog-content{display:block}.mat-mdc-dialog-container .mat-mdc-dialog-content{padding:var(--mat-dialog-content-padding, 20px 24px)}.mat-mdc-dialog-container-with-actions .mat-mdc-dialog-content{padding:var(--mat-dialog-with-actions-content-padding, 20px 24px)}.mat-mdc-dialog-container .mat-mdc-dialog-title+.mat-mdc-dialog-content{padding-top:0}.mat-mdc-dialog-actions{padding:var(--mat-dialog-actions-padding, 8px);justify-content:var(--mat-dialog-actions-alignment, start)}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-start,.mat-mdc-dialog-actions[align=start]{justify-content:start}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-center,.mat-mdc-dialog-actions[align=center]{justify-content:center}.mat-mdc-dialog-actions.mat-mdc-dialog-actions-align-end,.mat-mdc-dialog-actions[align=end]{justify-content:flex-end}.mat-mdc-dialog-actions .mat-button-base+.mat-button-base,.mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:8px}[dir=rtl] .mat-mdc-dialog-actions .mat-button-base+.mat-button-base,[dir=rtl] .mat-mdc-dialog-actions .mat-mdc-button-base+.mat-mdc-button-base{margin-left:0;margin-right:8px}'],encapsulation:2})}}return i})(),Yi="--mat-dialog-transition-duration";function Wi(i){return i==null?null:typeof i=="number"?i:i.endsWith("ms")?se(i.substring(0,i.length-2)):i.endsWith("s")?se(i.substring(0,i.length-1))*1e3:i==="0"?0:null}var ke=function(i){return i[i.OPEN=0]="OPEN",i[i.CLOSING=1]="CLOSING",i[i.CLOSED=2]="CLOSED",i}(ke||{}),Gt=class{constructor(n,t,e){this._ref=n,this._containerInstance=e,this._afterOpened=new g,this._beforeClosed=new g,this._state=ke.OPEN,this.disableClose=t.disableClose,this.id=n.id,n.addPanelClass("mat-mdc-dialog-panel"),e._animationStateChanged.pipe(it(o=>o.state==="opened"),ot(1)).subscribe(()=>{this._afterOpened.next(),this._afterOpened.complete()}),e._animationStateChanged.pipe(it(o=>o.state==="closed"),ot(1)).subscribe(()=>{clearTimeout(this._closeFallbackTimeout),this._finishDialogClose()}),n.overlayRef.detachments().subscribe(()=>{this._beforeClosed.next(this._result),this._beforeClosed.complete(),this._finishDialogClose()}),bt(this.backdropClick(),this.keydownEvents().pipe(it(o=>o.keyCode===27&&!this.disableClose&&!Et(o)))).subscribe(o=>{this.disableClose||(o.preventDefault(),Xi(this,o.type==="keydown"?"keyboard":"mouse"))})}close(n){this._result=n,this._containerInstance._animationStateChanged.pipe(it(t=>t.state==="closing"),ot(1)).subscribe(t=>{this._beforeClosed.next(n),this._beforeClosed.complete(),this._ref.overlayRef.detachBackdrop(),this._closeFallbackTimeout=setTimeout(()=>this._finishDialogClose(),t.totalTime+100)}),this._state=ke.CLOSING,this._containerInstance._startExitAnimation()}afterOpened(){return this._afterOpened}afterClosed(){return this._ref.closed}beforeClosed(){return this._beforeClosed}backdropClick(){return this._ref.backdropClick}keydownEvents(){return this._ref.keydownEvents}updatePosition(n){let t=this._ref.config.positionStrategy;return n&&(n.left||n.right)?n.left?t.left(n.left):t.right(n.right):t.centerHorizontally(),n&&(n.top||n.bottom)?n.top?t.top(n.top):t.bottom(n.bottom):t.centerVertically(),this._ref.updatePosition(),this}updateSize(n="",t=""){return this._ref.updateSize(n,t),this}addPanelClass(n){return this._ref.addPanelClass(n),this}removePanelClass(n){return this._ref.removePanelClass(n),this}getState(){return this._state}_finishDialogClose(){this._state=ke.CLOSED,this._ref.close(this._result,{focusOrigin:this._closeInteractionType}),this.componentInstance=null}};function Xi(i,n,t){return i._closeInteractionType=n,i.close(t)}var Xo=new S("MatMdcDialogData"),Zo=new S("mat-mdc-dialog-default-options"),Uo=new S("mat-mdc-dialog-scroll-strategy",{providedIn:"root",factory:()=>{let i=E(G);return()=>i.scrollStrategies.block()}});var $o=0,ni=(()=>{class i{get openDialogs(){return this._parentDialog?this._parentDialog.openDialogs:this._openDialogsAtThisLevel}get afterOpened(){return this._parentDialog?this._parentDialog.afterOpened:this._afterOpenedAtThisLevel}_getAfterAllClosed(){let t=this._parentDialog;return t?t._getAfterAllClosed():this._afterAllClosedAtThisLevel}constructor(t,e,o,a,r,s,d,m){this._overlay=t,this._defaultOptions=a,this._scrollStrategy=r,this._parentDialog=s,this._openDialogsAtThisLevel=[],this._afterAllClosedAtThisLevel=new g,this._afterOpenedAtThisLevel=new g,this.dialogConfigClass=Qt,this.afterAllClosed=ee(()=>this.openDialogs.length?this._getAfterAllClosed():this._getAfterAllClosed().pipe(Nt(void 0))),this._dialog=e.get(ii),this._dialogRefConstructor=Gt,this._dialogContainerType=Wo,this._dialogDataToken=Xo}open(t,e){let o;e=V(V({},this._defaultOptions||new Qt),e),e.id=e.id||`mat-mdc-dialog-${$o++}`,e.scrollStrategy=e.scrollStrategy||this._scrollStrategy();let a=this._dialog.open(t,qt(V({},e),{positionStrategy:this._overlay.position().global().centerHorizontally().centerVertically(),disableClose:!0,closeOnDestroy:!1,closeOnOverlayDetachments:!1,container:{type:this._dialogContainerType,providers:()=>[{provide:this.dialogConfigClass,useValue:e},{provide:xt,useValue:e}]},templateContext:()=>({dialogRef:o}),providers:(r,s,d)=>(o=new this._dialogRefConstructor(r,e,d),o.updatePosition(e?.position),[{provide:this._dialogContainerType,useValue:d},{provide:this._dialogDataToken,useValue:s.data},{provide:this._dialogRefConstructor,useValue:o}])}));return o.componentRef=a.componentRef,o.componentInstance=a.componentInstance,this.openDialogs.push(o),this.afterOpened.next(o),o.afterClosed().subscribe(()=>{let r=this.openDialogs.indexOf(o);r>-1&&(this.openDialogs.splice(r,1),this.openDialogs.length||this._getAfterAllClosed().next())}),o}closeAll(){this._closeDialogs(this.openDialogs)}getDialogById(t){return this.openDialogs.find(e=>e.id===t)}ngOnDestroy(){this._closeDialogs(this._openDialogsAtThisLevel),this._afterAllClosedAtThisLevel.complete(),this._afterOpenedAtThisLevel.complete()}_closeDialogs(t){let e=t.length;for(;e--;)t[e].close()}static{this.\u0275fac=function(e){return new(e||i)(h(G),h(K),h(ne,8),h(Zo,8),h(Uo),h(i,12),h(jt),h(lt,8))}}static{this.\u0275prov=k({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),Qo=0,Zr=(()=>{class i{constructor(t,e,o){this.dialogRef=t,this._elementRef=e,this._dialog=o,this.type="button"}ngOnInit(){this.dialogRef||(this.dialogRef=Ui(this._elementRef,this._dialog.openDialogs))}ngOnChanges(t){let e=t._matDialogClose||t._matDialogCloseResult;e&&(this.dialogResult=e.currentValue)}_onButtonClick(t){Xi(this.dialogRef,t.screenX===0&&t.screenY===0?"keyboard":"mouse",this.dialogResult)}static{this.\u0275fac=function(e){return new(e||i)(c(Gt,8),c(v),c(ni))}}static{this.\u0275dir=p({type:i,selectors:[["","mat-dialog-close",""],["","matDialogClose",""]],hostVars:2,hostBindings:function(e,o){e&1&&mi("click",function(r){return o._onButtonClick(r)}),e&2&&ut("aria-label",o.ariaLabel||null)("type",o.type)},inputs:{ariaLabel:[u.None,"aria-label","ariaLabel"],type:"type",dialogResult:[u.None,"mat-dialog-close","dialogResult"],_matDialogClose:[u.None,"matDialogClose","_matDialogClose"]},exportAs:["matDialogClose"],standalone:!0,features:[nt]})}}return i})(),Zi=(()=>{class i{constructor(t,e,o){this._dialogRef=t,this._elementRef=e,this._dialog=o}ngOnInit(){this._dialogRef||(this._dialogRef=Ui(this._elementRef,this._dialog.openDialogs)),this._dialogRef&&Promise.resolve().then(()=>{this._onAdd()})}ngOnDestroy(){this._dialogRef?._containerInstance&&Promise.resolve().then(()=>{this._onRemove()})}static{this.\u0275fac=function(e){return new(e||i)(c(Gt,8),c(v),c(ni))}}static{this.\u0275dir=p({type:i,standalone:!0})}}return i})(),Ur=(()=>{class i extends Zi{constructor(){super(...arguments),this.id=`mat-mdc-dialog-title-${Qo++}`}_onAdd(){this._dialogRef._containerInstance?._addAriaLabelledBy?.(this.id)}_onRemove(){this._dialogRef?._containerInstance?._removeAriaLabelledBy?.(this.id)}static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["","mat-dialog-title",""],["","matDialogTitle",""]],hostAttrs:[1,"mat-mdc-dialog-title","mdc-dialog__title"],hostVars:1,hostBindings:function(e,o){e&2&&Oe("id",o.id)},inputs:{id:"id"},exportAs:["matDialogTitle"],standalone:!0,features:[b]})}}return i})(),$r=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275dir=p({type:i,selectors:[["","mat-dialog-content",""],["mat-dialog-content"],["","matDialogContent",""]],hostAttrs:[1,"mat-mdc-dialog-content","mdc-dialog__content"],standalone:!0})}}return i})(),Qr=(()=>{class i extends Zi{_onAdd(){this._dialogRef._containerInstance?._updateActionSectionCount?.(1)}_onRemove(){this._dialogRef._containerInstance?._updateActionSectionCount?.(-1)}static{this.\u0275fac=(()=>{let t;return function(o){return(t||(t=D(i)))(o||i)}})()}static{this.\u0275dir=p({type:i,selectors:[["","mat-dialog-actions",""],["mat-dialog-actions"],["","matDialogActions",""]],hostAttrs:[1,"mat-mdc-dialog-actions","mdc-dialog__actions"],hostVars:6,hostBindings:function(e,o){e&2&&U("mat-mdc-dialog-actions-align-start",o.align==="start")("mat-mdc-dialog-actions-align-center",o.align==="center")("mat-mdc-dialog-actions-align-end",o.align==="end")},inputs:{align:"align"},standalone:!0,features:[b]})}}return i})();function Ui(i,n){let t=i.nativeElement.parentElement;for(;t&&!t.classList.contains("mat-mdc-dialog-container");)t=t.parentElement;return t?n.find(e=>e.id===t.id):null}var Gr=(()=>{class i{static{this.\u0275fac=function(e){return new(e||i)}}static{this.\u0275mod=T({type:i})}static{this.\u0275inj=M({providers:[ni],imports:[zi,De,yt,rt,rt]})}}return i})();export{dn as a,ln as b,mn as c,vi as d,bn as e,ue as f,oo as g,Mt as h,yi as i,Tt as j,pt as k,At as l,Na as m,Zt as n,yt as o,Pt as p,Ke as q,G as r,Mo as s,ir as t,De as u,Ii as v,ha as w,fa as x,pa as y,ba as z,ga as A,_a as B,va as C,ya as D,wa as E,xa as F,Ca as G,Da as H,ka as I,Ra as J,Sa as K,Ea as L,Oi as M,Gt as N,Xo as O,ni as P,Zr as Q,Ur as R,$r as S,Qr as T,Gr as U};
