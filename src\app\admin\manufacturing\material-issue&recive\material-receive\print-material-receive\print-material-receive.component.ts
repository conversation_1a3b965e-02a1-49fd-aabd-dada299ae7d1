import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ManufactureService } from '../../../../../services/manufacture.service';

@Component({
  selector: 'app-print-material-receive',
  templateUrl: './print-material-receive.component.html',
  styleUrl: './print-material-receive.component.css'
})
export class PrintMaterialReceiveComponent implements OnInit {
  printId: any;

  orderIssueData: any;
  totalArea: any;
  // Area to display on header: take from MaterialIssue.issueAreaInYard
  areaFromIssue?: string;

  // Material summary state (lagat from first issue doc; issue sum; receive sum; tLagat sum)
  materialIssues: any[] = [];
  materialsTotals: any = {
    kati: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    tana: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    soot: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    thari: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    silk: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    other: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
  };
  recDate?: string;

  // Material receive docs for katiData display
  materialReceives: any[] = [];

  constructor(private route: ActivatedRoute, private manufactureService: ManufactureService) {}

  ngOnInit(): void {
    this.printId = this.route.snapshot.paramMap.get('id') || '';
    if (this.printId) {
      this.loadOrderIssue(this.printId);
      this.loadMaterialIssues(this.printId);
      this.loadMaterialReceives(this.printId);
    }
  }

  loadOrderIssue(id: string) {
    this.manufactureService.getsOrderIssueList().subscribe((res: any) => {
      this.orderIssueData = res.find((x: any) => x._id === id);
      const areaItem = this.orderIssueData?.buyerOrder?.items?.find((x: any) => x._id === this.orderIssueData.itemId);
      this.totalArea = areaItem?.totalArea || 0;
    });
  }

  loadMaterialIssues(issueNoId: string) {
    this.manufactureService.getMaterialIssuesByIssueNo(issueNoId).subscribe((docs: any) => {
      this.materialIssues = Array.isArray(docs) ? docs : [];
      // Set area from issue (yard) if available
      this.areaFromIssue = this.materialIssues[0]?.issueAreaInYard || undefined;
      this.computeMaterialsTotals();
    });
  }

  loadMaterialReceives(issueNoId: string) {
    this.manufactureService.getMaterialReceivesByIssueNo(issueNoId).subscribe((list: any) => {
      const receives = Array.isArray(list) ? list : [];
      this.materialReceives = receives;
      this.recDate = receives.length ? new Date(receives[0].date).toISOString() : undefined;
      // Sum receive and tLagat from receives
      receives.forEach((doc: any) => {
        const m = doc?.materials || {};
        ['kati', 'tana', 'soot', 'thari', 'silk', 'other'].forEach((key: string) => {
          if (!m[key]) return;
          this.materialsTotals[key].receive += this.toNum(m[key].receive);
          this.materialsTotals[key].tLagat += this.toNum(m[key].tLagat);
        });
      });
    });
  }

  private toNum(v: any) {
    const n = parseFloat(v);
    return isNaN(n) ? 0 : n;
  }

  computeMaterialsTotals() {
    const totals: any = {
      kati: { lagat: 0, issue: 0, receive: this.materialsTotals.kati.receive, tLagat: this.materialsTotals.kati.tLagat },
      tana: { lagat: 0, issue: 0, receive: this.materialsTotals.tana.receive, tLagat: this.materialsTotals.tana.tLagat },
      soot: { lagat: 0, issue: 0, receive: this.materialsTotals.soot.receive, tLagat: this.materialsTotals.soot.tLagat },
      thari: { lagat: 0, issue: 0, receive: this.materialsTotals.thari.receive, tLagat: this.materialsTotals.thari.tLagat },
      silk: { lagat: 0, issue: 0, receive: this.materialsTotals.silk.receive, tLagat: this.materialsTotals.silk.tLagat },
      other: { lagat: 0, issue: 0, receive: this.materialsTotals.other.receive, tLagat: this.materialsTotals.other.tLagat },
    };

    const first = this.materialIssues[0]?.materials || {};
    ['kati', 'tana', 'soot', 'thari', 'silk', 'other'].forEach((key: string) => {
      if (first[key]) totals[key].lagat = this.toNum(first[key].lagat);
    });

    this.materialIssues.forEach((doc: any) => {
      const m = doc?.materials || {};
      ['kati', 'tana', 'soot', 'thari', 'silk', 'other'].forEach((key: string) => {
        if (!m[key]) return;
        totals[key].issue += this.toNum(m[key].issue);
      });
    });

    this.materialsTotals = totals;
  }

  printPage() {
    const invoiceEl = document.getElementById('invoice');
    const printContents = invoiceEl ? (invoiceEl as HTMLElement).outerHTML : null;
    if (printContents) {
      const originalContents = document.body.innerHTML;
      document.body.innerHTML = printContents; // keep #invoice wrapper so border shows in print
      window.print();
      document.body.innerHTML = originalContents;
      window.location.reload();
    } else {
      window.print();
    }
  }
}
