<div class="container mt-3">
  <div class="d-flex justify-content-end align-items-center">
        <div class="navigate-text">
          <a href="/admin/material-issue" class="text-end fs-6 fw-bold pe-1 mb-1">Go To Issue</a>
        </div>
        <div>
          <button
            mat-icon-button
            color="primary"
            class="navigate-button mb-2"
            (click)="navigateToMaterialIssue()"
            title="Go to Material Issue">
            <i class="fa-solid fa-arrow-left"></i>
          </button>
        </div>
      </div>

  <section>
    <fieldset>
      <legend>
        <b>{{ isEditMode ? 'Material Receive Update' : 'Material Receive' }}</b>
      </legend>


      <form [formGroup]="materialReceiveForm" class="mt-4">
        <!-- Basic Information Row -->
        <div class="row mb-3">
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Challan No *</mat-label>
              <input matInput formControlName="challanNo">
            </mat-form-field>
          </div>

          <div class="mb-2 col-md-2">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Date</mat-label>
              <input matInput [matDatepicker]="picker" formControlName="date">
              <mat-datepicker-toggle matIconSuffix [for]="picker"></mat-datepicker-toggle>
              <mat-datepicker #picker></mat-datepicker>
            </mat-form-field>
          </div>
          <div class="col-md-3">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Weaver Name *</mat-label>
              <mat-select formControlName="weaver" [disabled]="isEditMode" (selectionChange)="onWeaverChange()">
                <mat-option *ngFor="let weaver of list" [value]="weaver.name">
                  {{ weaver.displayName }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
          <div class="col-md-2">
            <mat-form-field appearance="outline" class="ex-width">
              <mat-label>Issue No *</mat-label>
              <mat-select formControlName="issueNo" [disabled]="isEditMode" (selectionChange)="onIssueChange()">
                <mat-option *ngFor="let issue of availableIssues" [value]="issue.issueNoKey">
                  {{ issue.issueNoDisplay }}
                </mat-option>
              </mat-select>
            </mat-form-field>
          </div>
            <div class="col-md-3 mb-4">
             <button mat-raised-button color="primary"
                     class="challan-details-btn"
                     *ngIf="selectedIssueData"
                     (click)="openChallanDetailsModal()">
                      <mat-icon>visibility</mat-icon>
                      View ({{ selectedIssueData.challanCount }} challan{{ selectedIssueData.challanCount > 1 ? 's' : '' }}) Details
                    </button>
              </div>
           </div>


<!-- First Row: Issue Details -->
    <div class="issue-details-header ">
      <h3>Issue Details</h3>
    </div>
       <!-- Issue Details Table -->
    <div class="issue-details-table-section">
      <table class="issue-details-table">
        <thead>
          <tr>
            <th>Issue No</th>
            <th>Quality</th>
            <th>Design</th>
            <th>Colour</th>
            <th>Size</th>
            <th>Pcs</th>
            <th>Area</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of dataSource.data">
            <td>{{item.issueNo}}</td>
            <td>{{item.quality?.quality || item.quality}}</td>
            <td>{{item.design?.design || item.design}}</td>
            <td>{{item.colour}}</td>
            <td>{{item.size?.sizeInYard || item.size}}</td>
            <td>{{item.pcs}}</td>
            <td>{{item.area}}</td>
          </tr>
          <tr *ngIf="dataSource.data.length === 0">
            <td colspan="8" class="no-data">No data available</td>
          </tr>
        </tbody>
      </table>
    </div>
      </form>
    </fieldset>
  </section>
</div>


 <!-- Material Description & Lagat Section -->
        <div class="container mt-4 mb-4">
          <section>
    <fieldset>
    <!-- Material Description Section -->

      <form [formGroup]="materialReceiveForm" >

          <div class="card-header bg-primary text-white text-center fw-bolder w-100 ">
            <h4 class="mb-0">Material Description & Lagat & Issued</h4>
          </div>

          <div class="card-body" style="border-top: 2px solid #1f497d;margin-top: 5px;">
            <!-- Bootstrap Grid Layout -->
            <div class="container-fluid">


              <!-- Material Columns with Description and Lagat vertically stacked -->
              <div class="row gx-3 col-md-12">
                <div class="coldes">

                   <div class="mt-2 Des-box-hieght">
                  <div class="text-center fw-bold text-primary fs-5  ">Description →</div>
                </div>

                 <div class="mt-2 Lag-box-hieght">
                  <div class="text-center fw-bold text-primary fs-5 pb-1">Lagat →</div>
                </div>

                <div class="Issue-box-hieght">
                  <div class="text-center fw-bold text-primary fs-5 pb-2">Issued →</div>
                </div>

                <div class="Receive-box-hieght">
                  <div class="text-center fw-bold text-primary fs-5 pb-2">Receive →</div>
                </div>
                <div class="Used-box-hieght">
                  <div class="text-center fw-bold text-primary fs-5 pb-2">Total Lagat →</div>
                </div>

                </div>



                <!-- Kati Column -->
                <div class="col21 kati-height">
                <div class="mb-3">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Kati</div>
                </div>

                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                       <mat-label>Kati Description</mat-label>
                <input matInput formControlName="katiDescription" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Kati Lagat</mat-label>
                      <input matInput formControlName="katiLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Kati Issued</mat-label>
                      <input type="text" matInput formControlName="katiIssued" placeholder=".000" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                  <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Kati Receive</mat-label>
                <input matInput type="text" formControlName="katiReceive" placeholder=".000" (click)="openKatiReceivedDetailsModal()" (blur)="setDigitReceive($event, 'katiReceive')" (input)="calculateTLagat('katiReceive')" readonly>
              </mat-form-field>
                    </div>
                    <div>
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Kati T.Lagat</mat-label>
                <input matInput type="text" formControlName="katiTLagat" readonly >
              </mat-form-field>


                  </div>
                </div>

                <!-- Tana Column -->
                <div class="col21">
                   <div class="mb-3 mt-2">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Tana</div>
                </div>
                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tana Description</mat-label>
                      <input matInput formControlName="tanaDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tana Lagat</mat-label>
                      <input matInput formControlName="tanaLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tana Issued</mat-label>
                      <input type="text" matInput formControlName="tanaIssued" readonly>
                    </mat-form-field>
                  </div>
                    <div>
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Tana Receive</mat-label>
                <input matInput type="text" formControlName="tanaReceive" placeholder=".000" (blur)="setDigitReceive($event, 'tanaReceive')" (input)="calculateTLagat('tanaReceive')">
              </mat-form-field>
               </div>
               <div>
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Tana T.Lagat</mat-label>
                <input matInput type="text" formControlName="tanaTLagat" readonly>
              </mat-form-field>
            </div>

                </div>

                <!-- Soot Column -->
                <div class="col21">
                  <div class="mb-3 mt-2">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Soot</div>
                </div>
                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Soot Description</mat-label>
                      <input matInput formControlName="sootDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Soot Lagat</mat-label>
                      <input matInput formControlName="sootLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Soot Issued</mat-label>
                      <input type="text" matInput formControlName="sootIssued" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                     <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Soot Receive</mat-label>
                <input matInput type="text" formControlName="sootReceive" placeholder=".000" (blur)="setDigitReceive($event, 'sootReceive')" (input)="calculateTLagat('sootReceive')">
              </mat-form-field>
                  </div>
                  <div>
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Soot T.Lagat</mat-label>
                <input matInput type="text" formControlName="sootTLagat" readonly>
              </mat-form-field>
                  </div>
                </div>

                <!-- Thari Column -->
                <div class="col21">
                  <div class="mb-3 mt-2">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Thari</div>
                </div>
                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tharri Description</mat-label>
                      <input matInput formControlName="thariDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tharri Lagat</mat-label>
                      <input matInput formControlName="thariLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Tharri Issued</mat-label>
                      <input type="text" matInput formControlName="thariIssued" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                     <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Tharri Receive</mat-label>
                <input matInput type="text" formControlName="thariReceive" placeholder=".000" (blur)="setDigitReceive($event, 'thariReceive')" (input)="calculateTLagat('thariReceive')">
              </mat-form-field>
              </div>
                <div>
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Tharri T.Lagat</mat-label>
                <input matInput type="text" formControlName="thariTLagat" readonly>
              </mat-form-field>
                  </div>
                </div>

                <!-- Silk Column -->
                <div class="col21">
                  <div class="mb-3 mt-2">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Silk</div>
                </div>
                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Silk Description</mat-label>
                      <input matInput formControlName="silkDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Silk T.Lagat</mat-label>
                      <input matInput formControlName="silkLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Silk Issued</mat-label>
                      <input type="text" matInput formControlName="silkIssued" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                     <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Silk Receive</mat-label>
                <input matInput type="text" formControlName="silkReceive" placeholder=".000" (blur)="setDigitReceive($event, 'silkReceive')" (input)="calculateTLagat('silkReceive')">
              </mat-form-field>
              </div>
                   <div>
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Silk T.Lagat</mat-label>
                <input matInput type="text" formControlName="silkTLagat" readonly>
              </mat-form-field>
                  </div>
                </div>
                 <!-- Other Column -->
                <div class="col21">
                 <div class="mb-3 mt-2">
                  <div class="text-center fw-bold text-primary border-bottom pb-2">Other</div>
                </div>
                  <div class="mb-2">
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Other Description</mat-label>
                      <input matInput formControlName="otherDescription" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Other Lagat</mat-label>
                      <input matInput formControlName="otherLagat" type="text" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                    <mat-form-field appearance="outline" class="ex-width">
                      <mat-label>Other Issued</mat-label>
                      <input type="text" matInput formControlName="otherIssued" readonly>
                    </mat-form-field>
                  </div>
                  <div>
                      <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Other Receive</mat-label>
                <input matInput type="text" formControlName="otherReceive" placeholder=".000" (blur)="setDigitReceive($event, 'otherReceive')" (input)="calculateTLagat('otherReceive')">
              </mat-form-field>
              </div>
            <div>
              <mat-form-field appearance="outline" class="ex-width">
                <mat-label>Other T.Lagat</mat-label>
                <input matInput type="text" formControlName="otherTLagat" readonly>
              </mat-form-field>
                  </div>
                </div>

              </div>
            </div>
          </div>

            <!-- Action Buttons -->
        <div class="d-flex justify-content-end text-center mt-3">
          <button mat-raised-button color="primary" type="submit" (click)="onSave()" class="me-2">
            <mat-icon>{{isEditMode ? 'update' : 'save'}}</mat-icon>
           {{ isEditMode ? 'Update' : 'Save' }}
          </button>
          <button mat-raised-button color="accent" type="button" (click)="saveAndPrint()" class="me-4">
            <mat-icon>print</mat-icon>
            Save & Print
          </button>
        </div>


      </form>

    </fieldset>
  </section>


<!-- Kati Data Table (shown after modal Save/Update) -->
<div class="d-flex justify-content-center">
  <div *ngIf="hasKatiRows" class="kati-data-section mt-5 col-md-12">
    <section>
      <fieldset>
        <legend><b>Kati Material Details</b></legend>
        <div class="kati-data-table-container">
          <table mat-table [dataSource]="katiChallanDataSource" class="mat-elevation-z4 kati-data-table">
            <!-- Sr.No Column -->
            <ng-container matColumnDef="srNo">
              <th mat-header-cell *matHeaderCellDef>Sr.No</th>
              <td mat-cell *matCellDef="let element">{{element.srNo}}</td>
              <td mat-footer-cell *matFooterCellDef></td>
            </ng-container>

            <!-- Colour Column -->
            <ng-container matColumnDef="colour">
              <th mat-header-cell *matHeaderCellDef>Colour</th>
              <td mat-cell *matCellDef="let element">{{element.colourText}}</td>
              <td mat-footer-cell *matFooterCellDef><b>Total</b></td>
            </ng-container>

            <!-- Lagat Column -->
            <ng-container matColumnDef="lagat">
              <th mat-header-cell *matHeaderCellDef>Lagat</th>
              <td mat-cell *matCellDef="let element">{{element.lagat | number:'1.3-3'}}</td>
              <td mat-footer-cell *matFooterCellDef>{{ totalKatiChallanLagat | number:'1.3-3' }}</td>
            </ng-container>

            <!-- Carpet Lagat Column -->
            <ng-container matColumnDef="carpetLagat">
              <th mat-header-cell *matHeaderCellDef>Carpet Lagat</th>
              <td mat-cell *matCellDef="let element">{{element.carpetLagat | number:'1.3-3'}}</td>
              <td mat-footer-cell *matFooterCellDef>{{ totalKatiChallanCarpetLagat | number:'1.3-3' }}</td>
            </ng-container>

            <!-- T. Issued Column -->
            <ng-container matColumnDef="issue">
              <th mat-header-cell *matHeaderCellDef>T. Issued</th>
              <td mat-cell *matCellDef="let element">{{element.tIssued | number:'1.3-3'}}</td>
              <td mat-footer-cell *matFooterCellDef>{{ totalKatiChallanIssued | number:'1.3-3' }}</td>
            </ng-container>

            <!-- Receive Column -->
            <ng-container matColumnDef="receive">
              <th mat-header-cell *matHeaderCellDef>Receive</th>
              <td mat-cell *matCellDef="let element">{{element.receive | number:'1.3-3'}}</td>
              <td mat-footer-cell *matFooterCellDef>{{ totalKatiChallanReceive | number:'1.3-3' }}</td>
            </ng-container>

            <!-- Total Lagat Column -->
            <ng-container matColumnDef="totalLagat">
              <th mat-header-cell *matHeaderCellDef>Total Lagat</th>
              <td mat-cell *matCellDef="let element">{{element.totalLagat | number:'1.3-3'}}</td>
              <td mat-footer-cell *matFooterCellDef>{{ totalKatiChallanTotalLagat | number:'1.3-3' }}</td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="katiChallanDisplayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: katiChallanDisplayedColumns;"></tr>
            <tr mat-footer-row *matFooterRowDef="katiChallanDisplayedColumns" class="totals-footer"></tr>
          </table>
        </div>
      </fieldset>
    </section>
  </div>
</div>



        </div>