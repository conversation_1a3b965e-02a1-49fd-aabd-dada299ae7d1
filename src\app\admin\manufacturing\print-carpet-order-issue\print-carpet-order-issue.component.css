/* Page frame and typography to match print-material-issue */
.invoice {
  background: #fff;
  padding: 6mm 2mm;
  border: 2px solid #000;
  box-shadow: 0 0 0 2px #666 inset;
  box-sizing: border-box;
}

.company-header {
  text-align: center;
  padding: 10px 0 10px;
  border-bottom: 2px solid #000;
  margin-bottom: 10px;
}
.company-header .title {
  font-size: 28px;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  text-decoration: underline;
}
.company-header .sub {
  font-size: 16px;
  padding-top: 5px;
}

/* Print tweaks */
@media print {
  @page { size: A4; margin: 6mm 5mm; }
  html, body { background: #fff; width: 210mm; height: 297mm; margin: 0; padding: 0; }
  /* Use block layout in print so page breaks work */
  .invoice { box-shadow: none; min-height: calc(297mm - 11mm); display: block; }
  .invoice > section:last-of-type { margin-bottom: 0 !important; }
  .page-break-before { page-break-before: always !important; break-before: page !important; }
  .page-break-after { page-break-after: always !important; break-after: page !important; }
  .avoid-break-inside { page-break-inside: avoid; break-inside: avoid; }
}

.text-right{
    text-align: end;
  }

       .text-center {
              text-align: center!important;
          }

          .py-5 {
              padding-top: 3rem!important;
              padding-bottom: 3rem!important;
          }

          .mt-5 {
              margin-top: 3rem!important;
          }

          .my-5 {
              margin-top: 3rem!important;
              margin-bottom: 3rem!important;
          }

          table {
              width: 100%;
              height: 10px;
              text-transform: capitalize;
          }

          .tablecontent2 {
              border-bottom: 2px solid black;
              border-top: 2px solid black;
              padding: 15px 0px;
              text-align: center;
          }

          .tablecontent3 {
              border: 2px solid black;
              text-align: start;
          }
          .tablecontent3 td{
             padding-left : 5px;
             border: 2px solid black;
              text-align: start;
          }

          .tablecontent4 {
              border-top: 2px solid rgb(0, 0, 0);

          }

          .tableborder {
            border: 2px solid rgb(0, 0, 0) !important;
            padding: 10px !important;
          }





          .tablecontent5 th {
              padding: 7px;
              border-right: 2px solid black;
          }

          .paragraph5 {
              font-weight: bold;
          }

          .container-fluid {
              width: 100%;
              margin-right: auto;
              margin-left: auto;
          }


/* Kati section emphasis */
.kati-title{ font-size: 20px; font-weight: 500; text-decoration: underline;}
.kati-table{ border:2px solid #000; border-collapse: collapse; }
.kati-table th, .kati-table td{ border:2px solid #000; padding: 6px 8px; }
.info-chip{ margin-left:10px; width: 20px;
    height: 20px;
    line-height: 20px;
    font-size: 16px;
    background-color: #3f51b5;
    color: white;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 4px; display:inline-flex; align-items:center; justify-content:center; }
.info-chip mat-icon{ font-size:14px; height:14px; width:14px; }
@media print{ .info-chip{ display:none !important; } }


