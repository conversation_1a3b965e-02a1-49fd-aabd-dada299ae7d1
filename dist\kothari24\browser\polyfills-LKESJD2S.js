(function(e){let t=e.performance;function c(H){t&&t.mark&&t.mark(H)}function s(H,r){t&&t.measure&&t.measure(H,r)}c("Zone");let a=e.__Zone_symbol_prefix||"__zone_symbol__";function l(H){return a+H}let y=e[l("forceDuplicateZoneCheck")]===!0;if(e.Zone){if(y||typeof e.Zone.__symbol__!="function")throw new Error("Zone already loaded.");return e.Zone}class _{static{this.__symbol__=l}static assertZonePatched(){if(e.Promise!==se.ZoneAwarePromise)throw new Error("Zone.js has detected that ZoneAwarePromise `(window|global).Promise` has been overwritten.\nMost likely cause is that a Promise polyfill has been loaded after Zone.js (Polyfilling Promise api is not necessary when zone.js is loaded. If you must load one, do so before loading zone.js.)")}static get root(){let r=_.current;for(;r.parent;)r=r.parent;return r}static get current(){return U.zone}static get currentTask(){return re}static __load_patch(r,n,o=!1){if(se.hasOwnProperty(r)){if(!o&&y)throw Error("Already loaded patch: "+r)}else if(!e["__Zone_disable_"+r]){let v="Zone:"+r;c(v),se[r]=n(e,_,z),s(v,v)}}get parent(){return this._parent}get name(){return this._name}constructor(r,n){this._parent=r,this._name=n?n.name||"unnamed":"<root>",this._properties=n&&n.properties||{},this._zoneDelegate=new k(this,this._parent&&this._parent._zoneDelegate,n)}get(r){let n=this.getZoneWith(r);if(n)return n._properties[r]}getZoneWith(r){let n=this;for(;n;){if(n._properties.hasOwnProperty(r))return n;n=n._parent}return null}fork(r){if(!r)throw new Error("ZoneSpec required!");return this._zoneDelegate.fork(this,r)}wrap(r,n){if(typeof r!="function")throw new Error("Expecting function got: "+r);let o=this._zoneDelegate.intercept(this,r,n),v=this;return function(){return v.runGuarded(o,this,arguments,n)}}run(r,n,o,v){U={parent:U,zone:this};try{return this._zoneDelegate.invoke(this,r,n,o,v)}finally{U=U.parent}}runGuarded(r,n=null,o,v){U={parent:U,zone:this};try{try{return this._zoneDelegate.invoke(this,r,n,o,v)}catch(G){if(this._zoneDelegate.handleError(this,G))throw G}}finally{U=U.parent}}runTask(r,n,o){if(r.zone!=this)throw new Error("A task can only be run in the zone of creation! (Creation: "+(r.zone||$).name+"; Execution: "+this.name+")");if(r.state===A&&(r.type===K||r.type===P))return;let v=r.state!=T;v&&r._transitionTo(T,M),r.runCount++;let G=re;re=r,U={parent:U,zone:this};try{r.type==P&&r.data&&!r.data.isPeriodic&&(r.cancelFn=void 0);try{return this._zoneDelegate.invokeTask(this,r,n,o)}catch(te){if(this._zoneDelegate.handleError(this,te))throw te}}finally{r.state!==A&&r.state!==d&&(r.type==K||r.data&&r.data.isPeriodic?v&&r._transitionTo(M,T):(r.runCount=0,this._updateTaskCount(r,-1),v&&r._transitionTo(A,T,A))),U=U.parent,re=G}}scheduleTask(r){if(r.zone&&r.zone!==this){let o=this;for(;o;){if(o===r.zone)throw Error(`can not reschedule task to ${this.name} which is descendants of the original zone ${r.zone.name}`);o=o.parent}}r._transitionTo(X,A);let n=[];r._zoneDelegates=n,r._zone=this;try{r=this._zoneDelegate.scheduleTask(this,r)}catch(o){throw r._transitionTo(d,X,A),this._zoneDelegate.handleError(this,o),o}return r._zoneDelegates===n&&this._updateTaskCount(r,1),r.state==X&&r._transitionTo(M,X),r}scheduleMicroTask(r,n,o,v){return this.scheduleTask(new m(N,r,n,o,v,void 0))}scheduleMacroTask(r,n,o,v,G){return this.scheduleTask(new m(P,r,n,o,v,G))}scheduleEventTask(r,n,o,v,G){return this.scheduleTask(new m(K,r,n,o,v,G))}cancelTask(r){if(r.zone!=this)throw new Error("A task can only be cancelled in the zone of creation! (Creation: "+(r.zone||$).name+"; Execution: "+this.name+")");if(!(r.state!==M&&r.state!==T)){r._transitionTo(x,M,T);try{this._zoneDelegate.cancelTask(this,r)}catch(n){throw r._transitionTo(d,x),this._zoneDelegate.handleError(this,n),n}return this._updateTaskCount(r,-1),r._transitionTo(A,x),r.runCount=0,r}}_updateTaskCount(r,n){let o=r._zoneDelegates;n==-1&&(r._zoneDelegates=null);for(let v=0;v<o.length;v++)o[v]._updateTaskCount(r.type,n)}}let b={name:"",onHasTask:(H,r,n,o)=>H.hasTask(n,o),onScheduleTask:(H,r,n,o)=>H.scheduleTask(n,o),onInvokeTask:(H,r,n,o,v,G)=>H.invokeTask(n,o,v,G),onCancelTask:(H,r,n,o)=>H.cancelTask(n,o)};class k{constructor(r,n,o){this._taskCounts={microTask:0,macroTask:0,eventTask:0},this.zone=r,this._parentDelegate=n,this._forkZS=o&&(o&&o.onFork?o:n._forkZS),this._forkDlgt=o&&(o.onFork?n:n._forkDlgt),this._forkCurrZone=o&&(o.onFork?this.zone:n._forkCurrZone),this._interceptZS=o&&(o.onIntercept?o:n._interceptZS),this._interceptDlgt=o&&(o.onIntercept?n:n._interceptDlgt),this._interceptCurrZone=o&&(o.onIntercept?this.zone:n._interceptCurrZone),this._invokeZS=o&&(o.onInvoke?o:n._invokeZS),this._invokeDlgt=o&&(o.onInvoke?n:n._invokeDlgt),this._invokeCurrZone=o&&(o.onInvoke?this.zone:n._invokeCurrZone),this._handleErrorZS=o&&(o.onHandleError?o:n._handleErrorZS),this._handleErrorDlgt=o&&(o.onHandleError?n:n._handleErrorDlgt),this._handleErrorCurrZone=o&&(o.onHandleError?this.zone:n._handleErrorCurrZone),this._scheduleTaskZS=o&&(o.onScheduleTask?o:n._scheduleTaskZS),this._scheduleTaskDlgt=o&&(o.onScheduleTask?n:n._scheduleTaskDlgt),this._scheduleTaskCurrZone=o&&(o.onScheduleTask?this.zone:n._scheduleTaskCurrZone),this._invokeTaskZS=o&&(o.onInvokeTask?o:n._invokeTaskZS),this._invokeTaskDlgt=o&&(o.onInvokeTask?n:n._invokeTaskDlgt),this._invokeTaskCurrZone=o&&(o.onInvokeTask?this.zone:n._invokeTaskCurrZone),this._cancelTaskZS=o&&(o.onCancelTask?o:n._cancelTaskZS),this._cancelTaskDlgt=o&&(o.onCancelTask?n:n._cancelTaskDlgt),this._cancelTaskCurrZone=o&&(o.onCancelTask?this.zone:n._cancelTaskCurrZone),this._hasTaskZS=null,this._hasTaskDlgt=null,this._hasTaskDlgtOwner=null,this._hasTaskCurrZone=null;let v=o&&o.onHasTask,G=n&&n._hasTaskZS;(v||G)&&(this._hasTaskZS=v?o:b,this._hasTaskDlgt=n,this._hasTaskDlgtOwner=this,this._hasTaskCurrZone=r,o.onScheduleTask||(this._scheduleTaskZS=b,this._scheduleTaskDlgt=n,this._scheduleTaskCurrZone=this.zone),o.onInvokeTask||(this._invokeTaskZS=b,this._invokeTaskDlgt=n,this._invokeTaskCurrZone=this.zone),o.onCancelTask||(this._cancelTaskZS=b,this._cancelTaskDlgt=n,this._cancelTaskCurrZone=this.zone))}fork(r,n){return this._forkZS?this._forkZS.onFork(this._forkDlgt,this.zone,r,n):new _(r,n)}intercept(r,n,o){return this._interceptZS?this._interceptZS.onIntercept(this._interceptDlgt,this._interceptCurrZone,r,n,o):n}invoke(r,n,o,v,G){return this._invokeZS?this._invokeZS.onInvoke(this._invokeDlgt,this._invokeCurrZone,r,n,o,v,G):n.apply(o,v)}handleError(r,n){return this._handleErrorZS?this._handleErrorZS.onHandleError(this._handleErrorDlgt,this._handleErrorCurrZone,r,n):!0}scheduleTask(r,n){let o=n;if(this._scheduleTaskZS)this._hasTaskZS&&o._zoneDelegates.push(this._hasTaskDlgtOwner),o=this._scheduleTaskZS.onScheduleTask(this._scheduleTaskDlgt,this._scheduleTaskCurrZone,r,n),o||(o=n);else if(n.scheduleFn)n.scheduleFn(n);else if(n.type==N)R(n);else throw new Error("Task is missing scheduleFn.");return o}invokeTask(r,n,o,v){return this._invokeTaskZS?this._invokeTaskZS.onInvokeTask(this._invokeTaskDlgt,this._invokeTaskCurrZone,r,n,o,v):n.callback.apply(o,v)}cancelTask(r,n){let o;if(this._cancelTaskZS)o=this._cancelTaskZS.onCancelTask(this._cancelTaskDlgt,this._cancelTaskCurrZone,r,n);else{if(!n.cancelFn)throw Error("Task is not cancelable");o=n.cancelFn(n)}return o}hasTask(r,n){try{this._hasTaskZS&&this._hasTaskZS.onHasTask(this._hasTaskDlgt,this._hasTaskCurrZone,r,n)}catch(o){this.handleError(r,o)}}_updateTaskCount(r,n){let o=this._taskCounts,v=o[r],G=o[r]=v+n;if(G<0)throw new Error("More tasks executed then were scheduled.");if(v==0||G==0){let te={microTask:o.microTask>0,macroTask:o.macroTask>0,eventTask:o.eventTask>0,change:r};this.hasTask(this.zone,te)}}}class m{constructor(r,n,o,v,G,te){if(this._zone=null,this.runCount=0,this._zoneDelegates=null,this._state="notScheduled",this.type=r,this.source=n,this.data=v,this.scheduleFn=G,this.cancelFn=te,!o)throw new Error("callback is not defined");this.callback=o;let f=this;r===K&&v&&v.useG?this.invoke=m.invokeTask:this.invoke=function(){return m.invokeTask.call(e,f,this,arguments)}}static invokeTask(r,n,o){r||(r=this),ee++;try{return r.runCount++,r.zone.runTask(r,n,o)}finally{ee==1&&E(),ee--}}get zone(){return this._zone}get state(){return this._state}cancelScheduleRequest(){this._transitionTo(A,X)}_transitionTo(r,n,o){if(this._state===n||this._state===o)this._state=r,r==A&&(this._zoneDelegates=null);else throw new Error(`${this.type} '${this.source}': can not transition to '${r}', expecting state '${n}'${o?" or '"+o+"'":""}, was '${this._state}'.`)}toString(){return this.data&&typeof this.data.handleId<"u"?this.data.handleId.toString():Object.prototype.toString.call(this)}toJSON(){return{type:this.type,state:this.state,source:this.source,zone:this.zone.name,runCount:this.runCount}}}let I=l("setTimeout"),Z=l("Promise"),O=l("then"),B=[],L=!1,J;function q(H){if(J||e[Z]&&(J=e[Z].resolve(0)),J){let r=J[O];r||(r=J.then),r.call(J,H)}else e[I](H,0)}function R(H){ee===0&&B.length===0&&q(E),H&&B.push(H)}function E(){if(!L){for(L=!0;B.length;){let H=B;B=[];for(let r=0;r<H.length;r++){let n=H[r];try{n.zone.runTask(n,null,null)}catch(o){z.onUnhandledError(o)}}}z.microtaskDrainDone(),L=!1}}let $={name:"NO ZONE"},A="notScheduled",X="scheduling",M="scheduled",T="running",x="canceling",d="unknown",N="microTask",P="macroTask",K="eventTask",se={},z={symbol:l,currentZoneFrame:()=>U,onUnhandledError:W,microtaskDrainDone:W,scheduleMicroTask:R,showUncaughtError:()=>!_[l("ignoreConsoleErrorUncaughtError")],patchEventTarget:()=>[],patchOnProperties:W,patchMethod:()=>W,bindArguments:()=>[],patchThen:()=>W,patchMacroTask:()=>W,patchEventPrototype:()=>W,isIEOrEdge:()=>!1,getGlobalObjects:()=>{},ObjectDefineProperty:()=>W,ObjectGetOwnPropertyDescriptor:()=>{},ObjectCreate:()=>{},ArraySlice:()=>[],patchClass:()=>W,wrapWithCurrentZone:()=>W,filterProperties:()=>[],attachOriginToPatched:()=>W,_redefineProperty:()=>W,patchCallbacks:()=>W,nativeScheduleMicroTask:q},U={parent:null,zone:new _(null,null)},re=null,ee=0;function W(){}return s("Zone","Zone"),e.Zone=_})(globalThis);var pe=Object.getOwnPropertyDescriptor,Ie=Object.defineProperty,Me=Object.getPrototypeOf,ct=Object.create,at=Array.prototype.slice,Le="addEventListener",je="removeEventListener",De=Zone.__symbol__(Le),Ze=Zone.__symbol__(je),ce="true",ae="false",ge=Zone.__symbol__("");function Ae(e,t){return Zone.current.wrap(e,t)}function He(e,t,c,s,a){return Zone.current.scheduleMacroTask(e,t,c,s,a)}var j=Zone.__symbol__,we=typeof window<"u",Te=we?window:void 0,Y=we&&Te||globalThis,lt="removeAttribute";function xe(e,t){for(let c=e.length-1;c>=0;c--)typeof e[c]=="function"&&(e[c]=Ae(e[c],t+"_"+c));return e}function ut(e,t){let c=e.constructor.name;for(let s=0;s<t.length;s++){let a=t[s],l=e[a];if(l){let y=pe(e,a);if(!$e(y))continue;e[a]=(_=>{let b=function(){return _.apply(this,xe(arguments,c+"."+a))};return le(b,_),b})(l)}}}function $e(e){return e?e.writable===!1?!1:!(typeof e.get=="function"&&typeof e.set>"u"):!0}var Je=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope,Re=!("nw"in Y)&&typeof Y.process<"u"&&{}.toString.call(Y.process)==="[object process]",Ge=!Re&&!Je&&!!(we&&Te.HTMLElement),Ke=typeof Y.process<"u"&&{}.toString.call(Y.process)==="[object process]"&&!Je&&!!(we&&Te.HTMLElement),Pe={},qe=function(e){if(e=e||Y.event,!e)return;let t=Pe[e.type];t||(t=Pe[e.type]=j("ON_PROPERTY"+e.type));let c=this||e.target||Y,s=c[t],a;if(Ge&&c===Te&&e.type==="error"){let l=e;a=s&&s.call(this,l.message,l.filename,l.lineno,l.colno,l.error),a===!0&&e.preventDefault()}else a=s&&s.apply(this,arguments),a!=null&&!a&&e.preventDefault();return a};function Xe(e,t,c){let s=pe(e,t);if(!s&&c&&pe(c,t)&&(s={enumerable:!0,configurable:!0}),!s||!s.configurable)return;let a=j("on"+t+"patched");if(e.hasOwnProperty(a)&&e[a])return;delete s.writable,delete s.value;let l=s.get,y=s.set,_=t.slice(2),b=Pe[_];b||(b=Pe[_]=j("ON_PROPERTY"+_)),s.set=function(k){let m=this;if(!m&&e===Y&&(m=Y),!m)return;typeof m[b]=="function"&&m.removeEventListener(_,qe),y&&y.call(m,null),m[b]=k,typeof k=="function"&&m.addEventListener(_,qe,!1)},s.get=function(){let k=this;if(!k&&e===Y&&(k=Y),!k)return null;let m=k[b];if(m)return m;if(l){let I=l.call(this);if(I)return s.set.call(this,I),typeof k[lt]=="function"&&k.removeAttribute(t),I}return null},Ie(e,t,s),e[a]=!0}function Qe(e,t,c){if(t)for(let s=0;s<t.length;s++)Xe(e,"on"+t[s],c);else{let s=[];for(let a in e)a.slice(0,2)=="on"&&s.push(a);for(let a=0;a<s.length;a++)Xe(e,s[a],c)}}var oe=j("originalInstance");function ke(e){let t=Y[e];if(!t)return;Y[j(e)]=t,Y[e]=function(){let a=xe(arguments,e);switch(a.length){case 0:this[oe]=new t;break;case 1:this[oe]=new t(a[0]);break;case 2:this[oe]=new t(a[0],a[1]);break;case 3:this[oe]=new t(a[0],a[1],a[2]);break;case 4:this[oe]=new t(a[0],a[1],a[2],a[3]);break;default:throw new Error("Arg list too long.")}},le(Y[e],t);let c=new t(function(){}),s;for(s in c)e==="XMLHttpRequest"&&s==="responseBlob"||function(a){typeof c[a]=="function"?Y[e].prototype[a]=function(){return this[oe][a].apply(this[oe],arguments)}:Ie(Y[e].prototype,a,{set:function(l){typeof l=="function"?(this[oe][a]=Ae(l,e+"."+a),le(this[oe][a],l)):this[oe][a]=l},get:function(){return this[oe][a]}})}(s);for(s in t)s!=="prototype"&&t.hasOwnProperty(s)&&(Y[e][s]=t[s])}function ue(e,t,c){let s=e;for(;s&&!s.hasOwnProperty(t);)s=Me(s);!s&&e[t]&&(s=e);let a=j(t),l=null;if(s&&(!(l=s[a])||!s.hasOwnProperty(a))){l=s[a]=s[t];let y=s&&pe(s,t);if($e(y)){let _=c(l,a,t);s[t]=function(){return _(this,arguments)},le(s[t],l)}}return l}function ft(e,t,c){let s=null;function a(l){let y=l.data;return y.args[y.cbIdx]=function(){l.invoke.apply(this,arguments)},s.apply(y.target,y.args),l}s=ue(e,t,l=>function(y,_){let b=c(y,_);return b.cbIdx>=0&&typeof _[b.cbIdx]=="function"?He(b.name,_[b.cbIdx],b,a):l.apply(y,_)})}function le(e,t){e[j("OriginalDelegate")]=t}var ze=!1,Oe=!1;function ht(){try{let e=Te.navigator.userAgent;if(e.indexOf("MSIE ")!==-1||e.indexOf("Trident/")!==-1)return!0}catch{}return!1}function dt(){if(ze)return Oe;ze=!0;try{let e=Te.navigator.userAgent;(e.indexOf("MSIE ")!==-1||e.indexOf("Trident/")!==-1||e.indexOf("Edge/")!==-1)&&(Oe=!0)}catch{}return Oe}Zone.__load_patch("ZoneAwarePromise",(e,t,c)=>{let s=Object.getOwnPropertyDescriptor,a=Object.defineProperty;function l(f){if(f&&f.toString===Object.prototype.toString){let u=f.constructor&&f.constructor.name;return(u||"")+": "+JSON.stringify(f)}return f?f.toString():Object.prototype.toString.call(f)}let y=c.symbol,_=[],b=e[y("DISABLE_WRAPPING_UNCAUGHT_PROMISE_REJECTION")]!==!1,k=y("Promise"),m=y("then"),I="__creationTrace__";c.onUnhandledError=f=>{if(c.showUncaughtError()){let u=f&&f.rejection;u?console.error("Unhandled Promise rejection:",u instanceof Error?u.message:u,"; Zone:",f.zone.name,"; Task:",f.task&&f.task.source,"; Value:",u,u instanceof Error?u.stack:void 0):console.error(f)}},c.microtaskDrainDone=()=>{for(;_.length;){let f=_.shift();try{f.zone.runGuarded(()=>{throw f.throwOriginal?f.rejection:f})}catch(u){O(u)}}};let Z=y("unhandledPromiseRejectionHandler");function O(f){c.onUnhandledError(f);try{let u=t[Z];typeof u=="function"&&u.call(this,f)}catch{}}function B(f){return f&&f.then}function L(f){return f}function J(f){return n.reject(f)}let q=y("state"),R=y("value"),E=y("finally"),$=y("parentPromiseValue"),A=y("parentPromiseState"),X="Promise.then",M=null,T=!0,x=!1,d=0;function N(f,u){return i=>{try{z(f,u,i)}catch(h){z(f,!1,h)}}}let P=function(){let f=!1;return function(i){return function(){f||(f=!0,i.apply(null,arguments))}}},K="Promise resolved with itself",se=y("currentTaskTrace");function z(f,u,i){let h=P();if(f===i)throw new TypeError(K);if(f[q]===M){let g=null;try{(typeof i=="object"||typeof i=="function")&&(g=i&&i.then)}catch(w){return h(()=>{z(f,!1,w)})(),f}if(u!==x&&i instanceof n&&i.hasOwnProperty(q)&&i.hasOwnProperty(R)&&i[q]!==M)re(i),z(f,i[q],i[R]);else if(u!==x&&typeof g=="function")try{g.call(i,h(N(f,u)),h(N(f,!1)))}catch(w){h(()=>{z(f,!1,w)})()}else{f[q]=u;let w=f[R];if(f[R]=i,f[E]===E&&u===T&&(f[q]=f[A],f[R]=f[$]),u===x&&i instanceof Error){let p=t.currentTask&&t.currentTask.data&&t.currentTask.data[I];p&&a(i,se,{configurable:!0,enumerable:!1,writable:!0,value:p})}for(let p=0;p<w.length;)ee(f,w[p++],w[p++],w[p++],w[p++]);if(w.length==0&&u==x){f[q]=d;let p=i;try{throw new Error("Uncaught (in promise): "+l(i)+(i&&i.stack?`
`+i.stack:""))}catch(C){p=C}b&&(p.throwOriginal=!0),p.rejection=i,p.promise=f,p.zone=t.current,p.task=t.currentTask,_.push(p),c.scheduleMicroTask()}}}return f}let U=y("rejectionHandledHandler");function re(f){if(f[q]===d){try{let u=t[U];u&&typeof u=="function"&&u.call(this,{rejection:f[R],promise:f})}catch{}f[q]=x;for(let u=0;u<_.length;u++)f===_[u].promise&&_.splice(u,1)}}function ee(f,u,i,h,g){re(f);let w=f[q],p=w?typeof h=="function"?h:L:typeof g=="function"?g:J;u.scheduleMicroTask(X,()=>{try{let C=f[R],S=!!i&&E===i[E];S&&(i[$]=C,i[A]=w);let D=u.run(p,void 0,S&&p!==J&&p!==L?[]:[C]);z(i,!0,D)}catch(C){z(i,!1,C)}},i)}let W="function ZoneAwarePromise() { [native code] }",H=function(){},r=e.AggregateError;class n{static toString(){return W}static resolve(u){return u instanceof n?u:z(new this(null),T,u)}static reject(u){return z(new this(null),x,u)}static withResolvers(){let u={};return u.promise=new n((i,h)=>{u.resolve=i,u.reject=h}),u}static any(u){if(!u||typeof u[Symbol.iterator]!="function")return Promise.reject(new r([],"All promises were rejected"));let i=[],h=0;try{for(let p of u)h++,i.push(n.resolve(p))}catch{return Promise.reject(new r([],"All promises were rejected"))}if(h===0)return Promise.reject(new r([],"All promises were rejected"));let g=!1,w=[];return new n((p,C)=>{for(let S=0;S<i.length;S++)i[S].then(D=>{g||(g=!0,p(D))},D=>{w.push(D),h--,h===0&&(g=!0,C(new r(w,"All promises were rejected")))})})}static race(u){let i,h,g=new this((C,S)=>{i=C,h=S});function w(C){i(C)}function p(C){h(C)}for(let C of u)B(C)||(C=this.resolve(C)),C.then(w,p);return g}static all(u){return n.allWithCallback(u)}static allSettled(u){return(this&&this.prototype instanceof n?this:n).allWithCallback(u,{thenCallback:h=>({status:"fulfilled",value:h}),errorCallback:h=>({status:"rejected",reason:h})})}static allWithCallback(u,i){let h,g,w=new this((D,V)=>{h=D,g=V}),p=2,C=0,S=[];for(let D of u){B(D)||(D=this.resolve(D));let V=C;try{D.then(F=>{S[V]=i?i.thenCallback(F):F,p--,p===0&&h(S)},F=>{i?(S[V]=i.errorCallback(F),p--,p===0&&h(S)):g(F)})}catch(F){g(F)}p++,C++}return p-=2,p===0&&h(S),w}constructor(u){let i=this;if(!(i instanceof n))throw new Error("Must be an instanceof Promise.");i[q]=M,i[R]=[];try{let h=P();u&&u(h(N(i,T)),h(N(i,x)))}catch(h){z(i,!1,h)}}get[Symbol.toStringTag](){return"Promise"}get[Symbol.species](){return n}then(u,i){let h=this.constructor?.[Symbol.species];(!h||typeof h!="function")&&(h=this.constructor||n);let g=new h(H),w=t.current;return this[q]==M?this[R].push(w,g,u,i):ee(this,w,g,u,i),g}catch(u){return this.then(null,u)}finally(u){let i=this.constructor?.[Symbol.species];(!i||typeof i!="function")&&(i=n);let h=new i(H);h[E]=E;let g=t.current;return this[q]==M?this[R].push(g,h,u,u):ee(this,g,h,u,u),h}}n.resolve=n.resolve,n.reject=n.reject,n.race=n.race,n.all=n.all;let o=e[k]=e.Promise;e.Promise=n;let v=y("thenPatched");function G(f){let u=f.prototype,i=s(u,"then");if(i&&(i.writable===!1||!i.configurable))return;let h=u.then;u[m]=h,f.prototype.then=function(g,w){return new n((C,S)=>{h.call(this,C,S)}).then(g,w)},f[v]=!0}c.patchThen=G;function te(f){return function(u,i){let h=f.apply(u,i);if(h instanceof n)return h;let g=h.constructor;return g[v]||G(g),h}}return o&&(G(o),ue(e,"fetch",f=>te(f))),Promise[t.__symbol__("uncaughtPromiseErrors")]=_,n});Zone.__load_patch("toString",e=>{let t=Function.prototype.toString,c=j("OriginalDelegate"),s=j("Promise"),a=j("Error"),l=function(){if(typeof this=="function"){let k=this[c];if(k)return typeof k=="function"?t.call(k):Object.prototype.toString.call(k);if(this===Promise){let m=e[s];if(m)return t.call(m)}if(this===Error){let m=e[a];if(m)return t.call(m)}}return t.call(this)};l[c]=t,Function.prototype.toString=l;let y=Object.prototype.toString,_="[object Promise]";Object.prototype.toString=function(){return typeof Promise=="function"&&this instanceof Promise?_:y.call(this)}});var _e=!1;if(typeof window<"u")try{let e=Object.defineProperty({},"passive",{get:function(){_e=!0}});window.addEventListener("test",e,e),window.removeEventListener("test",e,e)}catch{_e=!1}var _t={useG:!0},ne={},et={},tt=new RegExp("^"+ge+"(\\w+)(true|false)$"),nt=j("propagationStopped");function rt(e,t){let c=(t?t(e):e)+ae,s=(t?t(e):e)+ce,a=ge+c,l=ge+s;ne[e]={},ne[e][ae]=a,ne[e][ce]=l}function Et(e,t,c,s){let a=s&&s.add||Le,l=s&&s.rm||je,y=s&&s.listeners||"eventListeners",_=s&&s.rmAll||"removeAllListeners",b=j(a),k="."+a+":",m="prependListener",I="."+m+":",Z=function(R,E,$){if(R.isRemoved)return;let A=R.callback;typeof A=="object"&&A.handleEvent&&(R.callback=T=>A.handleEvent(T),R.originalDelegate=A);let X;try{R.invoke(R,E,[$])}catch(T){X=T}let M=R.options;if(M&&typeof M=="object"&&M.once){let T=R.originalDelegate?R.originalDelegate:R.callback;E[l].call(E,$.type,T,M)}return X};function O(R,E,$){if(E=E||e.event,!E)return;let A=R||E.target||e,X=A[ne[E.type][$?ce:ae]];if(X){let M=[];if(X.length===1){let T=Z(X[0],A,E);T&&M.push(T)}else{let T=X.slice();for(let x=0;x<T.length&&!(E&&E[nt]===!0);x++){let d=Z(T[x],A,E);d&&M.push(d)}}if(M.length===1)throw M[0];for(let T=0;T<M.length;T++){let x=M[T];t.nativeScheduleMicroTask(()=>{throw x})}}}let B=function(R){return O(this,R,!1)},L=function(R){return O(this,R,!0)};function J(R,E){if(!R)return!1;let $=!0;E&&E.useG!==void 0&&($=E.useG);let A=E&&E.vh,X=!0;E&&E.chkDup!==void 0&&(X=E.chkDup);let M=!1;E&&E.rt!==void 0&&(M=E.rt);let T=R;for(;T&&!T.hasOwnProperty(a);)T=Me(T);if(!T&&R[a]&&(T=R),!T||T[b])return!1;let x=E&&E.eventNameToString,d={},N=T[b]=T[a],P=T[j(l)]=T[l],K=T[j(y)]=T[y],se=T[j(_)]=T[_],z;E&&E.prepend&&(z=T[j(E.prepend)]=T[E.prepend]);function U(i,h){return!_e&&typeof i=="object"&&i?!!i.capture:!_e||!h?i:typeof i=="boolean"?{capture:i,passive:!0}:i?typeof i=="object"&&i.passive!==!1?{...i,passive:!0}:i:{passive:!0}}let re=function(i){if(!d.isExisting)return N.call(d.target,d.eventName,d.capture?L:B,d.options)},ee=function(i){if(!i.isRemoved){let h=ne[i.eventName],g;h&&(g=h[i.capture?ce:ae]);let w=g&&i.target[g];if(w){for(let p=0;p<w.length;p++)if(w[p]===i){w.splice(p,1),i.isRemoved=!0,w.length===0&&(i.allRemoved=!0,i.target[g]=null);break}}}if(i.allRemoved)return P.call(i.target,i.eventName,i.capture?L:B,i.options)},W=function(i){return N.call(d.target,d.eventName,i.invoke,d.options)},H=function(i){return z.call(d.target,d.eventName,i.invoke,d.options)},r=function(i){return P.call(i.target,i.eventName,i.invoke,i.options)},n=$?re:W,o=$?ee:r,v=function(i,h){let g=typeof h;return g==="function"&&i.callback===h||g==="object"&&i.originalDelegate===h},G=E&&E.diff?E.diff:v,te=Zone[j("UNPATCHED_EVENTS")],f=e[j("PASSIVE_EVENTS")],u=function(i,h,g,w,p=!1,C=!1){return function(){let S=this||e,D=arguments[0];E&&E.transferEventName&&(D=E.transferEventName(D));let V=arguments[1];if(!V)return i.apply(this,arguments);if(Re&&D==="uncaughtException")return i.apply(this,arguments);let F=!1;if(typeof V!="function"){if(!V.handleEvent)return i.apply(this,arguments);F=!0}if(A&&!A(i,V,S,arguments))return;let fe=_e&&!!f&&f.indexOf(D)!==-1,Q=U(arguments[2],fe),ye=Q&&typeof Q=="object"&&Q.signal&&typeof Q.signal=="object"?Q.signal:void 0;if(ye?.aborted)return;if(te){for(let he=0;he<te.length;he++)if(D===te[he])return fe?i.call(S,D,V,Q):i.apply(this,arguments)}let Ce=Q?typeof Q=="boolean"?!0:Q.capture:!1,Ve=Q&&typeof Q=="object"?Q.once:!1,it=Zone.current,Se=ne[D];Se||(rt(D,x),Se=ne[D]);let Fe=Se[Ce?ce:ae],de=S[Fe],Be=!1;if(de){if(Be=!0,X){for(let he=0;he<de.length;he++)if(G(de[he],V))return}}else de=S[Fe]=[];let ve,Ue=S.constructor.name,We=et[Ue];We&&(ve=We[D]),ve||(ve=Ue+h+(x?x(D):D)),d.options=Q,Ve&&(d.options.once=!1),d.target=S,d.capture=Ce,d.eventName=D,d.isExisting=Be;let me=$?_t:void 0;me&&(me.taskData=d),ye&&(d.options.signal=void 0);let ie=it.scheduleEventTask(ve,V,me,g,w);if(ye&&(d.options.signal=ye,i.call(ye,"abort",()=>{ie.zone.cancelTask(ie)},{once:!0})),d.target=null,me&&(me.taskData=null),Ve&&(Q.once=!0),!_e&&typeof ie.options=="boolean"||(ie.options=Q),ie.target=S,ie.capture=Ce,ie.eventName=D,F&&(ie.originalDelegate=V),C?de.unshift(ie):de.push(ie),p)return S}};return T[a]=u(N,k,n,o,M),z&&(T[m]=u(z,I,H,o,M,!0)),T[l]=function(){let i=this||e,h=arguments[0];E&&E.transferEventName&&(h=E.transferEventName(h));let g=arguments[2],w=g?typeof g=="boolean"?!0:g.capture:!1,p=arguments[1];if(!p)return P.apply(this,arguments);if(A&&!A(P,p,i,arguments))return;let C=ne[h],S;C&&(S=C[w?ce:ae]);let D=S&&i[S];if(D)for(let V=0;V<D.length;V++){let F=D[V];if(G(F,p)){if(D.splice(V,1),F.isRemoved=!0,D.length===0&&(F.allRemoved=!0,i[S]=null,typeof h=="string")){let fe=ge+"ON_PROPERTY"+h;i[fe]=null}return F.zone.cancelTask(F),M?i:void 0}}return P.apply(this,arguments)},T[y]=function(){let i=this||e,h=arguments[0];E&&E.transferEventName&&(h=E.transferEventName(h));let g=[],w=ot(i,x?x(h):h);for(let p=0;p<w.length;p++){let C=w[p],S=C.originalDelegate?C.originalDelegate:C.callback;g.push(S)}return g},T[_]=function(){let i=this||e,h=arguments[0];if(h){E&&E.transferEventName&&(h=E.transferEventName(h));let g=ne[h];if(g){let w=g[ae],p=g[ce],C=i[w],S=i[p];if(C){let D=C.slice();for(let V=0;V<D.length;V++){let F=D[V],fe=F.originalDelegate?F.originalDelegate:F.callback;this[l].call(this,h,fe,F.options)}}if(S){let D=S.slice();for(let V=0;V<D.length;V++){let F=D[V],fe=F.originalDelegate?F.originalDelegate:F.callback;this[l].call(this,h,fe,F.options)}}}}else{let g=Object.keys(i);for(let w=0;w<g.length;w++){let p=g[w],C=tt.exec(p),S=C&&C[1];S&&S!=="removeListener"&&this[_].call(this,S)}this[_].call(this,"removeListener")}if(M)return this},le(T[a],N),le(T[l],P),se&&le(T[_],se),K&&le(T[y],K),!0}let q=[];for(let R=0;R<c.length;R++)q[R]=J(c[R],s);return q}function ot(e,t){if(!t){let l=[];for(let y in e){let _=tt.exec(y),b=_&&_[1];if(b&&(!t||b===t)){let k=e[y];if(k)for(let m=0;m<k.length;m++)l.push(k[m])}}return l}let c=ne[t];c||(rt(t),c=ne[t]);let s=e[c[ae]],a=e[c[ce]];return s?a?s.concat(a):s.slice():a?a.slice():[]}function Tt(e,t){let c=e.Event;c&&c.prototype&&t.patchMethod(c.prototype,"stopImmediatePropagation",s=>function(a,l){a[nt]=!0,s&&s.apply(a,l)})}function yt(e,t,c,s,a){let l=Zone.__symbol__(s);if(t[l])return;let y=t[l]=t[s];t[s]=function(_,b,k){return b&&b.prototype&&a.forEach(function(m){let I=`${c}.${s}::`+m,Z=b.prototype;try{if(Z.hasOwnProperty(m)){let O=e.ObjectGetOwnPropertyDescriptor(Z,m);O&&O.value?(O.value=e.wrapWithCurrentZone(O.value,I),e._redefineProperty(b.prototype,m,O)):Z[m]&&(Z[m]=e.wrapWithCurrentZone(Z[m],I))}else Z[m]&&(Z[m]=e.wrapWithCurrentZone(Z[m],I))}catch{}}),y.call(t,_,b,k)},e.attachOriginToPatched(t[s],y)}function st(e,t,c){if(!c||c.length===0)return t;let s=c.filter(l=>l.target===e);if(!s||s.length===0)return t;let a=s[0].ignoreProperties;return t.filter(l=>a.indexOf(l)===-1)}function Ye(e,t,c,s){if(!e)return;let a=st(e,t,c);Qe(e,a,s)}function Ne(e){return Object.getOwnPropertyNames(e).filter(t=>t.startsWith("on")&&t.length>2).map(t=>t.substring(2))}function mt(e,t){if(Re&&!Ke||Zone[e.symbol("patchEvents")])return;let c=t.__Zone_ignore_on_properties,s=[];if(Ge){let a=window;s=s.concat(["Document","SVGElement","Element","HTMLElement","HTMLBodyElement","HTMLMediaElement","HTMLFrameSetElement","HTMLFrameElement","HTMLIFrameElement","HTMLMarqueeElement","Worker"]);let l=ht()?[{target:a,ignoreProperties:["error"]}]:[];Ye(a,Ne(a),c&&c.concat(l),Me(a))}s=s.concat(["XMLHttpRequest","XMLHttpRequestEventTarget","IDBIndex","IDBRequest","IDBOpenDBRequest","IDBDatabase","IDBTransaction","IDBCursor","WebSocket"]);for(let a=0;a<s.length;a++){let l=t[s[a]];l&&l.prototype&&Ye(l.prototype,Ne(l.prototype),c)}}Zone.__load_patch("util",(e,t,c)=>{let s=Ne(e);c.patchOnProperties=Qe,c.patchMethod=ue,c.bindArguments=xe,c.patchMacroTask=ft;let a=t.__symbol__("BLACK_LISTED_EVENTS"),l=t.__symbol__("UNPATCHED_EVENTS");e[l]&&(e[a]=e[l]),e[a]&&(t[a]=t[l]=e[a]),c.patchEventPrototype=Tt,c.patchEventTarget=Et,c.isIEOrEdge=dt,c.ObjectDefineProperty=Ie,c.ObjectGetOwnPropertyDescriptor=pe,c.ObjectCreate=ct,c.ArraySlice=at,c.patchClass=ke,c.wrapWithCurrentZone=Ae,c.filterProperties=st,c.attachOriginToPatched=le,c._redefineProperty=Object.defineProperty,c.patchCallbacks=yt,c.getGlobalObjects=()=>({globalSources:et,zoneSymbolEventNames:ne,eventNames:s,isBrowser:Ge,isMix:Ke,isNode:Re,TRUE_STR:ce,FALSE_STR:ae,ZONE_SYMBOL_PREFIX:ge,ADD_EVENT_LISTENER_STR:Le,REMOVE_EVENT_LISTENER_STR:je})});function pt(e,t){t.patchMethod(e,"queueMicrotask",c=>function(s,a){Zone.current.scheduleMicroTask("queueMicrotask",a[0])})}var be=j("zoneTask");function Ee(e,t,c,s){let a=null,l=null;t+=s,c+=s;let y={};function _(k){let m=k.data;return m.args[0]=function(){return k.invoke.apply(this,arguments)},m.handleId=a.apply(e,m.args),k}function b(k){return l.call(e,k.data.handleId)}a=ue(e,t,k=>function(m,I){if(typeof I[0]=="function"){let Z={isPeriodic:s==="Interval",delay:s==="Timeout"||s==="Interval"?I[1]||0:void 0,args:I},O=I[0];I[0]=function(){try{return O.apply(this,arguments)}finally{Z.isPeriodic||(typeof Z.handleId=="number"?delete y[Z.handleId]:Z.handleId&&(Z.handleId[be]=null))}};let B=He(t,I[0],Z,_,b);if(!B)return B;let L=B.data.handleId;return typeof L=="number"?y[L]=B:L&&(L[be]=B),L&&L.ref&&L.unref&&typeof L.ref=="function"&&typeof L.unref=="function"&&(B.ref=L.ref.bind(L),B.unref=L.unref.bind(L)),typeof L=="number"||L?L:B}else return k.apply(e,I)}),l=ue(e,c,k=>function(m,I){let Z=I[0],O;typeof Z=="number"?O=y[Z]:(O=Z&&Z[be],O||(O=Z)),O&&typeof O.type=="string"?O.state!=="notScheduled"&&(O.cancelFn&&O.data.isPeriodic||O.runCount===0)&&(typeof Z=="number"?delete y[Z]:Z&&(Z[be]=null),O.zone.cancelTask(O)):k.apply(e,I)})}function gt(e,t){let{isBrowser:c,isMix:s}=t.getGlobalObjects();if(!c&&!s||!e.customElements||!("customElements"in e))return;let a=["connectedCallback","disconnectedCallback","adoptedCallback","attributeChangedCallback","formAssociatedCallback","formDisabledCallback","formResetCallback","formStateRestoreCallback"];t.patchCallbacks(t,e.customElements,"customElements","define",a)}function kt(e,t){if(Zone[t.symbol("patchEventTarget")])return;let{eventNames:c,zoneSymbolEventNames:s,TRUE_STR:a,FALSE_STR:l,ZONE_SYMBOL_PREFIX:y}=t.getGlobalObjects();for(let b=0;b<c.length;b++){let k=c[b],m=k+l,I=k+a,Z=y+m,O=y+I;s[k]={},s[k][l]=Z,s[k][a]=O}let _=e.EventTarget;if(!(!_||!_.prototype))return t.patchEventTarget(e,t,[_&&_.prototype]),!0}function vt(e,t){t.patchEventPrototype(e,t)}Zone.__load_patch("legacy",e=>{let t=e[Zone.__symbol__("legacyPatch")];t&&t()});Zone.__load_patch("timers",e=>{let t="set",c="clear";Ee(e,t,c,"Timeout"),Ee(e,t,c,"Interval"),Ee(e,t,c,"Immediate")});Zone.__load_patch("requestAnimationFrame",e=>{Ee(e,"request","cancel","AnimationFrame"),Ee(e,"mozRequest","mozCancel","AnimationFrame"),Ee(e,"webkitRequest","webkitCancel","AnimationFrame")});Zone.__load_patch("blocking",(e,t)=>{let c=["alert","prompt","confirm"];for(let s=0;s<c.length;s++){let a=c[s];ue(e,a,(l,y,_)=>function(b,k){return t.current.run(l,e,k,_)})}});Zone.__load_patch("EventTarget",(e,t,c)=>{vt(e,c),kt(e,c);let s=e.XMLHttpRequestEventTarget;s&&s.prototype&&c.patchEventTarget(e,c,[s.prototype])});Zone.__load_patch("MutationObserver",(e,t,c)=>{ke("MutationObserver"),ke("WebKitMutationObserver")});Zone.__load_patch("IntersectionObserver",(e,t,c)=>{ke("IntersectionObserver")});Zone.__load_patch("FileReader",(e,t,c)=>{ke("FileReader")});Zone.__load_patch("on_property",(e,t,c)=>{mt(c,e)});Zone.__load_patch("customElements",(e,t,c)=>{gt(e,c)});Zone.__load_patch("XHR",(e,t)=>{b(e);let c=j("xhrTask"),s=j("xhrSync"),a=j("xhrListener"),l=j("xhrScheduled"),y=j("xhrURL"),_=j("xhrErrorBeforeScheduled");function b(k){let m=k.XMLHttpRequest;if(!m)return;let I=m.prototype;function Z(d){return d[c]}let O=I[De],B=I[Ze];if(!O){let d=k.XMLHttpRequestEventTarget;if(d){let N=d.prototype;O=N[De],B=N[Ze]}}let L="readystatechange",J="scheduled";function q(d){let N=d.data,P=N.target;P[l]=!1,P[_]=!1;let K=P[a];O||(O=P[De],B=P[Ze]),K&&B.call(P,L,K);let se=P[a]=()=>{if(P.readyState===P.DONE)if(!N.aborted&&P[l]&&d.state===J){let U=P[t.__symbol__("loadfalse")];if(P.status!==0&&U&&U.length>0){let re=d.invoke;d.invoke=function(){let ee=P[t.__symbol__("loadfalse")];for(let W=0;W<ee.length;W++)ee[W]===d&&ee.splice(W,1);!N.aborted&&d.state===J&&re.call(d)},U.push(d)}else d.invoke()}else!N.aborted&&P[l]===!1&&(P[_]=!0)};return O.call(P,L,se),P[c]||(P[c]=d),T.apply(P,N.args),P[l]=!0,d}function R(){}function E(d){let N=d.data;return N.aborted=!0,x.apply(N.target,N.args)}let $=ue(I,"open",()=>function(d,N){return d[s]=N[2]==!1,d[y]=N[1],$.apply(d,N)}),A="XMLHttpRequest.send",X=j("fetchTaskAborting"),M=j("fetchTaskScheduling"),T=ue(I,"send",()=>function(d,N){if(t.current[M]===!0||d[s])return T.apply(d,N);{let P={target:d,url:d[y],isPeriodic:!1,args:N,aborted:!1},K=He(A,R,P,q,E);d&&d[_]===!0&&!P.aborted&&K.state===J&&K.invoke()}}),x=ue(I,"abort",()=>function(d,N){let P=Z(d);if(P&&typeof P.type=="string"){if(P.cancelFn==null||P.data&&P.data.aborted)return;P.zone.cancelTask(P)}else if(t.current[X]===!0)return x.apply(d,N)})}});Zone.__load_patch("geolocation",e=>{e.navigator&&e.navigator.geolocation&&ut(e.navigator.geolocation,["getCurrentPosition","watchPosition"])});Zone.__load_patch("PromiseRejectionEvent",(e,t)=>{function c(s){return function(a){ot(e,s).forEach(y=>{let _=e.PromiseRejectionEvent;if(_){let b=new _(s,{promise:a.promise,reason:a.rejection});y.invoke(b)}})}}e.PromiseRejectionEvent&&(t[j("unhandledPromiseRejectionHandler")]=c("unhandledrejection"),t[j("rejectionHandledHandler")]=c("rejectionhandled"))});Zone.__load_patch("queueMicrotask",(e,t,c)=>{pt(e,c)});
