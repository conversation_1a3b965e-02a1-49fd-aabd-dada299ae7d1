import{a as fe}from"./chunk-FAK7M7C5.js";import{A as Re,F as Fe,I as Be,N as U,a as xe,b as ve,c as Ce,d as be,e as _e,f as Se,g as Ee,i as Pe,m as Ne}from"./chunk-BFLDFIH4.js";import"./chunk-DP6XK2WQ.js";import"./chunk-FK6H3RFT.js";import{I as Me,J as ye,T as Ie,W as ze,c as me,d as pe,e as ce,f as T,i as ge,j as he,l as ue,m as j}from"./chunk-5HNFHR6K.js";import{L as Ae,U as De,c as we}from"./chunk-FDYESHK5.js";import{N as Oe}from"./chunk-UCMEJ5RL.js";import{$ as O,$a as J,Db as ie,Ec as re,Ga as Z,Gb as oe,Ib as B,Mc as ae,Nc as le,Sb as t,Tb as S,Ub as s,Uc as de,Vb as A,Vc as se,Wb as k,X as $,Xa as d,Ya as C,Za as X,_ as K,bb as ee,db as te,ea as q,eb as ne,ec as h,gc as u,ja as v,ka as w,ma as W,mb as I,ob as N,p as _,ra as Q,sa as Y,xb as e,yb as i,zb as g}from"./chunk-TSVGDZRC.js";import{f as y}from"./chunk-CWTPBX7D.js";var Te=(()=>{class l{static{this.\u0275fac=function(o){return new(o||l)}}static{this.\u0275cmp=v({type:l,selectors:[["app-notfound"]],decls:11,vars:0,consts:[[1,"container","vh-100","d-flex","justify-content-center","align-items-center"],[1,"row"],[1,"col-lg-6"],[1,"error-number"],[1,"error-title"],[1,"error-text"],["routerLink","",1,"btn","btn-primary","btn-error"]],template:function(o,r){o&1&&(e(0,"div",0)(1,"div",1)(2,"div",2)(3,"h1",3),t(4,"404"),i(),e(5,"h2",4),t(6,"Page Not Found"),i(),e(7,"p",5),t(8,"Oopps. The page you were looking for doesn't exist. You may have mistyped the address or the page may have moved."),i(),e(9,"a",6),t(10,"Back to Dashboard"),i()()()())},dependencies:[ue]})}}return l})();function We(l,a){if(l&1){let n=ie();e(0,"button",73),oe("click",function(){Q(n);let r=B();return Y(r.generatePDF())}),t(1,` Generate PDF
`),i()}}function Qe(l,a){if(l&1&&(e(0,"div")(1,"h5",79),t(2),i()()),l&2){let n=B().$implicit;d(2),k(" RG:",n.challanNo," vom ",n.challanDate," an ",n.customer," ")}}function Ye(l,a){if(l&1&&(e(0,"div",74)(1,"div",55)(2,"div",56)(3,"h5",75),t(4),i()(),e(5,"div",57),I(6,Qe,3,3,"div",76),e(7,"P",77),t(8),i(),e(9,"p",77),t(10),i(),e(11,"p",77),t(12),i()(),e(13,"div",58)(14,"p",78),t(15),h(16,"number"),i()(),e(17,"div",60)(18,"p",78),t(19),h(20,"number"),i()(),e(21,"div",61)(22,"p",78),t(23),h(24,"number"),i()()()()),l&2){let n=a.$implicit;d(4),s(" ",n.carpetNo," "),d(2),N("ngIf",n.challanNo),d(2),S(n.qualityDesign),d(2),k(" ",n.qualityCode,", ",n.colourCode," ",n.colour," "),d(2),s(" ",n.size," cm "),d(3),s(" ",u(16,10,n.area,"1.2-2")," QM "),d(4),s(" ",u(20,13,n.evkPrice,"1.2-2")," "),d(4),s(" ",u(24,16,n.amount,"1.2-2")," ")}}var je=(()=>{class l{constructor(n,o,r,m,f){this._service=n,this.customeService=o,this.activeRoute=r,this.ngxLoader=m,this.pdfService=f,this.billId="",this._isChallan=!1,this.billDetails=[],this._bill={},this.totalCalculation={totalAmount:0,profit:0,grossAmt:0,gstAmt:0},this.total=0,this.profit=0,this.gst=0,this.isPrint=!1}ngOnInit(){this.billId=this.activeRoute.snapshot.paramMap.get("id")||"",this.initializeData(this.billId)}initializeData(n){Promise.all([this.getBills(),this.getChallans(),this.getContainerReceived(),this.getImporterName()]).then(()=>{let o=n.split(" ");o[1]==="print"&&this.viewBill(o[0]),this.ngxLoader.stop()}).catch(o=>{console.log("something went wrong")})}getBills(){return y(this,null,function*(){let n=yield _(this._service.getsBill());this.allBills=n})}getChallans(){return y(this,null,function*(){let n=yield _(this._service.getAllChallan());this.allChallans=n})}getContainerReceived(){return y(this,null,function*(){let n=yield _(this._service.getAllContainerRecieved());this.allContainerStock=n})}getImporterName(){return y(this,null,function*(){let n=yield _(this._service.getsWholesalerList());this.allImporterDetails=n})}viewBill(n){let o=0,r=0,m=0,f=0,D=0;this.ngxLoader.start();let b=this.allBills.find(M=>M._id===n.toString()),P=this.allImporterDetails.find(M=>M.customerName===b.wholesellerName);console.log(P);let G=this.customeService.convertDate(b.chooseAdate),V=G.split(".");this._bill={billNo:b.billNo,date:G,customer:b.wholesellerName,street:P.address,zipCode:P.zipCode,country:P.country,date2:"01."+V[1]+"."+V[2]};debugger;b.challanNo.forEach(M=>{let x=this.allChallans.find(p=>p.challanNo===M.challanNumber);if(!x||!x.carpetList){console.error("Challan data or carpet list is undefined for challan number");return}x.carpetList.forEach(p=>{this.allContainerStock.filter(z=>z.containerItem.some(c=>parseInt(c.GerCarpetNo)===p.barcodeNo&&p.isDeleted!=!0)).forEach(z=>{z.containerItem.forEach(c=>{if(parseInt(c.GerCarpetNo)===p.barcodeNo){o=0,r=0,m=0,f=0,D=0;let R=parseFloat(p.area?p.area:c.Area),He=parseFloat(p.evkPrice?p.evkPrice:c.EvKPrice),H=R*He;o++,p.status!="return"?(m+=R,r+=H):(m-=Math.abs(R),r-=Math.abs(H));let $e=this.customeService.convertDate(x.chooseAdate),F=this.billDetails.some(Ke=>Ke.challanNo==x.challanNo);this.billDetails.push({challanNo:F?void 0:x.challanNo,challanDate:F?void 0:$e,customer:F?void 0:x.retailerOutlet,carpetNo:c.GerCarpetNo,qualityDesign:c.QualityDesign,colour:c.Color,colourCode:c.CCode,qualityCode:c.QCode,size:p.size?p.size:c.Size,area:m,evkPrice:p.evkPrice?p.evkPrice:c.EvKPrice,amount:r,invoiceNo:c.InvoiceNo,saleStatus:c.status})}})})})}),this.calculation(this.billDetails),console.log(this.billDetails),this.ngxLoader.stop()}calculation(n){n.forEach(o=>{this.total=this.total+parseFloat(o.area)*parseFloat(o.evkPrice)}),this.profit=this.total/100*13,this.gst=(this.total+this.profit)/100*19,this.totalCalculation={totalAmount:this.total,profit:this.profit,gstAmt:this.gst,grossAmt:this.total+this.profit+this.gst},console.log(this.totalCalculation)}generatePDF(){window.print()}static{this.\u0275fac=function(o){return new(o||l)(C(Se),C(Ee),C(ge),C(ve),C(Pe))}}static{this.\u0275cmp=v({type:l,selectors:[["app-my-standalone-component"]],decls:228,vars:57,consts:[["class","no-print",3,"click",4,"ngIf"],["id","contentToConvert",1,"foot1",2,"margin-left","50px","margin-right","50px"],[1,"container"],[1,"row",2,"margin-bottom","245px"],[1,"col-md-12",2,"margin-top","30px !important"],["src","../../assets/auth-assets/images/Untitled-3.png","alt","",1,"img-fluid"],[1,"mt-5"],[1,"col-md-12","d-flex"],[1,"col-md-8"],[1,"font1","h5txt"],[1,"col-md-12","mt","d-flex","justify-content-between"],[1,"card","card1"],[1,"hd-text"],[1,"pl-4",2,"font-weight","bold","font-size","27px"],[1,"card","card1","mt-5"],[1,"text-hd-n"],[2,"margin-top","10px !important","display","inline-block"],[1,"col-md-12","mt-3","d-flex","justify-content-between"],[1,"card1","line1"],[1,"textsize"],[1,"line"],[1,"col-md-12","d-flex","justify-content-between","mt-1"],[1,"card1","bill","line1"],[1,"d-flex"],[1,"col-md-4","d-flex","justify-content-end",2,"text-align","end","padding-right","0px"],[1,"invoice-table"],[1,"table-colour"],[1,"font-weight-bold","text-lg","textsize"],[1,"right-align","line-above","fw-bold","textsize"],[1,"right-align","textsize"],[1,"fw-bold","textsize"],[1,"right-align","line-above","line-bottom","fw-bold","textsize"],[1,"col-md-12","mt-5","mb-5"],[1,"mb-5",2,"color","rgb(92, 92, 92)","font-size","20px","font-weight","bold"],[1,"row"],[1,"col-md-12","mt-3","text-center"],["src","../../assets/auth-assets/images/Footer set file.png","alt","",1,"img-fluid"],[1,"container","mt-5"],[1,"col-md-12","bb"],[1,"second-page11","mt-5"],[1,"second-page"],[1,"textsize",2,"color","rgb(85, 84, 84)"],[1,"fs-4","h5txt",2,"margin-bottom","3px"],[1,"second-page1"],[1,"fs-4",2,"color","rgb(85, 84, 84)"],[1,"container","mt-3"],[1,"col-md-12"],[1,"card-text"],[1,"second-text"],[2,"font-weight","bold","font-size","27px"],[1,"fs-5",2,"font-weight","bold"],[1,"second-text22"],[1,"second-text2"],[1,"fs-5","textsize"],[1,"second-text1"],[1,"counter-heading-text"],[1,"counter-heading-text1"],[1,"counter-heading-text2"],[1,"counter-heading-text3"],[1,"textsize",2,"text-align","end"],[1,"counter-heading-text4"],[1,"counter-heading-text5"],["class","col-md-12 mt-2 ",4,"ngFor","ngForOf"],[1,"mt-3"],[1,"col-md-12","last-counter1"],[1,"col-md-8","mt-4"],[1,"fs-4",2,"font-weight","bolder"],[1,"font-weight-bold","text-lg"],[1,"right-align","fw-bold"],[1,""],[1,"right-align"],[1,"fw-bold"],[1,"right-align","line-above","line-bottom","fw-bold"],[1,"no-print",3,"click"],[1,"col-md-12","mt-2"],[2,"font-size","18px"],[4,"ngIf"],[2,"margin-bottom","10px","font-size","18px"],[2,"margin-top","35px","font-size","18px","text-align","end"],[2,"font-weight","bold","font-size","18px","font-style","italic","margin-bottom","10px"]],template:function(o,r){o&1&&(I(0,We,2,0,"button",0),e(1,"section",1)(2,"div",2)(3,"div",3)(4,"div",4)(5,"div"),g(6,"img",5),i()(),e(7,"div",6)(8,"div",7)(9,"div",8)(10,"h5",9),t(11),i(),e(12,"h5",9),t(13),i(),e(14,"h5",9),t(15),i()()()(),e(16,"div",10)(17,"div",11)(18,"div",12)(19,"h4",13),t(20),i()()(),e(21,"div",14)(22,"div",15)(23,"p"),t(24," Datum "),g(25,"br"),e(26,"span",16),t(27),i()(),e(28,"p"),t(29,"Seite "),g(30,"br"),e(31,"span",16),t(32," 1 VON 1"),i()(),e(33,"p"),t(34,"Auftrags-Nr. "),g(35,"br"),e(36,"span",16),t(37," 10125298"),i()(),e(38,"p"),t(39,"Kunden"),g(40,"br"),e(41,"span",16),t(42,"31001"),i()(),e(43,"p"),t(44,"SB "),g(45,"br"),e(46,"span",16),t(47," HS"),i()()()()(),e(48,"div",17)(49,"div",18)(50,"ul")(51,"li",19),t(52,"Pos. ID-Nr"),i(),e(53,"li",19),t(54,"Artikelbezeichnung"),i()()(),e(55,"div",18)(56,"ul")(57,"li",19),t(58,"Menge."),i(),e(59,"li",19),t(60,"Einh."),i(),e(61,"li",19),t(62,"Einzel-Pr."),i(),e(63,"li",19),t(64,"Gesamt-Pr."),i()()()(),g(65,"span",20),e(66,"div",21)(67,"div",18)(68,"ul")(69,"li",19),t(70,"1"),i(),e(71,"li",19),t(72,"600"),i(),e(73,"li",19),t(74),i()()(),e(75,"div",22)(76,"ul")(77,"li",19),t(78,"1,00"),i(),e(79,"li",19),t(80,"ST"),i(),e(81,"li",19),t(82),h(83,"number"),i(),e(84,"li",19),t(85),h(86,"number"),i()()()(),e(87,"div",23),g(88,"div",8),e(89,"div",24)(90,"div",25)(91,"table",26)(92,"tr")(93,"td",27),t(94," Gesamtpreis Netto EUR "),i(),e(95,"td",28),t(96),h(97,"number"),i()(),e(98,"tr")(99,"td",19),t(100,"Profitieren"),i(),e(101,"td",29),t(102),h(103,"number"),i()(),e(104,"tr")(105,"td",19),t(106,"zzgl. 19% MwSt EUR"),i(),e(107,"td",29),t(108),h(109,"number"),i()(),e(110,"tr")(111,"td",30),t(112,"Gesamtpreis Brutto EUR"),i(),e(113,"td",31),t(114),h(115,"number"),i()()()()()(),e(116,"div",32)(117,"P"),t(118,"Ihr Ansprechpartner: OHNE KEINE BETREUUNG"),i(),e(119,"p"),t(120,"Wenn nicht gesondert angegeben, gilt: Rechnungsdatum gleich Liefer-/ Leistungsdatum. "),i(),e(121,"p"),t(122,"Die Lieferung erfolgt zu unseren Allgemainen Geschaftsbedingungen, einzusehen auf unserer Internetseite."),i(),e(123,"p"),t(124," Auf Wunsch senden wir Ihnen diese gern zu. "),i(),e(125,"h4",33),t(126," Bzgl der Entgeltminderungen verweisen wir auf die aktuellen Zahlungs- und Konditionsvereinbarungen. "),i()()(),e(127,"div",34)(128,"div",35),g(129,"img",36),i()(),e(130,"div",37)(131,"div",38)(132,"div",11)(133,"div",39)(134,"div",40)(135,"h5",41),t(136," Rechnungsempfanger : "),i(),e(137,"h5",42),t(138),i(),e(139,"h5",42),t(140),i(),e(141,"h5",42),t(142),i()(),e(143,"div",43)(144,"h5",44),t(145," Rechnungssteller: "),i(),e(146,"h5",42),t(147,"K.O.T.I. GmbH"),i(),e(148,"h5",42),t(149,"Stockum 2 a"),i(),e(150,"h5",42),t(151,"48653 Coesfeld"),i(),e(152,"h5",42),t(153,"Deutschland"),i()()()()()(),e(154,"div",45)(155,"div",34)(156,"div",46)(157,"div",11)(158,"div",47)(159,"div",48)(160,"h4",49),t(161),i(),e(162,"h5",50),t(163),i(),e(164,"h5",50),t(165," Berechnet mit einem Aufschlag Von: 0% "),i()(),e(166,"div",51)(167,"div",52)(168,"h5",53),t(169,"Datum"),i(),e(170,"h5",53),t(171),i()(),e(172,"div",54)(173,"h5",53),t(174,"Seite"),i(),e(175,"h5",53),t(176,"1 Von "),i()()()()()(),e(177,"div",46)(178,"div",55)(179,"div",56)(180,"h5",53),t(181,"Artikel-Nr."),i()(),e(182,"div",57)(183,"h5",53),t(184,"Bezeichnung"),i()(),e(185,"div",58)(186,"p",59),t(187,"Menge Einh."),i()(),e(188,"div",60)(189,"p",59),t(190,"Einzel-Pr."),i()(),e(191,"div",61)(192,"p",59),t(193,"Gesamt-Pr."),i()()()(),g(194,"span",20),I(195,Ye,25,19,"div",62),g(196,"span",63),e(197,"div",64)(198,"div",65)(199,"h6",66),t(200," Umlagerungen von Filiale3 zu Filiale 1 "),i()(),e(201,"div",24)(202,"div",25)(203,"table",26)(204,"tr")(205,"td",67),t(206," Gesamtpreis Netto EUR "),i(),e(207,"td",68),t(208),h(209,"number"),i()(),e(210,"tr")(211,"td",69),t(212,"Profitieren"),i(),e(213,"td",70),t(214),h(215,"number"),i()(),e(216,"tr")(217,"td"),t(218,"zzgl. 19% MwSt EUR"),i(),e(219,"td",70),t(220),h(221,"number"),i()(),e(222,"tr")(223,"td",71),t(224,"Gesamtpreis Brutto EUR"),i(),e(225,"td",72),t(226),h(227,"number"),i()()()()()()()()()()),o&2&&(N("ngIf",!r.isPrint),d(11),S(r._bill.customer),d(2),A("",r._bill.street,",",r._bill.zipCode,""),d(2),S(r._bill.country),d(5),s(" RECHNUNG ",r._bill.billNo," "),d(7),s(" ",r._bill.date,""),d(47),s("UMLAGERUNGSRECHNUNG ",r._bill.date,""),d(8),s(" ",u(83,27,r.totalCalculation.totalAmount,"1.2-2")," "),d(3),s(" ",u(86,30,r.totalCalculation.totalAmount,"1.2-2")," "),d(11),s(" ",u(97,33,r.totalCalculation.totalAmount,"1.2-2")," "),d(6),s(" ",u(103,36,r.totalCalculation.profit,"1.2-2")," "),d(6),s(" ",u(109,39,r.totalCalculation.gstAmt,"1.2-2")," "),d(6),s(" ",u(115,42,r.totalCalculation.grossAmt,"1.2-2")," "),d(24),s(" ",r._bill.customer," "),d(2),A(" ",r._bill.street,",",r._bill.zipCode," "),d(2),s(" ",r._bill.country," "),d(19),s(" ANLAGE ZUR RECHNUNG ",r._bill.billNo," "),d(2),A(" Abrechnung der Umlagerung Vom ",r._bill.date2," bis ",r._bill.date," "),d(8),S(r._bill.date2),d(24),N("ngForOf",r.billDetails),d(13),s(" ",u(209,45,r.totalCalculation.totalAmount,"1.2-2")," "),d(6),s(" ",u(215,48,r.totalCalculation.profit,"1.2-2")," "),d(6),s(" ",u(221,51,r.totalCalculation.gstAmt,"1.2-2")," "),d(6),s(" ",u(227,54,r.totalCalculation.grossAmt,"1.2-2")," "))},dependencies:[ae,le,de],styles:[".invoice-table[_ngcontent-%COMP%]{width:100%}.invoice-table[_ngcontent-%COMP%]{display:flex;justify-content:flex-end}.table-colour[_ngcontent-%COMP%]{border-color:#fff}.right-align[_ngcontent-%COMP%]{text-align:right}.line-above[_ngcontent-%COMP%]{border-top:1px solid black;width:100px}.line-bottom[_ngcontent-%COMP%]{border-bottom:2px solid black}.bb[_ngcontent-%COMP%]{margin-top:600px}.text-hd-n[_ngcontent-%COMP%]{display:flex;gap:20px;width:100%;font-size:17px}.hd-text[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%]{width:100%}.card1[_ngcontent-%COMP%]{border:none}.line1[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:inline;font-size:15px}.line1[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{display:flex;gap:40px}.bill[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{display:flex;gap:60px}.mdle-hd[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.md-hd1[_ngcontent-%COMP%]   h6[_ngcontent-%COMP%]{margin-left:24px}.space1[_ngcontent-%COMP%]{min-height:40vh}.footer[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{display:inline}.footer[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{display:flex;gap:215px}.footer11[_ngcontent-%COMP%]{display:flex}.footer12[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]{list-style:none}.footer12[_ngcontent-%COMP%]   ul[_ngcontent-%COMP%]   li[_ngcontent-%COMP%]{font-size:10px}@media (max-width: 767px){.footer11[_ngcontent-%COMP%]{display:flex}}.second-page11[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.second-page[_ngcontent-%COMP%]{margin-top:180px}.card-text[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.second-text22[_ngcontent-%COMP%]{display:flex;gap:20px}.footer[_ngcontent-%COMP%]{display:flex;gap:18%;height:auto!important;margin-top:5px!important}.line[_ngcontent-%COMP%]{border:1px solid #000;margin-right:30px}.counter-heading-text[_ngcontent-%COMP%]{display:flex;justify-content:space-between;gap:10px}.counter-heading-text1[_ngcontent-%COMP%]{width:24%}.counter-heading-text2[_ngcontent-%COMP%]{width:100%;line-height:10px}.counter-heading-text3[_ngcontent-%COMP%]{width:25%}.counter-heading-text3[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:15px}.counter-heading-text4[_ngcontent-%COMP%]{width:25%}.counter-heading-text4[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:15px}.counter-heading-text5[_ngcontent-%COMP%]{width:25%}.counter-heading-text5[_ngcontent-%COMP%]   p[_ngcontent-%COMP%]{font-size:15px}.last-counter1[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.mdle-hd[_ngcontent-%COMP%]{display:flex;gap:20px}.count[_ngcontent-%COMP%]{border:none}.mat-h5[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   .mat-h5[_ngcontent-%COMP%], .mat-typography[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%]{font:400 calc(20px* .83) / 20px Roboto,sans-serif;margin:0 0 12px}@media print{.no-print[_ngcontent-%COMP%]{display:none}}.textsize[_ngcontent-%COMP%]{font-size:20px!important}.spacing-row[_ngcontent-%COMP%]{margin-bottom:15px}.page[_ngcontent-%COMP%]{page-break-after:always}@page{margin-bottom:10px}.font1[_ngcontent-%COMP%]{font-size:25px!important}p[_ngcontent-%COMP%]{font-size:20px}.h5txt[_ngcontent-%COMP%]{font-weight:700}.mt[_ngcontent-%COMP%]{margin-top:150px}"]})}}return l})();var Ze=[{path:"",component:fe,loadChildren:()=>import("./chunk-Y3H42JRA.js").then(l=>l.AuthModule)},{path:"admin",component:_e,loadChildren:()=>import("./chunk-U7Z63RB5.js").then(l=>l.AdminModule),data:{title:"Admin"}},{path:"my/:id",component:je},{path:"**",component:Te,pathMatch:"full"},{path:"admin/view-carpet-reciving/:id",component:Fe}],Ue=(()=>{class l{static{this.\u0275fac=function(o){return new(o||l)}}static{this.\u0275mod=w({type:l})}static{this.\u0275inj=O({imports:[j.forRoot(Ze),j]})}}return l})();var Le=(()=>{class l{title(n){throw new Error("Method not implemented.")}static{this.\u0275fac=function(o){return new(o||l)}}static{this.\u0275cmp=v({type:l,selectors:[["app-root"]],decls:2,vars:0,template:function(o,r){o&1&&g(0,"router-outlet")(1,"ngx-ui-loader")},dependencies:[he,Ce]})}}return l})();var Xe="@",Je=(()=>{class l{constructor(n,o,r,m,f){this.doc=n,this.delegate=o,this.zone=r,this.animationType=m,this.moduleImpl=f,this._rendererFactoryPromise=null,this.scheduler=q(J,{optional:!0})}ngOnDestroy(){this._engine?.flush()}loadImpl(){return(this.moduleImpl??import("./chunk-UFPV577L.js")).catch(o=>{throw new $(5300,!1)}).then(({\u0275createEngine:o,\u0275AnimationRendererFactory:r})=>{this._engine=o(this.animationType,this.doc,this.scheduler);let m=new r(this.delegate,this._engine,this.zone);return this.delegate=m,m})}createRenderer(n,o){let r=this.delegate.createRenderer(n,o);if(r.\u0275type===0)return r;typeof r.throwOnSyntheticProps=="boolean"&&(r.throwOnSyntheticProps=!1);let m=new L(r);return o?.data?.animation&&!this._rendererFactoryPromise&&(this._rendererFactoryPromise=this.loadImpl()),this._rendererFactoryPromise?.then(f=>{let D=f.createRenderer(n,o);m.use(D)}).catch(f=>{m.use(r)}),m}begin(){this.delegate.begin?.()}end(){this.delegate.end?.()}whenRenderingDone(){return this.delegate.whenRenderingDone?.()??Promise.resolve()}static{this.\u0275fac=function(o){X()}}static{this.\u0275prov=K({token:l,factory:l.\u0275fac})}}return l})(),L=class{constructor(a){this.delegate=a,this.replay=[],this.\u0275type=1}use(a){if(this.delegate=a,this.replay!==null){for(let n of this.replay)n(a);this.replay=null}}get data(){return this.delegate.data}destroy(){this.replay=null,this.delegate.destroy()}createElement(a,n){return this.delegate.createElement(a,n)}createComment(a){return this.delegate.createComment(a)}createText(a){return this.delegate.createText(a)}get destroyNode(){return this.delegate.destroyNode}appendChild(a,n){this.delegate.appendChild(a,n)}insertBefore(a,n,o,r){this.delegate.insertBefore(a,n,o,r)}removeChild(a,n,o){this.delegate.removeChild(a,n,o)}selectRootElement(a,n){return this.delegate.selectRootElement(a,n)}parentNode(a){return this.delegate.parentNode(a)}nextSibling(a){return this.delegate.nextSibling(a)}setAttribute(a,n,o,r){this.delegate.setAttribute(a,n,o,r)}removeAttribute(a,n,o){this.delegate.removeAttribute(a,n,o)}addClass(a,n){this.delegate.addClass(a,n)}removeClass(a,n){this.delegate.removeClass(a,n)}setStyle(a,n,o,r){this.delegate.setStyle(a,n,o,r)}removeStyle(a,n,o){this.delegate.removeStyle(a,n,o)}setProperty(a,n,o){this.shouldReplay(n)&&this.replay.push(r=>r.setProperty(a,n,o)),this.delegate.setProperty(a,n,o)}setValue(a,n){this.delegate.setValue(a,n)}listen(a,n,o){return this.shouldReplay(n)&&this.replay.push(r=>r.listen(a,n,o)),this.delegate.listen(a,n,o)}shouldReplay(a){return this.replay!==null&&a.startsWith(Xe)}};function Ge(l="animations"){return te("NgAsyncAnimations"),W([{provide:ee,useFactory:(a,n,o)=>new Je(a,n,o,l),deps:[re,pe,ne]},{provide:Z,useValue:l==="noop"?"NoopAnimations":"BrowserAnimations"}])}var et={bgsColor:"red",text:"Loading",textColor:"#85c5ed",textPosition:"center-center",pbColor:"#85c5ed",fgsColor:"#85c5ed",fgsType:"three-strings",fgsSize:100,pbDirection:xe.leftToRight,pbThickness:5},Ve=(()=>{class l{static{this.\u0275fac=function(o){return new(o||l)}}static{this.\u0275mod=w({type:l,bootstrap:[Le]})}static{this.\u0275inj=O({providers:[Ge(),{provide:Oe,useValue:"en-GB"}],imports:[se,ye,Me,U,Ie,ze,we,Ae,De,Re,Ne,T,Ue,me,be.forRoot(et),T,U,Be]})}}return l})();ce().bootstrapModule(Ve).catch(l=>console.error(l));
