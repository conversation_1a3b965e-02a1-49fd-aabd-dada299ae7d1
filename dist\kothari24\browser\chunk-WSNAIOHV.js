import{A as v,B as b,D as y,E as O,G as P,H as k,J as N,N as R,O as $,Q as A,R as H,S as j,T as z,a as S,w as E,x as T,y as w}from"./chunk-FDYESHK5.js";import{Ab as m,Bb as r,Ib as h,Nc as I,Rb as u,Sb as l,Tb as s,Tc as D,Xa as o,Ya as p,ec as g,gc as x,hc as M,ja as C,mb as d,ob as _,xb as a,yb as i,zb as f}from"./chunk-TSVGDZRC.js";function G(t,n){t&1&&(a(0,"th",19),l(1,"Issue No"),i())}function K(t,n){if(t&1&&(a(0,"td",20),l(1),i()),t&2){let e=n.$implicit;o(),s(e.issueNo)}}function L(t,n){t&1&&(a(0,"th",19),l(1,"Challan No"),i())}function q(t,n){if(t&1&&(a(0,"td",20),l(1),i()),t&2){let e=n.$implicit;o(),s(e.challanNo)}}function J(t,n){t&1&&(a(0,"th",19),l(1,"C Date"),i())}function Q(t,n){if(t&1&&(a(0,"td",20),l(1),g(2,"date"),i()),t&2){let e=n.$implicit;o(),s(x(2,1,e.cDate,"dd-MM-yyyy"))}}function U(t,n){t&1&&(a(0,"th",19),l(1,"Kati Issued"),i())}function V(t,n){if(t&1&&(a(0,"td",20),l(1),i()),t&2){let e=n.$implicit;o(),s(e.katiIssued)}}function W(t,n){t&1&&(a(0,"th",19),l(1,"Tana Issued"),i())}function X(t,n){if(t&1&&(a(0,"td",20),l(1),i()),t&2){let e=n.$implicit;o(),s(e.tanaIssued)}}function Y(t,n){t&1&&(a(0,"th",19),l(1,"Soot Issued"),i())}function Z(t,n){if(t&1&&(a(0,"td",20),l(1),i()),t&2){let e=n.$implicit;o(),s(e.sootIssued)}}function tt(t,n){t&1&&(a(0,"th",19),l(1,"Tharri Issued"),i())}function et(t,n){if(t&1&&(a(0,"td",20),l(1),i()),t&2){let e=n.$implicit;o(),s(e.tharriIssued)}}function nt(t,n){t&1&&(a(0,"th",19),l(1,"Silk Issued"),i())}function at(t,n){if(t&1&&(a(0,"td",20),l(1),i()),t&2){let e=n.$implicit;o(),s(e.silkIssued)}}function it(t,n){t&1&&(a(0,"th",19),l(1,"Other Issued"),i())}function lt(t,n){if(t&1&&(a(0,"td",20),l(1),i()),t&2){let e=n.$implicit;o(),s(e.otherIssued)}}function ot(t,n){t&1&&f(0,"tr",21)}function dt(t,n){t&1&&f(0,"tr",22)}function mt(t,n){if(t&1&&(m(0),a(1,"table",5),m(2,6),d(3,G,2,0,"th",7)(4,K,2,1,"td",8),r(),m(5,9),d(6,L,2,0,"th",7)(7,q,2,1,"td",8),r(),m(8,10),d(9,J,2,0,"th",7)(10,Q,3,4,"td",8),r(),m(11,11),d(12,U,2,0,"th",7)(13,V,2,1,"td",8),r(),m(14,12),d(15,W,2,0,"th",7)(16,X,2,1,"td",8),r(),m(17,13),d(18,Y,2,0,"th",7)(19,Z,2,1,"td",8),r(),m(20,14),d(21,tt,2,0,"th",7)(22,et,2,1,"td",8),r(),m(23,15),d(24,nt,2,0,"th",7)(25,at,2,1,"td",8),r(),m(26,16),d(27,it,2,0,"th",7)(28,lt,2,1,"td",8),r(),d(29,ot,1,0,"tr",17)(30,dt,1,0,"tr",18),i(),r()),t&2){let e=h();o(),_("dataSource",e.challanDetails),o(28),_("matHeaderRowDef",e.displayedColumns),o(),_("matRowDefColumns",e.displayedColumns)}}function rt(t,n){t&1&&(a(0,"p"),l(1,"No Challan Issued Available."),i())}var ht=(()=>{class t{constructor(e,c){this.dialogRef=e,this.data=c,this.challanDetails=[],this.displayedColumns=["issueNo","challanNo","cDate","katiIssued","tanaIssued","sootIssued","tharriIssued","silkIssued","otherIssued"]}ngOnInit(){console.log("\u{1F50D} Challan Modal Data:",this.data),this.processChallanDetails()}processChallanDetails(){this.data&&this.data.challanDetails&&(this.challanDetails=this.data.challanDetails.map(e=>({issueNo:this.data.issueNo||"N/A",challanNo:e.challanNo||"N/A",cDate:e.date||new Date,katiIssued:e.materials?.kati?.issue||0,tanaIssued:e.materials?.tana?.issue||0,sootIssued:e.materials?.soot?.issue||0,tharriIssued:e.materials?.thari?.issue||0,silkIssued:e.materials?.silk?.issue||0,otherIssued:e.materials?.other?.issue||0}))),console.log("\u2705 Processed challan details:",this.challanDetails)}static{this.\u0275fac=function(c){return new(c||t)(p(R),p($))}}static{this.\u0275cmp=C({type:t,selectors:[["app-challan-modal"]],decls:9,vars:2,consts:[["noData",""],["mat-dialog-title","",1,"title","mt-2"],[4,"ngIf","ngIfElse"],["align","end"],["mat-button","","mat-dialog-close",""],["mat-table","",1,"mat-elevation-z8",3,"dataSource"],["matColumnDef","issueNo"],["mat-header-cell","",4,"matHeaderCellDef"],["mat-cell","",4,"matCellDef"],["matColumnDef","challanNo"],["matColumnDef","cDate"],["matColumnDef","katiIssued"],["matColumnDef","tanaIssued"],["matColumnDef","sootIssued"],["matColumnDef","tharriIssued"],["matColumnDef","silkIssued"],["matColumnDef","otherIssued"],["mat-header-row","",4,"matHeaderRowDef"],["mat-row","",4,"matRowDef","matRowDefColumns"],["mat-header-cell",""],["mat-cell",""],["mat-header-row",""],["mat-row",""]],template:function(c,B){if(c&1&&(a(0,"h2",1),l(1,"Challan Issued Details"),i(),a(2,"mat-dialog-content"),d(3,mt,31,3,"ng-container",2)(4,rt,2,0,"ng-template",null,0,M),i(),a(6,"mat-dialog-actions",3)(7,"button",4),l(8,"Close"),i()()),c&2){let F=u(5);o(3),_("ngIf",B.challanDetails.length>0)("ngIfElse",F)}},dependencies:[I,S,E,w,O,v,T,P,b,y,k,N,A,H,z,j,D],styles:["fieldset[_ngcontent-%COMP%]{font-family:sans-serif;border:2px solid #1F497D;background:#fff;border-radius:5px;padding:15px}fieldset[_ngcontent-%COMP%]   legend[_ngcontent-%COMP%]{background:#fff;color:#000;padding:5px 10px;font-size:20px;border-radius:5px;margin-left:20px}legend[_ngcontent-%COMP%]{float:left;width:auto;padding:0;margin-top:-32px;margin-bottom:.5rem;font-size:calc(1.275rem + .3vw);line-height:inherit}.ex-width[_ngcontent-%COMP%]{width:100%}.space[_ngcontent-%COMP%]{display:flex;justify-content:space-between}.mat-mdc-header-cell[_ngcontent-%COMP%]{background-color:#eaeaea;color:#1f497d;padding:12px 8px;text-align:left;font-weight:600;border-bottom:2px solid #0d47a1;border-top:2px solid #0d47a1}.mat-mdc-row[_ngcontent-%COMP%]:nth-child(2n){background-color:#f9f9f9}.mat-mdc-row[_ngcontent-%COMP%]:hover{background-color:#f0f0f0}.mat-column-amount[_ngcontent-%COMP%], .mat-column-rate[_ngcontent-%COMP%]{text-align:right!important;padding-right:24px!important}.mat-mdc-cell[_ngcontent-%COMP%], .mat-mdc-header-cell[_ngcontent-%COMP%]{padding:8px 16px!important}.mat-mdc-row[_ngcontent-%COMP%]{border-bottom:1px solid #e0e0e0}.title[_ngcontent-%COMP%]{color:#1f497d;display:flex;justify-content:center;text-decoration:underline}"]})}}return t})();export{ht as a};
