<div class="kati-modal-container">
  <div class="modal-header">
    <div></div>
    <h4 class="modal-title">{{ materialName }} Material Receive Details</h4>
    <button mat-icon-button type="button" aria-label="Close" class="close-button" (click)="onCancel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <div class="modal-body">
    <form [formGroup]="katiReceiveForm">
      <div class="table-container">
        <table mat-table [dataSource]="materialData" class="mat-elevation-2 custom-table">

          <!-- Sr.No Column -->
          <ng-container matColumnDef="srNo">
            <th mat-header-cell *matHeaderCellDef class="text-center">Sr.No</th>
            <td mat-cell *matCellDef="let element" class="text-center">
              {{ element.srNo }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <!-- Colour Column -->
          <ng-container matColumnDef="colour">
            <th mat-header-cell *matHeaderCellDef class="text-center">Colour</th>
            <td mat-cell *matCellDef="let element" class="text-center">
              {{ element.colour }}
            </td>
            <td mat-footer-cell *matFooterCellDef><b>Total</b></td>
          </ng-container>

          <!-- Lagat Column -->
          <ng-container matColumnDef="lagat">
            <th mat-header-cell *matHeaderCellDef class="num-cell">Lagat</th>
            <td mat-cell *matCellDef="let element" class="num-cell">
              {{ element.lagat | number:'1.3-3' }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <!-- Carpet Lagat Column -->
          <ng-container matColumnDef="carpetLagat">
            <th mat-header-cell *matHeaderCellDef class="num-cell">Carpet Lagat</th>
            <td mat-cell *matCellDef="let element" class="num-cell">
              {{ element.carpetLagat | number:'1.3-3' }}
            </td>
            <td mat-footer-cell *matFooterCellDef></td>
          </ng-container>

          <!-- T. Issued Column -->
          <ng-container matColumnDef="tIssued">
            <th mat-header-cell *matHeaderCellDef class="num-cell">T. Issued</th>
            <td mat-cell *matCellDef="let element" class="num-cell">
              {{ element.tIssued | number:'1.3-3' }}
            </td>
            <td mat-footer-cell *matFooterCellDef>{{ totalIssuedSum | number:'1.3-3' }}</td>
          </ng-container>

          <!-- Receive Column -->
          <ng-container matColumnDef="receive">
            <th mat-header-cell *matHeaderCellDef class="text-center">Receive</th>
            <td mat-cell *matCellDef="let element; let i = index" class="text-center">
              <div formArrayName="katiItems">
                <div [formGroupName]="i">
                  <input
                    class="custom-input receive-input"
                    type="text"
                    formControlName="receive"
                    placeholder=".000"
                    (blur)="setDigitReceive($event)"
                    (input)="onReceiveChange(i, $event)"
                  />
                </div>
              </div>
            </td>
            <td mat-footer-cell *matFooterCellDef>{{ totalReceive | number:'1.3-3' }}</td>
          </ng-container>

          <!-- Total Lagat Column (Remaining) -->
          <ng-container matColumnDef="totalLagat">
            <th mat-header-cell *matHeaderCellDef class="num-cell">Total Lagat</th>
            <td mat-cell *matCellDef="let element" class="num-cell">
              {{ element.totalLagat | number:'1.3-3' }}
            </td>
            <td mat-footer-cell *matFooterCellDef>{{ totalLagatSum | number:'1.3-3' }}</td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns; sticky: true"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
          <tr mat-footer-row *matFooterRowDef="displayedColumns; sticky: true"></tr>
        </table>
      </div>
    </form>
  </div>

  <div class="modal-footer">
    <button type="button" class="btn btn-secondary" (click)="onCancel()">Cancel</button>
    <button type="button" class="btn btn-primary save-button" (click)="onSave()">{{ isEditMode ? 'Update' : 'Save' }}</button>
  </div>
</div>
