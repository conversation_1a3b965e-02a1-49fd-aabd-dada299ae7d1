import { Component, Inject, ViewChild, ElementRef, AfterViewInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';

import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatAutocompleteModule, MatAutocompleteTrigger } from '@angular/material/autocomplete';
import { MatIconModule } from '@angular/material/icon';
import { MatTooltipModule } from '@angular/material/tooltip';
import { Observable, of } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { CommonModule } from '@angular/common';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-payment-modal',
  templateUrl: './payment-modal.component.html',
  styleUrls: ['./payment-modal.component.css'],
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatAutocompleteModule,
    MatIconModule,
    MatTooltipModule
  ]
})
export class PaymentModalComponent implements AfterViewInit {
  paymentForm: FormGroup;
  bankOptions: string[] = ['State Bank of India', 'HDFC Bank', 'ICICI Bank'];
  filteredBanks!: Observable<string[]>;

  @ViewChild(MatAutocompleteTrigger) autocompleteTrigger!: MatAutocompleteTrigger;
  @ViewChild('paymentDateInput') paymentDateInput!: ElementRef;
  @ViewChild('bankNameInput') bankNameInput!: ElementRef;
  @ViewChild('chequeInput') chequeInput!: ElementRef;
  @ViewChild('payedAmountInput') payedAmountInput!: ElementRef;
  @ViewChild('saveButton') saveButton!: ElementRef;

  constructor(
    public dialogRef: MatDialogRef<PaymentModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder
  ) {
    const today = new Date();
    let paymentDate = today;

    if (data.PaymentDt) {
      const dateValue = new Date(data.PaymentDt);
      if (dateValue.getFullYear() > 1970) {
        paymentDate = dateValue;
      }
    }

    const rawAmount = (data.PayedAmount !== undefined && data.PayedAmount !== null && data.PayedAmount !== '')
      ? data.PayedAmount
      : data.NetAmount;
    const defaultPayedAmount = this.floorTwoDecimals(rawAmount);

    this.paymentForm = this.fb.group({
      PaymentDt: [paymentDate, Validators.required],
      BankName: [data.BankName || ''],
      ChequeNoOrRGSno: [data.ChequeNoOrRGSno || ''],
      PayedAmount: [defaultPayedAmount || '', [Validators.required]],
      Remarks: [data.Remarks || '']
    });

    this.filteredBanks = this.paymentForm.get('BankName')!.valueChanges.pipe(
      startWith(''),
      map(value => this._filterBanks(value || ''))
    );
  }

  ngAfterViewInit() {
    // Focus on the payment date input field after view is initialized
    requestAnimationFrame(() => {
      if (this.paymentDateInput) {
        this.paymentDateInput.nativeElement.focus();
      }
      // Ensure the PayedAmount is rounded/floored as soon as modal opens
      this.roundPayedAmountNow();
    });
  }

  onPaymentDateEnter(): void {
    // Move focus to bank name field when Enter is pressed on Payment Date field
    if (this.bankNameInput) {
      this.bankNameInput.nativeElement.focus();
    }
  }

  onBankNameEnter(): void {
    // Move focus to cheque field when Enter is pressed on Bank Name field
    if (this.chequeInput) {
      this.chequeInput.nativeElement.focus();
    }
  }

  onChequeEnter(): void {
    // Move focus to payed amount field when Enter is pressed on Cheque field
    if (this.payedAmountInput) {
      this.payedAmountInput.nativeElement.focus();
      this.payedAmountInput.nativeElement.select();
    }
  }

  onPayedAmountEnter(): void {
    // Round the amount before moving focus
    this.roundPayedAmountNow();
    // Move focus to save button when Enter is pressed on Payed Amount field
    if (this.saveButton) {
      this.saveButton.nativeElement.focus();
    }
  }

  onSaveEnter(): void {
    // Trigger save when Enter is pressed on Save button
    this.onSave();
  }

  // Format to floor integer with two decimals (e.g., 435.42 -> 435.00)
  private floorTwoDecimals(val: any): string {
    const num = parseFloat((val ?? '').toString().replace(/,/g, ''));
    return isNaN(num) ? '' : Math.floor(num).toFixed(2);
  }

  // Round to nearest integer and show two decimals in the field immediately
  private roundPayedAmountNow(): void {
    const ctrl = this.paymentForm.get('PayedAmount');
    const val = parseFloat((ctrl?.value ?? '').toString());
    if (!isNaN(val)) {
      ctrl?.patchValue(Math.floor(val).toFixed(2), { emitEvent: false });
    }
  }

  setDigit(val: any) {
    const _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      // On blur also round as per requirement
      this.paymentForm.get('PayedAmount')?.patchValue(Math.round(_val).toFixed(2));
    }
  }

  private _filterBanks(value: string): string[] {
    const filterValue = value.toLowerCase();
    const matching = this.bankOptions.filter(opt =>
      opt.toLowerCase().includes(filterValue)
    );

    // Add typed value to list if not present
    if (value && !this.bankOptions.some(opt => opt.toLowerCase() === filterValue)) {
      return [value, ...matching];
    }

    return matching;
  }

  openAutocomplete() {
    setTimeout(() => {
      this.autocompleteTrigger.openPanel();
    }, 0);
  }

  removeBank(bank: string) {
    this.bankOptions = this.bankOptions.filter(b => b !== bank);
    this.filteredBanks = of(this.bankOptions);
  }

  onCancel(): void {
    this.dialogRef.close();
  }

  onSave(): void {
    if (!this.showValidationErrors()) {
      return; // Stop execution if validation fails
    }

    const formData = this.paymentForm.value;
    const date = new Date(formData.PaymentDt);
    const newBank = formData.BankName;

    // Round PayedAmount to nearest integer and format with two decimals (e.g., 50.31 -> 50.00)
    const numericPayed = parseFloat((formData.PayedAmount ?? '').toString().trim() || '0');
    const payedAmountStr = Math.floor(numericPayed).toFixed(2);

    // Add new bank to the list if it's not already there
    if (
      newBank &&
      !this.bankOptions.some(opt => opt.toLowerCase() === newBank.toLowerCase())
    ) {
      this.bankOptions.push(newBank);
    }

    // Show success alert
    Swal.fire({
      title: 'Success!',
      text: 'Payment details saved successfully',
      icon: 'success',
      confirmButtonText: 'OK'
    }).then(() => {
      this.dialogRef.close({
        PaymentDt: date,
        BankName: newBank,
        ChequeNoOrRGSno: formData.ChequeNoOrRGSno,
        PayedAmount: payedAmountStr,
        Remarks: formData.Remarks
      });
    });
  }

  showValidationErrors(): boolean {
    let errorFields = Object.keys(this.paymentForm.controls)
      .filter(key => this.paymentForm.get(key)?.invalid)
      .map(key => key.replace(/([A-Z])/g, ' $1').trim());

    if (errorFields.length > 0) {
      Swal.fire({
        title: 'Validation Error',
        text: `Please fill the following fields: ${errorFields.join(', ')}`,
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return false;  // Stop further execution
    }
    return true;  // Proceed if valid
  }
}
