import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ManufactureService } from '../../../../../services/manufacture.service';

@Component({
  selector: 'app-print-material-issue',
  templateUrl: './print-material-issue.component.html',
  styleUrl: './print-material-issue.component.css'
})
export class PrintMaterialIssueComponent implements OnInit {
  printId: any;

  // Header/order info
  orderIssueData: any;
  totalArea: any;
  // Area value should come from MaterialIssue.issueAreaInYard
  areaFromIssue?: string;
  // Challan currently being printed
  selectedChallanNo?: string;

  // Material summary
  materialIssues: any[] = [];
  materialsTotals: any = {
    kati: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    tana: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    soot: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    thari: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    silk: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    other: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
  };
  recDate?: string;

  constructor(private route: ActivatedRoute, private manufactureService: ManufactureService) {}

  ngOnInit(): void {
    this.printId = this.route.snapshot.paramMap.get('id') || '';
    // pick challan from query param if provided
    this.selectedChallanNo = this.route.snapshot.queryParamMap.get('challan') || undefined;
    if (this.printId) {
      this.loadOrderIssue(this.printId);
      this.loadMaterialIssues(this.printId);
      this.loadMaterialReceives(this.printId);
    }
  }

  // Order header
  loadOrderIssue(id: string) {
    this.manufactureService.getsOrderIssueList().subscribe((res: any) => {
      this.orderIssueData = res.find((x: any) => x._id === id);
      const areaItem = this.orderIssueData?.buyerOrder?.items?.find((x: any) => x._id === this.orderIssueData.itemId);
      this.totalArea = areaItem?.totalArea || 0;
    });
  }

  // Material issues for issue + compute totals (lagat from first, issue = sum)
  loadMaterialIssues(issueNoId: string) {
    this.manufactureService.getMaterialIssuesByIssueNo(issueNoId).subscribe((docs: any) => {
      const all = Array.isArray(docs) ? docs : [];
      // If challan filter present, filter to that challan only
      this.materialIssues = this.selectedChallanNo ? all.filter(d => d?.challanNo === this.selectedChallanNo) : all;
      this.areaFromIssue = this.materialIssues[0]?.issueAreaInYard || undefined;
      this.computeMaterialsTotals();
    });
  }

  // Material receives for issue (receive + tLagat sums) and Rec.Date
  loadMaterialReceives(issueNoId: string) {
    if (!this.manufactureService.getMaterialReceivesByIssueNo) return;
    this.manufactureService.getMaterialReceivesByIssueNo(issueNoId).subscribe((receives: any) => {
      const list = Array.isArray(receives) ? receives : [];
      // latest receive date if available (API sorts desc by createdAt)
      this.recDate = list.length ? new Date(list[0].date).toISOString() : undefined;
      list.forEach((doc: any) => {
        const m = doc?.materials || {};
        ['kati', 'tana', 'soot', 'thari', 'silk', 'other'].forEach((key: string) => {
          if (!m[key]) return;
          this.materialsTotals[key].receive += this.toNum(m[key].receive);
          this.materialsTotals[key].tLagat += this.toNum(m[key].tLagat);
        });
      });
    });
  }

  private toNum(v: any) {
    const n = parseFloat(v);
    return isNaN(n) ? 0 : n;
  }

  computeMaterialsTotals() {
    const totals: any = {
      kati: { lagat: 0, issue: 0, receive: this.materialsTotals.kati.receive, tLagat: this.materialsTotals.kati.tLagat },
      tana: { lagat: 0, issue: 0, receive: this.materialsTotals.tana.receive, tLagat: this.materialsTotals.tana.tLagat },
      soot: { lagat: 0, issue: 0, receive: this.materialsTotals.soot.receive, tLagat: this.materialsTotals.soot.tLagat },
      thari: { lagat: 0, issue: 0, receive: this.materialsTotals.thari.receive, tLagat: this.materialsTotals.thari.tLagat },
      silk: { lagat: 0, issue: 0, receive: this.materialsTotals.silk.receive, tLagat: this.materialsTotals.silk.tLagat },
      other: { lagat: 0, issue: 0, receive: this.materialsTotals.other.receive, tLagat: this.materialsTotals.other.tLagat },
    };

    // Base lagat: take from first issue doc
    const first = this.materialIssues[0]?.materials || {};
    ['kati', 'tana', 'soot', 'thari', 'silk', 'other'].forEach((key: string) => {
      if (first[key]) totals[key].lagat = this.toNum(first[key].lagat);
    });

    // Issue: sum across issues
    this.materialIssues.forEach((doc: any) => {
      const m = doc?.materials || {};
      ['kati', 'tana', 'soot', 'thari', 'silk', 'other'].forEach((key: string) => {
        if (!m[key]) return;
        totals[key].issue += this.toNum(m[key].issue);
      });
    });

    this.materialsTotals = totals;
  }

  printPage() {
    const invoiceEl = document.getElementById('invoice');
    const printContents = invoiceEl ? (invoiceEl as HTMLElement).outerHTML : null;
    if (printContents) {
      const originalContents = document.body.innerHTML;
      document.body.innerHTML = printContents; // keep #invoice wrapper so its styles (border) apply
      window.print();
      document.body.innerHTML = originalContents;
      window.location.reload();
    } else {
      window.print();
    }
  }
}
