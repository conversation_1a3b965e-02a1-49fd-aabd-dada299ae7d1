// import { formatDate } from '@angular/common';
import { Injectable } from '@angular/core';

@Injectable({
  providedIn: 'root',
})
export class CustomeServiceService {
  totalSizeInYaard: number=0;
  constructor() {}

  private currentNumber = 1;

  generateKOTINumber(num: any): string {
    this.currentNumber = num;
    const paddedNumber = this.currentNumber.toString().padStart(5, '0');
    const kotiNumber = `KOTI-${paddedNumber}`;
    this.currentNumber++;
    return kotiNumber;
  }

  //////////////////
  convertDate(date: any) {
    const dateObject = new Date(date);
    // Guard against invalid dates to avoid showing NaN.NaN.NaN
    if (isNaN(dateObject.getTime())) {
      return '';
    }

    const day = dateObject.getDate();
    const month = dateObject.getMonth() + 1;
    const year = dateObject.getFullYear();
    const formattedDate = `${day < 10 ? '0' : ''}${day}.${month < 10 ? '0' : ''}${month}.${year}`;
    return formattedDate;
  }

  convertDate2(date: any) {
    const dateObject = new Date(date);

    const day = dateObject.getDate();
    const month = dateObject.getMonth() + 1;
    const year = dateObject.getFullYear();
    const formattedDate = `${day < 10 ? '0' : ''}${day}/${
      month < 10 ? '0' : ''
    }${month}/${year}`;
    return formattedDate;
  }

  //////////////////////////////////////////////////////////////////
  ////////////////////////////////////
  ///////////////////////////
  ////////////////// convert date dd/mm/yyyy to gmt formate
  ///////

  formatDate(inputDate: any) {
    // Split the date string into day, month, and year parts
    const parts = inputDate.split('/');
    const day = parts[0];
    const month = parts[1];
    const year = parts[2];

    // Create a new Date object using the components
    const date = new Date(`${year}-${month}-${day}`);

    // Get the day of the week name
    const dayOfWeek = date.toLocaleString('en-US', { weekday: 'short' });

    // Get the month name and convert it to title case
    const monthName = date.toLocaleString('en-US', { month: 'long' });

    // Format the date in the desired format
    const formattedDate = `${dayOfWeek} ${monthName} ${day} ${year} 00:00:00 GMT+0530 (India Standard Time)`;

    return formattedDate;
  }
  formatDateToIso(inputDate: any) {
    // Split the date string into day, month, and year parts
    const parts = inputDate.split('/');
    const day = parts[0];
    const month = parts[1];
    const year = parts[2];

    // Create a new Date object using the components
    const date = new Date(`${year}-${month}-${day}T00:00:00.000Z`);

    // Convert the date to ISO format
    const isoDate = date.toISOString();

    return isoDate;
  }

  carpetData: any;
  setData(data: any) {
    debugger
    this.carpetData = data;
  }
  getData() {
    debugger
    return this.carpetData;
  }


  setSizeYard(size: any):any {
    const formData = size;
debugger
    const calcData = formData.split(/[Xx]/);

    const width1 = calcData[0].split('.')[0];
    const width2 = calcData[0].split('.')[1];

    console.log('before ', width1, 'after ', width2);

    console.log(calcData);

    const length1 = calcData[1].split('.')[0];
    const length2 = calcData[1].split('.')[1];

    const sizeInYaardCalc1 = parseInt(width1) * 12 + parseInt(width2);

    const sizeInYaardCalc2 = parseInt(length1) * 12 + parseInt(length2);
    console.log(sizeInYaardCalc1);
    console.log(sizeInYaardCalc2);

    this.totalSizeInYaard = (sizeInYaardCalc1 * sizeInYaardCalc2) / 1296;


let pcs = 1;

  let totalVal = pcs * this.totalSizeInYaard;

    return totalVal
  }
}
