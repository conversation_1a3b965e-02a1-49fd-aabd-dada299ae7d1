const mongoose = require('mongoose');

const materialReceiveSchema = new mongoose.Schema(
  {
    challanNo: { type: String, required: true, unique: true },
    date: { type: Date, required: true },

    weaver: { type: mongoose.Schema.Types.ObjectId, ref: 'WeaverEmployee', required: true },
    issueNo: { type: mongoose.Schema.Types.ObjectId, ref: 'CarpetOrderissue', required: true },
    // Total area in yard captured from Issue (same as MaterialIssue.issueAreaInYard)
    issueAreaInYard: { type: String, required: true },

    // Material wise summary values captured on Receive screen
    materials: {
      kati: {
        description: { type: mongoose.Schema.Types.ObjectId, ref: 'RawMaterialGroup', required: false },
        lagat: { type: String, default: '' },
        issue: { type: String, default: '0.000' },
        receive: { type: String, default: '0.000' },
        tLagat: { type: String, default: '0.000' },
      },
      tana: {
        description: { type: mongoose.Schema.Types.ObjectId, ref: 'RawMaterialGroup', required: false },
        lagat: { type: String, default: '' },
        issue: { type: String, default: '0.000' },
        receive: { type: String, default: '0.000' },
        tLagat: { type: String, default: '0.000' },
      },
      soot: {
        description: { type: mongoose.Schema.Types.ObjectId, ref: 'RawMaterialGroup', required: false },
        lagat: { type: String, default: '' },
        issue: { type: String, default: '0.000' },
        receive: { type: String, default: '0.000' },
        tLagat: { type: String, default: '0.000' },
      },
      thari: {
        description: { type: mongoose.Schema.Types.ObjectId, ref: 'RawMaterialGroup', required: false },
        lagat: { type: String, default: '' },
        issue: { type: String, default: '0.000' },
        receive: { type: String, default: '0.000' },
        tLagat: { type: String, default: '0.000' },
      },
      silk: {
        description: { type: mongoose.Schema.Types.ObjectId, ref: 'RawMaterialGroup', required: false },
        lagat: { type: String, default: '' },
        issue: { type: String, default: '0.000' },
        receive: { type: String, default: '0.000' },
        tLagat: { type: String, default: '0.000' },
      },
      other: {
        description: { type: mongoose.Schema.Types.ObjectId, ref: 'RawMaterialGroup', required: false },
        lagat: { type: String, default: '' },
        issue: { type: String, default: '0.000' },
        receive: { type: String, default: '0.000' },
        tLagat: { type: String, default: '0.000' },
      },
    },

    // Detailed Kati rows captured from modal
    katiData: [
      {
      colour: {
                type: mongoose.Schema.Types.ObjectId,
                ref: 'Color',
                required: true
              },
        lagat: { type: String, default: '' },
        carpetLagat: { type: String, default: '' },
        tIssued: { type: String, default: '0.000' },
        receiveValue: { type: String, default: '0.000' },
        tLagat: { type: String, default: '0.000' },
      },
    ],
  },
  { timestamps: true }
);

module.exports = mongoose.model('MaterialReceive', materialReceiveSchema);

