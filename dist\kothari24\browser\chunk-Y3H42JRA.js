import"./chunk-FAK7M7C5.js";import{A as w,D as C,H as E,I as _,J as A,K as O,L as U,T,W as B,b as G,k as v,l as d,m as M,o as b,s as S,t as x,x as y}from"./chunk-5HNFHR6K.js";import"./chunk-UCMEJ5RL.js";import{$ as p,Gb as h,Sb as n,Ta as D,Vc as P,Xa as f,Ya as s,_ as I,da as k,ja as l,ka as u,ob as g,xb as o,yb as t,zb as m}from"./chunk-TSVGDZRC.js";import{e as j}from"./chunk-CWTPBX7D.js";var F=j(U());var R=(()=>{class e{constructor(r){this.http=r,this.apiUrl=O.apiUrl,this.phaseOne=`${this.apiUrl}/phase-one/user`}register(r){return this.http.post(`${this.phaseOne}/create-user`,r)}login(r){return this.http.post(`${this.phaseOne}/loginUser`,r)}static{this.\u0275fac=function(i){return new(i||e)(k(G))}}static{this.\u0275prov=I({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();var N=(()=>{class e{constructor(r,i,a){this.fb=r,this.authService=i,this.route=a}ngOnInit(){this.frmLogin=this.fb.group({email:[],password:[]})}login(){let r=this.frmLogin.value;this.authService.login(r).subscribe({next:i=>{F.default.fire({title:"Success",text:"Login successfull",icon:"success"}),this.route.navigate(["/admin"])},error:i=>{F.default.fire({title:"Failed",text:"Something went wrong",icon:"warning"})}})}static{this.\u0275fac=function(i){return new(i||e)(s(E),s(R),s(v))}}static{this.\u0275cmp=l({type:e,selectors:[["app-login"]],decls:25,vars:1,consts:[[1,"bglogin","row","vh-100","d-flex","justify-content-center","align-items-center"],[1,"col-md-4","card","card-sign"],[1,"card-header"],[1,"card-title"],[1,"card-text"],[3,"ngSubmit","formGroup"],[1,"card-body"],[1,"mb-4"],[1,"form-label"],["type","text","placeholder","Enter your email address","formControlName","email",1,"form-control"],[1,"form-label","d-flex","justify-content-between"],["routerLink","forgot-password"],["type","password","placeholder","Enter your password","formControlName","password",1,"form-control"],[1,"btn","btn-primary","btn-sign"],[1,"mb-4","mt-2"],[1,"form-label","d-flex"],["routerLink","/register"]],template:function(i,a){i&1&&(o(0,"div",0)(1,"div",1)(2,"div",2)(3,"h3",3),n(4,"Rachin Export"),t(),o(5,"p",4),n(6,"Welcome back! Please signin to continue."),t()(),o(7,"form",5),h("ngSubmit",function(){return a.login()}),o(8,"div",6)(9,"div",7)(10,"label",8),n(11,"Contact No"),t(),m(12,"input",9),t(),o(13,"div",7)(14,"label",10),n(15,"Password "),o(16,"a",11),n(17,"Forgot password?"),t()(),m(18,"input",12),t(),o(19,"button",13),n(20,"Sign In"),t(),o(21,"div",14)(22,"label",15)(23,"a",16),n(24,"Signup"),t()()()()()()()),i&2&&(f(7),g("formGroup",a.frmLogin))},dependencies:[d,y,b,S,x,w,C],styles:['.bglogin[_ngcontent-%COMP%]{background:url("./media/loginbg-SYGBLMP5.webp")}']})}}return e})();var W=(()=>{class e{static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275cmp=l({type:e,selectors:[["app-forgot-password"]],decls:21,vars:0,consts:[[1,"page-auth"],[1,"header"],[1,"container"],["routerLink","",1,"header-logo"],[1,"content"],[1,"card","card-auth"],[1,"card-body","text-center"],[1,"mb-5"],["type","image/svg+xml","data",D`../../../assets/svg/forgot_password.svg`,1,"w-50"],[1,"card-title"],[1,"card-text","mb-5"],[1,"row","g-2"],[1,"col-sm-8"],["type","text","placeholder","Enter contact no.",1,"form-control"],[1,"col-sm-4"],["routerLink","",1,"btn","btn-primary"]],template:function(i,a){i&1&&(o(0,"body",0)(1,"div",1)(2,"div",2)(3,"a",3),n(4,"Rachin Export"),t()()(),o(5,"div",4)(6,"div",2)(7,"div",5)(8,"div",6)(9,"div",7),m(10,"object",8),t(),o(11,"h3",9),n(12,"Reset your password"),t(),o(13,"p",10),n(14,"Enter your phone no. address and we will send you a link to reset your password."),t(),o(15,"div",11)(16,"div",12),m(17,"input",13),t(),o(18,"div",14)(19,"a",15),n(20,"Reset"),t()()()()()()()())},dependencies:[d]})}}return e})();var L=j(U());var Y=(()=>{class e{constructor(r,i,a){this.fb=r,this.authService=i,this.route=a}ngOnInit(){this.frmRegister=this.fb.group({name:[],email:[],password:[]})}register(){debugger;let r=this.frmRegister.value;this.authService.register(r).subscribe({next:i=>{L.default.fire({title:"Success",text:"Registration has been successfull",icon:"success"}),this.route.navigate(["/login"])},error:i=>{L.default.fire({title:"Failed",text:"Something went wrong",icon:"warning"})}})}static{this.\u0275fac=function(i){return new(i||e)(s(E),s(R),s(v))}}static{this.\u0275cmp=l({type:e,selectors:[["app-register"]],decls:30,vars:1,consts:[[1,"bglogin","row","vh-100","d-flex","justify-content-center","align-items-center"],[1,"col-md-4","card","card-sign"],[1,"card-header"],[1,"card-title"],[1,"card-text"],[3,"ngSubmit","formGroup"],[1,"card-body"],[1,"mb-4"],[1,"form-label"],["type","text","placeholder","Enter your name","formControlName","name",1,"form-control"],["type","text","placeholder","Enter your email address","formControlName","email",1,"form-control"],[1,"form-label","d-flex","justify-content-between"],["type","password","placeholder","Enter your password","formControlName","password",1,"form-control"],["type","checkbox","name","Remember","id",""],[1,"btn","btn-primary"],[1,"mb-4","mt-2"],[1,"form-label","d-flex"],["routerLink","/login"]],template:function(i,a){i&1&&(o(0,"div",0)(1,"div",1)(2,"div",2)(3,"h3",3),n(4,"Rachin Export"),t(),o(5,"p",4),n(6,"Welcome back! Please signin to continue."),t()(),o(7,"form",5),h("ngSubmit",function(){return a.register()}),o(8,"div",6)(9,"div",7)(10,"label",8),n(11,"Name"),t(),m(12,"input",9),t(),o(13,"div",7)(14,"label",8),n(15,"E-mail"),t(),m(16,"input",10),t(),o(17,"div",7)(18,"label",11),n(19,"Password "),t(),m(20,"input",12),t(),o(21,"div",7),m(22,"input",13),n(23," Remember me "),t(),o(24,"button",14),n(25,"Sign Up"),t(),o(26,"div",15)(27,"label",16)(28,"a",17),n(29,"Sign In"),t()()()()()()()),i&2&&(f(7),g("formGroup",a.frmRegister))},dependencies:[d,y,b,S,x,w,C],styles:['.bglogin[_ngcontent-%COMP%]{background:url("./media/loginbg-SYGBLMP5.webp")}']})}}return e})();var J=[{path:"",component:N},{path:"login",component:N},{path:"forgot-password",component:W},{path:"register",component:Y}],H=(()=>{class e{static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275mod=u({type:e})}static{this.\u0275inj=p({imports:[M.forChild(J),M]})}}return e})();var Se=(()=>{class e{static{this.\u0275fac=function(i){return new(i||e)}}static{this.\u0275mod=u({type:e})}static{this.\u0275inj=p({imports:[P,H,B,T,_,A]})}}return e})();export{Se as AuthModule};
