/* Layout polish for Kati Issue modal */
.kati-modal-container { width: 100%; max-width: 1200px; margin: 0 auto; }

.modal-header { display: flex; justify-content: space-between; align-items: center; padding: 12px 16px; border-bottom: 1px solid #e0e0e0; }
.modal-content { padding: 12px 16px; }
.modal-actions { display: flex; justify-content: flex-end; gap: 8px; padding: 12px 16px; border-top: 1px solid #e0e0e0; }

/* Table sizing and spacing */
.mat-mdc-row { height: 44px !important; }
.mat-mdc-cell, .mat-mdc-header-cell, .mat-mdc-footer-cell { padding: 8px 12px !important; }

/* Align numeric cells and inputs */
.num-cell { text-align: center !important; }
.num-input { width: 120px; height: 34px; padding: 6px 8px; border: 1px solid #d0d7de; border-radius: 6px; font-size: 14px; box-sizing: border-box; text-align: center !important; }
.num-input:focus { border-color: #1976d2; box-shadow: 0 0 0 2px rgba(25,118,210,0.15); outline: none; }
.num-input::placeholder { color: #9aa0a6; }

/* Sticky header/footer for totals visibility */
tr.mat-mdc-header-row, tr.mat-mdc-footer-row { background: #fff;}
tr.mat-mdc-header-row { top: 0; border-bottom: 1px solid #e0e0e0; }
tr.mat-mdc-footer-row { bottom: 0; border-top: 1px solid #e0e0e0; }

/* Totals footer emphasis */
.totals-footer .mat-mdc-footer-cell { font-weight: 600; background: #f9fafb; }
.totals-footer .mat-mdc-footer-cell { color: #1f497d; }
