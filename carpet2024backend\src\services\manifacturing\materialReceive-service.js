const materialReceiveRepository = require('../../repositories/manifacturing/materialReceive-repository');

const createMaterialReceive = async (data) => {
  return await materialReceiveRepository.createMaterialReceive(data);
};

const getAllMaterialReceives = async () => {
  return await materialReceiveRepository.getAllMaterialReceives();
};

const getMaterialReceiveById = async (id) => {
  return await materialReceiveRepository.getMaterialReceiveById(id);
};

const updateMaterialReceive = async (id, data) => {
  return await materialReceiveRepository.updateMaterialReceive(id, data);
};

const deleteMaterialReceive = async (id) => {
  return await materialReceiveRepository.deleteMaterialReceive(id);
};

const getMaterialReceivesByChallanNo = async (challanNo) => {
  return await materialReceiveRepository.getMaterialReceivesByChallanNo(challanNo);
};

const getMaterialReceivesByWeaver = async (weaver) => {
  return await materialReceiveRepository.getMaterialReceivesByWeaver(weaver);
};

const getMaterialReceivesByIssueNo = async (issueNoId) => {
  return await materialReceiveRepository.getMaterialReceivesByIssueNo(issueNoId);
};

module.exports = {
  createMaterialReceive,
  getAllMaterialReceives,
  getMaterialReceiveById,
  updateMaterialReceive,
  deleteMaterialReceive,
  getMaterialReceivesByChallanNo,
  getMaterialReceivesByWeaver,
  getMaterialReceivesByIssueNo,
};

