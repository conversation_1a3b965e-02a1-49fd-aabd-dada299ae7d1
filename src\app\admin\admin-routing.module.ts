import { ExportReportComponent } from './manufacturing/export-report/export-report.component';
import { Component, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

import { DashboardComponent } from './dashboard/dashboard.component';
import { CarpetStockComponent } from './master/carpet-stock/carpet-stock.component';
import { CodeDyeingDetailsComponent } from './master/code-dyeing-details/code-dyeing-details.component';
import { ColorCodeDetailsComponent } from './master/color-code-details/color-code-details.component';
import { FinishingHeadComponent } from './master/finishing-head/finishing-head.component';
import { FinishingProcessComponent } from './master/finishing-process/finishing-process.component';
import { ManageBranchComponent } from './master/manage-branch/manage-branch.component';
import { MapMasterComponent } from './master/map-master/map-master.component';
import { MaterialLagatComponent } from './master/material-lagat/material-lagat.component';

import { QualityDesignComponent } from './master/quality-design/quality-design.component';
import { RawMaterialGroupComponent } from './master/raw-material-group/raw-material-group.component';
import { SizeComponent } from './master/size/size.component';
import { ViewBuyerMasterComponent } from './master/view-buyer-master/view-buyer-master.component';
import { ViewDetailMaterialLagatComponent } from './master/view-detail-material-lagat/view-detail-material-lagat.component';
import { ViewMaterialLagatComponent } from './master/view-material-lagat/view-material-lagat.component';
import { WeaverEmployeeComponent } from './master/weaver-employee/weaver-employee.component';
import { BuyerMasterComponent } from './master/buyer-master/buyer-master.component';
import { MaterialDeyingComponent } from './manufacturing/material-deying/material-deying.component';
import { PurchaseManufacturingComponent } from './manufacturing/purchase-manufacturing/purchase-manufacturing.component';
import { BayerOrderComponent } from './manufacturing/bayer-order/bayer-order.component';
import { DesigningMapOrderComponent } from './manufacturing/designing-map-order/designing-map-order.component';
import { CarpetOrderIssueComponent } from './manufacturing/carpet-order-issue/carpet-order-issue.component';
import { RecivingCarpetComponent } from './manufacturing/reciving-carpet/reciving-carpet.component';
import { CarpetFinishingComponent } from './manufacturing/carpet-finishing/carpet-finishing.component';
import { ImporterDetailComponent } from './koti/master/importer-detail/importer-detail.component';
import { WholesellerCustomerComponent } from './koti/master/wholeseller-customer/wholeseller-customer.component';
import { RetailerCustomerComponent } from './koti/master/retailer-customer/retailer-customer.component';
import { QualityDesignCodeComponent } from './koti/master/quality-design-code/quality-design-code.component';
import { SizeCodeComponent } from './koti/master/size-code/size-code.component';
import { GstComponent } from './koti/master/gst/gst.component';
import { BarCodeDetailsComponent } from './koti/master/bar-code-details/bar-code-details.component';
import { ReportKotiComponent } from './koti/master/report-koti/report-koti.component';
import { OrderReportComponent } from './koti/order-form/order-report/order-report.component';
import { ContainerDespatchComponent } from './koti/import-details/container-despatch/container-despatch.component';
import { ContainerReceivedComponent } from './koti/import-details/container-received/container-received.component';
import { ImportDetailsReportComponent } from './koti/import-details/import-details-report/import-details-report.component';
import { NewChallanSaleComponent } from './koti/sale/new-challan-sale/new-challan-sale.component';
import { ChallanReportComponent } from './koti/sale/challan-report/challan-report.component';
import { BillsForWholesellerComponent } from './koti/sale/bills-for-wholeseller/bills-for-wholeseller.component';
import { BillsForRetailerComponent } from './koti/sale/bills-for-retailer/bills-for-retailer.component';
import { BillsReportComponent } from './koti/sale/bills-report/bills-report.component';
import { StockReportComponent } from './koti/stock/stock-report/stock-report.component';
import { ReportOrderComponent } from './report/report-order/report-order.component';
import { ReportMonthlyComponent } from './report/report-monthly/report-monthly.component';
import { ReportYearlyComponent } from './report/report-yearly/report-yearly.component';
import { ReportStockComponent } from './report/report-stock/report-stock.component';
import { ReportSaleComponent } from './report/report-sale/report-sale.component';
import { ReportGstComponent } from './report/report-gst/report-gst.component';
import { ImportInvoicesComponent } from './koti/import-details/import-invoices/import-invoices.component';
import { ImporterOrderPriceListComponent } from './koti/master/importer-order-price-list/importer-order-price-list.component';
import { DesignComponent } from './master/design/design.component';
import { ViewImporterInvoiceComponent } from './koti/import-details/view-importer-invoice/view-importer-invoice.component';
import { ContDetailsReportComponent } from './report/importer-invoice/cont-details-report/cont-details-report.component';
import { ImporterInvoicesReportComponent } from './report/importer-invoice/importer-invoices-report/importer-invoices-report.component';
import { ContainerDespatchReportComponent } from './koti/import-details/container-despatch-report/container-despatch-report.component';
import { ContainerReceivedReportComponent } from './koti/import-details/container-received-report/container-received-report.component';
import { ImportInvoiceReportComponent } from './koti/import-details/import-invoice-report/import-invoice-report.component';
import { MapRateComponent } from './master/map-rate/map-rate.component';
import { PrintBillComponent } from './koti/order-form/print-bill/print-bill.component';
import { DyeingRateComponent } from './master/dyeing-rate/dyeing-rate.component';
import { CarpetStockDetailsComponent } from './master/carpet-stock-details/carpet-stock-details.component';
import { PurchaseDetailsComponent } from './master/purchase-details/purchase-details.component';
import { RegularMapOrderComponent } from './manufacturing/regular-map-order/regular-map-order.component';
import { CreateBuyerOrderComponent } from './manufacturing/create-buyer-order/create-buyer-order.component';
import { PrintComponent } from './koti/order-form/print/print.component';
import { ViewBuyerOrderComponent } from './manufacturing/view-buyer-order/view-buyer-order.component';
import { BuyerOrderListComponent } from './manufacturing/buyer-order-list/buyer-order-list.component';
import { ViewCarpetOrderIssueComponent } from './manufacturing/view-carpet-order-issue/view-carpet-order-issue.component';
import { PrintCarpetOrderIssueComponent } from './manufacturing/print-carpet-order-issue/print-carpet-order-issue.component';
import { ExportInvoiceComponent } from './manufacturing/export-invoice/export-invoice.component';
import { ExportPackingListComponent } from './manufacturing/export-packing-list/export-packing-list.component';
import { ViewCarpetRecivingComponent } from './manufacturing/view-carpet-reciving/view-carpet-reciving.component';
import { ViewExportPackingListComponent } from './manufacturing/view-export-packing-list/view-export-packing-list.component';
import { ViewMainCarpetRecivingComponentComponent } from './manufacturing/view-main-carpet-reciving.component/view-main-carpet-reciving.component.component';
import { MaterialComponent } from './manufacturing/material-issue&recive/material/material.component';
import { MaterialIssueComponent } from './manufacturing/material-issue&recive/material-issue/material-issue.component';
import { MaterialReceiveComponent } from './manufacturing/material-issue&recive/material-receive/material-receive.component';
import { ViewWeaverMaterialComponent } from './manufacturing/material-issue&recive/view-weaver-material/view-weaver-material.component';
import { PrintMaterialIssueComponent } from './manufacturing/material-issue&recive/material-issue/print-material-issue/print-material-issue.component';
import { PrintMaterialReceiveComponent } from './manufacturing/material-issue&recive/material-receive/print-material-receive/print-material-receive.component';
import { ViewMaterialReceiveComponent } from './manufacturing/material-issue&recive/view-material-receive/view-material-receive.component';
import { WeaverPaymentComponent } from './account/weaver-payment/weaver-payment.component';

const routes: Routes = [
  { path: '', component: DashboardComponent },
  { path: 'dashboard', component: DashboardComponent , data: { title: 'Dashboard' }},
  { path: 'buyer-master', component: BuyerMasterComponent, data: { title: 'Buyer Master' } },
  { path: 'view-buyer-master', component: ViewBuyerMasterComponent,data: { title: 'View Buyer Master' } },
  { path: 'raw-material-group', component: RawMaterialGroupComponent,data: { title: 'Raw Material Group' } },
  { path: 'purchase-details', component: PurchaseDetailsComponent,data: { title: 'Purchase Ledger' } },
  { path: 'weaver-employee', component: WeaverEmployeeComponent, data: { title: 'Weaver Employee' } },
  { path: 'quality', component: QualityDesignComponent ,data: { title: 'Quality' }},
  { path: 'design', component: DesignComponent ,data: { title: 'Design' }},

  { path: 'material-lagat', component: MaterialLagatComponent,data: { title: 'Material Lagat' } },
  { path: 'size', component: SizeComponent,data: { title: 'Size' } },
  { path: 'color-code-details', component: ColorCodeDetailsComponent ,data: { title: 'Color Code Details' }},
  { path: 'code-dyeing-details', component: CodeDyeingDetailsComponent ,data: { title: 'Dyeing Details' } },
  { path: 'dyeing-rate', component:DyeingRateComponent ,data: { title: 'Dyeing Rate' }},
  { path: 'finishing-head', component: FinishingHeadComponent ,data: { title: 'Finishing Head' }},
  { path: 'finishing-process', component: FinishingProcessComponent ,data: { title: 'Finishing Process' }},
  { path: 'manage-branch', component: ManageBranchComponent ,data: { title: 'Manage Branch' }},
  { path: 'carpet-stock', component: CarpetStockComponent ,data: { title: 'Carpet Stock' }},
  { path: 'map-master', component: MapMasterComponent , data: { title: 'Map Master' } },
  { path: 'map-rate', component: MapRateComponent ,data: { title: 'Map Rate' }},
  { path: 'view-material-lagat', component: ViewMaterialLagatComponent ,data: { title: 'View Material Lagat' }},
  {
    path: 'view-detail-material-lagat',
    component: ViewDetailMaterialLagatComponent,data: { title: 'View Detail Material Lagat' }
  },


  { path: 'purches-manufacturing', component: PurchaseManufacturingComponent,data: { title: 'Purchase Manufacture' } },
  { path:'buyerOrderList',component:BuyerOrderListComponent},
  { path:'buyerOrderList/:id',component:BuyerOrderListComponent},
  { path: 'material-deying', component: MaterialDeyingComponent ,data: { title: 'Material Deying' }},
  { path: 'buyer-order', component: BayerOrderComponent ,data: { title: 'Buyer Order' }},
  { path: 'buyer-order/:id', component: BayerOrderComponent ,data: { title: 'Buyer Order' }},
  { path: 'designing-map-order', component: DesigningMapOrderComponent,data: { title: 'Design Map Order' } },
  { path:  'regular-map-order',component:RegularMapOrderComponent,data: { title: 'Regular Map Order' }},
  { path: 'carpet-order-issue', component: CarpetOrderIssueComponent ,data: { title: 'Carpet Order Issue' }},
  { path: 'carpet-order-issue/:id', component: CarpetOrderIssueComponent ,data: { title: 'Carpet Order Issue' }},
  { path: 'export-invoice', component: ExportInvoiceComponent , data: { title: 'Invoice' }},
  { path: 'export-packing-list', component: ExportPackingListComponent},
  { path: 'export-report', component: ExportReportComponent},


  { path: 'reciving-carpet', component: RecivingCarpetComponent ,data: { title: 'Recived Carpet' }},
  {path:'carpet-order-issue-print',component:PrintCarpetOrderIssueComponent
  },
  {path:'carpet-order-issue-print/:id',component:PrintCarpetOrderIssueComponent
  },
  { path: 'carpet-finishing', component: CarpetFinishingComponent ,data: { title: '' }},
  { path: 'importer-detail', component: ImporterDetailComponent ,data: { title: '' }},
  { path: 'wholeseller-customer', component: WholesellerCustomerComponent ,data: { title: '' }},
  { path: 'retailer-customer', component: RetailerCustomerComponent ,data: { title: '' }},
  { path: 'quality-design-code', component: QualityDesignCodeComponent ,data: { title: '' }},
  { path: 'size-code', component: SizeCodeComponent ,data: { title: '' }},
  { path: 'gst', component: GstComponent ,data: { title: '' }},
  { path: 'bar-code-details', component: BarCodeDetailsComponent ,data: { title: '' }},
  { path: 'report-koti', component: ReportKotiComponent ,data: { title: '' }},
  { path: 'order-form-report', component: OrderReportComponent ,data: { title: '' }},
  { path: 'import-invoices', component: ImportInvoicesComponent ,data: { title: '' }},
  { path: 'import-invoices/:id', component: ImportInvoicesComponent ,data: { title: '' }},
  { path: 'import-invoices-report', component: ImportInvoiceReportComponent ,data: { title: '' }},  {
    path: 'importer-order-price-list/:id',
    component: ImporterOrderPriceListComponent,data: { title: '' }  },
  { path: 'container-despatch', component: ContainerDespatchComponent ,data: { title: '' }},
    {
    path: 'container-despatch-report',
    component: ContainerDespatchReportComponent,data: { title: '' }
  },

  { path: 'container-received', component: ContainerReceivedComponent,data: { title: '' } },
  {
    path: 'container-received-report',
    component: ContainerReceivedReportComponent,data: { title: '' }
  },

  { path: 'new-challan-sale', component: NewChallanSaleComponent,data: { title: '' }},
  { path: 'new-challan-sale/:id', component: NewChallanSaleComponent,data: { title: '' } },
  { path: 'challan-report', component: ChallanReportComponent,data: { title: '' } },
  { path: 'bills-for-wholeseller', component: BillsForWholesellerComponent,data: { title: '' } },
  {
    path: 'bills-for-wholeseller/:id',
    component: BillsForWholesellerComponent,data: { title: '' }
  },
  { path: 'bills-for-retailer', component: BillsForRetailerComponent,data: { title: '' } },
  { path: 'container-despatch-report', component: BillsReportComponent ,data: { title: '' }},
  { path: 'stock-report', component: StockReportComponent,data: { title: '' } },

  {
    path: 'importer-invoice-report',
    component: ImporterInvoicesReportComponent,data: { title: '' }
  },
  { path: 'cont-details-report', component: ContDetailsReportComponent,data: { title: '' } },
  { path: 'report-order/:id', component: ReportOrderComponent,data: { title: '' } },
  { path: 'report-monthly', component: ReportMonthlyComponent ,data: { title: '' }},
  { path: 'report-yearly', component: ReportYearlyComponent,data: { title: '' } },
  { path: 'report-stock', component: ReportStockComponent ,data: { title: '' }},
  { path: 'report-sale/:id', component: ReportSaleComponent ,data: { title: '' }},
  { path: 'report-gst', component: ReportGstComponent ,data: { title: '' }},
  { path: 'view-importer-invoice', component: ViewImporterInvoiceComponent ,data: { title: '' }},

  { path: 'bills-report', component: BillsReportComponent ,data: { title: '' }},
  { path: 'print-bill/:id', component: PrintBillComponent ,data: { title: '' }},
  {path:'carpet-stock-details', component:CarpetStockDetailsComponent,data: { title: 'Carpet Stock Details' }},
  {path:'create-buyer-order', component:CreateBuyerOrderComponent,data: { title: '' }},
  {path:'print',component:PrintComponent},
  {path:'view-buyer-order',component:ViewBuyerOrderComponent},
  {path:'view-carpet-order-issue',component:ViewCarpetOrderIssueComponent},
  {path:'view-carpet-reciving',component:ViewCarpetRecivingComponent},
  {path:'view-main-carpet-reciving',component:ViewMainCarpetRecivingComponentComponent},

    {path:'material',component:MaterialComponent},
  {path:'material-issue',component:MaterialIssueComponent},
  {path:'material-receive',component:MaterialReceiveComponent},
  {path:'view-weaver-material',component:ViewWeaverMaterialComponent},
  
  {path:'view-material-receive',component:ViewMaterialReceiveComponent},
  // Print view for Material Issue (by Carpet Order Issue ID)
  {path:'material-issue-print/:id', component: PrintMaterialIssueComponent},
  // Print view for Material Receive (by Carpet Order Issue ID)
  {path:'material-receive-print/:id', component: PrintMaterialReceiveComponent},

  {path:'view-export-packing-list',component:ViewExportPackingListComponent},

  // Account routes
  { path: 'weaver-payment', component: WeaverPaymentComponent, data: { title: 'Weaver Payment' } },
  {
    path: 'weaver-advanced',
    loadComponent: () => import('./account/weaver-advanced/weaver-advanced.component').then(m => m.WeaverAdvancedComponent),
    data: { title: 'Weaver Advanced' }
  },

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class AdminRoutingModule {}
