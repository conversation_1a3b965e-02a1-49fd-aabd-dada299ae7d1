.text-right{
    text-align: end;
  }
  .print-container{
    border: 1px solid #000;
  }

       .text-center {
              text-align: center!important;
          }

          .py-5 {
              padding-top: 3rem!important;
              padding-bottom: 3rem!important;
          }

          .mt-5 {
              margin-top: 3rem!important;
          }

          .my-5 {
              margin-top: 3rem!important;
              margin-bottom: 3rem!important;
          }
.tablecontent2{ border-bottom:2px solid #000; border-top:2px solid #000; padding:15px 0; text-align:center; }
.tablecontent3{ border:2px solid #000; text-align:start; }
.tablecontent3 td{ padding-left:5px; border:2px solid #000; text-align:start; }

.container-fluid{ width:100%; margin-right:auto; margin-left:auto; }
button.btn{ margin-left:auto; }

/* Hide print controls when printing */
@media print {
  .no-print { display: none !important; }

}

/* Kati section emphasis */
.kati-title{ font-size: 20px; font-weight: 500; text-decoration: underline;}
.kati-table{ border:2px solid #000; border-collapse: collapse; }
.kati-table th, .kati-table td{ border:2px solid #000; padding: 6px 8px; }

          .tablecontent4 {
              border-top: 2px solid black;
              margin-top: 50px;
          }



          .tablecontent5 th {
              padding: 7px;
              border-right: 2px solid black;
          }

          .paragraph5 {
              font-weight: bold;
          }

/* Page frame and typography */
#invoice {
  background: #fff;
  padding: 6mm 2mm; /* good print margins */
  border: 2px solid #000; /* outer border */
  box-shadow: 0 0 0 2px #666 inset; /* subtle inner frame */
  box-sizing: border-box; /* include padding/border in height calc */
}

.company-header {
  text-align: center;
  padding: 10px 0 10px;
  border-bottom: 2px solid #000;
  margin-bottom: 10px;
}
.company-header .title {
  font-size: 28px;
  font-weight: 700;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  text-decoration: underline;

}
.company-header .sub {
  font-size: 16px;
  padding-top: 5px;
}

/* Signature section */
.signatures {
  display: flex;
  justify-content: space-between;
  margin-top: 36px;
}
.signature-block {
  width: 20%;
  text-align: center;
  font-weight: 600;
}
.signature-space { height: 60px; }
.signature-line {
  border-top: 1.5px solid #000;
}
.signature-label {
  margin-top: 8px;
  margin-bottom: 0;
}

/* Print tweaks */
@media print {
  @page { size: A4; margin: 6mm 5mm; }
  html, body { background: #fff; width: 210mm; height: 297mm; margin: 0; padding: 0; }
  #invoice { box-shadow: none; min-height: calc(297mm - 11mm); display: flex; flex-direction: column; }
  #invoice > section:last-of-type { margin-bottom: 0 !important; }
  .signatures { margin-top: auto; }
}
