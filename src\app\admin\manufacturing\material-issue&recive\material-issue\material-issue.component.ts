import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';
import { ManufactureService } from '../../../../services/manufacture.service';
import { MasterService } from '../../../../services/master.service';
import { KatiModalComponent } from './modal/kati-modal/kati-modal.component';
import Swal from 'sweetalert2';


interface WeaverData {
displayName: any;
  id: string;
  branch: string;
  weaver: string;
}

interface IssueData {
  _id: string;
  Br_issueNo: string;
  weaver: string;
  quality: string;
  design: string;
  borderColour: string;
  size: string;
  pcs: number;
  area: string;
}

@Component({
  selector: 'app-material-issue',
  templateUrl: './material-issue.component.html',
  styleUrl: './material-issue.component.css'
})
export class MaterialIssueComponent implements OnInit {
  materialIssueForm!: FormGroup;
  list: WeaverData[] = [];
  availableIssues: IssueData[] = [];
  selectedWeaverIssues: IssueData[] = [];
  allOrderList: any[] = [];
  filterData: any[] = [];
  materialLagatList: any[] = [];
  rawMaterialList: any[] = [];
  selectedIssue: any = null;
  selectedWeaver: any = null;

  // Kati data storage
  katiData: any[] = [];
  katiDisplayedColumns: string[] = ['srNo', 'colour', 'lagat', 'carpetLagat', 'issue', 'toIssue'];
  katiDataSource = new MatTableDataSource<any>([]);
  // Kati totals shown under the table
  totalKatiLagat: number = 0;
  totalKatiCarpetLagat: number = 0;
  totalKatiIssued: number = 0;
  totalKatiToIssue: number = 0;

  // Prevent immediate re-open of Kati modal due to backdrop click-through
  private isKatiModalOpen = false;
  private lastKatiDialogCloseAt = 0;

  displayedColumns: string[] = ['weaver', 'issueNo', 'quality', 'design', 'colour', 'size', 'pcs', 'area'];
  dataSource = new MatTableDataSource<any>([]);
  isEditMode: any;
  pendingEditData: any = null;
  editModeIssueObjectId: any = null;
  // When true, after save we will navigate to print page
  private printAfterSave: boolean = false;
  dataLoadedFlags = {
    weavers: false,
    rawMaterials: false,
    materialLagat: false
  };

  constructor(
    private fb: FormBuilder,
    private router: Router,
    private route: ActivatedRoute,
    private manufactureService: ManufactureService,
    private masterService: MasterService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    console.log('🚀 Material Issue Component Initialized');
    this.initializeForm();
    this.loadWeavers();
    this.loadRawMaterials();
    this.loadMaterialLagat();

    // Check if we're in edit mode
    this.route.queryParams.subscribe(params => {
      if (params['editMode'] === 'true' && params['editId']) {
        console.log('🔍 Edit mode detected, ID:', params['editId']);
        this.isEditMode = true;
        this.loadEditData(params['editId']);
      } else {
        this.isEditMode = false;
        this.generateChallanNo();
      }
    });
  }

  loadEditData(editId: string): void {
    console.log('🔍 Loading edit data for ID:', editId);

    // Get the edit data from navigation state or fetch from API
    const editData = history.state?.editData;

    if (editData) {
      console.log('🔍 Edit data from navigation state:', editData);
      // Store edit data to populate after all data is loaded
      this.pendingEditData = editData;
      this.checkAndPopulateEditData();
    } else {
      // Fallback: fetch from API
      this.manufactureService.getMaterialIssue(editId).subscribe({
        next: (data: any) => {
          console.log('🔍 Edit data from API:', data);
          this.pendingEditData = data;
          this.checkAndPopulateEditData();
        },
        error: (err) => {
          console.error('❌ Error loading edit data:', err);
          Swal.fire({
            title: 'Error!',
            text: 'Failed to load material issue data for editing.',
            icon: 'error',
            confirmButtonText: 'OK'
          });
          this.generateChallanNo(); // Fallback to new entry
        }
      });
    }
  }

  checkAndPopulateEditData(): void {
    // Check if all required data is loaded
    if (this.dataLoadedFlags.weavers &&
        this.dataLoadedFlags.rawMaterials &&
        this.dataLoadedFlags.materialLagat &&
        this.pendingEditData) {
      console.log('🔍 All data loaded, populating form with edit data');
      this.populateFormWithEditData(this.pendingEditData);
      this.pendingEditData = null; // Clear pending data
    }
  }

  populateFormWithEditData(data: any): void {
    console.log('🔍 Populating form with edit data:', data);

    // Get weaver name for form population
    let weaverName = '';
    if (typeof data.weaver === 'object' && data.weaver?.name) {
      weaverName = data.weaver.name;
    } else if (typeof data.weaver === 'string') {
      weaverName = data.weaver;
    }

    // Get issue number for form population
    let issueNumber = '';
    if (typeof data.issueNo === 'object' && data.issueNo?.Br_issueNo) {
      issueNumber = data.issueNo.Br_issueNo;
    } else if (typeof data.issueNo === 'string') {
      issueNumber = data.issueNo;
    }

    // Store the original issue ObjectId for later use
    this.editModeIssueObjectId = data.issueNo;

    // Populate basic form fields
    this.materialIssueForm.patchValue({
      challanNo: data.challanNo,
      date: new Date(data.date),
      weaver: weaverName,
      issueNo: issueNumber
    });

    // Populate material data if available
    if (data.materials) {
      const materials = data.materials;

      // Helper function to get description text
      const getDescriptionText = (description: any) => {
        if (!description) return '';
        if (typeof description === 'string') return description;
        if (typeof description === 'object') {
          // RawMaterialGroup has Description field (capital D)
          return description.Description || description.description || description.name || description.title || '';
        }
        return '';
      };

      // Populate kati data
      if (materials.kati) {
        this.materialIssueForm.patchValue({
          katiDescription: getDescriptionText(materials.kati.description),
          katiLagat: materials.kati.lagat || '',
          katiIssue: materials.kati.issue || ''
        });
      }

      // Populate tana data
      if (materials.tana) {
        this.materialIssueForm.patchValue({
          tanaDescription: getDescriptionText(materials.tana.description),
          tanaLagat: materials.tana.lagat || '',
          tanaIssue: materials.tana.issue || ''
        });
      }

      // Populate soot data
      if (materials.soot) {
        this.materialIssueForm.patchValue({
          sootDescription: getDescriptionText(materials.soot.description),
          sootLagat: materials.soot.lagat || '',
          sootIssue: materials.soot.issue || ''
        });
      }

      // Populate thari data
      if (materials.thari) {
        this.materialIssueForm.patchValue({
          thariDescription: getDescriptionText(materials.thari.description),
          thariLagat: materials.thari.lagat || '',
          thariIssue: materials.thari.issue || ''
        });
      }

      // Populate silk data
      if (materials.silk) {
        this.materialIssueForm.patchValue({
          silkDescription: getDescriptionText(materials.silk.description),
          silkLagat: materials.silk.lagat || '',
          silkIssue: materials.silk.issue || ''
        });
      }

      // Populate other data
      if (materials.other) {
        this.materialIssueForm.patchValue({
          otherDescription: getDescriptionText(materials.other.description),
          otherLagat: materials.other.lagat || '',
          otherIssue: materials.other.issue || ''
        });
      }
    }

    // Populate katiData if available
    if (data.katiData && Array.isArray(data.katiData)) {
      console.log('🔍 Populating katiData:', data.katiData);
      this.katiData = data.katiData.map((item: any, index: number) => ({
        ...item,
        srNo: index + 1,
        // Keep original values for editing
        issueValue: item.issueValue || '',
        toIssueValue: item.toIssueValue || ''
      }));
      this.katiDataSource.data = this.katiData;
      console.log('✅ KatiData populated:', this.katiData);
    }

    // Load issue details for edit mode
    this.loadIssueDetailsForEdit(data);

    // Disable weaver, issue number, challan number, and date fields in edit mode
    this.materialIssueForm.get('weaver')?.disable();
    this.materialIssueForm.get('issueNo')?.disable();
    this.materialIssueForm.get('challanNo')?.disable();
    this.materialIssueForm.get('date')?.disable();

    console.log('✅ Form populated with edit data and fields disabled');
  }

  loadIssueDetailsForEdit(data: any): void {
    console.log('🔍 Loading issue details for edit mode:', data);

    // Get weaver name and issue number from edit data
    let weaverName = '';
    if (typeof data.weaver === 'object' && data.weaver?.name) {
      weaverName = data.weaver.name;
    } else if (typeof data.weaver === 'string') {
      weaverName = data.weaver;
    }

    let issueNumber = '';
    if (typeof data.issueNo === 'object' && data.issueNo?.Br_issueNo) {
      issueNumber = data.issueNo.Br_issueNo;
    } else if (typeof data.issueNo === 'string') {
      issueNumber = data.issueNo;
    }

    console.log('🔍 Edit mode - Weaver:', weaverName, 'Issue:', issueNumber);

    // Find matching issues for this weaver and issue number
    if (weaverName && issueNumber && this.allOrderList) {
      const matchingIssues = this.allOrderList.filter(item => {
        const itemWeaverName = typeof item.weaver === 'object' && item.weaver?.name
          ? item.weaver.name
          : item.weaver;

        return itemWeaverName === weaverName && item.Br_issueNo === issueNumber;
      });

      console.log('🔍 Found matching issues for edit:', matchingIssues);

      if (matchingIssues.length > 0) {
        // Set the issue details data
        this.dataSource.data = matchingIssues;
        this.filterData = matchingIssues;

        // Set selected objects
        this.selectedWeaver = this.list.find(w => w.weaver === weaverName);
        this.selectedWeaverIssues = matchingIssues;
        this.availableIssues = matchingIssues.filter((item, index, self) =>
          index === self.findIndex(t => t.Br_issueNo === item.Br_issueNo)
        );

        // Set the selected issue number in the form
        this.materialIssueForm.patchValue({
          issueNo: issueNumber
        });

        console.log('✅ Issue details loaded for edit mode');
        console.log('✅ DataSource data:', this.dataSource.data);
      }
    }
  }

  initializeSelectedObjects(): void {
    const formData = this.materialIssueForm.value;

    // Initialize selectedWeaver if weaver is selected
    if (formData.weaver && this.list.length > 0) {
      this.selectedWeaver = this.list.find(w => w.weaver === formData.weaver);
      console.log('🔍 Initialized Selected Weaver:', this.selectedWeaver);

      // If weaver is found, trigger weaver change to load issues
      if (this.selectedWeaver) {
        this.onWeaverChange();
      }
    }

    // Initialize selectedIssue if issue is selected (after weaver change)
    setTimeout(() => {
      if (formData.issueNo && this.availableIssues.length > 0) {
        this.selectedIssue = this.availableIssues.find(issue => issue.Br_issueNo === formData.issueNo);
        console.log('🔍 Initialized Selected Issue:', this.selectedIssue);

        // If issue is found, trigger issue change to load data
        if (this.selectedIssue) {
          this.onIssueNoChange();
        }
      }
    }, 100);
  }

  initializeForm(): void {
    this.materialIssueForm = this.fb.group({
      challanNo: ['', Validators.required],
      date: [new Date(), Validators.required],
      weaver: ['', Validators.required],
      issueNo: ['', Validators.required],
      // Description fields
      katiDescription: [''],
      tanaDescription: [''],
      sootDescription: [''],
      thariDescription: [''],
      silkDescription: [''],
       otherDescription: [''],
      // Lagat fields
      katiLagat: [''],
      tanaLagat: [''],
      sootLagat: [''],
      thariLagat: [''],
      silkLagat: [''],
        otherLagat: [''],
      // Issue fields
      katiIssue: [''],
      tanaIssue: [''],
      sootIssue: [''],
      thariIssue: [''],
      silkIssue: [''],
      otherIssue: ['']
    });
  }

  generateChallanNo(): void {
    // Get all material issues to find the highest challan number (always use next highest)
    this.manufactureService.getAllMaterialIssues().subscribe({
      next: (value: any) => {
        console.log('🔍 All existing challan numbers:', value.map((item: any) => item.challanNo));

        let nextChallanNo = 'MI-2400001';

        if (value && value.length > 0) {
          // Find the highest sequential number across all challan numbers
          let maxNum = 2400000;

          value.forEach((item: any) => {
            if (item.challanNo && item.challanNo.includes('-')) {
              const parts = item.challanNo.split('-');
              const num = parseInt(parts[1]);
              if (!isNaN(num) && num > maxNum) {
                maxNum = num;
                console.log('🔍 New max challan number found:', maxNum);
              }
            }
          });

          // Always generate next sequential number (highest + 1)
          const nextSequentialNum = maxNum + 1;
          nextChallanNo = `MI-${nextSequentialNum}`;

          console.log('🔍 Generated next challan number:', nextChallanNo);
          console.log('🔍 Next sequential number:', nextSequentialNum);
        } else {
          console.log('🔍 No existing challan numbers found, using default starting number');
        }

        this.materialIssueForm.patchValue({ challanNo: nextChallanNo });
      },
      error: (err) => {
        console.error('Error fetching challan numbers:', err);
        // fallback
        this.materialIssueForm.patchValue({ challanNo: 'MI-2400001' });
      }
    });
  }

  setDefaultDescriptions(): void {
    // Set default placeholder values to show the expected format
    this.materialIssueForm.patchValue({
      katiDescription: 'Kati Description',
      tanaDescription: 'Tana Description',
      sootDescription: 'Soot Description',
      thariDescription: 'Tharri Description',
      silkDescription: 'Silk Description',
      otherDescription: 'Other Description'
    });
  }

  loadWeavers(): void {
    console.log('Starting to load weavers...');

    // Use populated API to get carpet order issues data with weaver details
    this.manufactureService.getPopulatedOrderIssueList()
      .subscribe({
        next: (data: any) => {
          console.log('✅ Carpet Order Issues Data:', data);
          console.log('✅ Data length:', data?.length);

          if (data && data.length > 0) {
            this.allOrderList = data;

            // Extract unique weavers with their ObjectIds from the populated weaver objects
            const uniqueWeavers = new Map();

            data.forEach((item: any) => {
              console.log('🔍 Processing item weaver:', item.weaver);

              if (typeof item.weaver === 'object' && item.weaver?.name && item.weaver?._id) {
                // Get branch code from populated branch data
                let branchCode = 'K'; // Default

                console.log('🔍 Weaver branch data:', item.weaver.branch);

                if (item.weaver.branch && typeof item.weaver.branch === 'object' && item.weaver.branch.branchCode) {
                  branchCode = item.weaver.branch.branchCode;
                  console.log('🔍 Found branch code from object:', branchCode);
                } else if (item.weaver.branch && typeof item.weaver.branch === 'string') {
                  branchCode = item.weaver.branch;
                  console.log('🔍 Found branch code from string:', branchCode);
                } else {
                  console.log('🔍 Using default branch code:', branchCode);
                }

                // Store both name and ObjectId for populated weaver objects
                uniqueWeavers.set(item.weaver.name, {
                  id: item.weaver._id,
                  name: item.weaver.name,
                  branchCode: branchCode,
                  originalWeaver: item.weaver // Store original weaver object for edit mode
                });
              } else if (typeof item.weaver === 'string') {
                // For string weavers, we'll need to find their ObjectId differently
                // For now, store the string as both id and name
                uniqueWeavers.set(item.weaver, {
                  id: item.weaver,
                  name: item.weaver,
                  branchCode: 'K',
                  originalWeaver: item.weaver
                });
              }
            });

            console.log('✅ Unique weavers with IDs:', Array.from(uniqueWeavers.values()));

            this.list = Array.from(uniqueWeavers.values()).map((weaverData: any) => ({
              id: weaverData.id,
              branch: weaverData.branchCode,
              weaver: weaverData.name, // Store original name for comparison
              displayName: `${weaverData.branchCode} - ${weaverData.name}`, // Display format
              originalWeaver: weaverData.originalWeaver
            }));

            console.log('✅ Final weaver list:', this.list);
            console.log('✅ Sample weaver display names:', this.list.slice(0, 3).map(w => w.displayName));

            // Set flag and check if we can populate edit data
            this.dataLoadedFlags.weavers = true;
            this.checkAndPopulateEditData();

            // Initialize selected objects after data is loaded
            this.initializeSelectedObjects();
          } else {
            console.log('⚠️ No data received from API');
          }
        },
        error: (error) => {
          console.error('❌ Error loading data:', error);
          console.error('❌ Error details:', error.message);
          console.error('❌ Error status:', error.status);
        }
      });
  }

  loadRawMaterials(): void {
    console.log('Loading Raw Materials data...');
    this.masterService.getsRawMaterial()
      .subscribe({
        next: (data: any) => {
          console.log('✅ Raw Materials Data:', data);
          this.rawMaterialList = data;

          // Debug: Show structure of first few items
          if (data && data.length > 0) {
            console.log('🔍 First raw material item structure:', data[0]);
            console.log('🔍 Available properties:', Object.keys(data[0]));
            console.log('🔍 Sample items (first 3):', data.slice(0, 3));
          }

          // Set flag and check if we can populate edit data
          this.dataLoadedFlags.rawMaterials = true;
          this.checkAndPopulateEditData();

          this.resolveMaterialDescriptions();
        },
        error: (error) => {
          console.error('❌ Error loading Raw Materials data:', error);
        }
      });
  }

  setDigitKatiIssue(val: any) {
    const formattedValue = this.formatIssueValue(val.target.value);
    this.materialIssueForm.get('katiIssue')?.patchValue(formattedValue);
  }

  setDigitTanaIssue(val: any) {
    const formattedValue = this.formatIssueValue(val.target.value);
    this.materialIssueForm.get('tanaIssue')?.patchValue(formattedValue);
  }

  setDigitSootIssue(val: any) {
    const formattedValue = this.formatIssueValue(val.target.value);
    this.materialIssueForm.get('sootIssue')?.patchValue(formattedValue);
  }

  setDigitTharriIssue(val: any) {
    const formattedValue = this.formatIssueValue(val.target.value);
    this.materialIssueForm.get('thariIssue')?.patchValue(formattedValue);
  }

  setDigitSilkIssue(val: any) {
    const formattedValue = this.formatIssueValue(val.target.value);
    this.materialIssueForm.get('silkIssue')?.patchValue(formattedValue);
  }

  setDigitOtherIssue(val: any) {
    const formattedValue = this.formatIssueValue(val.target.value);
    this.materialIssueForm.get('otherIssue')?.patchValue(formattedValue);
  }




  loadMaterialLagat(): void {
    console.log('Loading Material Lagat data...');
    this.masterService.getsMaterialLagat()
      .subscribe({
        next: (data: any) => {
          console.log('✅ Material Lagat Data:', data);
          console.log('🔍 First Material Lagat Item Structure:', data[0]);
          console.log('🔍 Description Fields Check:', {
            katiDescription: data[0]?.katiDescription,
            tanaDescription: data[0]?.tanaDescription,
            sootDescription: data[0]?.sootDescription,
            tharriDescription: data[0]?.tharriDescription,
            silkDescription: data[0]?.silkDescription,
            itemArray: data[0]?.item
          });
          this.materialLagatList = data;

          // Set flag and check if we can populate edit data
          this.dataLoadedFlags.materialLagat = true;
          this.checkAndPopulateEditData();

          this.resolveMaterialDescriptions();
        },
        error: (error) => {
          console.error('❌ Error loading Material Lagat data:', error);
        }
      });
  }

  resolveMaterialDescriptions(): void {
    console.log('🔄 Resolving Material Descriptions...');

    if (this.materialLagatList.length === 0 || this.rawMaterialList.length === 0) {
      console.log('⏳ Waiting for both Material Lagat and Raw Materials data...');
      return;
    }

    this.materialLagatList.forEach((lagat: any, index: number) => {
      console.log(`🔍 Processing Material Lagat ${index + 1}:`, lagat);

      // Resolve description ObjectIds to actual text
      const resolvedDescriptions = {
        katiDescription: this.getDescriptionById(lagat.katiDescription),
        tanaDescription: this.getDescriptionById(lagat.tanaDescription),
        sootDescription: this.getDescriptionById(lagat.sootDescription),
        tharriDescription: this.getDescriptionById(lagat.tharriDescription),
        silkDescription: this.getDescriptionById(lagat.silkDescription),
        // Handle otherDescription from item array if it exists
        otherDescription: lagat.item && lagat.item.length > 0 ? this.getDescriptionById(lagat.item[0]?.description) : ''
      };

      // Resolve lagat ObjectIds to actual values
      const resolvedLagats = {
        katiLagat: this.getLagatById(lagat.katiLagat),
        tanaLagat: this.getLagatById(lagat.tanaLagat),
        sootLagat: this.getLagatById(lagat.sootLagat),
        tharriLagat: this.getLagatById(lagat.tharriLagat),
        silkLagat: this.getLagatById(lagat.silkLagat),
        // Handle otherLagat from item array if it exists
        otherLagat: lagat.item && lagat.item.length > 0 ? this.getLagatById(lagat.item[0]) : ''
      };

      // Update the lagat object with resolved values
      Object.assign(lagat, resolvedDescriptions, resolvedLagats);

      console.log(`✅ Resolved Material Lagat ${index + 1}:`, {
        descriptions: resolvedDescriptions,
        lagats: resolvedLagats
      });
    });

    console.log('🎉 All Material Descriptions Resolved!', this.materialLagatList);

    // After resolving descriptions, populate form fields if issue is selected
    if (this.filterData.length > 0) {
      console.log('🔄 Re-populating form fields after description resolution...');
      this.populateDescriptionFields();
    }
  }

  getDescriptionById(id: string): string {
    if (!id) return '';
    const rawMaterial = this.rawMaterialList.find((item: any) => item._id === id);
    return rawMaterial ? rawMaterial.description : id;
  }

  getLagatById(lagatData: any): string {
    console.log('🔍 getLagatById called with:', lagatData);

    if (!lagatData) {
      console.log('❌ No lagat data provided');
      return '';
    }

    // If it's already a string (direct lagat value)
    if (typeof lagatData === 'string') {
      console.log('✅ Direct string lagat:', lagatData);
      return lagatData;
    }

    // If it's an object with lagat property (from item array)
    if (typeof lagatData === 'object' && lagatData.lagat) {
      console.log('✅ Object with lagat property:', lagatData.lagat);
      return lagatData.lagat;
    }

    // If it's an ObjectId, try to resolve from rawMaterialList
    if (typeof lagatData === 'string' && lagatData.match(/^[0-9a-fA-F]{24}$/)) {
      console.log('🔍 Trying to resolve ObjectId:', lagatData);
      const rawMaterial = this.rawMaterialList.find((item: any) => item._id === lagatData);
      if (rawMaterial && rawMaterial.lagat) {
        console.log('✅ Resolved from rawMaterial:', rawMaterial.lagat);
        return rawMaterial.lagat;
      }
    }

    console.log('❌ Could not resolve lagat, returning original:', lagatData);
    return lagatData.toString();
  }

  getResolvedDescription(descriptionData: any, defaultText: string): string {
    if (!descriptionData) {
      return defaultText;
    }

    // If it's already resolved as a string (from our resolveMaterialDescriptions method)
    if (typeof descriptionData === 'string' && descriptionData !== '' && !descriptionData.match(/^[0-9a-fA-F]{24}$/)) {
      return descriptionData;
    }

    // If it's still an ObjectId, try to resolve it from rawMaterialList
    if (typeof descriptionData === 'string' && descriptionData.match(/^[0-9a-fA-F]{24}$/)) {
      const rawMaterial = this.rawMaterialList.find((item: any) => item._id === descriptionData);
      return rawMaterial ? rawMaterial.description : defaultText;
    }

    // If it's an object with description property
    if (typeof descriptionData === 'object' && descriptionData.description) {
      return descriptionData.description;
    }

    return defaultText;
  }

  getResolvedLagat(lagatData: any, defaultValue: string): string {
    if (!lagatData) {
      return defaultValue;
    }

    // If it's already resolved as a string (from our resolveMaterialDescriptions method)
    if (typeof lagatData === 'string' && lagatData !== '' && !lagatData.match(/^[0-9a-fA-F]{24}$/)) {
      return lagatData;
    }

    // If it's still an ObjectId, try to resolve it from rawMaterialList
    if (typeof lagatData === 'string' && lagatData.match(/^[0-9a-fA-F]{24}$/)) {
      const rawMaterial = this.rawMaterialList.find((item: any) => item._id === lagatData);
      return rawMaterial ? rawMaterial.lagat : defaultValue;
    }

    // If it's an object with lagat property
    if (typeof lagatData === 'object' && lagatData.lagat) {
      return lagatData.lagat;
    }

    return defaultValue;
  }

  getLagatValue(lagatData: any): string {
    if (!lagatData) return '';

    // If it's already resolved as a string (from our resolveMaterialDescriptions method)
    if (typeof lagatData === 'string' && lagatData !== '' && !lagatData.match(/^[0-9a-fA-F]{24}$/)) {
      return lagatData;
    }

    // If it's an ObjectId string, try to resolve it from rawMaterialList
    if (typeof lagatData === 'string' && lagatData.match(/^[0-9a-fA-F]{24}$/)) {
      const resolved = this.getLagatById(lagatData);
      return resolved !== lagatData ? resolved : '';
    }

    // If it's a populated object with lagat field
    if (typeof lagatData === 'object' && lagatData.lagat) {
      return lagatData.lagat;
    }

    // If it's just a string
    if (typeof lagatData === 'string') {
      return lagatData;
    }

    return '';
  }

  onWeaverChange(): void {
    const selectedWeaverName = this.materialIssueForm.get('weaver')?.value;
    console.log('Selected Weaver:', selectedWeaverName);

    if (selectedWeaverName) {
      // Find and set the selected weaver object
      this.selectedWeaver = this.list.find(w => w.weaver === selectedWeaverName);
      console.log('🔍 Selected Weaver Object:', this.selectedWeaver);

      // Filter issues for selected weaver (handle both populated and string weaver data)
      this.selectedWeaverIssues = this.allOrderList.filter(item => {
        const weaverName = typeof item.weaver === 'object' && item.weaver?.name
          ? item.weaver.name
          : item.weaver;

        // Remove branch code prefix for comparison if present
        const cleanSelectedName = selectedWeaverName.includes(' - ')
          ? selectedWeaverName.split(' - ')[1]
          : selectedWeaverName;

        console.log('🔍 Comparing Weaver:', weaverName, 'with Selected:', cleanSelectedName);
        return weaverName === cleanSelectedName;
      });

      // Extract unique issue numbers for this weaver
      this.availableIssues = this.selectedWeaverIssues.filter((item, index, self) =>
        index === self.findIndex(t => t.Br_issueNo === item.Br_issueNo)
      );

      console.log('Available Issues for Weaver:', this.availableIssues);

      // Reset issue number selection
      this.materialIssueForm.patchValue({ issueNo: '' });
      this.dataSource.data = [];
    } else {
      this.selectedWeaver = null;
      this.availableIssues = [];
      this.selectedWeaverIssues = [];
      this.dataSource.data = [];
    }
  }

  onIssueNoChange(): void {
    const selectedIssueNo = this.materialIssueForm.get('issueNo')?.value;
    const selectedWeaver = this.materialIssueForm.get('weaver')?.value;

    console.log('🔍 Selected Issue No:', selectedIssueNo);
    console.log('🔍 Selected Weaver:', selectedWeaver);
    console.log('🔍 All Order List Length:', this.allOrderList.length);
    console.log('🔍 Material Lagat List Length:', this.materialLagatList.length);

    if (selectedIssueNo && selectedWeaver) {
      // Find and set the selected issue object
      this.selectedIssue = this.availableIssues.find(issue => issue.Br_issueNo === selectedIssueNo);
      console.log('🔍 Selected Issue Object:', this.selectedIssue);

      // Filter data for selected weaver and issue number (handle populated weaver data)
      this.filterData = this.allOrderList.filter(item => {
        const weaverName = typeof item.weaver === 'object' && item.weaver?.name
          ? item.weaver.name
          : item.weaver;
        return weaverName === selectedWeaver && item.Br_issueNo === selectedIssueNo;
      });

      console.log('🎯 Filtered Data for Table:', this.filterData);

      // Update table data source
      this.dataSource.data = this.filterData;

      // Auto-populate description fields based on issue data
      this.populateDescriptionFields();
    } else {
      this.selectedIssue = null;
      this.dataSource.data = [];
      this.clearDescriptionFields();
    }
  }



  populateDescriptionFields(): void {
    console.log('🚀 populateDescriptionFields() called');
    console.log('🔍 Filter Data Length:', this.filterData.length);
    console.log('🔍 Material Lagat List Length:', this.materialLagatList.length);

    if (this.filterData.length > 0 && this.materialLagatList.length > 0) {
      const issueData = this.filterData[0]; // Get first item from filtered data
      console.log('🔍 Issue Data for matching:', issueData);
      console.log('🔍 Material Lagat List:', this.materialLagatList);

      // Extract quality, design, and color IDs from issue data
      const qualityId = typeof issueData.quality === 'object' ? issueData.quality._id : issueData.quality;
      const designId = typeof issueData.design === 'object' ? issueData.design._id : issueData.design;
      const borderColorId = typeof issueData.borderColour === 'object' ? issueData.borderColour._id : issueData.borderColour;

      console.log('🎯 Looking for match with:', {
        qualityId,
        designId,
        borderColorId,
        rawQuality: issueData.quality,
        rawDesign: issueData.design
      });

      // Find matching material lagat entry
      const matchingLagat = this.materialLagatList.find(lagat => {
        const lagatQualityId = typeof lagat.quality === 'object' ? lagat.quality._id : lagat.quality;
        const lagatDesignId = typeof lagat.AddDesign === 'object' ? lagat.AddDesign._id : lagat.AddDesign;

        // Check if quality and design match
        const qualityMatch = lagatQualityId === qualityId;
        const designMatch = lagatDesignId === designId;

        console.log('🔍 Checking lagat:', {
          lagatId: lagat._id,
          lagatQualityId,
          lagatDesignId,
          qualityMatch,
          designMatch,
          lagat: lagat
        });

        return qualityMatch && designMatch;
      });

      console.log('🎯 Matching Lagat found:', matchingLagat);

      if (matchingLagat) {
        // Get otherLagat from item array (item[0] contains the lagat values)
        const itemData = matchingLagat.item && matchingLagat.item.length > 0 ? matchingLagat.item[0] : {};

        // Helper function to extract description from object or string
        const getDescription = (data: any): string => {
          if (!data) return '';
          if (typeof data === 'string') return data;
          if (typeof data === 'object' && data.Description) return data.Description;
          return '';
        };

        // Helper function to extract lagat from object or string
        const getLagat = (data: any): string => {
          if (!data) return '';
          if (typeof data === 'string') return data;
          if (typeof data === 'object') {
            // For otherLagat, use 'lagat' property, for others use 'Count'
            return data.lagat || data.Count || '';
          }
          return '';
        };

        // Populate description fields with data from matching lagat
        const descriptions = {
          katiDescription: getDescription(matchingLagat.katiDescription),
          tanaDescription: getDescription(matchingLagat.tanaDescription),
          sootDescription: getDescription(matchingLagat.sootDescription),
          thariDescription: getDescription(matchingLagat.tharriDescription),
          silkDescription: getDescription(matchingLagat.silkDescription),
          otherDescription: getDescription(matchingLagat.otherDescription) || getDescription(itemData.description)
        };

        // Calculate lagat fields by multiplying area with lagat values
        const totalArea = this.calculateTotalArea();
        console.log('🔍 Total Area for calculation:', totalArea);

        // Get base lagat values from matching lagat
        const baseLagatValues = {
          katiLagat: parseFloat(getLagat(matchingLagat.katiLagat)) || 0,
          tanaLagat: parseFloat(getLagat(matchingLagat.tanaLagat)) || 0,
          sootLagat: parseFloat(getLagat(matchingLagat.sootLagat)) || 0,
          thariLagat: parseFloat(getLagat(matchingLagat.tharriLagat)) || 0, // Note: DB field is 'tharriLagat' not 'thariLagat'
          silkLagat: parseFloat(getLagat(matchingLagat.silkLagat)) || 0,
          otherLagat: parseFloat(getLagat(matchingLagat.otherLagat) || getLagat(itemData.lagat) || '0') || 0
        };

        console.log('🔍 Base Lagat Values:', baseLagatValues);

        // Calculate final lagat values (area * lagat)
        const lagatValues = {
          katiLagat: (totalArea * baseLagatValues.katiLagat).toFixed(3),
          tanaLagat: (totalArea * baseLagatValues.tanaLagat).toFixed(3),
          sootLagat: (totalArea * baseLagatValues.sootLagat).toFixed(3),
          thariLagat: (totalArea * baseLagatValues.thariLagat).toFixed(3),
          silkLagat: (totalArea * baseLagatValues.silkLagat).toFixed(3),
          otherLagat: (totalArea * baseLagatValues.otherLagat).toFixed(3)
        };

        console.log('🔍 Calculated Lagat Values:', lagatValues);

        console.log('📝 Populating with descriptions:', descriptions);
        console.log('💰 Populating with lagat values:', lagatValues);
        console.log('🔍 Complete itemData structure:', itemData);

        // Combine both descriptions and lagat values
        const formData = {
          ...descriptions,
          ...lagatValues
        };

        console.log('🔧 Form data to be set:', formData);
        console.log('🔧 Current form values before patch:', this.materialIssueForm.value);

        // Set form values using patchValue with timeout to ensure form is ready
        setTimeout(() => {
          this.materialIssueForm.patchValue(formData);

          // Force form update and mark as touched to trigger UI update
          this.materialIssueForm.markAsTouched();
          this.materialIssueForm.updateValueAndValidity();

          console.log('🔧 Form values after patchValue:', this.materialIssueForm.value);
          console.log('✅ Description and Lagat fields populated successfully');
        }, 100);
      } else {
        console.log('⚠️ No matching material lagat found, using default values');
        // Use default values if no match found
        this.materialIssueForm.patchValue({
          katiDescription: 'Kati',
          tanaDescription: 'Tana',
          sootDescription: 'Soot',
          thariDescription: 'Thari',
          silkDescription: 'Silk',
          otherDescription: 'Other',
          katiLagat: '0.000',
          tanaLagat: '0.000',
          sootLagat: '0.000',
          thariLagat: '0.000',
          silkLagat: '0.000',
          otherLagat: '0.000'
        });
      }
    } else {
      console.log('⚠️ No filter data or material lagat data available');
      // Use default values
      this.materialIssueForm.patchValue({
        katiDescription: 'Kati',
        tanaDescription: 'Tana',
        sootDescription: 'Soot',
        thariDescription: 'Thari',
        silkDescription: 'Silk',
        otherDescription: 'Other',
        katiLagat: '0.000',
        tanaLagat: '0.000',
        sootLagat: '0.000',
        thariLagat: '0.000',
        silkLagat: '0.000',
        otherLagat: '0.000'
      });
    }
  }

  clearDescriptionFields(): void {
    this.materialIssueForm.patchValue({
      katiDescription: '',
      tanaDescription: '',
      sootDescription: '',
      thariDescription: '',
      silkDescription: '',
      otherDescription: '',
      katiLagat: '0.000',
      tanaLagat: '0.000',
      sootLagat: '0.000',
      thariLagat: '0.000',
      silkLagat: '0.000',
      otherLagat: '0.000'
    });
  }

  // Helper function to extract description text from populated or ObjectId data
  getDescriptionText(descriptionData: any, defaultText: string): string {
    if (!descriptionData) {
      return defaultText;
    }

    // If it's already resolved as a string (from our resolveMaterialDescriptions method)
    if (typeof descriptionData === 'string' && descriptionData !== '' && !descriptionData.match(/^[0-9a-fA-F]{24}$/)) {
      return descriptionData;
    }

    // If it's a populated object with description text
    if (typeof descriptionData === 'object' && descriptionData.description) {
      return descriptionData.description;
    }

    // If it's a populated object with name field
    if (typeof descriptionData === 'object' && descriptionData.name) {
      return descriptionData.name;
    }

    // If it's a populated object with rawMaterial field
    if (typeof descriptionData === 'object' && descriptionData.rawMaterial) {
      return descriptionData.rawMaterial;
    }

    // If it's an ObjectId string, try to resolve it from rawMaterialList
    if (typeof descriptionData === 'string' && descriptionData.match(/^[0-9a-fA-F]{24}$/)) {
      const resolved = this.getDescriptionById(descriptionData);
      return resolved !== descriptionData ? resolved : defaultText;
    }

    // If it's just a string
    if (typeof descriptionData === 'string') {
      return descriptionData;
    }

    // If it's an ObjectId (not populated), return default
    return defaultText;
  }

  showValidationErrors(): boolean {
    if (this.materialIssueForm.invalid) {
      console.log('❌ Form is invalid');

      // Mark all fields as touched to show validation errors
      Object.keys(this.materialIssueForm.controls).forEach(key => {
        this.materialIssueForm.get(key)?.markAsTouched();
      });

      Swal.fire({
        title: 'Validation Error!',
        text: 'Please fill in all required fields.',
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return false;
    }

    const formData = this.materialIssueForm.value;

    // Check if weaver is selected (either through selectedWeaver object or form value)
    if (!this.selectedWeaver && !formData.weaver) {
      Swal.fire({
        title: 'Validation Error!',
        text: 'Please select a weaver.',
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return false;
    }

    // Check if issue is selected (either through selectedIssue object or form value)
    if (!this.selectedIssue && !formData.issueNo) {
      Swal.fire({
        title: 'Validation Error!',
        text: 'Please select an issue number.',
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return false;
    }

    // Check if we have filter data (issue details)
    if (!this.filterData || this.filterData.length === 0) {
      Swal.fire({
        title: 'Validation Error!',
        text: 'Please select an issue to load issue details.',
        icon: 'warning',
        confirmButtonText: 'OK'
      });
      return false;
    }

    return true;
  }

  handleSubmissionError(error: any): void {
    console.error('❌ Error saving Material Issue:', error);

    let errorMessage = 'Error saving Material Issue. Please try again.';

    if (error.status === 404) {
      errorMessage = 'API endpoint not found. Please check if the backend server is running.';
    } else if (error.status === 400) {
      errorMessage = 'Invalid data provided. Please check all fields.';
      if (error.error?.message) {
        errorMessage = error.error.message;
      }
    } else if (error.status === 500) {
      errorMessage = 'Server error. Please try again later.';
    } else if (error.status === 0) {
      errorMessage = 'Cannot connect to server. Please check if the backend is running.';
    } else if (error.error?.message) {
      errorMessage = error.error.message;
    }

    Swal.fire({
      title: 'Error!',
      text: errorMessage,
      icon: 'error',
      confirmButtonText: 'OK'
    });
  }

  // Helper function to format issue values
  private formatIssueValue(value: any): string {
    if (value === null || value === undefined || value === '' || value === '0') {
      return '0.000';
    }
    const numValue = parseFloat(value.toString());
    return isNaN(numValue) ? '0.000' : numValue.toFixed(3);
  }

  onSubmit(): void {
    console.log('🚀 Form submission started');

    if (!this.showValidationErrors()) {
      return;
    }

    if (this.materialIssueForm.valid && this.filterData.length > 0) {
      const formData = this.materialIssueForm.value;

      // Get weaver ObjectId from selected weaver
      const selectedWeaverData = this.list.find(w => w.weaver === formData.weaver);
      let weaverObjectId = selectedWeaverData ? selectedWeaverData.id : null;

      // If we don't have ObjectId, try to find it from allOrderList
      if (!weaverObjectId || weaverObjectId === formData.weaver) {
        const orderWithWeaver = this.allOrderList.find(order => {
          if (typeof order.weaver === 'object' && order.weaver?.name === formData.weaver) {
            return true;
          } else if (typeof order.weaver === 'string' && order.weaver === formData.weaver) {
            return true;
          }
          return false;
        });

        if (orderWithWeaver && typeof orderWithWeaver.weaver === 'object' && orderWithWeaver.weaver?._id) {
          weaverObjectId = orderWithWeaver.weaver._id;
        }
      }

      console.log('🔍 Weaver ObjectId Resolution:', {
        formDataWeaver: formData.weaver,
        selectedWeaverData,
        finalWeaverObjectId: weaverObjectId
      });

      // Get issue ObjectId from selected issue
      // In edit mode, get the issue number from form (even if disabled, it should have value)
      const issueNoToFind = formData.issueNo;
      console.log('🔍 Looking for issue number:', issueNoToFind);
      console.log('🔍 Available issues:', this.availableIssues.map(i => i.Br_issueNo));

      const selectedIssueData = this.availableIssues.find(issue => issue.Br_issueNo === issueNoToFind);
      let issueObjectId = selectedIssueData ? selectedIssueData._id : null;

      // In edit mode, if we can't find the issue in availableIssues, use the stored ObjectId
      if (!issueObjectId && this.isEditMode) {
        if (this.editModeIssueObjectId) {
          issueObjectId = this.editModeIssueObjectId;
        } else if (issueNoToFind) {
          // If issueNoToFind looks like an ObjectId (24 hex characters), use it directly
          if (typeof issueNoToFind === 'string' && issueNoToFind.match(/^[0-9a-fA-F]{24}$/)) {
            issueObjectId = issueNoToFind;
          }
        }
      }

      console.log('🔍 Final issueObjectId:', issueObjectId);
      console.log('🔍 Edit mode stored ObjectId:', this.editModeIssueObjectId);

      // Calculate total area
      const totalArea = this.calculateTotalArea();

      // Get RawMaterial ObjectIds from rawMaterialList
      const getRawMaterialId = (description: string) => {
        if (!description || description.trim() === '') return null;
        console.log('🔍 Looking for material:', description);

        // First try exact matches with Description field (capital D)
        const exactMatch = this.rawMaterialList.find(rm =>
          rm.Description === description
        );

        if (exactMatch && exactMatch._id) {
          console.log('✅ Found exact match:', exactMatch);
          return exactMatch._id;
        }

        // If no exact match, try other fields
        const material = this.rawMaterialList.find(rm => {
          return rm.name === description ||
                 rm.description === description ||
                 rm.materialName === description ||
                 rm.groupName === description ||
                 rm.rawMaterialName === description ||
                 rm.Item === description;
        });

        if (material && material._id) {
          console.log('✅ Found material match:', material);
          return material._id;
        }

        // If still not found, create a warning but don't fail
        console.warn('⚠️ Material not found in rawMaterialList:', description);
        console.warn('⚠️ Available materials sample:', this.rawMaterialList.slice(0, 3));
        return null;
      };

      // Prepare katiData with proper ObjectIds
      const processedKatiData = this.katiData.map(item => {
        console.log('🔍 Processing kati item:', item);

        // Handle colour ObjectId - it might be already an ObjectId or an object
        let colourId = null;
        if (item.colour) {
          if (typeof item.colour === 'string') {
            // Check if it's a valid ObjectId format
            if (item.colour.match(/^[0-9a-fA-F]{24}$/)) {
              colourId = item.colour; // Already an ObjectId string
            }
          } else if (item.colour._id) {
            colourId = item.colour._id; // Object with _id property
          } else if (item.colour.id) {
            colourId = item.colour.id; // Object with id property
          }
        }

        // If no valid colour ObjectId found, skip this item or use a default
        if (!colourId) {
          console.warn('⚠️ No valid colour ObjectId found for kati item:', item);
          return null; // Skip invalid items
        }

        const lagatNum = parseFloat((item as any).lagat) || 0;
        const carpetLagatNum = parseFloat((item as any).carpetLagat);
        const issued = parseFloat(item.issueValue || '0') || 0;
        const toIssue = parseFloat(item.toIssueValue || '0') || 0;
        return {
          colour: colourId,
          lagat: lagatNum.toFixed(3),
          carpetLagat: !isNaN(carpetLagatNum) ? carpetLagatNum.toFixed(3) : (lagatNum * totalArea).toFixed(3),
          issueValue: issued > 0 ? issued.toFixed(3) : '0.000',
          toIssueValue: toIssue > 0 ? toIssue.toFixed(3) : '0.000'
        };
      }).filter(item => item !== null); // Remove null items

      // Prepare the data to be sent to backend
      const areaStr = totalArea?.toString() || '';
      const issueAreaStr = areaStr && !/\bYd\b/i.test(areaStr) ? `${areaStr} Yd` : areaStr;
      const materialIssueData = {
        challanNo: formData.challanNo,
        date: formData.date,
        weaver: weaverObjectId, // Send ObjectId
        issueNo: issueObjectId, // Send ObjectId
        issueAreaInYard: issueAreaStr, // Save with unit suffix
        katiData: processedKatiData, // Processed kati data with ObjectIds
        materials: {
          kati: {
            description: getRawMaterialId(formData.katiDescription), // Only send valid ObjectId
            lagat: formData.katiLagat?.toString() || '0',
            issue: this.formatIssueValue(formData.katiIssue)
          },
          tana: {
            description: getRawMaterialId(formData.tanaDescription), // Only send valid ObjectId
            lagat: formData.tanaLagat?.toString() || '0',
            issue: this.formatIssueValue(formData.tanaIssue)
          },
          soot: {
            description: getRawMaterialId(formData.sootDescription), // Only send valid ObjectId
            lagat: formData.sootLagat?.toString() || '0',
            issue: this.formatIssueValue(formData.sootIssue)
          },
          thari: {
            description: getRawMaterialId(formData.thariDescription), // Only send valid ObjectId
            lagat: formData.thariLagat?.toString() || '0',
            issue: this.formatIssueValue(formData.thariIssue)
          },
          silk: {
            description: getRawMaterialId(formData.silkDescription), // Only send valid ObjectId
            lagat: formData.silkLagat?.toString() || '0',
            issue: this.formatIssueValue(formData.silkIssue)
          },
          other: {
            description: getRawMaterialId(formData.otherDescription), // Only send valid ObjectId
            lagat: formData.otherLagat?.toString() || '0',
            issue: this.formatIssueValue(formData.otherIssue)
          }
        }
      };

      // Validate required ObjectIds
      if (!weaverObjectId || weaverObjectId === formData.weaver) {
        Swal.fire({
          title: 'Validation Error!',
          text: 'Please select a valid weaver. Could not find weaver ObjectId.',
          icon: 'warning',
          confirmButtonText: 'OK'
        });
        console.error('❌ Weaver ObjectId validation failed:', {
          weaverObjectId,
          formDataWeaver: formData.weaver,
          selectedWeaverData,
          allOrderListSample: this.allOrderList.slice(0, 2)
        });
        return;
      }

      // Validate issue ObjectId
      if (!issueObjectId) {
        if (this.isEditMode) {
          // In edit mode, this should not happen if data was loaded correctly
          console.error('❌ Edit mode: No issue ObjectId found');
          Swal.fire({
            title: 'Error!',
            text: 'Issue data not found. Please try reloading the page.',
            icon: 'error',
            confirmButtonText: 'OK'
          });
          return;
        } else {
          // In new mode, user must select an issue
          Swal.fire({
            title: 'Validation Error!',
            text: 'Please select a valid issue number.',
            icon: 'warning',
            confirmButtonText: 'OK'
          });
          return;
        }
      }

      // Check for valid ObjectIds for materials
      const materialDescriptions = [
        formData.katiDescription,
        formData.tanaDescription,
        formData.sootDescription,
        formData.thariDescription,
        formData.silkDescription,
        formData.otherDescription
      ].filter(desc => desc && desc.trim() !== '');

      const validMaterialIds = [
        getRawMaterialId(formData.katiDescription),
        getRawMaterialId(formData.tanaDescription),
        getRawMaterialId(formData.sootDescription),
        getRawMaterialId(formData.thariDescription),
        getRawMaterialId(formData.silkDescription),
        getRawMaterialId(formData.otherDescription)
      ].filter(id => id !== null);

      console.log('🔍 Material Descriptions:', materialDescriptions);
      console.log('🔍 Valid Material IDs:', validMaterialIds);

      // Call the service to save data
      this.manufactureService.addMaterialIssue(materialIssueData).subscribe({
        next: (response: any) => {
          console.log('✅ Material Issue saved successfully:', response);
          Swal.fire({
            title: 'Success!',
            text: `Material Issue saved successfully!\nChallan No: ${formData.challanNo}`,
            icon: 'success',
            confirmButtonText: 'OK',
            timer: 2000,
            timerProgressBar: true
          }).then(() => {
            if (this.printAfterSave) {
              // Reset flag and navigate to print page with challan param
              this.printAfterSave = false;
              const challanNo = formData.challanNo;
              // Determine the Issue ObjectId to print (same logic as printCurrentIssue)
              let issueId: string | null = null;
              if (this.isEditMode && this.editModeIssueObjectId) {
                issueId = typeof this.editModeIssueObjectId === 'string' ? this.editModeIssueObjectId : this.editModeIssueObjectId?._id;
              }
              if (!issueId) {
                const issueNo = this.materialIssueForm.get('issueNo')?.value;
                const selectedIssue = this.availableIssues.find(i => i.Br_issueNo === issueNo);
                issueId = selectedIssue?._id || null;
              }
              if (!issueId && this.filterData && this.filterData[0]) {
                const fallback = this.filterData[0];
                issueId = (typeof fallback?._id === 'string') ? fallback._id : null;
              }
              if (issueId) {
                this.router.navigate([`/admin/material-issue-print/${issueId}`], { queryParams: { challan: challanNo } });
              }
            } else {
              this.clearForm();
            }
          });
        },
        error: (error: any) => {
          this.handleSubmissionError(error);
        }
      });
    }
  }

  clearForm(): void {
    this.materialIssueForm.reset();
    this.dataSource.data = [];
    this.availableIssues = [];
    this.selectedWeaverIssues = [];
    this.filterData = [];
    this.katiData = [];
    this.katiDataSource.data = [];
    this.generateChallanNo();
    this.materialIssueForm.patchValue({ date: new Date() });
    this.setDefaultDescriptions();
  }



  navigateToMaterialReceive(): void {
    // Navigate to material-receive component
    console.log('Navigating to Material Receive Component');
    this.router.navigate(['/admin/material-receive']);
  }

  // Save current data then open print
  saveAndPrint(): void {
    this.printAfterSave = true;
    this.onSubmit();
  }

  getKatiColourDisplay(item: any): string {
    if (item.colour) {
      return `${item.colour.newColor || ''} - ${item.colour.companyColorCode || ''} - ${item.colour.remark || ''}`;
    }
    return '';
  }


  printCurrentIssue(): void {
    // Determine the current selected Issue ObjectId to print
    let issueId: string | null = null;
    // If we are in edit mode, prefer stored editModeIssueObjectId
    if (this.isEditMode && this.editModeIssueObjectId) {
      issueId = typeof this.editModeIssueObjectId === 'string' ? this.editModeIssueObjectId : this.editModeIssueObjectId?._id;
    }
    // Otherwise, try to find by selected issue number from form
    if (!issueId) {
      const issueNo = this.materialIssueForm.get('issueNo')?.value;
      const selectedIssue = this.availableIssues.find(i => i.Br_issueNo === issueNo);
      issueId = selectedIssue?._id || null;
    }
    if (!issueId && this.filterData && this.filterData[0]) {
      const fallback = this.filterData[0];
      issueId = (typeof fallback?._id === 'string') ? fallback._id : null;
    }
    if (issueId) {
      this.router.navigate([`/admin/material-issue-print/${issueId}`]);
    }
  }

  openKatiModal(): void {
    // Prevent immediate re-open loops
    if (this.isKatiModalOpen) return;
    const now = Date.now();
    if (now - this.lastKatiDialogCloseAt < 300) return;
    this.isKatiModalOpen = true;

    const totalArea = this.calculateTotalArea();
    const katiLagat = parseFloat(this.materialIssueForm.get('katiLagat')?.value) || 0;

    console.log('Opening Kati Modal with data:', { totalArea, katiLagat });

    // Determine selected issueNo ObjectId for fetching prior issued
    const selectedIssueNoVal = this.materialIssueForm.get('issueNo')?.value;
    const selectedIssueData = this.availableIssues.find(issue => issue.Br_issueNo === selectedIssueNoVal);
    const issueNoId = selectedIssueData ? selectedIssueData._id : null;

    const dialogRef = this.dialog.open(KatiModalComponent, {
      width: '1200px',
      data: {
        totalArea: totalArea,
        katiLagat: katiLagat,
        filterData: this.filterData,
        materialLagatList: this.materialLagatList,
        existingKatiData: this.katiData.length > 0 ? this.katiData : null,
        isEditMode: this.katiData.length > 0,
        issueNoId: issueNoId,
        issueNoStr: selectedIssueNoVal
      },
      disableClose: false,
      autoFocus: false
    });

    dialogRef.afterClosed().subscribe(result => {
      this.isKatiModalOpen = false;
      this.lastKatiDialogCloseAt = Date.now();
      if (result) {
        console.log('Kati Modal closed with result:', result);
        // Store the kati data and update the table
        this.katiData = result.map((item: any, index: number) => ({
          ...item,
          srNo: index + 1,
          carpetLagat: (item.lagat * totalArea).toFixed(3)
        }));
        this.katiDataSource.data = this.katiData;

        // Compute totals for footer
        this.totalKatiLagat = this.katiData.reduce((s, r) => s + (parseFloat(r.lagat) || 0), 0);
        this.totalKatiCarpetLagat = this.katiData.reduce((s, r) => s + (parseFloat(r.carpetLagat) || 0), 0);
        this.totalKatiIssued = this.katiData.reduce((s, r) => s + (parseFloat(r.issueValue || '0') || 0), 0);
        this.totalKatiToIssue = this.katiData.reduce((s, r) => s + (parseFloat(r.toIssueValue || '0') || 0), 0);

        // Update Kati Issue field as only Total(ToIssue) per your request
        const katiIssueSum = (this.totalKatiToIssue).toFixed(3);
        this.materialIssueForm.patchValue({ katiIssue: katiIssueSum });

        console.log('✅ Kati data stored:', this.katiData, 'Totals => Lagat:', this.totalKatiLagat.toFixed(3), 'CarpetLagat:', this.totalKatiCarpetLagat.toFixed(3), 'Issued:', this.totalKatiIssued.toFixed(3), 'ToIssue:', this.totalKatiToIssue.toFixed(3), 'KatiIssue:', katiIssueSum);
      }
    });
  }

  calculateTotalArea(): number {
    // Calculate total area from the filtered data (Issue Details table)
    let totalArea = 0;

    if (this.filterData && this.filterData.length > 0) {
      totalArea = this.filterData.reduce((sum, item) => {
        // Use the calculated area (areaInYard * pcs) instead of direct area field
        const calculatedArea = this.getCalculatedAreaValue(item);
        return sum + calculatedArea;
      }, 0);
    }

    console.log('🔢 Calculated Total Area:', totalArea);
    console.log('🔢 Filter Data for calculation:', this.filterData);

    return totalArea;
  }

  getCalculatedArea(item: any): string {
    const calculatedArea = this.getCalculatedAreaValue(item);
    return `${calculatedArea.toFixed(2)} Yd`;
  }

  getCalculatedAreaValue(item: any): number {
    // Calculate area as areaInYard * pcs
    const pcs = parseInt(item.pcs) || 0;
    let areaInYard = 0;

    // Try to get areaInYard from size object
    if (item.size && typeof item.size === 'object' && item.size.areaInYard) {
      areaInYard = parseFloat(item.size.areaInYard) || 0;
    } else if (item.areaInYard) {
      // Fallback if areaInYard is directly available
      areaInYard = parseFloat(item.areaInYard) || 0;
    } else {
      // If no areaInYard available, try to parse from existing area field
      // This handles cases where area might be stored as "45.80 Ft" format
      const areaStr = item.area || '0';
      const areaValue = parseFloat(areaStr.toString().split(' ')[0]) || 0;
      // Convert to approximate yard value if it seems to be in feet
      areaInYard = areaStr.includes('Ft') ? areaValue / 9 : areaValue;
    }

    const calculatedArea = areaInYard * pcs;
    console.log(`🔢 Area calculation for item: areaInYard=${areaInYard}, pcs=${pcs}, result=${calculatedArea}`);

    return calculatedArea;
  }
}
