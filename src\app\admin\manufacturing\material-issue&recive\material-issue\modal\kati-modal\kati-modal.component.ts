import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatTableDataSource } from '@angular/material/table';
import { ManufactureService } from '../../../../../../services/manufacture.service';

export interface ColourLagat {
  colour: {
    newColor: string;
    companyColorCode: string;
    remark: string;
  };
  lagat: number;
  // Issued is cumulative and read-only in UI
  issueValue?: string; // stored as string with 3 decimals (sum of base + current toIssue)
  toIssueValue?: string; // latest to-issue input
  _baseIssuedNumeric?: number; // internal: previously issued before current session
}

@Component({
  selector: 'app-kati-modal',
  templateUrl: './kati-modal.component.html',
  styleUrls: ['./kati-modal.component.css']
})
export class KatiModalComponent implements OnInit {
  colourLagats: ColourLagat[] = [];
  totalArea: number = 0;
  katiLagat: number = 0;
  isEditMode: boolean = false;
  viewOnly: boolean = false;

  // Angular Material Table properties
  displayedColumns: string[] = ['srNo', 'colour', 'lagat', 'carpetLagat', 'issue', 'toIssue'];
  dataSource = new MatTableDataSource<ColourLagat>([]);

  constructor(
    public dialogRef: MatDialogRef<KatiModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private manufactureService: ManufactureService,
  ) {}

  ngOnInit(): void {
    console.log('🔍 Kati Modal Data:', this.data);
    this.isEditMode = this.data.isEditMode || false;
    this.viewOnly = !!this.data.viewOnly;
    this.initializeData();
    // If we have issueNoId, fetch prior issues and prefill Issued totals
    if (this.data.issueNoId) {
      this.prefillIssuedFromHistory(this.data.issueNoId);
    }

    // Add debugging for table display
    console.log('🔍 Final colourLagats:', this.colourLagats);
    console.log('🔍 DataSource data:', this.dataSource.data);
    console.log('🔍 DisplayedColumns:', this.displayedColumns);
    console.log('🔍 Is Edit Mode:', this.isEditMode, 'ViewOnly:', this.viewOnly);
  }

  setDigitIssue(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      val.target.value = _val.toFixed(3);
    } else if (val.target.value === '' || val.target.value === null || val.target.value === undefined) {
      val.target.value = '0.000';
    }
  }

  setDigitToIssue(val: any) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      val.target.value = _val.toFixed(3);
    } else if (val.target.value === '' || val.target.value === null || val.target.value === undefined) {
      val.target.value = '0.000';
    }
    // Sync the element's model if available
    try {
      const input = val.target as HTMLInputElement;
      const rowIdxAttr = input.getAttribute('data-row-idx');
      if (rowIdxAttr) {
        const idx = parseInt(rowIdxAttr, 10);
        if (!isNaN(idx) && this.colourLagats[idx]) {
          this.colourLagats[idx].toIssueValue = val.target.value;
          this.computeTotals();
        }
      }
    } catch {}
  }

  initializeData(): void {
    // Get total area from the passed data
    this.totalArea = this.data.totalArea || 5.00; // Default value for testing
    this.katiLagat = this.data.katiLagat || 1.500; // Default value for testing

    console.log('🔍 Total Area:', this.totalArea);
    console.log('🔍 Kati Lagat:', this.katiLagat);

    // Get colourLagats from the materialLagatList
    if (this.data.materialLagatList && this.data.materialLagatList.length > 0 &&
        this.data.filterData && this.data.filterData.length > 0) {

      const issueData = this.data.filterData[0];
      const qualityId = typeof issueData.quality === 'object' ? issueData.quality._id : issueData.quality;
      const designId = typeof issueData.design === 'object' ? issueData.design._id : issueData.design;

      const matchingLagat = this.data.materialLagatList.find((lagat: any) => {
        const lagatQualityId = typeof lagat.quality === 'object' ? lagat.quality._id : lagat.quality;
        const lagatDesignId = typeof lagat.AddDesign === 'object' ? lagat.AddDesign._id : lagat.AddDesign;
        return lagatQualityId === qualityId && lagatDesignId === designId;
      });

      if (matchingLagat?.AddDesign?.colourLagats) {
        const designColors = matchingLagat.AddDesign.colourLagats.map((item: any) => ({
          colour: item.colour || {},
          lagat: parseFloat(item.lagat) || 0,
          issueValue: '',
          toIssueValue: '',
          _baseIssuedNumeric: 0,
        }));

        if (this.data.existingKatiData?.length > 0) {
          const processedColors = new Map();
          const finalColors: any[] = [];

          designColors.forEach((designColor: any) => {
            const colorId = designColor.colour._id || designColor.colour.id;
            if (colorId) {
              const existingItem = this.data.existingKatiData.find((existing: any) =>
                existing.colour && (existing.colour._id === colorId || existing.colour.id === colorId)
              );

              if (existingItem) {
                const issued = parseFloat(existingItem.issueValue || '0') || 0;
                designColor.issueValue = issued > 0 ? issued.toFixed(3) : '';
                designColor._baseIssuedNumeric = issued;

                const toIssue = parseFloat(existingItem.toIssueValue || '0') || 0;
                designColor.toIssueValue = toIssue > 0 ? toIssue.toFixed(3) : '';
              }

              processedColors.set(colorId, true);
              finalColors.push(designColor);
            }
          });

          this.data.existingKatiData.forEach((existingItem: any) => {
            const colorId = existingItem.colour && (existingItem.colour._id || existingItem.colour.id);
            if (colorId && !processedColors.has(colorId)) {
              const issued = parseFloat(existingItem.issueValue || '0') || 0;
              const toIssue = parseFloat(existingItem.toIssueValue || '0') || 0;
              finalColors.push({
                colour: existingItem.colour || {},
                lagat: parseFloat(existingItem.lagat) || 0,
                issueValue: issued > 0 ? issued.toFixed(3) : '',
                toIssueValue: toIssue > 0 ? toIssue.toFixed(3) : '',
                _issuedNumeric: issued,
              });
            }
          });

          this.colourLagats = finalColors;
        } else {
          this.colourLagats = designColors;
        }
      }
    }

    // Update the dataSource for Angular Material Table
    this.dataSource.data = this.colourLagats;
    this.computeTotals();
  }

  getColourDisplay(item: ColourLagat): string {
    if (item.colour) {
      return `${item.colour.newColor || ''} - ${item.colour.companyColorCode || ''} - ${item.colour.remark || ''}`;
    }
    return '';
  }

  calculateCarpetLagat(lagat: number): string {
    // Carpet Lagat = lagat * totalArea
    const carpetLagat = lagat * this.totalArea;
    return carpetLagat.toFixed(3);
  }

  onInputChange(element: ColourLagat, field: string, event: any): void {
    const raw = event.target.value;

    if (field === 'toIssue') {
      // Do not format on each keystroke; just keep the raw string
      element.toIssueValue = raw;
      // Recompute totals live using a tolerant parse
      this.computeTotals();
    }

    // Do not touch issueValue here; it's the historical sum only
  }

  totalIssuedSum: number = 0;
  totalToIssueSum: number = 0;

  computeTotals(): void {
    this.totalIssuedSum = this.colourLagats.reduce((sum, r: any) => sum + (parseFloat(r.issueValue || '0') || 0), 0);
    this.totalToIssueSum = this.colourLagats.reduce((sum, r: any) => sum + (parseFloat(r.toIssueValue || '0') || 0), 0);
  }

  getIssuedDisplay(element: ColourLagat): string {
    const issued = parseFloat(element.issueValue || '0') || 0;
    return issued > 0 ? issued.toFixed(3) : '0.000';
  }

  // Load all previous material issues for this issueNo and sum prior toIssue as Issued base
  prefillIssuedFromHistory(issueNoId: string): void {
    this.manufactureService.getMaterialIssuesByIssueNo(issueNoId).subscribe((issues: any) => {
      if (!issues || issues.length === 0) {
        // no history, keep zeros
        this.computeTotals();
        return;
      }
      const issuedMap = new Map<string, number>();
      issues.forEach((doc: any) => {
        if (Array.isArray(doc.katiData)) {
          doc.katiData.forEach((row: any) => {
            const colourId = row.colour?._id || row.colour;
            const toIssue = parseFloat(row.toIssueValue || '0') || 0;
            if (!colourId) return;
            issuedMap.set(colourId, (issuedMap.get(colourId) || 0) + toIssue);
          });
        }
      });
      this.colourLagats = this.colourLagats.map((row: any) => {
        const colourId = row.colour?._id || row.colour?.id;
        const base = colourId ? (issuedMap.get(colourId) || 0) : 0;
        return {
          ...row,
          _baseIssuedNumeric: base,
          issueValue: base > 0 ? base.toFixed(3) : '0.000',
        };
      });
      this.dataSource.data = this.colourLagats;
      this.computeTotals();
    }, (err) => {
      console.error('Failed to prefill issued from history:', err);
    });
  }

  onSave(): void {
    // Ensure strings with 3 decimals for persistence
    this.computeTotals();
    const normalized = this.colourLagats.map((el) => ({
      ...el,
      issueValue: this.getIssuedDisplay(el),
      toIssueValue: (el.toIssueValue && parseFloat(el.toIssueValue)) ? parseFloat(el.toIssueValue).toFixed(3) : '0.000',
    }));
    this.dialogRef.close(normalized);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
