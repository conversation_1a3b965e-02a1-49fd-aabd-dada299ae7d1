import{$ as h,A as qe,Aa as k,Ba as fe,Da as nt,E as L,Eb as rt,Ec as E,Fa as st,G,Ga as X,Gb as at,Ha as ot,Ib as te,J as Je,Jb as ye,Kb as ie,Ob as ct,Pb as dt,Qb as lt,R as Z,S as et,Sb as ut,U as Y,Ub as ht,W as tt,Xa as H,Xc as ft,Ya as l,_ as d,a as Ge,ac as ne,ba as m,c as U,da as a,ea as p,eb as y,f as v,g as Ze,ia as f,ib as S,ja as Q,ka as _,l as Ye,la as T,mb as _e,nb as ge,ob as q,q as V,qa as it,qb as K,r as Qe,sc as mt,tb as J,u as Xe,uc as pt,xb as be,yb as ve,yc as O,za as I,zb as ee}from"./chunk-TSVGDZRC.js";import{a as u,b as ze}from"./chunk-CWTPBX7D.js";var Ie;try{Ie=typeof Intl<"u"&&Intl.v8BreakIterator}catch{Ie=!1}var g=(()=>{class i{constructor(e){this._platformId=e,this.isBrowser=this._platformId?ft(this._platformId):typeof document=="object"&&!!document,this.EDGE=this.isBrowser&&/(edge)/i.test(navigator.userAgent),this.TRIDENT=this.isBrowser&&/(msie|trident)/i.test(navigator.userAgent),this.BLINK=this.isBrowser&&!!(window.chrome||Ie)&&typeof CSS<"u"&&!this.EDGE&&!this.TRIDENT,this.WEBKIT=this.isBrowser&&/AppleWebKit/i.test(navigator.userAgent)&&!this.BLINK&&!this.EDGE&&!this.TRIDENT,this.IOS=this.isBrowser&&/iPad|iPhone|iPod/.test(navigator.userAgent)&&!("MSStream"in window),this.FIREFOX=this.isBrowser&&/(firefox|minefield)/i.test(navigator.userAgent),this.ANDROID=this.isBrowser&&/android/i.test(navigator.userAgent)&&!this.TRIDENT,this.SAFARI=this.isBrowser&&/safari/i.test(navigator.userAgent)&&this.WEBKIT}static{this.\u0275fac=function(t){return new(t||i)(a(st))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var j,_t=["color","button","checkbox","date","datetime-local","email","file","hidden","image","month","number","password","radio","range","reset","search","submit","tel","text","time","url","week"];function Tn(){if(j)return j;if(typeof document!="object"||!document)return j=new Set(_t),j;let i=document.createElement("input");return j=new Set(_t.filter(n=>(i.setAttribute("type",n),i.type===n))),j}var W;function Zt(){if(W==null&&typeof window<"u")try{window.addEventListener("test",null,Object.defineProperty({},"passive",{get:()=>W=!0}))}finally{W=W||!1}return W}function P(i){return Zt()?i:!!i.capture}var $=function(i){return i[i.NORMAL=0]="NORMAL",i[i.NEGATED=1]="NEGATED",i[i.INVERTED=2]="INVERTED",i}($||{}),se,N;function kn(){if(N==null){if(typeof document!="object"||!document||typeof Element!="function"||!Element)return N=!1,N;if("scrollBehavior"in document.documentElement.style)N=!0;else{let i=Element.prototype.scrollTo;i?N=!/\{\s*\[native code\]\s*\}/.test(i.toString()):N=!1}}return N}function xn(){if(typeof document!="object"||!document)return $.NORMAL;if(se==null){let i=document.createElement("div"),n=i.style;i.dir="rtl",n.width="1px",n.overflow="auto",n.visibility="hidden",n.pointerEvents="none",n.position="absolute";let e=document.createElement("div"),t=e.style;t.width="2px",t.height="1px",i.appendChild(e),document.body.appendChild(i),se=$.NORMAL,i.scrollLeft===0&&(i.scrollLeft=1,se=i.scrollLeft===0?$.NEGATED:$.INVERTED),i.remove()}return se}var Ee;function Yt(){if(Ee==null){let i=typeof document<"u"?document.head:null;Ee=!!(i&&(i.createShadowRoot||i.attachShadow))}return Ee}function gt(i){if(Yt()){let n=i.getRootNode?i.getRootNode():null;if(typeof ShadowRoot<"u"&&ShadowRoot&&n instanceof ShadowRoot)return n}return null}function bt(){let i=typeof document<"u"&&document?document.activeElement:null;for(;i&&i.shadowRoot;){let n=i.shadowRoot.activeElement;if(n===i)break;i=n}return i}function x(i){return i.composedPath?i.composedPath()[0]:i.target}function vt(){return typeof __karma__<"u"&&!!__karma__||typeof jasmine<"u"&&!!jasmine||typeof jest<"u"&&!!jest||typeof Mocha<"u"&&!!Mocha}function oe(i,...n){return n.length?n.some(e=>i[e]):i.altKey||i.shiftKey||i.ctrlKey||i.metaKey}function Qt(i){return i!=null&&`${i}`!="false"}function De(i,n=0){return Xt(i)?Number(i):n}function Xt(i){return!isNaN(parseFloat(i))&&!isNaN(Number(i))}function Me(i){return Array.isArray(i)?i:[i]}function Nn(i){return i==null?"":typeof i=="string"?i:`${i}px`}function w(i){return i instanceof I?i.nativeElement:i}function Fn(i,n=/\s+/){let e=[];if(i!=null){let t=Array.isArray(i)?i:`${i}`.split(n);for(let s of t){let o=`${s}`.trim();o&&e.push(o)}}return e}var yt=(()=>{class i{create(e){return typeof MutationObserver>"u"?null:new MutationObserver(e)}static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),qt=(()=>{class i{constructor(e){this._mutationObserverFactory=e,this._observedElements=new Map}ngOnDestroy(){this._observedElements.forEach((e,t)=>this._cleanupObserver(t))}observe(e){let t=w(e);return new U(s=>{let r=this._observeElement(t).subscribe(s);return()=>{r.unsubscribe(),this._unobserveElement(t)}})}_observeElement(e){if(this._observedElements.has(e))this._observedElements.get(e).count++;else{let t=new v,s=this._mutationObserverFactory.create(o=>t.next(o));s&&s.observe(e,{characterData:!0,childList:!0,subtree:!0}),this._observedElements.set(e,{observer:s,stream:t,count:1})}return this._observedElements.get(e).stream}_unobserveElement(e){this._observedElements.has(e)&&(this._observedElements.get(e).count--,this._observedElements.get(e).count||this._cleanupObserver(e))}_cleanupObserver(e){if(this._observedElements.has(e)){let{observer:t,stream:s}=this._observedElements.get(e);t&&t.disconnect(),s.complete(),this._observedElements.delete(e)}}static{this.\u0275fac=function(t){return new(t||i)(a(yt))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),Wn=(()=>{class i{get disabled(){return this._disabled}set disabled(e){this._disabled=e,this._disabled?this._unsubscribe():this._subscribe()}get debounce(){return this._debounce}set debounce(e){this._debounce=De(e),this._subscribe()}constructor(e,t,s){this._contentObserver=e,this._elementRef=t,this._ngZone=s,this.event=new k,this._disabled=!1,this._currentSubscription=null}ngAfterContentInit(){!this._currentSubscription&&!this.disabled&&this._subscribe()}ngOnDestroy(){this._unsubscribe()}_subscribe(){this._unsubscribe();let e=this._contentObserver.observe(this._elementRef);this._ngZone.runOutsideAngular(()=>{this._currentSubscription=(this.debounce?e.pipe(L(this.debounce)):e).subscribe(this.event)})}_unsubscribe(){this._currentSubscription?.unsubscribe()}static{this.\u0275fac=function(t){return new(t||i)(l(qt),l(I),l(y))}}static{this.\u0275dir=T({type:i,selectors:[["","cdkObserveContent",""]],inputs:{disabled:[f.HasDecoratorInputTransform,"cdkObserveContentDisabled","disabled",O],debounce:"debounce"},outputs:{event:"cdkObserveContent"},exportAs:["cdkObserveContent"],standalone:!0,features:[S]})}}return i})(),Et=(()=>{class i{static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275mod=_({type:i})}static{this.\u0275inj=h({providers:[yt]})}}return i})();var It=new Set,F,Jt=(()=>{class i{constructor(e,t){this._platform=e,this._nonce=t,this._matchMedia=this._platform.isBrowser&&window.matchMedia?window.matchMedia.bind(window):ti}matchMedia(e){return(this._platform.WEBKIT||this._platform.BLINK)&&ei(e,this._nonce),this._matchMedia(e)}static{this.\u0275fac=function(t){return new(t||i)(a(g),a(ot,8))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();function ei(i,n){if(!It.has(i))try{F||(F=document.createElement("style"),n&&(F.nonce=n),F.setAttribute("type","text/css"),document.head.appendChild(F)),F.sheet&&(F.sheet.insertRule(`@media ${i} {body{ }}`,0),It.add(i))}catch(e){console.error(e)}}function ti(i){return{matches:i==="all"||i==="",media:i,addListener:()=>{},removeListener:()=>{}}}var Dt=(()=>{class i{constructor(e,t){this._mediaMatcher=e,this._zone=t,this._queries=new Map,this._destroySubject=new v}ngOnDestroy(){this._destroySubject.next(),this._destroySubject.complete()}isMatched(e){return At(Me(e)).some(s=>this._registerQuery(s).mql.matches)}observe(e){let s=At(Me(e)).map(r=>this._registerQuery(r).observable),o=Qe(s);return o=Xe(o.pipe(G(1)),o.pipe(Z(1),L(0))),o.pipe(V(r=>{let c={matches:!1,breakpoints:{}};return r.forEach(({matches:M,query:D})=>{c.matches=c.matches||M,c.breakpoints[D]=M}),c}))}_registerQuery(e){if(this._queries.has(e))return this._queries.get(e);let t=this._mediaMatcher.matchMedia(e),o={observable:new U(r=>{let c=M=>this._zone.run(()=>r.next(M));return t.addListener(c),()=>{t.removeListener(c)}}).pipe(et(t),V(({matches:r})=>({query:e,matches:r})),Y(this._destroySubject)),mql:t};return this._queries.set(e,o),o}static{this.\u0275fac=function(t){return new(t||i)(a(Jt),a(y))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();function At(i){return i.map(n=>n.split(",")).reduce((n,e)=>n.concat(e)).map(n=>n.trim())}var es={XSmall:"(max-width: 599.98px)",Small:"(min-width: 600px) and (max-width: 959.98px)",Medium:"(min-width: 960px) and (max-width: 1279.98px)",Large:"(min-width: 1280px) and (max-width: 1919.98px)",XLarge:"(min-width: 1920px)",Handset:"(max-width: 599.98px) and (orientation: portrait), (max-width: 959.98px) and (orientation: landscape)",Tablet:"(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait), (min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)",Web:"(min-width: 840px) and (orientation: portrait), (min-width: 1280px) and (orientation: landscape)",HandsetPortrait:"(max-width: 599.98px) and (orientation: portrait)",TabletPortrait:"(min-width: 600px) and (max-width: 839.98px) and (orientation: portrait)",WebPortrait:"(min-width: 840px) and (orientation: portrait)",HandsetLandscape:"(max-width: 959.98px) and (orientation: landscape)",TabletLandscape:"(min-width: 960px) and (max-width: 1279.98px) and (orientation: landscape)",WebLandscape:"(min-width: 1280px) and (orientation: landscape)"};var Ot=" ";function yi(i,n,e){let t=de(i,n);e=e.trim(),!t.some(s=>s.trim()===e)&&(t.push(e),i.setAttribute(n,t.join(Ot)))}function Ei(i,n,e){let t=de(i,n);e=e.trim();let s=t.filter(o=>o!==e);s.length?i.setAttribute(n,s.join(Ot)):i.removeAttribute(n)}function de(i,n){return i.getAttribute(n)?.match(/\S+/g)??[]}var Nt="cdk-describedby-message",re="cdk-describedby-host",ke=0,bs=(()=>{class i{constructor(e,t){this._platform=t,this._messageRegistry=new Map,this._messagesContainer=null,this._id=`${ke++}`,this._document=e,this._id=p(nt)+"-"+ke++}describe(e,t,s){if(!this._canBeDescribed(e,t))return;let o=we(t,s);typeof t!="string"?(Mt(t,this._id),this._messageRegistry.set(o,{messageElement:t,referenceCount:0})):this._messageRegistry.has(o)||this._createMessageElement(t,s),this._isElementDescribedByMessage(e,o)||this._addMessageReference(e,o)}removeDescription(e,t,s){if(!t||!this._isElementNode(e))return;let o=we(t,s);if(this._isElementDescribedByMessage(e,o)&&this._removeMessageReference(e,o),typeof t=="string"){let r=this._messageRegistry.get(o);r&&r.referenceCount===0&&this._deleteMessageElement(o)}this._messagesContainer?.childNodes.length===0&&(this._messagesContainer.remove(),this._messagesContainer=null)}ngOnDestroy(){let e=this._document.querySelectorAll(`[${re}="${this._id}"]`);for(let t=0;t<e.length;t++)this._removeCdkDescribedByReferenceIds(e[t]),e[t].removeAttribute(re);this._messagesContainer?.remove(),this._messagesContainer=null,this._messageRegistry.clear()}_createMessageElement(e,t){let s=this._document.createElement("div");Mt(s,this._id),s.textContent=e,t&&s.setAttribute("role",t),this._createMessagesContainer(),this._messagesContainer.appendChild(s),this._messageRegistry.set(we(e,t),{messageElement:s,referenceCount:0})}_deleteMessageElement(e){this._messageRegistry.get(e)?.messageElement?.remove(),this._messageRegistry.delete(e)}_createMessagesContainer(){if(this._messagesContainer)return;let e="cdk-describedby-message-container",t=this._document.querySelectorAll(`.${e}[platform="server"]`);for(let o=0;o<t.length;o++)t[o].remove();let s=this._document.createElement("div");s.style.visibility="hidden",s.classList.add(e),s.classList.add("cdk-visually-hidden"),this._platform&&!this._platform.isBrowser&&s.setAttribute("platform","server"),this._document.body.appendChild(s),this._messagesContainer=s}_removeCdkDescribedByReferenceIds(e){let t=de(e,"aria-describedby").filter(s=>s.indexOf(Nt)!=0);e.setAttribute("aria-describedby",t.join(" "))}_addMessageReference(e,t){let s=this._messageRegistry.get(t);yi(e,"aria-describedby",s.messageElement.id),e.setAttribute(re,this._id),s.referenceCount++}_removeMessageReference(e,t){let s=this._messageRegistry.get(t);s.referenceCount--,Ei(e,"aria-describedby",s.messageElement.id),e.removeAttribute(re)}_isElementDescribedByMessage(e,t){let s=de(e,"aria-describedby"),o=this._messageRegistry.get(t),r=o&&o.messageElement.id;return!!r&&s.indexOf(r)!=-1}_canBeDescribed(e,t){if(!this._isElementNode(e))return!1;if(t&&typeof t=="object")return!0;let s=t==null?"":`${t}`.trim(),o=e.getAttribute("aria-label");return s?!o||o.trim()!==s:!1}_isElementNode(e){return e.nodeType===this._document.ELEMENT_NODE}static{this.\u0275fac=function(t){return new(t||i)(a(E),a(g))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();function we(i,n){return typeof i=="string"?`${n||""}/${i}`:i}function Mt(i,n){i.id||(i.id=`${Nt}-${n}-${ke++}`)}var le=class{constructor(n){this._items=n,this._activeItemIndex=-1,this._activeItem=null,this._wrap=!1,this._letterKeyStream=new v,this._typeaheadSubscription=Ge.EMPTY,this._vertical=!0,this._allowedModifierKeys=[],this._homeAndEnd=!1,this._pageUpAndDown={enabled:!1,delta:10},this._skipPredicateFn=e=>e.disabled,this._pressedLetters=[],this.tabOut=new v,this.change=new v,n instanceof fe&&(this._itemChangesSubscription=n.changes.subscribe(e=>{if(this._activeItem){let s=e.toArray().indexOf(this._activeItem);s>-1&&s!==this._activeItemIndex&&(this._activeItemIndex=s)}}))}skipPredicate(n){return this._skipPredicateFn=n,this}withWrap(n=!0){return this._wrap=n,this}withVerticalOrientation(n=!0){return this._vertical=n,this}withHorizontalOrientation(n){return this._horizontal=n,this}withAllowedModifierKeys(n){return this._allowedModifierKeys=n,this}withTypeAhead(n=200){return this._typeaheadSubscription.unsubscribe(),this._typeaheadSubscription=this._letterKeyStream.pipe(tt(e=>this._pressedLetters.push(e)),L(n),qe(()=>this._pressedLetters.length>0),V(()=>this._pressedLetters.join(""))).subscribe(e=>{let t=this._getItemsArray();for(let s=1;s<t.length+1;s++){let o=(this._activeItemIndex+s)%t.length,r=t[o];if(!this._skipPredicateFn(r)&&r.getLabel().toUpperCase().trim().indexOf(e)===0){this.setActiveItem(o);break}}this._pressedLetters=[]}),this}cancelTypeahead(){return this._pressedLetters=[],this}withHomeAndEnd(n=!0){return this._homeAndEnd=n,this}withPageUpDown(n=!0,e=10){return this._pageUpAndDown={enabled:n,delta:e},this}setActiveItem(n){let e=this._activeItem;this.updateActiveItem(n),this._activeItem!==e&&this.change.next(this._activeItemIndex)}onKeydown(n){let e=n.keyCode,s=["altKey","ctrlKey","metaKey","shiftKey"].every(o=>!n[o]||this._allowedModifierKeys.indexOf(o)>-1);switch(e){case 9:this.tabOut.next();return;case 40:if(this._vertical&&s){this.setNextItemActive();break}else return;case 38:if(this._vertical&&s){this.setPreviousItemActive();break}else return;case 39:if(this._horizontal&&s){this._horizontal==="rtl"?this.setPreviousItemActive():this.setNextItemActive();break}else return;case 37:if(this._horizontal&&s){this._horizontal==="rtl"?this.setNextItemActive():this.setPreviousItemActive();break}else return;case 36:if(this._homeAndEnd&&s){this.setFirstItemActive();break}else return;case 35:if(this._homeAndEnd&&s){this.setLastItemActive();break}else return;case 33:if(this._pageUpAndDown.enabled&&s){let o=this._activeItemIndex-this._pageUpAndDown.delta;this._setActiveItemByIndex(o>0?o:0,1);break}else return;case 34:if(this._pageUpAndDown.enabled&&s){let o=this._activeItemIndex+this._pageUpAndDown.delta,r=this._getItemsArray().length;this._setActiveItemByIndex(o<r?o:r-1,-1);break}else return;default:(s||oe(n,"shiftKey"))&&(n.key&&n.key.length===1?this._letterKeyStream.next(n.key.toLocaleUpperCase()):(e>=65&&e<=90||e>=48&&e<=57)&&this._letterKeyStream.next(String.fromCharCode(e)));return}this._pressedLetters=[],n.preventDefault()}get activeItemIndex(){return this._activeItemIndex}get activeItem(){return this._activeItem}isTyping(){return this._pressedLetters.length>0}setFirstItemActive(){this._setActiveItemByIndex(0,1)}setLastItemActive(){this._setActiveItemByIndex(this._items.length-1,-1)}setNextItemActive(){this._activeItemIndex<0?this.setFirstItemActive():this._setActiveItemByDelta(1)}setPreviousItemActive(){this._activeItemIndex<0&&this._wrap?this.setLastItemActive():this._setActiveItemByDelta(-1)}updateActiveItem(n){let e=this._getItemsArray(),t=typeof n=="number"?n:e.indexOf(n),s=e[t];this._activeItem=s??null,this._activeItemIndex=t}destroy(){this._typeaheadSubscription.unsubscribe(),this._itemChangesSubscription?.unsubscribe(),this._letterKeyStream.complete(),this.tabOut.complete(),this.change.complete(),this._pressedLetters=[]}_setActiveItemByDelta(n){this._wrap?this._setActiveInWrapMode(n):this._setActiveInDefaultMode(n)}_setActiveInWrapMode(n){let e=this._getItemsArray();for(let t=1;t<=e.length;t++){let s=(this._activeItemIndex+n*t+e.length)%e.length,o=e[s];if(!this._skipPredicateFn(o)){this.setActiveItem(s);return}}}_setActiveInDefaultMode(n){this._setActiveItemByIndex(this._activeItemIndex+n,n)}_setActiveItemByIndex(n,e){let t=this._getItemsArray();if(t[n]){for(;this._skipPredicateFn(t[n]);)if(n+=e,!t[n])return;this.setActiveItem(n)}}_getItemsArray(){return this._items instanceof fe?this._items.toArray():this._items}},wt=class extends le{setActiveItem(n){this.activeItem&&this.activeItem.setInactiveStyles(),super.setActiveItem(n),this.activeItem&&this.activeItem.setActiveStyles()}},Tt=class extends le{constructor(){super(...arguments),this._origin="program"}setFocusOrigin(n){return this._origin=n,this}setActiveItem(n){super.setActiveItem(n),this.activeItem&&this.activeItem.focus(this._origin)}};var Ii=(()=>{class i{constructor(e){this._platform=e}isDisabled(e){return e.hasAttribute("disabled")}isVisible(e){return Di(e)&&getComputedStyle(e).visibility==="visible"}isTabbable(e){if(!this._platform.isBrowser)return!1;let t=Ai(Ni(e));if(t&&(kt(t)===-1||!this.isVisible(t)))return!1;let s=e.nodeName.toLowerCase(),o=kt(e);return e.hasAttribute("contenteditable")?o!==-1:s==="iframe"||s==="object"||this._platform.WEBKIT&&this._platform.IOS&&!Ci(e)?!1:s==="audio"?e.hasAttribute("controls")?o!==-1:!1:s==="video"?o===-1?!1:o!==null?!0:this._platform.FIREFOX||e.hasAttribute("controls"):e.tabIndex>=0}isFocusable(e,t){return Oi(e)&&!this.isDisabled(e)&&(t?.ignoreVisibility||this.isVisible(e))}static{this.\u0275fac=function(t){return new(t||i)(a(g))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();function Ai(i){try{return i.frameElement}catch{return null}}function Di(i){return!!(i.offsetWidth||i.offsetHeight||typeof i.getClientRects=="function"&&i.getClientRects().length)}function Mi(i){let n=i.nodeName.toLowerCase();return n==="input"||n==="select"||n==="button"||n==="textarea"}function wi(i){return ki(i)&&i.type=="hidden"}function Ti(i){return xi(i)&&i.hasAttribute("href")}function ki(i){return i.nodeName.toLowerCase()=="input"}function xi(i){return i.nodeName.toLowerCase()=="a"}function Ft(i){if(!i.hasAttribute("tabindex")||i.tabIndex===void 0)return!1;let n=i.getAttribute("tabindex");return!!(n&&!isNaN(parseInt(n,10)))}function kt(i){if(!Ft(i))return null;let n=parseInt(i.getAttribute("tabindex")||"",10);return isNaN(n)?-1:n}function Ci(i){let n=i.nodeName.toLowerCase(),e=n==="input"&&i.type;return e==="text"||e==="password"||n==="select"||n==="textarea"}function Oi(i){return wi(i)?!1:Mi(i)||Ti(i)||i.hasAttribute("contenteditable")||Ft(i)}function Ni(i){return i.ownerDocument&&i.ownerDocument.defaultView||window}var xe=class{get enabled(){return this._enabled}set enabled(n){this._enabled=n,this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(n,this._startAnchor),this._toggleAnchorTabIndex(n,this._endAnchor))}constructor(n,e,t,s,o=!1){this._element=n,this._checker=e,this._ngZone=t,this._document=s,this._hasAttached=!1,this.startAnchorListener=()=>this.focusLastTabbableElement(),this.endAnchorListener=()=>this.focusFirstTabbableElement(),this._enabled=!0,o||this.attachAnchors()}destroy(){let n=this._startAnchor,e=this._endAnchor;n&&(n.removeEventListener("focus",this.startAnchorListener),n.remove()),e&&(e.removeEventListener("focus",this.endAnchorListener),e.remove()),this._startAnchor=this._endAnchor=null,this._hasAttached=!1}attachAnchors(){return this._hasAttached?!0:(this._ngZone.runOutsideAngular(()=>{this._startAnchor||(this._startAnchor=this._createAnchor(),this._startAnchor.addEventListener("focus",this.startAnchorListener)),this._endAnchor||(this._endAnchor=this._createAnchor(),this._endAnchor.addEventListener("focus",this.endAnchorListener))}),this._element.parentNode&&(this._element.parentNode.insertBefore(this._startAnchor,this._element),this._element.parentNode.insertBefore(this._endAnchor,this._element.nextSibling),this._hasAttached=!0),this._hasAttached)}focusInitialElementWhenReady(n){return new Promise(e=>{this._executeOnStable(()=>e(this.focusInitialElement(n)))})}focusFirstTabbableElementWhenReady(n){return new Promise(e=>{this._executeOnStable(()=>e(this.focusFirstTabbableElement(n)))})}focusLastTabbableElementWhenReady(n){return new Promise(e=>{this._executeOnStable(()=>e(this.focusLastTabbableElement(n)))})}_getRegionBoundary(n){let e=this._element.querySelectorAll(`[cdk-focus-region-${n}], [cdkFocusRegion${n}], [cdk-focus-${n}]`);return n=="start"?e.length?e[0]:this._getFirstTabbableElement(this._element):e.length?e[e.length-1]:this._getLastTabbableElement(this._element)}focusInitialElement(n){let e=this._element.querySelector("[cdk-focus-initial], [cdkFocusInitial]");if(e){if(!this._checker.isFocusable(e)){let t=this._getFirstTabbableElement(e);return t?.focus(n),!!t}return e.focus(n),!0}return this.focusFirstTabbableElement(n)}focusFirstTabbableElement(n){let e=this._getRegionBoundary("start");return e&&e.focus(n),!!e}focusLastTabbableElement(n){let e=this._getRegionBoundary("end");return e&&e.focus(n),!!e}hasAttached(){return this._hasAttached}_getFirstTabbableElement(n){if(this._checker.isFocusable(n)&&this._checker.isTabbable(n))return n;let e=n.children;for(let t=0;t<e.length;t++){let s=e[t].nodeType===this._document.ELEMENT_NODE?this._getFirstTabbableElement(e[t]):null;if(s)return s}return null}_getLastTabbableElement(n){if(this._checker.isFocusable(n)&&this._checker.isTabbable(n))return n;let e=n.children;for(let t=e.length-1;t>=0;t--){let s=e[t].nodeType===this._document.ELEMENT_NODE?this._getLastTabbableElement(e[t]):null;if(s)return s}return null}_createAnchor(){let n=this._document.createElement("div");return this._toggleAnchorTabIndex(this._enabled,n),n.classList.add("cdk-visually-hidden"),n.classList.add("cdk-focus-trap-anchor"),n.setAttribute("aria-hidden","true"),n}_toggleAnchorTabIndex(n,e){n?e.setAttribute("tabindex","0"):e.removeAttribute("tabindex")}toggleAnchors(n){this._startAnchor&&this._endAnchor&&(this._toggleAnchorTabIndex(n,this._startAnchor),this._toggleAnchorTabIndex(n,this._endAnchor))}_executeOnStable(n){this._ngZone.isStable?n():this._ngZone.onStable.pipe(G(1)).subscribe(n)}},Fi=(()=>{class i{constructor(e,t,s){this._checker=e,this._ngZone=t,this._document=s}create(e,t=!1){return new xe(e,this._checker,this._ngZone,this._document,t)}static{this.\u0275fac=function(t){return new(t||i)(a(Ii),a(y),a(E))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),vs=(()=>{class i{get enabled(){return this.focusTrap?.enabled||!1}set enabled(e){this.focusTrap&&(this.focusTrap.enabled=e)}constructor(e,t,s){this._elementRef=e,this._focusTrapFactory=t,this._previouslyFocusedElement=null,p(g).isBrowser&&(this.focusTrap=this._focusTrapFactory.create(this._elementRef.nativeElement,!0))}ngOnDestroy(){this.focusTrap?.destroy(),this._previouslyFocusedElement&&(this._previouslyFocusedElement.focus(),this._previouslyFocusedElement=null)}ngAfterContentInit(){this.focusTrap?.attachAnchors(),this.autoCapture&&this._captureFocus()}ngDoCheck(){this.focusTrap&&!this.focusTrap.hasAttached()&&this.focusTrap.attachAnchors()}ngOnChanges(e){let t=e.autoCapture;t&&!t.firstChange&&this.autoCapture&&this.focusTrap?.hasAttached()&&this._captureFocus()}_captureFocus(){this._previouslyFocusedElement=bt(),this.focusTrap?.focusInitialElementWhenReady()}static{this.\u0275fac=function(t){return new(t||i)(l(I),l(Fi),l(E))}}static{this.\u0275dir=T({type:i,selectors:[["","cdkTrapFocus",""]],inputs:{enabled:[f.HasDecoratorInputTransform,"cdkTrapFocus","enabled",O],autoCapture:[f.HasDecoratorInputTransform,"cdkTrapFocusAutoCapture","autoCapture",O]},exportAs:["cdkTrapFocus"],standalone:!0,features:[S,it]})}}return i})();function Ce(i){return i.buttons===0||i.detail===0}function Oe(i){let n=i.touches&&i.touches[0]||i.changedTouches&&i.changedTouches[0];return!!n&&n.identifier===-1&&(n.radiusX==null||n.radiusX===1)&&(n.radiusY==null||n.radiusY===1)}var Ri=new m("cdk-input-modality-detector-options"),Li={ignoreKeys:[18,17,224,91,16]},Rt=650,B=P({passive:!0,capture:!0}),Si=(()=>{class i{get mostRecentModality(){return this._modality.value}constructor(e,t,s,o){this._platform=e,this._mostRecentTarget=null,this._modality=new Ze(null),this._lastTouchMs=0,this._onKeydown=r=>{this._options?.ignoreKeys?.some(c=>c===r.keyCode)||(this._modality.next("keyboard"),this._mostRecentTarget=x(r))},this._onMousedown=r=>{Date.now()-this._lastTouchMs<Rt||(this._modality.next(Ce(r)?"keyboard":"mouse"),this._mostRecentTarget=x(r))},this._onTouchstart=r=>{if(Oe(r)){this._modality.next("keyboard");return}this._lastTouchMs=Date.now(),this._modality.next("touch"),this._mostRecentTarget=x(r)},this._options=u(u({},Li),o),this.modalityDetected=this._modality.pipe(Z(1)),this.modalityChanged=this.modalityDetected.pipe(Je()),e.isBrowser&&t.runOutsideAngular(()=>{s.addEventListener("keydown",this._onKeydown,B),s.addEventListener("mousedown",this._onMousedown,B),s.addEventListener("touchstart",this._onTouchstart,B)})}ngOnDestroy(){this._modality.complete(),this._platform.isBrowser&&(document.removeEventListener("keydown",this._onKeydown,B),document.removeEventListener("mousedown",this._onMousedown,B),document.removeEventListener("touchstart",this._onTouchstart,B))}static{this.\u0275fac=function(t){return new(t||i)(a(g),a(y),a(E),a(Ri,8))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),ji=new m("liveAnnouncerElement",{providedIn:"root",factory:Pi});function Pi(){return null}var Bi=new m("LIVE_ANNOUNCER_DEFAULT_OPTIONS"),Ui=0,ys=(()=>{class i{constructor(e,t,s,o){this._ngZone=t,this._defaultOptions=o,this._document=s,this._liveElement=e||this._createLiveElement()}announce(e,...t){let s=this._defaultOptions,o,r;return t.length===1&&typeof t[0]=="number"?r=t[0]:[o,r]=t,this.clear(),clearTimeout(this._previousTimeout),o||(o=s&&s.politeness?s.politeness:"polite"),r==null&&s&&(r=s.duration),this._liveElement.setAttribute("aria-live",o),this._liveElement.id&&this._exposeAnnouncerToModals(this._liveElement.id),this._ngZone.runOutsideAngular(()=>(this._currentPromise||(this._currentPromise=new Promise(c=>this._currentResolve=c)),clearTimeout(this._previousTimeout),this._previousTimeout=setTimeout(()=>{this._liveElement.textContent=e,typeof r=="number"&&(this._previousTimeout=setTimeout(()=>this.clear(),r)),this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0},100),this._currentPromise))}clear(){this._liveElement&&(this._liveElement.textContent="")}ngOnDestroy(){clearTimeout(this._previousTimeout),this._liveElement?.remove(),this._liveElement=null,this._currentResolve?.(),this._currentPromise=this._currentResolve=void 0}_createLiveElement(){let e="cdk-live-announcer-element",t=this._document.getElementsByClassName(e),s=this._document.createElement("div");for(let o=0;o<t.length;o++)t[o].remove();return s.classList.add(e),s.classList.add("cdk-visually-hidden"),s.setAttribute("aria-atomic","true"),s.setAttribute("aria-live","polite"),s.id=`cdk-live-announcer-${Ui++}`,this._document.body.appendChild(s),s}_exposeAnnouncerToModals(e){let t=this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal="true"]');for(let s=0;s<t.length;s++){let o=t[s],r=o.getAttribute("aria-owns");r?r.indexOf(e)===-1&&o.setAttribute("aria-owns",r+" "+e):o.setAttribute("aria-owns",e)}}static{this.\u0275fac=function(t){return new(t||i)(a(ji,8),a(y),a(E),a(Bi,8))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var ce=function(i){return i[i.IMMEDIATE=0]="IMMEDIATE",i[i.EVENTUAL=1]="EVENTUAL",i}(ce||{}),Vi=new m("cdk-focus-monitor-default-options"),ae=P({passive:!0,capture:!0}),Hi=(()=>{class i{constructor(e,t,s,o,r){this._ngZone=e,this._platform=t,this._inputModalityDetector=s,this._origin=null,this._windowFocused=!1,this._originFromTouchInteraction=!1,this._elementInfo=new Map,this._monitoredElementCount=0,this._rootNodeFocusListenerCount=new Map,this._windowFocusListener=()=>{this._windowFocused=!0,this._windowFocusTimeoutId=window.setTimeout(()=>this._windowFocused=!1)},this._stopInputModalityDetector=new v,this._rootNodeFocusAndBlurListener=c=>{let M=x(c);for(let D=M;D;D=D.parentElement)c.type==="focus"?this._onFocus(c,D):this._onBlur(c,D)},this._document=o,this._detectionMode=r?.detectionMode||ce.IMMEDIATE}monitor(e,t=!1){let s=w(e);if(!this._platform.isBrowser||s.nodeType!==1)return Ye();let o=gt(s)||this._getDocument(),r=this._elementInfo.get(s);if(r)return t&&(r.checkChildren=!0),r.subject;let c={checkChildren:t,subject:new v,rootNode:o};return this._elementInfo.set(s,c),this._registerGlobalListeners(c),c.subject}stopMonitoring(e){let t=w(e),s=this._elementInfo.get(t);s&&(s.subject.complete(),this._setClasses(t),this._elementInfo.delete(t),this._removeGlobalListeners(s))}focusVia(e,t,s){let o=w(e),r=this._getDocument().activeElement;o===r?this._getClosestElementsInfo(o).forEach(([c,M])=>this._originChanged(c,t,M)):(this._setOrigin(t),typeof o.focus=="function"&&o.focus(s))}ngOnDestroy(){this._elementInfo.forEach((e,t)=>this.stopMonitoring(t))}_getDocument(){return this._document||document}_getWindow(){return this._getDocument().defaultView||window}_getFocusOrigin(e){return this._origin?this._originFromTouchInteraction?this._shouldBeAttributedToTouch(e)?"touch":"program":this._origin:this._windowFocused&&this._lastFocusOrigin?this._lastFocusOrigin:e&&this._isLastInteractionFromInputLabel(e)?"mouse":"program"}_shouldBeAttributedToTouch(e){return this._detectionMode===ce.EVENTUAL||!!e?.contains(this._inputModalityDetector._mostRecentTarget)}_setClasses(e,t){e.classList.toggle("cdk-focused",!!t),e.classList.toggle("cdk-touch-focused",t==="touch"),e.classList.toggle("cdk-keyboard-focused",t==="keyboard"),e.classList.toggle("cdk-mouse-focused",t==="mouse"),e.classList.toggle("cdk-program-focused",t==="program")}_setOrigin(e,t=!1){this._ngZone.runOutsideAngular(()=>{if(this._origin=e,this._originFromTouchInteraction=e==="touch"&&t,this._detectionMode===ce.IMMEDIATE){clearTimeout(this._originTimeoutId);let s=this._originFromTouchInteraction?Rt:1;this._originTimeoutId=setTimeout(()=>this._origin=null,s)}})}_onFocus(e,t){let s=this._elementInfo.get(t),o=x(e);!s||!s.checkChildren&&t!==o||this._originChanged(t,this._getFocusOrigin(o),s)}_onBlur(e,t){let s=this._elementInfo.get(t);!s||s.checkChildren&&e.relatedTarget instanceof Node&&t.contains(e.relatedTarget)||(this._setClasses(t),this._emitOrigin(s,null))}_emitOrigin(e,t){e.subject.observers.length&&this._ngZone.run(()=>e.subject.next(t))}_registerGlobalListeners(e){if(!this._platform.isBrowser)return;let t=e.rootNode,s=this._rootNodeFocusListenerCount.get(t)||0;s||this._ngZone.runOutsideAngular(()=>{t.addEventListener("focus",this._rootNodeFocusAndBlurListener,ae),t.addEventListener("blur",this._rootNodeFocusAndBlurListener,ae)}),this._rootNodeFocusListenerCount.set(t,s+1),++this._monitoredElementCount===1&&(this._ngZone.runOutsideAngular(()=>{this._getWindow().addEventListener("focus",this._windowFocusListener)}),this._inputModalityDetector.modalityDetected.pipe(Y(this._stopInputModalityDetector)).subscribe(o=>{this._setOrigin(o,!0)}))}_removeGlobalListeners(e){let t=e.rootNode;if(this._rootNodeFocusListenerCount.has(t)){let s=this._rootNodeFocusListenerCount.get(t);s>1?this._rootNodeFocusListenerCount.set(t,s-1):(t.removeEventListener("focus",this._rootNodeFocusAndBlurListener,ae),t.removeEventListener("blur",this._rootNodeFocusAndBlurListener,ae),this._rootNodeFocusListenerCount.delete(t))}--this._monitoredElementCount||(this._getWindow().removeEventListener("focus",this._windowFocusListener),this._stopInputModalityDetector.next(),clearTimeout(this._windowFocusTimeoutId),clearTimeout(this._originTimeoutId))}_originChanged(e,t,s){this._setClasses(e,t),this._emitOrigin(s,t),this._lastFocusOrigin=t}_getClosestElementsInfo(e){let t=[];return this._elementInfo.forEach((s,o)=>{(o===e||s.checkChildren&&o.contains(e))&&t.push([o,s])}),t}_isLastInteractionFromInputLabel(e){let{_mostRecentTarget:t,mostRecentModality:s}=this._inputModalityDetector;if(s!=="mouse"||!t||t===e||e.nodeName!=="INPUT"&&e.nodeName!=="TEXTAREA"||e.disabled)return!1;let o=e.labels;if(o){for(let r=0;r<o.length;r++)if(o[r].contains(t))return!0}return!1}static{this.\u0275fac=function(t){return new(t||i)(a(y),a(g),a(Si),a(E,8),a(Vi,8))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),Es=(()=>{class i{constructor(e,t){this._elementRef=e,this._focusMonitor=t,this._focusOrigin=null,this.cdkFocusChange=new k}get focusOrigin(){return this._focusOrigin}ngAfterViewInit(){let e=this._elementRef.nativeElement;this._monitorSubscription=this._focusMonitor.monitor(e,e.nodeType===1&&e.hasAttribute("cdkMonitorSubtreeFocus")).subscribe(t=>{this._focusOrigin=t,this.cdkFocusChange.emit(t)})}ngOnDestroy(){this._focusMonitor.stopMonitoring(this._elementRef),this._monitorSubscription&&this._monitorSubscription.unsubscribe()}static{this.\u0275fac=function(t){return new(t||i)(l(I),l(Hi))}}static{this.\u0275dir=T({type:i,selectors:[["","cdkMonitorElementFocus",""],["","cdkMonitorSubtreeFocus",""]],outputs:{cdkFocusChange:"cdkFocusChange"},exportAs:["cdkMonitorFocus"],standalone:!0})}}return i})(),R=function(i){return i[i.NONE=0]="NONE",i[i.BLACK_ON_WHITE=1]="BLACK_ON_WHITE",i[i.WHITE_ON_BLACK=2]="WHITE_ON_BLACK",i}(R||{}),xt="cdk-high-contrast-black-on-white",Ct="cdk-high-contrast-white-on-black",Te="cdk-high-contrast-active",Ne=(()=>{class i{constructor(e,t){this._platform=e,this._document=t,this._breakpointSubscription=p(Dt).observe("(forced-colors: active)").subscribe(()=>{this._hasCheckedHighContrastMode&&(this._hasCheckedHighContrastMode=!1,this._applyBodyHighContrastModeCssClasses())})}getHighContrastMode(){if(!this._platform.isBrowser)return R.NONE;let e=this._document.createElement("div");e.style.backgroundColor="rgb(1,2,3)",e.style.position="absolute",this._document.body.appendChild(e);let t=this._document.defaultView||window,s=t&&t.getComputedStyle?t.getComputedStyle(e):null,o=(s&&s.backgroundColor||"").replace(/ /g,"");switch(e.remove(),o){case"rgb(0,0,0)":case"rgb(45,50,54)":case"rgb(32,32,32)":return R.WHITE_ON_BLACK;case"rgb(255,255,255)":case"rgb(255,250,239)":return R.BLACK_ON_WHITE}return R.NONE}ngOnDestroy(){this._breakpointSubscription.unsubscribe()}_applyBodyHighContrastModeCssClasses(){if(!this._hasCheckedHighContrastMode&&this._platform.isBrowser&&this._document.body){let e=this._document.body.classList;e.remove(Te,xt,Ct),this._hasCheckedHighContrastMode=!0;let t=this.getHighContrastMode();t===R.BLACK_ON_WHITE?e.add(Te,xt):t===R.WHITE_ON_BLACK&&e.add(Te,Ct)}}static{this.\u0275fac=function(t){return new(t||i)(a(g),a(E))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),Is=(()=>{class i{constructor(e){e._applyBodyHighContrastModeCssClasses()}static{this.\u0275fac=function(t){return new(t||i)(a(Ne))}}static{this.\u0275mod=_({type:i})}static{this.\u0275inj=h({imports:[Et]})}}return i})();var $i=new m("cdk-dir-doc",{providedIn:"root",factory:zi});function zi(){return p(E)}var Gi=/^(ar|ckb|dv|he|iw|fa|nqo|ps|sd|ug|ur|yi|.*[-_](Adlm|Arab|Hebr|Nkoo|Rohg|Thaa))(?!.*[-_](Latn|Cyrl)($|-|_))($|-|_)/i;function Zi(i){let n=i?.toLowerCase()||"";return n==="auto"&&typeof navigator<"u"&&navigator?.language?Gi.test(navigator.language)?"rtl":"ltr":n==="rtl"?"rtl":"ltr"}var Fs=(()=>{class i{constructor(e){if(this.value="ltr",this.change=new k,e){let t=e.body?e.body.dir:null,s=e.documentElement?e.documentElement.dir:null;this.value=Zi(t||s||"ltr")}}ngOnDestroy(){this.change.complete()}static{this.\u0275fac=function(t){return new(t||i)(a($i,8))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var Fe=(()=>{class i{static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275mod=_({type:i})}static{this.\u0275inj=h({})}}return i})();var Xi=["text"],qi=[[["mat-icon"]],"*"],Ji=["mat-icon","*"];function en(i,n){if(i&1&&ee(0,"mat-pseudo-checkbox",1),i&2){let e=te();q("disabled",e.disabled)("state",e.selected?"checked":"unchecked")}}function tn(i,n){if(i&1&&ee(0,"mat-pseudo-checkbox",3),i&2){let e=te();q("disabled",e.disabled)}}function nn(i,n){if(i&1&&(be(0,"span",4),ut(1),ve()),i&2){let e=te();H(),ht("(",e.group.label,")")}}var sn=["mat-internal-form-field",""],on=["*"];var to=(()=>{class i{static{this.STANDARD_CURVE="cubic-bezier(0.4,0.0,0.2,1)"}static{this.DECELERATION_CURVE="cubic-bezier(0.0,0.0,0.2,1)"}static{this.ACCELERATION_CURVE="cubic-bezier(0.4,0.0,1,1)"}static{this.SHARP_CURVE="cubic-bezier(0.4,0.0,0.6,1)"}}return i})(),io=(()=>{class i{static{this.COMPLEX="375ms"}static{this.ENTERING="225ms"}static{this.EXITING="195ms"}}return i})();function rn(){return!0}var an=new m("mat-sanity-checks",{providedIn:"root",factory:rn}),he=(()=>{class i{constructor(e,t,s){this._sanityChecks=t,this._document=s,this._hasDoneGlobalChecks=!1,e._applyBodyHighContrastModeCssClasses(),this._hasDoneGlobalChecks||(this._hasDoneGlobalChecks=!0)}_checkIsEnabled(e){return vt()?!1:typeof this._sanityChecks=="boolean"?this._sanityChecks:!!this._sanityChecks[e]}static{this.\u0275fac=function(t){return new(t||i)(a(Ne),a(an,8),a(E))}}static{this.\u0275mod=_({type:i})}static{this.\u0275inj=h({imports:[Fe,Fe]})}}return i})();var Lt=class{constructor(n,e,t,s,o){this._defaultMatcher=n,this.ngControl=e,this._parentFormGroup=t,this._parentForm=s,this._stateChanges=o,this.errorState=!1}updateErrorState(){let n=this.errorState,e=this._parentFormGroup||this._parentForm,t=this.matcher||this._defaultMatcher,s=this.ngControl?this.ngControl.control:null,o=t?.isErrorState(s,e)??!1;o!==n&&(this.errorState=o,this._stateChanges.next())}};var St=new m("MAT_DATE_LOCALE",{providedIn:"root",factory:cn});function cn(){return p(mt)}var me=class{constructor(){this._localeChanges=new v,this.localeChanges=this._localeChanges}getValidDateOrNull(n){return this.isDateInstance(n)&&this.isValid(n)?n:null}deserialize(n){return n==null||this.isDateInstance(n)&&this.isValid(n)?n:this.invalid()}setLocale(n){this.locale=n,this._localeChanges.next()}compareDate(n,e){return this.getYear(n)-this.getYear(e)||this.getMonth(n)-this.getMonth(e)||this.getDate(n)-this.getDate(e)}sameDate(n,e){if(n&&e){let t=this.isValid(n),s=this.isValid(e);return t&&s?!this.compareDate(n,e):t==s}return n==e}clampDate(n,e,t){return e&&this.compareDate(n,e)<0?e:t&&this.compareDate(n,t)>0?t:n}},dn=new m("mat-date-formats"),ln=/^\d{4}-\d{2}-\d{2}(?:T\d{2}:\d{2}:\d{2}(?:\.\d+)?(?:Z|(?:(?:\+|-)\d{2}:\d{2}))?)?$/;function Re(i,n){let e=Array(i);for(let t=0;t<i;t++)e[t]=n(t);return e}var un=(()=>{class i extends me{constructor(e){super(),this.useUtcForDisplay=!1,this._matDateLocale=p(St,{optional:!0}),e!==void 0&&(this._matDateLocale=e),super.setLocale(this._matDateLocale)}getYear(e){return e.getFullYear()}getMonth(e){return e.getMonth()}getDate(e){return e.getDate()}getDayOfWeek(e){return e.getDay()}getMonthNames(e){let t=new Intl.DateTimeFormat(this.locale,{month:e,timeZone:"utc"});return Re(12,s=>this._format(t,new Date(2017,s,1)))}getDateNames(){let e=new Intl.DateTimeFormat(this.locale,{day:"numeric",timeZone:"utc"});return Re(31,t=>this._format(e,new Date(2017,0,t+1)))}getDayOfWeekNames(e){let t=new Intl.DateTimeFormat(this.locale,{weekday:e,timeZone:"utc"});return Re(7,s=>this._format(t,new Date(2017,0,s+1)))}getYearName(e){let t=new Intl.DateTimeFormat(this.locale,{year:"numeric",timeZone:"utc"});return this._format(t,e)}getFirstDayOfWeek(){return 0}getNumDaysInMonth(e){return this.getDate(this._createDateWithOverflow(this.getYear(e),this.getMonth(e)+1,0))}clone(e){return new Date(e.getTime())}createDate(e,t,s){let o=this._createDateWithOverflow(e,t,s);return o.getMonth()!=t,o}today(){return new Date}parse(e,t){return typeof e=="number"?new Date(e):e?new Date(Date.parse(e)):null}format(e,t){if(!this.isValid(e))throw Error("NativeDateAdapter: Cannot format invalid date.");let s=new Intl.DateTimeFormat(this.locale,ze(u({},t),{timeZone:"utc"}));return this._format(s,e)}addCalendarYears(e,t){return this.addCalendarMonths(e,t*12)}addCalendarMonths(e,t){let s=this._createDateWithOverflow(this.getYear(e),this.getMonth(e)+t,this.getDate(e));return this.getMonth(s)!=((this.getMonth(e)+t)%12+12)%12&&(s=this._createDateWithOverflow(this.getYear(s),this.getMonth(s),0)),s}addCalendarDays(e,t){return this._createDateWithOverflow(this.getYear(e),this.getMonth(e),this.getDate(e)+t)}toIso8601(e){return[e.getUTCFullYear(),this._2digit(e.getUTCMonth()+1),this._2digit(e.getUTCDate())].join("-")}deserialize(e){if(typeof e=="string"){if(!e)return null;if(ln.test(e)){let t=new Date(e);if(this.isValid(t))return t}}return super.deserialize(e)}isDateInstance(e){return e instanceof Date}isValid(e){return!isNaN(e.getTime())}invalid(){return new Date(NaN)}_createDateWithOverflow(e,t,s){let o=new Date;return o.setFullYear(e,t,s),o.setHours(0,0,0,0),o}_2digit(e){return("00"+e).slice(-2)}_format(e,t){let s=new Date;return s.setUTCFullYear(t.getFullYear(),t.getMonth(),t.getDate()),s.setUTCHours(t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()),e.format(s)}static{this.\u0275fac=function(t){return new(t||i)(a(St,8))}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac})}}return i})(),hn={parse:{dateInput:null},display:{dateInput:{year:"numeric",month:"numeric",day:"numeric"},monthYearLabel:{year:"numeric",month:"short"},dateA11yLabel:{year:"numeric",month:"long",day:"numeric"},monthYearA11yLabel:{year:"numeric",month:"long"}}};var no=(()=>{class i{static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275mod=_({type:i})}static{this.\u0275inj=h({providers:[mn()]})}}return i})();function mn(i=hn){return[{provide:me,useClass:un},{provide:dn,useValue:i}]}var so=(()=>{class i{isErrorState(e,t){return!!(e&&e.invalid&&(e.touched||t&&t.submitted))}static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})();var A=function(i){return i[i.FADING_IN=0]="FADING_IN",i[i.VISIBLE=1]="VISIBLE",i[i.FADING_OUT=2]="FADING_OUT",i[i.HIDDEN=3]="HIDDEN",i}(A||{}),je=class{constructor(n,e,t,s=!1){this._renderer=n,this.element=e,this.config=t,this._animationForciblyDisabledThroughCss=s,this.state=A.HIDDEN}fadeOut(){this._renderer.fadeOutRipple(this)}},jt=P({passive:!0,capture:!0}),Pe=class{constructor(){this._events=new Map,this._delegateEventHandler=n=>{let e=x(n);e&&this._events.get(n.type)?.forEach((t,s)=>{(s===e||s.contains(e))&&t.forEach(o=>o.handleEvent(n))})}}addHandler(n,e,t,s){let o=this._events.get(e);if(o){let r=o.get(t);r?r.add(s):o.set(t,new Set([s]))}else this._events.set(e,new Map([[t,new Set([s])]])),n.runOutsideAngular(()=>{document.addEventListener(e,this._delegateEventHandler,jt)})}removeHandler(n,e,t){let s=this._events.get(n);if(!s)return;let o=s.get(e);o&&(o.delete(t),o.size===0&&s.delete(e),s.size===0&&(this._events.delete(n),document.removeEventListener(n,this._delegateEventHandler,jt)))}},Pt={enterDuration:225,exitDuration:150},pn=800,Bt=P({passive:!0,capture:!0}),Ut=["mousedown","touchstart"],Vt=["mouseup","mouseleave","touchend","touchcancel"],Be=class i{static{this._eventManager=new Pe}constructor(n,e,t,s){this._target=n,this._ngZone=e,this._platform=s,this._isPointerDown=!1,this._activeRipples=new Map,this._pointerUpEventsRegistered=!1,s.isBrowser&&(this._containerElement=w(t))}fadeInRipple(n,e,t={}){let s=this._containerRect=this._containerRect||this._containerElement.getBoundingClientRect(),o=u(u({},Pt),t.animation);t.centered&&(n=s.left+s.width/2,e=s.top+s.height/2);let r=t.radius||fn(n,e,s),c=n-s.left,M=e-s.top,D=o.enterDuration,b=document.createElement("div");b.classList.add("mat-ripple-element"),b.style.left=`${c-r}px`,b.style.top=`${M-r}px`,b.style.height=`${r*2}px`,b.style.width=`${r*2}px`,t.color!=null&&(b.style.backgroundColor=t.color),b.style.transitionDuration=`${D}ms`,this._containerElement.appendChild(b);let Ve=window.getComputedStyle(b),Gt=Ve.transitionProperty,He=Ve.transitionDuration,pe=Gt==="none"||He==="0s"||He==="0s, 0s"||s.width===0&&s.height===0,C=new je(this,b,t,pe);b.style.transform="scale3d(1, 1, 1)",C.state=A.FADING_IN,t.persistent||(this._mostRecentTransientRipple=C);let Ke=null;return!pe&&(D||o.exitDuration)&&this._ngZone.runOutsideAngular(()=>{let We=()=>this._finishRippleTransition(C),$e=()=>this._destroyRipple(C);b.addEventListener("transitionend",We),b.addEventListener("transitioncancel",$e),Ke={onTransitionEnd:We,onTransitionCancel:$e}}),this._activeRipples.set(C,Ke),(pe||!D)&&this._finishRippleTransition(C),C}fadeOutRipple(n){if(n.state===A.FADING_OUT||n.state===A.HIDDEN)return;let e=n.element,t=u(u({},Pt),n.config.animation);e.style.transitionDuration=`${t.exitDuration}ms`,e.style.opacity="0",n.state=A.FADING_OUT,(n._animationForciblyDisabledThroughCss||!t.exitDuration)&&this._finishRippleTransition(n)}fadeOutAll(){this._getActiveRipples().forEach(n=>n.fadeOut())}fadeOutAllNonPersistent(){this._getActiveRipples().forEach(n=>{n.config.persistent||n.fadeOut()})}setupTriggerEvents(n){let e=w(n);!this._platform.isBrowser||!e||e===this._triggerElement||(this._removeTriggerEvents(),this._triggerElement=e,Ut.forEach(t=>{i._eventManager.addHandler(this._ngZone,t,e,this)}))}handleEvent(n){n.type==="mousedown"?this._onMousedown(n):n.type==="touchstart"?this._onTouchStart(n):this._onPointerUp(),this._pointerUpEventsRegistered||(this._ngZone.runOutsideAngular(()=>{Vt.forEach(e=>{this._triggerElement.addEventListener(e,this,Bt)})}),this._pointerUpEventsRegistered=!0)}_finishRippleTransition(n){n.state===A.FADING_IN?this._startFadeOutTransition(n):n.state===A.FADING_OUT&&this._destroyRipple(n)}_startFadeOutTransition(n){let e=n===this._mostRecentTransientRipple,{persistent:t}=n.config;n.state=A.VISIBLE,!t&&(!e||!this._isPointerDown)&&n.fadeOut()}_destroyRipple(n){let e=this._activeRipples.get(n)??null;this._activeRipples.delete(n),this._activeRipples.size||(this._containerRect=null),n===this._mostRecentTransientRipple&&(this._mostRecentTransientRipple=null),n.state=A.HIDDEN,e!==null&&(n.element.removeEventListener("transitionend",e.onTransitionEnd),n.element.removeEventListener("transitioncancel",e.onTransitionCancel)),n.element.remove()}_onMousedown(n){let e=Ce(n),t=this._lastTouchStartEvent&&Date.now()<this._lastTouchStartEvent+pn;!this._target.rippleDisabled&&!e&&!t&&(this._isPointerDown=!0,this.fadeInRipple(n.clientX,n.clientY,this._target.rippleConfig))}_onTouchStart(n){if(!this._target.rippleDisabled&&!Oe(n)){this._lastTouchStartEvent=Date.now(),this._isPointerDown=!0;let e=n.changedTouches;if(e)for(let t=0;t<e.length;t++)this.fadeInRipple(e[t].clientX,e[t].clientY,this._target.rippleConfig)}}_onPointerUp(){this._isPointerDown&&(this._isPointerDown=!1,this._getActiveRipples().forEach(n=>{let e=n.state===A.VISIBLE||n.config.terminateOnPointerUp&&n.state===A.FADING_IN;!n.config.persistent&&e&&n.fadeOut()}))}_getActiveRipples(){return Array.from(this._activeRipples.keys())}_removeTriggerEvents(){let n=this._triggerElement;n&&(Ut.forEach(e=>i._eventManager.removeHandler(e,n,this)),this._pointerUpEventsRegistered&&Vt.forEach(e=>n.removeEventListener(e,this,Bt)))}};function fn(i,n,e){let t=Math.max(Math.abs(i-e.left),Math.abs(i-e.right)),s=Math.max(Math.abs(n-e.top),Math.abs(n-e.bottom));return Math.sqrt(t*t+s*s)}var $t=new m("mat-ripple-global-options"),zt=(()=>{class i{get disabled(){return this._disabled}set disabled(e){e&&this.fadeOutAllNonPersistent(),this._disabled=e,this._setupTriggerEventsIfEnabled()}get trigger(){return this._trigger||this._elementRef.nativeElement}set trigger(e){this._trigger=e,this._setupTriggerEventsIfEnabled()}constructor(e,t,s,o,r){this._elementRef=e,this._animationMode=r,this.radius=0,this._disabled=!1,this._isInitialized=!1,this._globalOptions=o||{},this._rippleRenderer=new Be(this,t,e,s)}ngOnInit(){this._isInitialized=!0,this._setupTriggerEventsIfEnabled()}ngOnDestroy(){this._rippleRenderer._removeTriggerEvents()}fadeOutAll(){this._rippleRenderer.fadeOutAll()}fadeOutAllNonPersistent(){this._rippleRenderer.fadeOutAllNonPersistent()}get rippleConfig(){return{centered:this.centered,radius:this.radius,color:this.color,animation:u(u(u({},this._globalOptions.animation),this._animationMode==="NoopAnimations"?{enterDuration:0,exitDuration:0}:{}),this.animation),terminateOnPointerUp:this._globalOptions.terminateOnPointerUp}}get rippleDisabled(){return this.disabled||!!this._globalOptions.disabled}_setupTriggerEventsIfEnabled(){!this.disabled&&this._isInitialized&&this._rippleRenderer.setupTriggerEvents(this.trigger)}launch(e,t=0,s){return typeof e=="number"?this._rippleRenderer.fadeInRipple(e,t,u(u({},this.rippleConfig),s)):this._rippleRenderer.fadeInRipple(0,0,u(u({},this.rippleConfig),e))}static{this.\u0275fac=function(t){return new(t||i)(l(I),l(y),l(g),l($t,8),l(X,8))}}static{this.\u0275dir=T({type:i,selectors:[["","mat-ripple",""],["","matRipple",""]],hostAttrs:[1,"mat-ripple"],hostVars:2,hostBindings:function(t,s){t&2&&K("mat-ripple-unbounded",s.unbounded)},inputs:{color:[f.None,"matRippleColor","color"],unbounded:[f.None,"matRippleUnbounded","unbounded"],centered:[f.None,"matRippleCentered","centered"],radius:[f.None,"matRippleRadius","radius"],animation:[f.None,"matRippleAnimation","animation"],disabled:[f.None,"matRippleDisabled","disabled"],trigger:[f.None,"matRippleTrigger","trigger"]},exportAs:["matRipple"],standalone:!0})}}return i})(),_n=(()=>{class i{static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275mod=_({type:i})}static{this.\u0275inj=h({imports:[he,he]})}}return i})(),gn=(()=>{class i{constructor(e){this._animationMode=e,this.state="unchecked",this.disabled=!1,this.appearance="full"}static{this.\u0275fac=function(t){return new(t||i)(l(X,8))}}static{this.\u0275cmp=Q({type:i,selectors:[["mat-pseudo-checkbox"]],hostAttrs:[1,"mat-pseudo-checkbox"],hostVars:12,hostBindings:function(t,s){t&2&&K("mat-pseudo-checkbox-indeterminate",s.state==="indeterminate")("mat-pseudo-checkbox-checked",s.state==="checked")("mat-pseudo-checkbox-disabled",s.disabled)("mat-pseudo-checkbox-minimal",s.appearance==="minimal")("mat-pseudo-checkbox-full",s.appearance==="full")("_mat-animation-noopable",s._animationMode==="NoopAnimations")},inputs:{state:"state",disabled:"disabled",appearance:"appearance"},standalone:!0,features:[ne],decls:0,vars:0,template:function(t,s){},styles:['.mat-pseudo-checkbox{border-radius:2px;cursor:pointer;display:inline-block;vertical-align:middle;box-sizing:border-box;position:relative;flex-shrink:0;transition:border-color 90ms cubic-bezier(0, 0, 0.2, 0.1),background-color 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox::after{position:absolute;opacity:0;content:"";border-bottom:2px solid currentColor;transition:opacity 90ms cubic-bezier(0, 0, 0.2, 0.1)}.mat-pseudo-checkbox._mat-animation-noopable{transition:none !important;animation:none !important}.mat-pseudo-checkbox._mat-animation-noopable::after{transition:none}.mat-pseudo-checkbox-disabled{cursor:default}.mat-pseudo-checkbox-indeterminate::after{left:1px;opacity:1;border-radius:2px}.mat-pseudo-checkbox-checked::after{left:1px;border-left:2px solid currentColor;transform:rotate(-45deg);opacity:1;box-sizing:content-box}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-minimal-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-minimal-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox-full{border-color:var(--mat-full-pseudo-checkbox-unselected-icon-color);border-width:2px;border-style:solid}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-disabled{border-color:var(--mat-full-pseudo-checkbox-disabled-unselected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate{background-color:var(--mat-full-pseudo-checkbox-selected-icon-color);border-color:rgba(0,0,0,0)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{color:var(--mat-full-pseudo-checkbox-selected-checkmark-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled{background-color:var(--mat-full-pseudo-checkbox-disabled-selected-icon-color)}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked.mat-pseudo-checkbox-disabled::after,.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate.mat-pseudo-checkbox-disabled::after{color:var(--mat-full-pseudo-checkbox-disabled-selected-checkmark-color)}.mat-pseudo-checkbox{width:18px;height:18px}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-checked::after{width:14px;height:6px;transform-origin:center;top:-4.2426406871px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-minimal.mat-pseudo-checkbox-indeterminate::after{top:8px;width:16px}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-checked::after{width:10px;height:4px;transform-origin:center;top:-2.8284271247px;left:0;bottom:0;right:0;margin:auto}.mat-pseudo-checkbox-full.mat-pseudo-checkbox-indeterminate::after{top:6px;width:12px}'],encapsulation:2,changeDetection:0})}}return i})(),bn=(()=>{class i{static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275mod=_({type:i})}static{this.\u0275inj=h({imports:[he]})}}return i})(),vn=new m("MAT_OPTION_PARENT_COMPONENT");var yn=new m("MatOptgroup");var En=0,Ue=class{constructor(n,e=!1){this.source=n,this.isUserInput=e}},oo=(()=>{class i{get multiple(){return this._parent&&this._parent.multiple}get selected(){return this._selected}get disabled(){return this.group&&this.group.disabled||this._disabled}set disabled(e){this._disabled=e}get disableRipple(){return!!(this._parent&&this._parent.disableRipple)}get hideSingleSelectionIndicator(){return!!(this._parent&&this._parent.hideSingleSelectionIndicator)}constructor(e,t,s,o){this._element=e,this._changeDetectorRef=t,this._parent=s,this.group=o,this._selected=!1,this._active=!1,this._disabled=!1,this._mostRecentViewValue="",this.id=`mat-option-${En++}`,this.onSelectionChange=new k,this._stateChanges=new v}get active(){return this._active}get viewValue(){return(this._text?.nativeElement.textContent||"").trim()}select(e=!0){this._selected||(this._selected=!0,this._changeDetectorRef.markForCheck(),e&&this._emitSelectionChangeEvent())}deselect(e=!0){this._selected&&(this._selected=!1,this._changeDetectorRef.markForCheck(),e&&this._emitSelectionChangeEvent())}focus(e,t){let s=this._getHostElement();typeof s.focus=="function"&&s.focus(t)}setActiveStyles(){this._active||(this._active=!0,this._changeDetectorRef.markForCheck())}setInactiveStyles(){this._active&&(this._active=!1,this._changeDetectorRef.markForCheck())}getLabel(){return this.viewValue}_handleKeydown(e){(e.keyCode===13||e.keyCode===32)&&!oe(e)&&(this._selectViaInteraction(),e.preventDefault())}_selectViaInteraction(){this.disabled||(this._selected=this.multiple?!this._selected:!0,this._changeDetectorRef.markForCheck(),this._emitSelectionChangeEvent(!0))}_getTabIndex(){return this.disabled?"-1":"0"}_getHostElement(){return this._element.nativeElement}ngAfterViewChecked(){if(this._selected){let e=this.viewValue;e!==this._mostRecentViewValue&&(this._mostRecentViewValue&&this._stateChanges.next(),this._mostRecentViewValue=e)}}ngOnDestroy(){this._stateChanges.complete()}_emitSelectionChangeEvent(e=!1){this.onSelectionChange.emit(new Ue(this,e))}static{this.\u0275fac=function(t){return new(t||i)(l(I),l(pt),l(vn,8),l(yn,8))}}static{this.\u0275cmp=Q({type:i,selectors:[["mat-option"]],viewQuery:function(t,s){if(t&1&&ct(Xi,7),t&2){let o;dt(o=lt())&&(s._text=o.first)}},hostAttrs:["role","option",1,"mat-mdc-option","mdc-list-item"],hostVars:11,hostBindings:function(t,s){t&1&&at("click",function(){return s._selectViaInteraction()})("keydown",function(r){return s._handleKeydown(r)}),t&2&&(rt("id",s.id),ge("aria-selected",s.selected)("aria-disabled",s.disabled.toString()),K("mdc-list-item--selected",s.selected)("mat-mdc-option-multiple",s.multiple)("mat-mdc-option-active",s.active)("mdc-list-item--disabled",s.disabled))},inputs:{value:"value",id:"id",disabled:[f.HasDecoratorInputTransform,"disabled","disabled",O]},outputs:{onSelectionChange:"onSelectionChange"},exportAs:["matOption"],standalone:!0,features:[S,ne],ngContentSelectors:Ji,decls:8,vars:5,consts:[["text",""],["aria-hidden","true",1,"mat-mdc-option-pseudo-checkbox",3,"disabled","state"],[1,"mdc-list-item__primary-text"],["state","checked","aria-hidden","true","appearance","minimal",1,"mat-mdc-option-pseudo-checkbox",3,"disabled"],[1,"cdk-visually-hidden"],["aria-hidden","true","mat-ripple","",1,"mat-mdc-option-ripple","mat-mdc-focus-indicator",3,"matRippleTrigger","matRippleDisabled"]],template:function(t,s){t&1&&(ye(qi),_e(0,en,1,2,"mat-pseudo-checkbox",1),ie(1),be(2,"span",2,0),ie(4,1),ve(),_e(5,tn,1,1,"mat-pseudo-checkbox",3)(6,nn,2,1,"span",4),ee(7,"div",5)),t&2&&(J(0,s.multiple?0:-1),H(5),J(5,!s.multiple&&s.selected&&!s.hideSingleSelectionIndicator?5:-1),H(),J(6,s.group&&s.group._inert?6:-1),H(),q("matRippleTrigger",s._getHostElement())("matRippleDisabled",s.disabled||s.disableRipple))},dependencies:[gn,zt],styles:['.mat-mdc-option{display:flex;position:relative;align-items:center;justify-content:flex-start;overflow:hidden;padding:0;padding-left:16px;padding-right:16px;-webkit-user-select:none;user-select:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;cursor:pointer;-webkit-tap-highlight-color:rgba(0,0,0,0);color:var(--mat-option-label-text-color);font-family:var(--mat-option-label-text-font);line-height:var(--mat-option-label-text-line-height);font-size:var(--mat-option-label-text-size);letter-spacing:var(--mat-option-label-text-tracking);font-weight:var(--mat-option-label-text-weight);min-height:48px}.mat-mdc-option:focus{outline:none}[dir=rtl] .mat-mdc-option,.mat-mdc-option[dir=rtl]{padding-left:16px;padding-right:16px}.mat-mdc-option:hover:not(.mdc-list-item--disabled){background-color:var(--mat-option-hover-state-layer-color)}.mat-mdc-option:focus.mdc-list-item,.mat-mdc-option.mat-mdc-option-active.mdc-list-item{background-color:var(--mat-option-focus-state-layer-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled) .mdc-list-item__primary-text{color:var(--mat-option-selected-state-label-text-color)}.mat-mdc-option.mdc-list-item--selected:not(.mdc-list-item--disabled):not(.mat-mdc-option-multiple){background-color:var(--mat-option-selected-state-layer-color)}.mat-mdc-option.mdc-list-item{align-items:center;background:rgba(0,0,0,0)}.mat-mdc-option.mdc-list-item--disabled{cursor:default;pointer-events:none}.mat-mdc-option.mdc-list-item--disabled .mat-mdc-option-pseudo-checkbox,.mat-mdc-option.mdc-list-item--disabled .mdc-list-item__primary-text,.mat-mdc-option.mdc-list-item--disabled>mat-icon{opacity:.38}.mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:32px}[dir=rtl] .mat-mdc-optgroup .mat-mdc-option:not(.mat-mdc-option-multiple){padding-left:16px;padding-right:32px}.mat-mdc-option .mat-icon,.mat-mdc-option .mat-pseudo-checkbox-full{margin-right:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-icon,[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-full{margin-right:0;margin-left:16px}.mat-mdc-option .mat-pseudo-checkbox-minimal{margin-left:16px;flex-shrink:0}[dir=rtl] .mat-mdc-option .mat-pseudo-checkbox-minimal{margin-right:16px;margin-left:0}.mat-mdc-option .mat-mdc-option-ripple{top:0;left:0;right:0;bottom:0;position:absolute;pointer-events:none}.mat-mdc-option .mdc-list-item__primary-text{white-space:normal;font-size:inherit;font-weight:inherit;letter-spacing:inherit;line-height:inherit;font-family:inherit;text-decoration:inherit;text-transform:inherit;margin-right:auto}[dir=rtl] .mat-mdc-option .mdc-list-item__primary-text{margin-right:0;margin-left:auto}.cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{content:"";position:absolute;top:50%;right:16px;transform:translateY(-50%);width:10px;height:0;border-bottom:solid 10px;border-radius:10px}[dir=rtl] .cdk-high-contrast-active .mat-mdc-option.mdc-list-item--selected:not(.mat-mdc-option-multiple)::after{right:auto;left:16px}.mat-mdc-option-multiple{--mdc-list-list-item-selected-container-color:var(--mdc-list-list-item-container-color, transparent)}.mat-mdc-option-active .mat-mdc-focus-indicator::before{content:""}'],encapsulation:2,changeDetection:0})}}return i})();function ro(i,n,e){if(e.length){let t=n.toArray(),s=e.toArray(),o=0;for(let r=0;r<i+1;r++)t[r].group&&t[r].group===s[o]&&o++;return o}return 0}function ao(i,n,e,t){return i<e?i:i+n>e+t?Math.max(0,i-t+n):e}var co=(()=>{class i{static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275mod=_({type:i})}static{this.\u0275inj=h({imports:[_n,he,bn]})}}return i})(),Ht={capture:!0},Kt=["focus","click","mouseenter","touchstart"],Le="mat-ripple-loader-uninitialized",Se="mat-ripple-loader-class-name",Wt="mat-ripple-loader-centered",ue="mat-ripple-loader-disabled",lo=(()=>{class i{constructor(){this._document=p(E,{optional:!0}),this._animationMode=p(X,{optional:!0}),this._globalRippleOptions=p($t,{optional:!0}),this._platform=p(g),this._ngZone=p(y),this._hosts=new Map,this._onInteraction=e=>{if(!(e.target instanceof HTMLElement))return;let s=e.target.closest(`[${Le}]`);s&&this._createRipple(s)},this._ngZone.runOutsideAngular(()=>{for(let e of Kt)this._document?.addEventListener(e,this._onInteraction,Ht)})}ngOnDestroy(){let e=this._hosts.keys();for(let t of e)this.destroyRipple(t);for(let t of Kt)this._document?.removeEventListener(t,this._onInteraction,Ht)}configureRipple(e,t){e.setAttribute(Le,""),(t.className||!e.hasAttribute(Se))&&e.setAttribute(Se,t.className||""),t.centered&&e.setAttribute(Wt,""),t.disabled&&e.setAttribute(ue,"")}getRipple(e){return this._hosts.get(e)||this._createRipple(e)}setDisabled(e,t){let s=this._hosts.get(e);if(s){s.disabled=t;return}t?e.setAttribute(ue,""):e.removeAttribute(ue)}_createRipple(e){if(!this._document)return;let t=this._hosts.get(e);if(t)return t;e.querySelector(".mat-ripple")?.remove();let s=this._document.createElement("span");s.classList.add("mat-ripple",e.getAttribute(Se)),e.append(s);let o=new zt(new I(s),this._ngZone,this._platform,this._globalRippleOptions?this._globalRippleOptions:void 0,this._animationMode?this._animationMode:void 0);return o._isInitialized=!0,o.trigger=e,o.centered=e.hasAttribute(Wt),o.disabled=e.hasAttribute(ue),this.attachRipple(e,o),o}attachRipple(e,t){e.removeAttribute(Le),this._hosts.set(e,t)}destroyRipple(e){let t=this._hosts.get(e);t&&(t.ngOnDestroy(),this._hosts.delete(e))}static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275prov=d({token:i,factory:i.\u0275fac,providedIn:"root"})}}return i})(),uo=(()=>{class i{static{this.\u0275fac=function(t){return new(t||i)}}static{this.\u0275cmp=Q({type:i,selectors:[["div","mat-internal-form-field",""]],hostAttrs:[1,"mdc-form-field","mat-internal-form-field"],hostVars:2,hostBindings:function(t,s){t&2&&K("mdc-form-field--align-end",s.labelPosition==="before")},inputs:{labelPosition:"labelPosition"},standalone:!0,features:[ne],attrs:sn,ngContentSelectors:on,decls:1,vars:0,template:function(t,s){t&1&&(ye(),ie(0))},styles:[".mdc-form-field{display:inline-flex;align-items:center;vertical-align:middle}.mdc-form-field[hidden]{display:none}.mdc-form-field>label{margin-left:0;margin-right:auto;padding-left:4px;padding-right:0;order:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{margin-left:auto;margin-right:0}[dir=rtl] .mdc-form-field>label,.mdc-form-field>label[dir=rtl]{padding-left:0;padding-right:4px}.mdc-form-field--nowrap>label{text-overflow:ellipsis;overflow:hidden;white-space:nowrap}.mdc-form-field--align-end>label{margin-left:auto;margin-right:0;padding-left:0;padding-right:4px;order:-1}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{margin-left:0;margin-right:auto}[dir=rtl] .mdc-form-field--align-end>label,.mdc-form-field--align-end>label[dir=rtl]{padding-left:4px;padding-right:0}.mdc-form-field--space-between{justify-content:space-between}.mdc-form-field--space-between>label{margin:0}[dir=rtl] .mdc-form-field--space-between>label,.mdc-form-field--space-between>label[dir=rtl]{margin:0}.mdc-form-field{font-family:var(--mdc-form-field-label-text-font);line-height:var(--mdc-form-field-label-text-line-height);font-size:var(--mdc-form-field-label-text-size);font-weight:var(--mdc-form-field-label-text-weight);letter-spacing:var(--mdc-form-field-label-text-tracking);color:var(--mdc-form-field-label-text-color)}.mat-internal-form-field{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased}"],encapsulation:2,changeDetection:0})}}return i})();export{g as a,Tn as b,P as c,$ as d,kn as e,xn as f,bt as g,x as h,vt as i,oe as j,Qt as k,De as l,Xt as m,Me as n,Nn as o,w as p,Fn as q,Wn as r,Et as s,Dt as t,es as u,yi as v,Ei as w,bs as x,wt as y,Tt as z,Ii as A,Fi as B,vs as C,ys as D,Hi as E,Es as F,Is as G,Fs as H,Fe as I,to as J,io as K,he as L,Lt as M,St as N,me as O,dn as P,no as Q,mn as R,so as S,zt as T,_n as U,vn as V,yn as W,Ue as X,oo as Y,ro as Z,ao as _,co as $,lo as aa,uo as ba};
