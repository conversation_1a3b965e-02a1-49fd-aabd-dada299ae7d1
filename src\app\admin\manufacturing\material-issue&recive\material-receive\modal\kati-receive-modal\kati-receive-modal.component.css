/* Layout polish for Kati Receive modal */
.kati-modal-container { width: 100%; max-width: 1200px; margin: 0 auto; }

.modal-header { display: grid; grid-template-columns: 1fr auto 1fr; align-items: center; gap: 8px; padding: 12px 16px; border-bottom: 1px solid #e0e0e0; }
.modal-header .modal-title { justify-self: center; margin: 0; }
.close-button { justify-self: end; color: #555; }

.modal-body { padding: 12px 16px 0 16px; }
.table-container { overflow: auto; max-height: 60vh; }

/* Table cell sizing and spacing */
.mat-mdc-row { height: 44px !important; }
.mat-mdc-cell, .mat-mdc-header-cell, .mat-mdc-footer-cell { padding: 8px 12px !important; }

/* Align numeric cells to the right for readability */
.num-cell { text-align: right !important; }

/* Receive input (plain input) */
.receive-input { width: 120px; height: 34px; padding: 6px 8px; border: 1px solid #d0d7de; border-radius: 6px; font-size: 14px; box-sizing: border-box; }
.receive-input:focus { border-color: #1976d2; box-shadow: 0 0 0 2px rgba(25,118,210,0.15); outline: none; }
.receive-input::placeholder { color: #9aa0a6; }

/* Footer/header sticky for totals visibility */
tr.mat-mdc-header-row, tr.mat-mdc-footer-row { position: sticky; background: #fff; z-index: 1; }
tr.mat-mdc-header-row { top: 0; border-bottom: 1px solid #e0e0e0; }
tr.mat-mdc-footer-row { bottom: 0; border-top: 1px solid #e0e0e0; }

/* Modal footer buttons */
.modal-footer { display: flex; justify-content: flex-end; gap: 8px; padding: 12px 16px; border-top: 1px solid #e0e0e0; }
.save-button { background-color: #1976d2; color: #fff; }
