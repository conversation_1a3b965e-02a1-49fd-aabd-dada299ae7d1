<div class="container-fluid">
   <div class="container mt-4 no-print mb-2">
  <div class="d-flex justify-content-end">
    <div class="col-md-3 text-right">
      <button mat-raised-button color="primary" (click)="printPage()">Print</button>
    </div>
   </div>
 </div>

<div id="invoice"   tabindex="0" >

      <section class="container-fluid">
        <div class="company-header">
          <div class="title">M/S. RACHIN EXPORTS</div>
          <div class="sub mt-2">SUREKA ESTATE, MAHANTH SHIWALA, MIRZAPUR, U.P. INDIA</div>
          <div class="sub">Mobile: 6390335666</div>
        </div>

        <div class="container">
          <div class="d-flex justify-content-between">
            <div class="main-div">
              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 90px;">Challan No.</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                  <span class="px-3 fw-medium">{{ selectedChallanNo || materialIssues[0]?.challanNo }}</span
                  >
                </p>
              </div>
              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 90px;">Name</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p> 
                   <span class="px-3 fw-medium">{{ orderIssueData.weaver.name | uppercase}}</span>
      
                </p>
              </div>
           <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 90px;">Address</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p> 
                   <span class="px-3 fw-medium">{{orderIssueData.weaver.address}}</span>
                </p>
              </div>

              <!-- <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 90px;">Aadhaar No.</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p> 
                   <span class="px-3 fw-medium">{{ orderIssueData?.weaver?.aadhaarDetails?.aadhaarNo }}</span>
                </p>
              </div> -->
            </div>
            <div class="main-div">
              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 100px;">Challan Date</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                  <span class="px-3 fw-medium">{{ materialIssues[0]?.date | date:'dd-MM-yyyy'}}</span>
                </p>
              </div>
              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 100px;">Phone No.</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                  <span class="px-3 fw-medium">{{ orderIssueData.weaver.contactNo }}</span>
                </p>
              </div>
              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 100px;">Map No.</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                  <span class="px-3 fw-medium">{{ orderIssueData.MapOrderNo }}</span>
                </p>
              </div>
            </div>
          </div>
        </div>


      </section>

  <section class="container-fluid mt-4">
    <table>
      <tr class="tablecontent2">
        <th>Issue No.</th>
         <th>Issue Date</th>
        <th>Quality</th>
        <th>Design</th>
        <th>Colour</th>
        <th>Size</th>
        <th>Khap Size</th>
        <th>Pcs</th>
        <th>Area</th>
        
        <!-- <th>Rate</th>
        <th>Amount</th> -->
      </tr>
      <tr class="tablecontent2" *ngIf="orderIssueData">
        <td>{{ orderIssueData?.Br_issueNo }}</td>
        <td>{{ orderIssueData?.date | date:'dd-MM-yyyy'}}</td>
        <td>{{ orderIssueData?.buyerOrder?.quality?.quality || orderIssueData?.quality?.quality || orderIssueData?.quality }}</td>
        <td>{{ orderIssueData?.buyerOrder?.design?.design || orderIssueData?.design?.design || orderIssueData?.design }}</td>
        <td>{{ orderIssueData?.buyerOrder?.borderColour || orderIssueData?.buyerOrder?.groundColour || orderIssueData?.borderColour || orderIssueData?.groundColour }}</td>
        <td>{{ orderIssueData?.buyerOrder?.size?.sizeInYard || orderIssueData?.size?.sizeInYard || orderIssueData?.size || orderIssueData?.sizeInYard }}</td>
        <td>{{ orderIssueData?.buyerOrder?.size?.khapSize || orderIssueData?.buyerOrder?.khapSize || orderIssueData?.khapSize }}</td>
         <td>{{ orderIssueData?.pcs }}</td>
        <td>{{ areaFromIssue || orderIssueData?.area }}</td>
       
        <!-- <td>{{ orderIssueData?.rate | number:'1.2-3' }}</td>
        <td>{{ orderIssueData?.amount | number:'1.0-0' }}.00</td> -->
      </tr>
    </table>
  </section>

  <section class="container-fluid my-5">
    <table style="border: 2" class="tablecontent5">
      <tr class="tablecontent3">
        <!-- <th>Date : {{ materialIssues[0]?.date | date:'dd-MM-yyyy' }}</th> -->
         <th></th>
        <th>Kati</th>
        <th>Tana</th>
        <th>Soot</th>
        <th>Tharri</th>
        <th>Silk</th>
        <th>Other</th>
      </tr>
      <tr class="tablecontent3">
        <th>Lagat</th>
        <td>{{ materialsTotals.kati.lagat | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.tana.lagat | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.soot.lagat | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.thari.lagat | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.silk.lagat | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.other.lagat | number:'1.3-3' }}</td>
      </tr>
      <tr class="tablecontent3">
        <th>Issue</th>
        <td>{{ materialsTotals.kati.issue | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.tana.issue | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.soot.issue | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.thari.issue | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.silk.issue | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.other.issue | number:'1.3-3' }}</td>
      </tr>
      <!-- <tr class="tablecontent3">
        <th>Receive</th>
        <td>{{ materialsTotals.kati.receive | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.tana.receive | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.soot.receive | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.thari.receive | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.silk.receive | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.other.receive | number:'1.3-3' }}</td>
      </tr> -->
      <!-- <tr class="tablecontent3">
        <th>Total Lagat</th>
        <td>{{ materialsTotals.kati.tLagat | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.tana.tLagat | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.soot.tLagat | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.thari.tLagat | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.silk.tLagat | number:'1.3-3' }}</td>
        <td>{{ materialsTotals.other.tLagat | number:'1.3-3' }}</td>
      </tr> -->
    </table>
  </section>

  <!-- Kati colour-wise details -->
  <section class="container-fluid" *ngIf="materialIssues.length && materialIssues[0]?.katiData?.length">
    <h5 class="text-center mb-2 kati-title">Kati Material Details</h5>
    <table class="tablecontent2 kati-table">
      <tr>
        <th>Sr.No</th>
        <th>Colour</th>
        <th>Lagat</th>
        <th>Carpet Lagat</th>
        <th>Issued</th>
        <th>To Issue</th>
      </tr>
      <tr *ngFor="let row of materialIssues[0].katiData; let i = index">
        <td>{{ i + 1 }}</td>
        <td>{{ row?.colour?.newColor ? (row?.colour?.newColor + ' - ' + (row?.colour?.companyColorCode || '') + ' - ' + (row?.colour?.remark || '')) : (row?.colour?.name || row?.colour) }}</td>
        <td>{{ row?.lagat }}</td>
        <td>{{ row?.carpetLagat }}</td>
        <td>{{ row?.issueValue || '-' }}</td>
        <td>{{ row?.toIssueValue }}</td>
      </tr>
    </table>
  </section>

  <section class="container-fluid">
      
  <div class="signatures">
            <div class="signature-block">
              <div class="signature-space"></div>
              <div class="signature-line"></div>
              <p class="signature-label">Auth. Signature</p>
            </div>
            <div class="signature-block">
              <div class="signature-space"></div>
              <div class="signature-line"></div>
              <p class="signature-label">Signature</p>
            </div>
         </div>
      </section>
</div>
