import { Component, Inject, OnInit } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ManufactureService } from '../../../../../../services/manufacture.service';
import { MasterService } from '../../../../../../services/master.service';

@Component({
  selector: 'app-challan-modal',
  templateUrl: './challan-modal.component.html',
  styleUrl: './challan-modal.component.css'
})
export class ChallanModalComponent implements OnInit {
  challanDetails: any[] = [];
  displayedColumns: string[] = ['issueNo', 'challanNo', 'cDate', 'katiIssued', 'tanaIssued', 'sootIssued', 'tharriIssued', 'silkIssued', 'otherIssued'];

  // keep original challan docs for lookups
  private sourceChallans: any[] = [];

  constructor(
    public dialogRef: MatDialogRef<ChallanModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private dialog: MatDialog,
    private manufactureService: ManufactureService,
    private masterService: MasterService
  ) {}
  // Draggable Kati details panel state
  showKatiPanel = false;
  activeChallan: any = null;
  activeKatiRows: any[] = [];
  activeTotals = { issue: 0, toIssue: 0 };
  private coloursCache = new Map<string, any>();

  private loadColoursCache(): void {
    this.masterService.getsColourCodeDetails().subscribe(
      (list: any) => {
        if (Array.isArray(list)) {
          list.forEach((c: any) => {
            const id = c?._id;
            if (id) this.coloursCache.set(id, c);
          });
        }
      },
      () => {}
    );
  }


  ngOnInit(): void {
    this.loadColoursCache();
    this.processChallanDetails();
  }

  processChallanDetails(): void {
    this.sourceChallans = Array.isArray(this.data?.challanDetails) ? this.data.challanDetails : [];
    if (this.sourceChallans.length) {
      this.challanDetails = this.sourceChallans.map((challan: any) => ({
        issueNo: this.data.issueNo || 'N/A',
        challanNo: challan.challanNo || 'N/A',
        cDate: challan.date || new Date(),
        katiIssued: challan.materials?.kati?.issue || 0,
        tanaIssued: challan.materials?.tana?.issue || 0,
        sootIssued: challan.materials?.soot?.issue || 0,
        tharriIssued: challan.materials?.thari?.issue || 0,
        silkIssued: challan.materials?.silk?.issue || 0,
        otherIssued: challan.materials?.other?.issue || 0
      }));
    }
    console.log('✅ Processed challan details:', this.challanDetails);
  }

  toggleKatiDetail(detail: any): void {
    // Same challan clicked again => toggle close
    if (this.showKatiPanel && this.activeChallan?.challanNo === detail?.challanNo) {
      this.closeKatiPanel();
      return;
    }
    // Otherwise open for the clicked challan
    this.openKatiDetail(detail);
  }

  // Resolve colour display text from object or id
  getColourText(colour: any): string {
    if (!colour) return '';
    // If populated object with fields
    if (typeof colour === 'object') {
      const id = (colour as any)._id || (colour as any).id;
      const newColor = (colour as any).newColor;
      const companyColorCode = (colour as any).companyColorCode;
      const remark = (colour as any).remark || (colour as any).name || '';
      if (newColor || companyColorCode || remark) {
        return `${newColor || ''} - ${companyColorCode || ''} - ${remark || ''}`.replace(/\s-\s-\s$/,'').trim();
      }
      // Try cache by id
      if (id && this.coloursCache.has(id)) {
        const c = this.coloursCache.get(id);
        return `${c?.newColor || ''} - ${c?.companyColorCode || ''} - ${c?.remark || ''}`.trim();
      }
    }
    // If it's an id string
    if (typeof colour === 'string' && this.coloursCache.has(colour)) {
      const c = this.coloursCache.get(colour);
      return `${c?.newColor || ''} - ${c?.companyColorCode || ''} - ${c?.remark || ''}`.trim();
    }
    // Fallback
    return typeof colour === 'string' ? colour : '';
  }

  private setActiveKatiFromDoc(doc: any): void {
    this.activeChallan = doc || null;
    this.activeKatiRows = (doc?.katiData || []).map((r: any) => ({
      colour: r?.colour,
      colourText: this.getColourText(r?.colour),
      lagat: r?.lagat,
      carpetLagat: r?.carpetLagat,
      issueValue: r?.issueValue || '0.000',
      toIssueValue: r?.toIssueValue || '0.000'
    }));
    this.activeTotals = {
      issue: this.activeKatiRows.reduce((s, r) => s + (parseFloat(r.issueValue) || 0), 0),
      toIssue: this.activeKatiRows.reduce((s, r) => s + (parseFloat(r.toIssueValue) || 0), 0)
    };
  }

  // Open draggable Kati details panel
  openKatiDetail(detail: any): void {
    this.showKatiPanel = true;
    // Prefer already-provided doc (contains katiData), else fetch by challanNo
    const localDoc = (this.sourceChallans || []).find((c: any) => c?.challanNo === detail?.challanNo);
    if (localDoc && Array.isArray(localDoc.katiData)) {
      this.setActiveKatiFromDoc(localDoc);
      return;
    }

    // Fallback: fetch from API using challan number
    const challanNo = detail?.challanNo;
    if (!challanNo) return;
    this.manufactureService.getMaterialIssueByChallanNo(challanNo).subscribe({
      next: (doc: any) => {
        this.setActiveKatiFromDoc(doc);
      },
      error: (err) => {
        console.error('Failed to load katiData by challanNo', challanNo, err);
        this.closeKatiPanel();
      }
    });
  }

  closeKatiPanel(): void {
    this.showKatiPanel = false;
    this.activeChallan = null;
    this.activeKatiRows = [];
    this.activeTotals = { issue: 0, toIssue: 0 };
  }
}
