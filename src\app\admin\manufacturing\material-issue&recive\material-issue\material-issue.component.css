fieldset {
  border: 2px solid #1f497d;
  border-radius: 8px;
  padding: 5px;
  margin-bottom: 20px;
}

fieldset legend {
  background: #ffffff;
  color: #000000;
  padding: 5px 10px ;
  font-size: 20px;
  border-radius: 5px;
  /* box-shadow: 0 0 0 5px #ddd; */
  margin-left: 20px;
}

legend {
  float: left;
  width: auto;
  padding: 0;
  margin-top: -32px;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + .3vw);
  line-height: inherit;
}
 .ex-width{
    width:100%;
  }
  .space{
    display: flex;
    justify-content:space-between;

  }


  fieldset legend {
  background: #ffffff;
  color: #000000;
  padding: 5px 10px ;
  font-size: 20px;
  border-radius: 5px;
  /* box-shadow: 0 0 0 5px #ddd; */
  margin-left: 20px;
}

legend {
  float: left;
  width: auto;
  padding: 0;
  margin-top: -32px;
  margin-bottom: 0.5rem;
  font-size: calc(1.275rem + .3vw);
  line-height: inherit;
}
.ex-width{
  width:100%;
}
.space{
  display: flex;
  justify-content:space-between;
}

.navigate-text a{
color: #1f497d;
text-decoration: none;
}

/* Navigation Button Styling */
.navigate-button {
  background-color: #1f497d;
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: end;
  transition: all 0.3s ease;
  margin-left: auto;
  margin-right: 20px;

}

.navigate-button:hover {
  background-color: #2196F3;
  transform: translateX(2px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}
.coldes{
  margin-top: 10px;
  width: 155px;
  height: 240px;
}
.Des-box-hieght{
  align-content: center;
 height: 80px;
}
.Lag-box-hieght{
  height: 80px;
  align-content: center;
}
.Issue-box-hieght{
height: 80px;
  align-content: center;
}

.col21{
  width: 182px !important;
}
.kati-height{
  height: 280px;
}

.navigate-button i {
  font-size: 16px;
}

/* Issue Details Table Styling */
.issue-details-header {
  display: flex;
  justify-content: center;
  align-items: center;
}

.issue-details-header h3 {
  color: #1f497d;
  font-weight: 600;
}

.issue-details-table-section {
  margin-bottom: 30px;
}

.issue-details-table {
  width: 100%;
  border-collapse: collapse;
  border: 1px solid #ddd;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.issue-details-table th {
  background-color: #eaeaea;
  color: #1f497d;
  padding: 12px 8px;
  text-align: left;
  font-weight: 600;
  border-bottom: 2px solid #0d47a1;
}

.issue-details-table td {
  padding: 10px 8px;
  border-bottom: 1px solid #eee;
  color: #333;
}

.issue-details-table tr:hover {
  background-color: #f5f5f5;
}

.no-data {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 20px;
}

.kati-details-btn {
  width: 100%;
  font-size: 12px;
  padding: 8px 16px;
}

.kati-details-btn mat-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* Material Description Section */



.card-header.bg-primary {
  padding-top: 5px;
  padding-bottom: 5px;
  border-radius: 7px 7px 0px 0px;
  background-color: #1f497d !important;
  border-bottom: 2px solid #1f497d;
}
.pb-1 {
  padding-bottom: 4px;
}

.mb-2 {
  margin-bottom: 8px;
}

/* Bootstrap Grid Enhancements */
.container-fluid {
  padding: 0;
}

.row {
  margin: 0;
  align-items: center;
}

.col, .col-2 {
  padding: 0 8px;
}

/* Form field styling for Bootstrap layout */
.w-100 {
  width: 100% !important;
}

.w-100 .mat-form-field-wrapper {
  padding-bottom: 0;
  margin-bottom: 0;
}

.w-100 .mat-form-field-infix {
  padding: 8px 12px;
  border-top: none;
}

.w-100 .mat-form-field-underline {
  display: none;
}

.w-100 input {
  font-size: 14px;
  padding: 8px;
}

.w-100 .mat-form-field-outline {
  color: #1f497d;
}

.w-100.mat-focused .mat-form-field-outline-thick {
  color: #1f497d;
}

.w-100 .mat-form-field-outline-start,
.w-100 .mat-form-field-outline-end {
  border-radius: 4px;
}

/* Readonly input styling */
input[readonly] {
  background-color: #ffffff;
  color: #666;
}

/* Ensure consistent height for all form fields */
.w-100 .mat-form-field-flex {
  align-items: center;
  min-height: 40px;
}

/* Remove default mat-form-field margins */
.w-100 .mat-form-field-subscript-wrapper {
  display: none;
}

/* Header styling */
.border-bottom {
  border-bottom: 2px solid #1f497d !important;
}

.text-primary {
  color: #1f497d !important;
}

.fw-bold {
  font-weight: 600 !important;
}

/* Responsive adjustments */
@media (max-width: 992px) {
  .col {
    margin-bottom: 10px;
  }

  .col-2 {
    margin-bottom: 15px;
  }
}

.material-description-row {
  align-items: flex-start;
}

.description-label-col {
  display: flex;
  align-items: center;
  padding-right: 20px;
}

.description-label {
  color: #1f497d;
  font-weight: 600;
  margin: 0;
  font-size: 18px;
  writing-mode: horizontal-tb;
}

.material-inputs {
  margin-bottom: 0;
}

.material-label {
  display: block;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
  font-size: 14px;
  text-align: center;
}

.action-buttons {
  text-align: center;
  padding-top: 20px;
  margin-top: 20px;
}

.action-buttons button {
  margin: 0 5px;
  padding: 10px 20px;
  font-weight: 600;
}

.action-buttons button mat-icon {
  margin-right: 5px;
}

mat-form-field {
  width: 100%;
}

mat-label {
  color: #333;
}

/* Button Styling */
.space {
  margin-top: 20px;
}

.space button {
  margin-right: 10px;
  margin-bottom: 10px;
}

/* Table Styling */
.table-container {
  margin-top: 20px;
}

.mat-elevation-z8 {
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
              0 8px 10px 1px rgba(0, 0, 0, 0.14),
              0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

table {
  width: 100%;
}

th.mat-header-cell {
  background-color: #f5f5f5;
  font-weight: 600;
  color: #1f497d;
}

td.mat-cell {
  padding: 12px 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  fieldset {
    padding: 15px;
  }

  .row {
    margin-bottom: 10px;
  }

  .space button {
    width: 100%;
    margin-bottom: 10px;
  }

  .description-table {
    overflow-x: auto;
  }

  .description-label-cell {
    min-width: 100px;
    font-size: 14px;
  }

  .column-header {
    font-size: 14px;
    padding: 8px 4px;
  }

  .material-column {
    min-width: 120px;
  }
}

/* Loading and Empty States */
.no-data {
  text-align: center;
  padding: 40px;
  color: #666;
  font-style: italic;
}

/* Form Field Focus */
.mat-form-field.mat-focused .mat-form-field-label {
  color: #1f497d;
}

.mat-form-field.mat-focused .mat-form-field-ripple {
  background-color: #1f497d;
}

/* Select Dropdown */
.mat-select-panel {
  max-height: 300px;
}


.issue-details-header h3 {
  color: #1f497d;
  font-weight: 600;
  font-size: 18px;
  margin: 0;
  padding: 10px 0;
}

.description-table {
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
  background: white;
}

.description-row {
  display: flex;
  align-items: stretch;
  min-height: 60px;
}

.description-label {
  border-right: 1px solid #ddd;
  padding: 15px 20px;
  display: flex;
  align-items: center;
  min-width: 120px;
  font-weight: 600;
  color: #333;
}

.material-columns {
  display: flex;
  flex: 1;
}


.column-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #ddd;
  padding: 8px 12px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  font-size: 14px;
  margin: 0;
  display: block;
}

.material-input {
  border: none;
  outline: none;
  padding: 12px;
  font-size: 14px;
  background: white;
  height: 40px;
  width: 100%;
  box-sizing: border-box;
}

/* Kati Data Table Styles */
.kati-data-section {
  margin-top: 20px;
  margin-bottom: 20px;
}

.kati-data-header {
  margin-bottom: 15px;
}

.kati-data-header h4 {
  color: #1f497d;
  font-weight: 600;
  font-size: 18px;
  margin: 0;
  padding: 8px 0;
  border-bottom: 2px solid #1f497d;
  display: inline-block;
}

.kati-data-table-container {
  overflow-x: auto;
  margin-top: 10px;
}

.kati-data-table {
  width: 100%;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.kati-data-table .mat-mdc-header-cell {
  background-color: #f5f5f5;
  color: #1f497d;
  font-weight: 600;
  font-size: 14px;
  padding: 12px 8px;
  border-bottom: 2px solid #1f497d;
}

.kati-data-table .mat-mdc-cell {
  padding: 10px 8px;
  font-size: 13px;
  border-bottom: 1px solid #e0e0e0;
}

.kati-data-table .mat-mdc-row:hover {
  background-color: #f9f9f9;
}

.kati-data-table .mat-mdc-row:nth-child(even) {
  background-color: #fafafa;
}

/* Responsive for kati table */
@media (max-width: 768px) {
  .kati-data-table-container {
    overflow-x: auto;
  }

  .kati-data-table {
    min-width: 700px;
  }

  .kati-data-table .mat-mdc-header-cell,
  .kati-data-table .mat-mdc-cell {
    padding: 8px 4px;
    font-size: 12px;
  }
}


.colour-width{
  width: 250px;
}

/* Footer totals for Kati table */
.kati-data-table .totals-footer .mat-mdc-footer-cell {
  font-weight: 600;
  background: #f9fafb;
  border-top: 2px solid #e0e0e0;
  color: #1f497d;
}

.kati-data-table .totals-footer .mat-mdc-footer-cell {
  text-align: left;
}

.kati-data-table .totals-footer .mat-mdc-footer-cell:nth-child(2) {
  text-align: left;
  color: #333;
}


/* Keep Kati footer attached to table visually */
.kati-data-table .mat-mdc-footer-row {
  position: sticky;
  bottom: 0;
  z-index: 1;
}
