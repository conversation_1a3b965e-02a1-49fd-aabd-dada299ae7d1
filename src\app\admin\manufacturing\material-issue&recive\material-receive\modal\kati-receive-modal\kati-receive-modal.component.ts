import { Component, OnInit, Inject } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { FormBuilder, FormGroup, FormArray } from '@angular/forms';

import Swal from 'sweetalert2';
import { ManufactureService } from '../../../../../../services/manufacture.service';

@Component({
  selector: 'app-kati-receive-modal',
  templateUrl: './kati-receive-modal.component.html',
  styleUrls: ['./kati-receive-modal.component.css']
})
export class KatiReceiveModalComponent implements OnInit {
  katiReceiveForm!: FormGroup;
  displayedColumns: string[] = ['srNo', 'colour', 'lagat', 'carpetLagat', 'tIssued', 'receive', 'totalLagat'];
  materialData: any[] = [];
  totalReceive: number = 0;
  totalIssuedSum: number = 0;
  totalLagatSum: number = 0;
  materialType: string = 'kati';
  materialName: string = 'Kati';
  isEditMode: boolean = false;
  existingReceiveData: any[] = [];

  constructor(
    private fb: FormBuilder,
    private manufactureService: ManufactureService,
    public dialogRef: MatDialogRef<KatiReceiveModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {}

  ngOnInit(): void {
    // Initialize material type and name from data
    this.materialType = this.data.materialType || 'kati';
    this.materialName = this.data.materialName || 'Kati';
    this.isEditMode = !!this.data?.isEditMode;
    this.existingReceiveData = this.data?.existingReceiveData || [];

    this.initializeForm();
    this.loadMaterialData();
  }

  initializeForm(): void {
    this.katiReceiveForm = this.fb.group({
      katiItems: this.fb.array([])
    });
  }

  get katiItems(): FormArray {
    return this.katiReceiveForm.get('katiItems') as FormArray;
  }

  loadMaterialData(): void {
    console.log(`🔍 Loading ${this.materialName} data for:`, this.data);

    if (!this.data.issueNo || !this.data.weaver) {
      console.log('⚠️ Missing required data');
      return;
    }

    // Fetch all material issues to aggregate totals for this issue + weaver
    this.manufactureService.getAllMaterialIssues().subscribe({
      next: (materialIssues: any) => {
        const issuesArray = materialIssues as any[];
        console.log('📦 All material issues:', issuesArray?.length || 0);

        // Filter by weaver and issue number
        const filteredIssues = issuesArray.filter((issue: any) =>
          issue.weaver &&
          (issue.weaver._id === this.data.weaver._id || issue.weaver === this.data.weaver._id) &&
          issue.issueNo &&
          (issue.issueNo.Br_issueNo === this.data.issueNo.Br_issueNo || issue.issueNo === this.data.issueNo._id)
        );

        console.log('🎯 Filtered issues:', filteredIssues?.length || 0);

        // Build base colour rows from material lagat design colours (like Kati modal)
        const area = parseFloat(this.data.area || '0') || 0;
        const materialLagatList = this.data.materialLagatList || [];
        const order = this.data.carpetOrderIssue || this.data.issueNo; // try either

        let qualityId = order?.quality?._id || order?.quality;
        let designId = order?.design?._id || order?.design || order?.AddDesign?._id;

        // Find matching material lagat entry
        let designColours: any[] = [];
        try {
          const matchingLagat = materialLagatList.find((lagat: any) => {
            const lagatQualityId = typeof lagat.quality === 'object' ? lagat.quality._id : lagat.quality;
            const lagatDesignId = typeof lagat.AddDesign === 'object' ? lagat.AddDesign._id : lagat.AddDesign;
            return lagatQualityId === qualityId && lagatDesignId === designId;
          });
          if (matchingLagat?.AddDesign?.colourLagats) {
            designColours = matchingLagat.AddDesign.colourLagats;
          }
        } catch (e) {
          console.warn('⚠️ Could not resolve material lagat colours for receive modal:', e);
        }

        // Helper: sum toIssue by colour across filtered issues
        const sumIssuedByColour = (colourId: any): number => {
          let sum = 0;
          filteredIssues.forEach((mi: any) => {
            const rows = mi[`${this.materialType}Data`];
            if (Array.isArray(rows)) {
              rows.forEach((r: any) => {
                const rColourId = r.colour?._id || r.colour?.id || r.colour;
                if (rColourId && (rColourId === colourId)) {
                  sum += parseFloat(r.toIssueValue || r.issueValue || '0') || 0;
                }
              });
            }
          });
          return sum;
        };

        // Build materialData rows strictly from material issues (ignore lagat designColours)
        this.materialData = [];
        let srNo = 1;

        const materialDataKey = `${this.materialType}Data`;
        // Aggregate by colour so Colour/Lagat/Carpet Lagat appear only once
        const aggMap = new Map<string, any>();
        filteredIssues.forEach((issue: any) => {
          const rows = issue[materialDataKey] || (issue.materials && issue.materials[materialDataKey]);
          if (rows && Array.isArray(rows)) {
            rows.forEach((materialItem: any) => {
              const colourObj = materialItem.colour || {};
              const colourId = colourObj._id || colourObj.id || colourObj;
              if (!colourId) return;
              const colourText = colourObj.newColor
                ? `${colourObj.newColor || ''} - ${colourObj.companyColorCode || ''} - ${colourObj.remark || ''}`
                : (colourObj.name || '');
              const issued = parseFloat(materialItem.issueValue || materialItem.toIssueValue || '0') || 0;
              const lagatNum = parseFloat(materialItem.lagat || '0') || 0;

              const ex = aggMap.get(colourId);
              if (ex) {
                ex.tIssued = +(ex.tIssued + issued).toFixed(3);
                // Keep lagat from first occurrence
              } else {
                aggMap.set(colourId, {
                  srNo: 0, // set later
                  colour: colourText,
                  lagat: lagatNum,
                  carpetLagat: +(lagatNum * area).toFixed(3),
                  tIssued: +(issued.toFixed(3)),
                  receive: '',
                  totalLagat: +(issued.toFixed(3)),
                  originalData: { colour: colourObj, lagat: lagatNum }
                });
              }
            });
          }
        });

        // Finalize materialData array
        this.materialData = Array.from(aggMap.values()).map((row: any, idx: number) => ({
          ...row,
          srNo: idx + 1
        }));

        console.log(`✅ Processed ${this.materialName} data:`, this.materialData);
        this.setupFormArray();
      },
      error: (error) => {
        console.error('❌ Error loading material issues:', error);
        Swal.fire('Error', `Failed to load ${this.materialName} data`, 'error');
      }
    });
  }

  setupFormArray(): void {
    const materialItemsArray = this.fb.array([]);

    this.materialData.forEach((item: any) => {
      // Prefill receive value if editing and we have existing data for this colour
      let prefill: any = '';
      if (this.isEditMode && Array.isArray(this.existingReceiveData)) {
        const colourId = (item?.originalData?.colour?._id || item?.originalData?.colour?.id || item?.originalData?.colour);
        const found = this.existingReceiveData.find((r: any) => {
          const rId = r?.colour?._id || r?.colour?.id || r?.colour;
          return rId && colourId && rId === colourId;
        });
        if (found && found.receiveValue !== undefined) {
          const n = parseFloat(found.receiveValue);
          prefill = isNaN(n) ? '' : n.toFixed(3);
        }
      }

      const itemGroup = this.fb.group({
        receive: [prefill]
      });

      // Listen for receive value changes
      itemGroup.get('receive')?.valueChanges.subscribe((receiveValue: any) => {
        const receive = parseFloat(receiveValue || '0');
        const issued = item.tIssued;

        // Calculate total lagat (remaining)
        item.totalLagat = Math.max(0, issued - receive);

        // Update total receive
        this.calculateTotalReceive();
      });

      materialItemsArray.push(itemGroup as any);
    });

    this.katiReceiveForm.setControl('katiItems', materialItemsArray);
    // Recompute totals after prefill
    this.calculateTotalReceive();
  }

  calculateTotalReceive(): void {
    this.totalReceive = 0;
    this.totalIssuedSum = 0;
    this.totalLagatSum = 0;

    this.katiItems.controls.forEach((control, idx) => {
      const receiveValue = parseFloat(control.get('receive')?.value || '0');
      this.totalReceive += receiveValue;
      const item = this.materialData[idx];
      this.totalIssuedSum += parseFloat(item?.tIssued || 0);
      this.totalLagatSum += Math.max(0, parseFloat(item?.tIssued || 0) - receiveValue);
    });

    console.log('📊 Totals — issued:', this.totalIssuedSum, ' receive:', this.totalReceive, ' totalLagat:', this.totalLagatSum);
  }

  onReceiveChange(index: number, event: any): void {
    const raw = (event.target.value ?? '').toString();
    const item = this.materialData[index];

    const parsed = raw.trim() === '' ? 0 : parseFloat(raw);
    const receiveValue = isNaN(parsed) ? 0 : parsed;

    // Validate receive value; if over issued, cap it and format but otherwise do not overwrite typing (e.g., "0.")
    if (receiveValue > item.tIssued) {
      Swal.fire('Warning', 'Receive value cannot be greater than issued value', 'warning');
      const capped = (parseFloat(item.tIssued) || 0).toFixed(3);
      event.target.value = capped;
      this.katiItems.at(index).patchValue({ receive: capped });
    } else {
      // Trigger totals recompute without altering the raw input
      this.calculateTotalReceive();
    }
  }

  setDigitReceive(event: any): void {
    const valueStr = (event.target.value ?? '').toString();
    if (valueStr.trim() === '') {
      // keep blank; do not force 0.000
    } else {
      const n = parseFloat(valueStr);
      if (!isNaN(n)) {
        event.target.value = n.toFixed(3);
        // also update control with formatted value
        const active = document.activeElement as any;
      }
    }
    this.calculateTotalReceive();
  }

  onSave(): void {
    console.log(`💾 Saving ${this.materialName} receive data...`);

    // Helper to format numbers to 3 decimals, defaulting to 0.000
    const to3 = (val: any) => {
      const n = parseFloat(val);
      return isNaN(n) ? '0.000' : n.toFixed(3);
    };

    // Prepare data to return — include carpetLagat, tIssued and tLagat
    const receiveData = this.materialData.map((item, index) => {
      const receiveRaw = this.katiItems.at(index).get('receive')?.value;
      const receiveNum = parseFloat(receiveRaw || '0') || 0;
      const formattedReceiveValue = to3(receiveNum);
      const tIssued = parseFloat(item.tIssued || '0') || 0;
      const tLagat = Math.max(0, tIssued - receiveNum);

      return {
        ...item.originalData,
        lagat: to3(item.lagat), // ensure 3-decimal string in DB
        carpetLagat: to3(item.carpetLagat),
        tIssued: to3(tIssued),
        receiveValue: formattedReceiveValue,
        tLagat: to3(tLagat)
      };
    });

    // Build table rows for immediate display in Material Receive screen
    const tableRows = this.materialData.map((item, index) => {
      const rec = parseFloat(this.katiItems.at(index).get('receive')?.value || '0') || 0;
      const tIssuedNum = parseFloat(item.tIssued || '0') || 0;
      const remaining = Math.max(0, tIssuedNum - rec);
      return {
        srNo: item.srNo,
        colourText: item.colour,
        lagat: item.lagat,
        carpetLagat: item.carpetLagat,
        tIssued: tIssuedNum,
        receive: rec,
        totalLagat: +(remaining.toFixed(3))
      };
    });

    const result = {
      materialReceiveData: receiveData,
      tableRows,
      totals: {
        issued: this.totalIssuedSum,
        receive: this.totalReceive,
        totalLagat: this.totalLagatSum
      },
      totalReceive: this.totalReceive,
      materialType: this.materialType
    };

    console.log('✅ Returning data:', result);
    this.dialogRef.close(result);
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
