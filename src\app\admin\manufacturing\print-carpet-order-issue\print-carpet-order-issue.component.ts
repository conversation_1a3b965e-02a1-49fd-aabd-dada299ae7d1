import { Component, Input, OnInit } from '@angular/core';
import { ManufactureService } from '../../../services/manufacture.service';
import { ActivatedRoute } from '@angular/router';
import { CustomeServiceService } from '../../../services/custome-service.service';
import { MatDialog } from '@angular/material/dialog';

@Component({
  selector: 'app-print-carpet-order-issue',
  templateUrl: './print-carpet-order-issue.component.html',
  styleUrl: './print-carpet-order-issue.component.css'
})
export class PrintCarpetOrderIssueComponent implements OnInit{

  printId:any

  constructor(
    private manufactureService:ManufactureService,
    private activatedRoute:ActivatedRoute,
    private customerService:CustomeServiceService,
    private dialog: MatDialog
  ){}

  // Material summary state
  materialIssues: any[] = [];
  // Material receive docs for Kati details table
  materialReceives: any[] = [];
  // Base lagat (should not be summed)
  materialsBase: any = { kati: 0, tana: 0, soot: 0, thari: 0, silk: 0, other: 0 };
  materialsTotals: any = {
    kati: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    tana: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    soot: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    thari: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    silk: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
    other: { lagat: 0, issue: 0, receive: 0, tLagat: 0 },
  };
  recDate?: string; // from carpetReceiveds
  carpetNo?: string | number; // from carpetReceiveds
  weight?: string; // from carpetReceivedUpdates

  ngOnInit(): void {
    this.printId = this.activatedRoute.snapshot.paramMap.get('id') || ''
    if(this.printId){
      this.getAllCarpetOrderData(this.printId);
      this.loadMaterialIssues(this.printId);
      this.loadMaterialReceives(this.printId);
      // Load Rec. Date, Carpet No. from carpetReceiveds and weight from updates
      this.loadReceiveMetaFromCarpets(this.printId);
    }
    this.getData();
 }

  obj:any

  getData(){
    let data = localStorage.getItem('printData');
    this.obj = data ? JSON.parse(data) : null;
    console.log(this.obj);
  }

  getById(id:any){
   this.manufactureService.getOrderIssue(id).subscribe(()=>{})
  }

  // Load material issue docs and compute lagat/issue totals
  loadMaterialIssues(issueNoId: string){
    this.manufactureService.getMaterialIssuesByIssueNo(issueNoId).subscribe((docs: any)=>{
      this.materialIssues = Array.isArray(docs) ? docs : [];
      this.computeMaterialsTotals();
    });
  }

  // Load material receives to compute receive and total lagat
  loadMaterialReceives(issueNoId: string){
    if(!this.manufactureService.getMaterialReceivesByIssueNo) return;
    this.manufactureService.getMaterialReceivesByIssueNo(issueNoId).subscribe((receives:any)=>{
      const list = Array.isArray(receives) ? receives : [];
      this.materialReceives = list; // for Kati details table
      // Keep totals in sync
      list.forEach((doc:any)=>{
        const m = doc?.materials || {};
        const add = (key:string)=>{
          if(!m[key]) return;
          this.materialsTotals[key].receive += this.toNum(m[key].receive);
          this.materialsTotals[key].tLagat += this.toNum(m[key].tLagat);
        }
        ;['kati','tana','soot','thari','silk','other'].forEach(add);
      })
    })
  }

  // Get Rec. Date & Carpet No from CarpetReceiveds + Weight from carpetReceivedUpdates
  private loadReceiveMetaFromCarpets(issueNoId: string){
    if(!this.manufactureService.getReceivedCarpetsByIssueIds) return;
    this.manufactureService.getReceivedCarpetsByIssueIds([issueNoId]).subscribe((rows:any)=>{
      const list = Array.isArray(rows) ? rows : [];
      if(!list.length) return;
      // pick latest by receivingDate
      const latest = list
        .filter((x:any)=> !!x)
        .sort((a:any,b:any)=> new Date(b.receivingDate).getTime() - new Date(a.receivingDate).getTime())[0];
      this.recDate = latest?.receivingDate ? new Date(latest.receivingDate).toISOString() : this.recDate;
      this.carpetNo = latest?.carpetNo || this.carpetNo || 0;
      const carpetReceivedId = latest?._id;
      if(carpetReceivedId && this.manufactureService.getCarpetReceivedUpdates){
        this.manufactureService.getCarpetReceivedUpdates(carpetReceivedId).subscribe((updates:any)=>{
          const arr = Array.isArray(updates) ? updates : (updates ? [updates] : []);
          // updates are sorted desc by updatedAt in backend repository
          const withWeight = arr.find((u:any)=> (u && (u.weight || (u.carpetInfo && u.carpetInfo.weight))));
          this.weight = withWeight?.weight || withWeight?.carpetInfo?.weight || this.weight;
        })
      }
    })
  }

  private toNum(v:any){
    const n = parseFloat(v);
    return isNaN(n) ? 0 : n;
  }

  computeMaterialsTotals(){
    const totals:any = {
      kati: { lagat: 0, issue: 0, receive: this.materialsTotals.kati.receive, tLagat: this.materialsTotals.kati.tLagat },
      tana: { lagat: 0, issue: 0, receive: this.materialsTotals.tana.receive, tLagat: this.materialsTotals.tana.tLagat },
      soot: { lagat: 0, issue: 0, receive: this.materialsTotals.soot.receive, tLagat: this.materialsTotals.soot.tLagat },
      thari: { lagat: 0, issue: 0, receive: this.materialsTotals.thari.receive, tLagat: this.materialsTotals.thari.tLagat },
      silk: { lagat: 0, issue: 0, receive: this.materialsTotals.silk.receive, tLagat: this.materialsTotals.silk.tLagat },
      other: { lagat: 0, issue: 0, receive: this.materialsTotals.other.receive, tLagat: this.materialsTotals.other.tLagat },
    };

    // Base lagat should be taken from first issue doc only (not summed)
    const first = this.materialIssues[0]?.materials || {};
    ;['kati','tana','soot','thari','silk','other'].forEach((key:string)=>{
      if(first[key]) totals[key].lagat = this.toNum(first[key].lagat);
    });

    // Issue should be summed across issues
    this.materialIssues.forEach((doc:any)=>{
      const m = doc?.materials || {};
      const addIssue = (key:string)=>{
        if(!m[key]) return;
        totals[key].issue += this.toNum(m[key].issue);
      }
      ;['kati','tana','soot','thari','silk','other'].forEach(addIssue);
    });

    this.materialsTotals = totals;
  }

  orderIssueData:any
  totalArea:any
  getAllCarpetOrderData(id:string){

    this.manufactureService.getsOrderIssueList().subscribe((res:any)=>{

      this.orderIssueData =  res.find((x:any) => x._id === id)
      this.orderIssueData.date
      let areaItem = this.orderIssueData.buyerOrder.items.find((x:any)=> x._id === this.orderIssueData.itemId);

      // ✅ Area value ko assign karna
      this.totalArea = areaItem?.totalArea || 0;

      console.log(this.orderIssueData)

      console.log("✅ Total Area:", this.totalArea);

    })
  }

  // Open challan details in modal (used by info icon next to Total Issue)
  openChallanDetailsModal(): void {
    const challans = (this.materialIssues || []).map((doc: any) => ({
      challanNo: doc?.challanNo,
      date: doc?.date,
      materials: doc?.materials,
    }));

    import('../material-issue&recive/material-receive/modal/challan-modal/challan-modal.component').then(({ ChallanModalComponent }) => {
      this.dialog.open(ChallanModalComponent, {
        width: '1200px',
        data: {
          issueNo: this.orderIssueData?.Br_issueNo,
          challanDetails: challans,
        },
      });
    });
  }

  printPage() {
    const root = document.getElementById('print-root');
    const printContents = root ? (root as HTMLElement).outerHTML : null;
    if (printContents) {
      const originalContents = document.body.innerHTML;
      document.body.innerHTML = printContents; // keep wrappers so borders print per page
      window.print();
      document.body.innerHTML = originalContents;
      window.location.reload();
    } else {
      window.print();
    }
  }
}

