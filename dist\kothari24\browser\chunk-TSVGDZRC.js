import{a as it,b as ln}from"./chunk-CWTPBX7D.js";var Ya=null;var Po=1,ko=Symbol("SIGNAL");function _(e){let t=Ya;return Ya=e,t}var Qa={version:0,lastCleanEpoch:0,dirty:!1,producerNode:void 0,producerLastReadVersion:void 0,producerIndexOfThis:void 0,nextProducerIndex:0,liveConsumerNode:void 0,liveConsumerIndexOfThis:void 0,consumerAllowSignalWrites:!1,consumerIsAlwaysLive:!1,producerMustRecompute:()=>!1,producerRecomputeValue:()=>{},consumerMarkedDirty:()=>{},consumerOnSignalRead:()=>{}};function wf(e){if(!(Vo(e)&&!e.dirty)&&!(!e.dirty&&e.lastCleanEpoch===Po)){if(!e.producerMustRecompute(e)&&!Lo(e)){e.dirty=!1,e.lastCleanEpoch=Po;return}e.producerRecomputeValue(e),e.dirty=!1,e.lastCleanEpoch=Po}}function Za(e){return e&&(e.nextProducerIndex=0),_(e)}function Ka(e,t){if(_(t),!(!e||e.producerNode===void 0||e.producerIndexOfThis===void 0||e.producerLastReadVersion===void 0)){if(Vo(e))for(let n=e.nextProducerIndex;n<e.producerNode.length;n++)jo(e.producerNode[n],e.producerIndexOfThis[n]);for(;e.producerNode.length>e.nextProducerIndex;)e.producerNode.pop(),e.producerLastReadVersion.pop(),e.producerIndexOfThis.pop()}}function Lo(e){Wn(e);for(let t=0;t<e.producerNode.length;t++){let n=e.producerNode[t],r=e.producerLastReadVersion[t];if(r!==n.version||(wf(n),r!==n.version))return!0}return!1}function Ja(e){if(Wn(e),Vo(e))for(let t=0;t<e.producerNode.length;t++)jo(e.producerNode[t],e.producerIndexOfThis[t]);e.producerNode.length=e.producerLastReadVersion.length=e.producerIndexOfThis.length=0,e.liveConsumerNode&&(e.liveConsumerNode.length=e.liveConsumerIndexOfThis.length=0)}function jo(e,t){if(If(e),Wn(e),e.liveConsumerNode.length===1)for(let r=0;r<e.producerNode.length;r++)jo(e.producerNode[r],e.producerIndexOfThis[r]);let n=e.liveConsumerNode.length-1;if(e.liveConsumerNode[t]=e.liveConsumerNode[n],e.liveConsumerIndexOfThis[t]=e.liveConsumerIndexOfThis[n],e.liveConsumerNode.length--,e.liveConsumerIndexOfThis.length--,t<e.liveConsumerNode.length){let r=e.liveConsumerIndexOfThis[t],o=e.liveConsumerNode[t];Wn(o),o.producerIndexOfThis[r]=t}}function Vo(e){return e.consumerIsAlwaysLive||(e?.liveConsumerNode?.length??0)>0}function Wn(e){e.producerNode??=[],e.producerIndexOfThis??=[],e.producerLastReadVersion??=[]}function If(e){e.liveConsumerNode??=[],e.liveConsumerIndexOfThis??=[]}function Ef(){throw new Error}var Cf=Ef;function Xa(e){Cf=e}function m(e){return typeof e=="function"}function At(e){let n=e(r=>{Error.call(r),r.stack=new Error().stack});return n.prototype=Object.create(Error.prototype),n.prototype.constructor=n,n}var qn=At(e=>function(n){e(this),this.message=n?`${n.length} errors occurred during unsubscription:
${n.map((r,o)=>`${o+1}) ${r.toString()}`).join(`
  `)}`:"",this.name="UnsubscriptionError",this.errors=n});function st(e,t){if(e){let n=e.indexOf(t);0<=n&&e.splice(n,1)}}var $=class e{constructor(t){this.initialTeardown=t,this.closed=!1,this._parentage=null,this._finalizers=null}unsubscribe(){let t;if(!this.closed){this.closed=!0;let{_parentage:n}=this;if(n)if(this._parentage=null,Array.isArray(n))for(let i of n)i.remove(this);else n.remove(this);let{initialTeardown:r}=this;if(m(r))try{r()}catch(i){t=i instanceof qn?i.errors:[i]}let{_finalizers:o}=this;if(o){this._finalizers=null;for(let i of o)try{eu(i)}catch(s){t=t??[],s instanceof qn?t=[...t,...s.errors]:t.push(s)}}if(t)throw new qn(t)}}add(t){var n;if(t&&t!==this)if(this.closed)eu(t);else{if(t instanceof e){if(t.closed||t._hasParent(this))return;t._addParent(this)}(this._finalizers=(n=this._finalizers)!==null&&n!==void 0?n:[]).push(t)}}_hasParent(t){let{_parentage:n}=this;return n===t||Array.isArray(n)&&n.includes(t)}_addParent(t){let{_parentage:n}=this;this._parentage=Array.isArray(n)?(n.push(t),n):n?[n,t]:t}_removeParent(t){let{_parentage:n}=this;n===t?this._parentage=null:Array.isArray(n)&&st(n,t)}remove(t){let{_finalizers:n}=this;n&&st(n,t),t instanceof e&&t._removeParent(this)}};$.EMPTY=(()=>{let e=new $;return e.closed=!0,e})();var Bo=$.EMPTY;function Yn(e){return e instanceof $||e&&"closed"in e&&m(e.remove)&&m(e.add)&&m(e.unsubscribe)}function eu(e){m(e)?e():e.unsubscribe()}var we={onUnhandledError:null,onStoppedNotification:null,Promise:void 0,useDeprecatedSynchronousErrorHandling:!1,useDeprecatedNextContext:!1};var Ot={setTimeout(e,t,...n){let{delegate:r}=Ot;return r?.setTimeout?r.setTimeout(e,t,...n):setTimeout(e,t,...n)},clearTimeout(e){let{delegate:t}=Ot;return(t?.clearTimeout||clearTimeout)(e)},delegate:void 0};function Qn(e){Ot.setTimeout(()=>{let{onUnhandledError:t}=we;if(t)t(e);else throw e})}function at(){}var tu=$o("C",void 0,void 0);function nu(e){return $o("E",void 0,e)}function ru(e){return $o("N",e,void 0)}function $o(e,t,n){return{kind:e,value:t,error:n}}var ut=null;function Ft(e){if(we.useDeprecatedSynchronousErrorHandling){let t=!ut;if(t&&(ut={errorThrown:!1,error:null}),e(),t){let{errorThrown:n,error:r}=ut;if(ut=null,n)throw r}}else e()}function ou(e){we.useDeprecatedSynchronousErrorHandling&&ut&&(ut.errorThrown=!0,ut.error=e)}var ct=class extends ${constructor(t){super(),this.isStopped=!1,t?(this.destination=t,Yn(t)&&t.add(this)):this.destination=Mf}static create(t,n,r){return new Ie(t,n,r)}next(t){this.isStopped?Uo(ru(t),this):this._next(t)}error(t){this.isStopped?Uo(nu(t),this):(this.isStopped=!0,this._error(t))}complete(){this.isStopped?Uo(tu,this):(this.isStopped=!0,this._complete())}unsubscribe(){this.closed||(this.isStopped=!0,super.unsubscribe(),this.destination=null)}_next(t){this.destination.next(t)}_error(t){try{this.destination.error(t)}finally{this.unsubscribe()}}_complete(){try{this.destination.complete()}finally{this.unsubscribe()}}},bf=Function.prototype.bind;function Ho(e,t){return bf.call(e,t)}var Go=class{constructor(t){this.partialObserver=t}next(t){let{partialObserver:n}=this;if(n.next)try{n.next(t)}catch(r){Zn(r)}}error(t){let{partialObserver:n}=this;if(n.error)try{n.error(t)}catch(r){Zn(r)}else Zn(t)}complete(){let{partialObserver:t}=this;if(t.complete)try{t.complete()}catch(n){Zn(n)}}},Ie=class extends ct{constructor(t,n,r){super();let o;if(m(t)||!t)o={next:t??void 0,error:n??void 0,complete:r??void 0};else{let i;this&&we.useDeprecatedNextContext?(i=Object.create(t),i.unsubscribe=()=>this.unsubscribe(),o={next:t.next&&Ho(t.next,i),error:t.error&&Ho(t.error,i),complete:t.complete&&Ho(t.complete,i)}):o=t}this.destination=new Go(o)}};function Zn(e){we.useDeprecatedSynchronousErrorHandling?ou(e):Qn(e)}function _f(e){throw e}function Uo(e,t){let{onStoppedNotification:n}=we;n&&Ot.setTimeout(()=>n(e,t))}var Mf={closed:!0,next:at,error:_f,complete:at};var Rt=typeof Symbol=="function"&&Symbol.observable||"@@observable";function X(e){return e}function xf(...e){return zo(e)}function zo(e){return e.length===0?X:e.length===1?e[0]:function(n){return e.reduce((r,o)=>o(r),n)}}var x=(()=>{class e{constructor(n){n&&(this._subscribe=n)}lift(n){let r=new e;return r.source=this,r.operator=n,r}subscribe(n,r,o){let i=Tf(n)?n:new Ie(n,r,o);return Ft(()=>{let{operator:s,source:a}=this;i.add(s?s.call(i,a):a?this._subscribe(i):this._trySubscribe(i))}),i}_trySubscribe(n){try{return this._subscribe(n)}catch(r){n.error(r)}}forEach(n,r){return r=iu(r),new r((o,i)=>{let s=new Ie({next:a=>{try{n(a)}catch(u){i(u),s.unsubscribe()}},error:i,complete:o});this.subscribe(s)})}_subscribe(n){var r;return(r=this.source)===null||r===void 0?void 0:r.subscribe(n)}[Rt](){return this}pipe(...n){return zo(n)(this)}toPromise(n){return n=iu(n),new n((r,o)=>{let i;this.subscribe(s=>i=s,s=>o(s),()=>r(i))})}}return e.create=t=>new e(t),e})();function iu(e){var t;return(t=e??we.Promise)!==null&&t!==void 0?t:Promise}function Sf(e){return e&&m(e.next)&&m(e.error)&&m(e.complete)}function Tf(e){return e&&e instanceof ct||Sf(e)&&Yn(e)}function Wo(e){return m(e?.lift)}function w(e){return t=>{if(Wo(t))return t.lift(function(n){try{return e(n,this)}catch(r){this.error(r)}});throw new TypeError("Unable to lift unknown Observable type")}}function D(e,t,n,r,o){return new qo(e,t,n,r,o)}var qo=class extends ct{constructor(t,n,r,o,i,s){super(t),this.onFinalize=i,this.shouldUnsubscribe=s,this._next=n?function(a){try{n(a)}catch(u){t.error(u)}}:super._next,this._error=o?function(a){try{o(a)}catch(u){t.error(u)}finally{this.unsubscribe()}}:super._error,this._complete=r?function(){try{r()}catch(a){t.error(a)}finally{this.unsubscribe()}}:super._complete}unsubscribe(){var t;if(!this.shouldUnsubscribe||this.shouldUnsubscribe()){let{closed:n}=this;super.unsubscribe(),!n&&((t=this.onFinalize)===null||t===void 0||t.call(this))}}};function Yo(){return w((e,t)=>{let n=null;e._refCount++;let r=D(t,void 0,void 0,void 0,()=>{if(!e||e._refCount<=0||0<--e._refCount){n=null;return}let o=e._connection,i=n;n=null,o&&(!i||o===i)&&o.unsubscribe(),t.unsubscribe()});e.subscribe(r),r.closed||(n=e.connect())})}var Qo=class extends x{constructor(t,n){super(),this.source=t,this.subjectFactory=n,this._subject=null,this._refCount=0,this._connection=null,Wo(t)&&(this.lift=t.lift)}_subscribe(t){return this.getSubject().subscribe(t)}getSubject(){let t=this._subject;return(!t||t.isStopped)&&(this._subject=this.subjectFactory()),this._subject}_teardown(){this._refCount=0;let{_connection:t}=this;this._subject=this._connection=null,t?.unsubscribe()}connect(){let t=this._connection;if(!t){t=this._connection=new $;let n=this.getSubject();t.add(this.source.subscribe(D(n,void 0,()=>{this._teardown(),n.complete()},r=>{this._teardown(),n.error(r)},()=>this._teardown()))),t.closed&&(this._connection=null,t=$.EMPTY)}return t}refCount(){return Yo()(this)}};var Pt={schedule(e){let t=requestAnimationFrame,n=cancelAnimationFrame,{delegate:r}=Pt;r&&(t=r.requestAnimationFrame,n=r.cancelAnimationFrame);let o=t(i=>{n=void 0,e(i)});return new $(()=>n?.(o))},requestAnimationFrame(...e){let{delegate:t}=Pt;return(t?.requestAnimationFrame||requestAnimationFrame)(...e)},cancelAnimationFrame(...e){let{delegate:t}=Pt;return(t?.cancelAnimationFrame||cancelAnimationFrame)(...e)},delegate:void 0};var su=At(e=>function(){e(this),this.name="ObjectUnsubscribedError",this.message="object unsubscribed"});var ge=(()=>{class e extends x{constructor(){super(),this.closed=!1,this.currentObservers=null,this.observers=[],this.isStopped=!1,this.hasError=!1,this.thrownError=null}lift(n){let r=new Kn(this,this);return r.operator=n,r}_throwIfClosed(){if(this.closed)throw new su}next(n){Ft(()=>{if(this._throwIfClosed(),!this.isStopped){this.currentObservers||(this.currentObservers=Array.from(this.observers));for(let r of this.currentObservers)r.next(n)}})}error(n){Ft(()=>{if(this._throwIfClosed(),!this.isStopped){this.hasError=this.isStopped=!0,this.thrownError=n;let{observers:r}=this;for(;r.length;)r.shift().error(n)}})}complete(){Ft(()=>{if(this._throwIfClosed(),!this.isStopped){this.isStopped=!0;let{observers:n}=this;for(;n.length;)n.shift().complete()}})}unsubscribe(){this.isStopped=this.closed=!0,this.observers=this.currentObservers=null}get observed(){var n;return((n=this.observers)===null||n===void 0?void 0:n.length)>0}_trySubscribe(n){return this._throwIfClosed(),super._trySubscribe(n)}_subscribe(n){return this._throwIfClosed(),this._checkFinalizedStatuses(n),this._innerSubscribe(n)}_innerSubscribe(n){let{hasError:r,isStopped:o,observers:i}=this;return r||o?Bo:(this.currentObservers=null,i.push(n),new $(()=>{this.currentObservers=null,st(i,n)}))}_checkFinalizedStatuses(n){let{hasError:r,thrownError:o,isStopped:i}=this;r?n.error(o):i&&n.complete()}asObservable(){let n=new x;return n.source=this,n}}return e.create=(t,n)=>new Kn(t,n),e})(),Kn=class extends ge{constructor(t,n){super(),this.destination=t,this.source=n}next(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.next)===null||r===void 0||r.call(n,t)}error(t){var n,r;(r=(n=this.destination)===null||n===void 0?void 0:n.error)===null||r===void 0||r.call(n,t)}complete(){var t,n;(n=(t=this.destination)===null||t===void 0?void 0:t.complete)===null||n===void 0||n.call(t)}_subscribe(t){var n,r;return(r=(n=this.source)===null||n===void 0?void 0:n.subscribe(t))!==null&&r!==void 0?r:Bo}};var dn=class extends ge{constructor(t){super(),this._value=t}get value(){return this.getValue()}_subscribe(t){let n=super._subscribe(t);return!n.closed&&t.next(this._value),n}getValue(){let{hasError:t,thrownError:n,_value:r}=this;if(t)throw n;return this._throwIfClosed(),r}next(t){super.next(this._value=t)}};var fn={now(){return(fn.delegate||Date).now()},delegate:void 0};var pn=class extends ge{constructor(t=1/0,n=1/0,r=fn){super(),this._bufferSize=t,this._windowTime=n,this._timestampProvider=r,this._buffer=[],this._infiniteTimeWindow=!0,this._infiniteTimeWindow=n===1/0,this._bufferSize=Math.max(1,t),this._windowTime=Math.max(1,n)}next(t){let{isStopped:n,_buffer:r,_infiniteTimeWindow:o,_timestampProvider:i,_windowTime:s}=this;n||(r.push(t),!o&&r.push(i.now()+s)),this._trimBuffer(),super.next(t)}_subscribe(t){this._throwIfClosed(),this._trimBuffer();let n=this._innerSubscribe(t),{_infiniteTimeWindow:r,_buffer:o}=this,i=o.slice();for(let s=0;s<i.length&&!t.closed;s+=r?1:2)t.next(i[s]);return this._checkFinalizedStatuses(t),n}_trimBuffer(){let{_bufferSize:t,_timestampProvider:n,_buffer:r,_infiniteTimeWindow:o}=this,i=(o?1:2)*t;if(t<1/0&&i<r.length&&r.splice(0,r.length-i),!o){let s=n.now(),a=0;for(let u=1;u<r.length&&r[u]<=s;u+=2)a=u;a&&r.splice(0,a+1)}}};var Jn=class extends ${constructor(t,n){super()}schedule(t,n=0){return this}};var hn={setInterval(e,t,...n){let{delegate:r}=hn;return r?.setInterval?r.setInterval(e,t,...n):setInterval(e,t,...n)},clearInterval(e){let{delegate:t}=hn;return(t?.clearInterval||clearInterval)(e)},delegate:void 0};var kt=class extends Jn{constructor(t,n){super(t,n),this.scheduler=t,this.work=n,this.pending=!1}schedule(t,n=0){var r;if(this.closed)return this;this.state=t;let o=this.id,i=this.scheduler;return o!=null&&(this.id=this.recycleAsyncId(i,o,n)),this.pending=!0,this.delay=n,this.id=(r=this.id)!==null&&r!==void 0?r:this.requestAsyncId(i,this.id,n),this}requestAsyncId(t,n,r=0){return hn.setInterval(t.flush.bind(t,this),r)}recycleAsyncId(t,n,r=0){if(r!=null&&this.delay===r&&this.pending===!1)return n;n!=null&&hn.clearInterval(n)}execute(t,n){if(this.closed)return new Error("executing a cancelled action");this.pending=!1;let r=this._execute(t,n);if(r)return r;this.pending===!1&&this.id!=null&&(this.id=this.recycleAsyncId(this.scheduler,this.id,null))}_execute(t,n){let r=!1,o;try{this.work(t)}catch(i){r=!0,o=i||new Error("Scheduled action threw falsy error")}if(r)return this.unsubscribe(),o}unsubscribe(){if(!this.closed){let{id:t,scheduler:n}=this,{actions:r}=n;this.work=this.state=this.scheduler=null,this.pending=!1,st(r,this),t!=null&&(this.id=this.recycleAsyncId(n,t,null)),this.delay=null,super.unsubscribe()}}};var Lt=class e{constructor(t,n=e.now){this.schedulerActionCtor=t,this.now=n}schedule(t,n=0,r){return new this.schedulerActionCtor(this,t).schedule(r,n)}};Lt.now=fn.now;var jt=class extends Lt{constructor(t,n=Lt.now){super(t,n),this.actions=[],this._active=!1}flush(t){let{actions:n}=this;if(this._active){n.push(t);return}let r;this._active=!0;do if(r=t.execute(t.state,t.delay))break;while(t=n.shift());if(this._active=!1,r){for(;t=n.shift();)t.unsubscribe();throw r}}};var lt=new jt(kt),au=lt;var Xn=class extends kt{constructor(t,n){super(t,n),this.scheduler=t,this.work=n}requestAsyncId(t,n,r=0){return r!==null&&r>0?super.requestAsyncId(t,n,r):(t.actions.push(this),t._scheduled||(t._scheduled=Pt.requestAnimationFrame(()=>t.flush(void 0))))}recycleAsyncId(t,n,r=0){var o;if(r!=null?r>0:this.delay>0)return super.recycleAsyncId(t,n,r);let{actions:i}=t;n!=null&&((o=i[i.length-1])===null||o===void 0?void 0:o.id)!==n&&(Pt.cancelAnimationFrame(n),t._scheduled=void 0)}};var er=class extends jt{flush(t){this._active=!0;let n=this._scheduled;this._scheduled=void 0;let{actions:r}=this,o;t=t||r.shift();do if(o=t.execute(t.state,t.delay))break;while((t=r[0])&&t.id===n&&r.shift());if(this._active=!1,o){for(;(t=r[0])&&t.id===n&&r.shift();)t.unsubscribe();throw o}}};var Nf=new er(Xn);var dt=new x(e=>e.complete());function tr(e){return e&&m(e.schedule)}function Zo(e){return e[e.length-1]}function nr(e){return m(Zo(e))?e.pop():void 0}function Ae(e){return tr(Zo(e))?e.pop():void 0}function uu(e,t){return typeof Zo(e)=="number"?e.pop():t}function lu(e,t,n,r){function o(i){return i instanceof n?i:new n(function(s){s(i)})}return new(n||(n=Promise))(function(i,s){function a(l){try{c(r.next(l))}catch(d){s(d)}}function u(l){try{c(r.throw(l))}catch(d){s(d)}}function c(l){l.done?i(l.value):o(l.value).then(a,u)}c((r=r.apply(e,t||[])).next())})}function cu(e){var t=typeof Symbol=="function"&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&typeof e.length=="number")return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function ft(e){return this instanceof ft?(this.v=e,this):new ft(e)}function du(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r=n.apply(e,t||[]),o,i=[];return o=Object.create((typeof AsyncIterator=="function"?AsyncIterator:Object).prototype),a("next"),a("throw"),a("return",s),o[Symbol.asyncIterator]=function(){return this},o;function s(f){return function(h){return Promise.resolve(h).then(f,d)}}function a(f,h){r[f]&&(o[f]=function(g){return new Promise(function(M,b){i.push([f,g,M,b])>1||u(f,g)})},h&&(o[f]=h(o[f])))}function u(f,h){try{c(r[f](h))}catch(g){p(i[0][3],g)}}function c(f){f.value instanceof ft?Promise.resolve(f.value.v).then(l,d):p(i[0][2],f)}function l(f){u("next",f)}function d(f){u("throw",f)}function p(f,h){f(h),i.shift(),i.length&&u(i[0][0],i[0][1])}}function fu(e){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var t=e[Symbol.asyncIterator],n;return t?t.call(e):(e=typeof cu=="function"?cu(e):e[Symbol.iterator](),n={},r("next"),r("throw"),r("return"),n[Symbol.asyncIterator]=function(){return this},n);function r(i){n[i]=e[i]&&function(s){return new Promise(function(a,u){s=e[i](s),o(a,u,s.done,s.value)})}}function o(i,s,a,u){Promise.resolve(u).then(function(c){i({value:c,done:a})},s)}}var Vt=e=>e&&typeof e.length=="number"&&typeof e!="function";function rr(e){return m(e?.then)}function or(e){return m(e[Rt])}function ir(e){return Symbol.asyncIterator&&m(e?.[Symbol.asyncIterator])}function sr(e){return new TypeError(`You provided ${e!==null&&typeof e=="object"?"an invalid object":`'${e}'`} where a stream was expected. You can provide an Observable, Promise, ReadableStream, Array, AsyncIterable, or Iterable.`)}function Af(){return typeof Symbol!="function"||!Symbol.iterator?"@@iterator":Symbol.iterator}var ar=Af();function ur(e){return m(e?.[ar])}function cr(e){return du(this,arguments,function*(){let n=e.getReader();try{for(;;){let{value:r,done:o}=yield ft(n.read());if(o)return yield ft(void 0);yield yield ft(r)}}finally{n.releaseLock()}})}function lr(e){return m(e?.getReader)}function T(e){if(e instanceof x)return e;if(e!=null){if(or(e))return Of(e);if(Vt(e))return Ff(e);if(rr(e))return Rf(e);if(ir(e))return pu(e);if(ur(e))return Pf(e);if(lr(e))return kf(e)}throw sr(e)}function Of(e){return new x(t=>{let n=e[Rt]();if(m(n.subscribe))return n.subscribe(t);throw new TypeError("Provided object does not correctly implement Symbol.observable")})}function Ff(e){return new x(t=>{for(let n=0;n<e.length&&!t.closed;n++)t.next(e[n]);t.complete()})}function Rf(e){return new x(t=>{e.then(n=>{t.closed||(t.next(n),t.complete())},n=>t.error(n)).then(null,Qn)})}function Pf(e){return new x(t=>{for(let n of e)if(t.next(n),t.closed)return;t.complete()})}function pu(e){return new x(t=>{Lf(e,t).catch(n=>t.error(n))})}function kf(e){return pu(cr(e))}function Lf(e,t){var n,r,o,i;return lu(this,void 0,void 0,function*(){try{for(n=fu(e);r=yield n.next(),!r.done;){let s=r.value;if(t.next(s),t.closed)return}}catch(s){o={error:s}}finally{try{r&&!r.done&&(i=n.return)&&(yield i.call(n))}finally{if(o)throw o.error}}t.complete()})}function re(e,t,n,r=0,o=!1){let i=t.schedule(function(){n(),o?e.add(this.schedule(null,r)):this.unsubscribe()},r);if(e.add(i),!o)return i}function dr(e,t=0){return w((n,r)=>{n.subscribe(D(r,o=>re(r,e,()=>r.next(o),t),()=>re(r,e,()=>r.complete(),t),o=>re(r,e,()=>r.error(o),t)))})}function fr(e,t=0){return w((n,r)=>{r.add(e.schedule(()=>n.subscribe(r),t))})}function hu(e,t){return T(e).pipe(fr(t),dr(t))}function gu(e,t){return T(e).pipe(fr(t),dr(t))}function mu(e,t){return new x(n=>{let r=0;return t.schedule(function(){r===e.length?n.complete():(n.next(e[r++]),n.closed||this.schedule())})})}function yu(e,t){return new x(n=>{let r;return re(n,t,()=>{r=e[ar](),re(n,t,()=>{let o,i;try{({value:o,done:i}=r.next())}catch(s){n.error(s);return}i?n.complete():n.next(o)},0,!0)}),()=>m(r?.return)&&r.return()})}function pr(e,t){if(!e)throw new Error("Iterable cannot be null");return new x(n=>{re(n,t,()=>{let r=e[Symbol.asyncIterator]();re(n,t,()=>{r.next().then(o=>{o.done?n.complete():n.next(o.value)})},0,!0)})})}function Du(e,t){return pr(cr(e),t)}function vu(e,t){if(e!=null){if(or(e))return hu(e,t);if(Vt(e))return mu(e,t);if(rr(e))return gu(e,t);if(ir(e))return pr(e,t);if(ur(e))return yu(e,t);if(lr(e))return Du(e,t)}throw sr(e)}function Oe(e,t){return t?vu(e,t):T(e)}function jf(...e){let t=Ae(e);return Oe(e,t)}function Vf(e,t){let n=m(e)?e:()=>e,r=o=>o.error(n());return new x(t?o=>t.schedule(r,0,o):r)}function Bf(e){return!!e&&(e instanceof x||m(e.lift)&&m(e.subscribe))}var Ve=At(e=>function(){e(this),this.name="EmptyError",this.message="no elements in sequence"});function $f(e,t){let n=typeof t=="object";return new Promise((r,o)=>{let i=new Ie({next:s=>{r(s),i.unsubscribe()},error:o,complete:()=>{n?r(t.defaultValue):o(new Ve)}});e.subscribe(i)})}function wu(e){return e instanceof Date&&!isNaN(e)}function Fe(e,t){return w((n,r)=>{let o=0;n.subscribe(D(r,i=>{r.next(e.call(t,i,o++))}))})}var{isArray:Hf}=Array;function Uf(e,t){return Hf(t)?e(...t):e(t)}function Bt(e){return Fe(t=>Uf(e,t))}var{isArray:Gf}=Array,{getPrototypeOf:zf,prototype:Wf,keys:qf}=Object;function hr(e){if(e.length===1){let t=e[0];if(Gf(t))return{args:t,keys:null};if(Yf(t)){let n=qf(t);return{args:n.map(r=>t[r]),keys:n}}}return{args:e,keys:null}}function Yf(e){return e&&typeof e=="object"&&zf(e)===Wf}function gr(e,t){return e.reduce((n,r,o)=>(n[r]=t[o],n),{})}function Qf(...e){let t=Ae(e),n=nr(e),{args:r,keys:o}=hr(e);if(r.length===0)return Oe([],t);let i=new x(Zf(r,t,o?s=>gr(o,s):X));return n?i.pipe(Bt(n)):i}function Zf(e,t,n=X){return r=>{Iu(t,()=>{let{length:o}=e,i=new Array(o),s=o,a=o;for(let u=0;u<o;u++)Iu(t,()=>{let c=Oe(e[u],t),l=!1;c.subscribe(D(r,d=>{i[u]=d,l||(l=!0,a--),a||r.next(n(i.slice()))},()=>{--s||r.complete()}))},r)},r)}}function Iu(e,t,n){e?re(n,e,t):t()}function Eu(e,t,n,r,o,i,s,a){let u=[],c=0,l=0,d=!1,p=()=>{d&&!u.length&&!c&&t.complete()},f=g=>c<r?h(g):u.push(g),h=g=>{i&&t.next(g),c++;let M=!1;T(n(g,l++)).subscribe(D(t,b=>{o?.(b),i?f(b):t.next(b)},()=>{M=!0},void 0,()=>{if(M)try{for(c--;u.length&&c<r;){let b=u.shift();s?re(t,s,()=>h(b)):h(b)}p()}catch(b){t.error(b)}}))};return e.subscribe(D(t,f,()=>{d=!0,p()})),()=>{a?.()}}function Ee(e,t,n=1/0){return m(t)?Ee((r,o)=>Fe((i,s)=>t(r,i,o,s))(T(e(r,o))),n):(typeof t=="number"&&(n=t),w((r,o)=>Eu(r,o,e,n)))}function gn(e=1/0){return Ee(X,e)}function Cu(){return gn(1)}function $t(...e){return Cu()(Oe(e,Ae(e)))}function Kf(e){return new x(t=>{T(e()).subscribe(t)})}function Jf(...e){let t=nr(e),{args:n,keys:r}=hr(e),o=new x(i=>{let{length:s}=n;if(!s){i.complete();return}let a=new Array(s),u=s,c=s;for(let l=0;l<s;l++){let d=!1;T(n[l]).subscribe(D(i,p=>{d||(d=!0,c--),a[l]=p},()=>u--,void 0,()=>{(!u||!d)&&(c||i.next(r?gr(r,a):a),i.complete())}))}});return t?o.pipe(Bt(t)):o}var Xf=["addListener","removeListener"],ep=["addEventListener","removeEventListener"],tp=["on","off"];function Ko(e,t,n,r){if(m(n)&&(r=n,n=void 0),r)return Ko(e,t,n).pipe(Bt(r));let[o,i]=op(e)?ep.map(s=>a=>e[s](t,a,n)):np(e)?Xf.map(bu(e,t)):rp(e)?tp.map(bu(e,t)):[];if(!o&&Vt(e))return Ee(s=>Ko(s,t,n))(T(e));if(!o)throw new TypeError("Invalid event target");return new x(s=>{let a=(...u)=>s.next(1<u.length?u:u[0]);return o(a),()=>i(a)})}function bu(e,t){return n=>r=>e[n](t,r)}function np(e){return m(e.addListener)&&m(e.removeListener)}function rp(e){return m(e.on)&&m(e.off)}function op(e){return m(e.addEventListener)&&m(e.removeEventListener)}function mn(e=0,t,n=au){let r=-1;return t!=null&&(tr(t)?n=t:r=t),new x(o=>{let i=wu(e)?+e-n.now():e;i<0&&(i=0);let s=0;return n.schedule(function(){o.closed||(o.next(s++),0<=r?this.schedule(void 0,r):o.complete())},i)})}function ip(...e){let t=Ae(e),n=uu(e,1/0),r=e;return r.length?r.length===1?T(r[0]):gn(n)(Oe(r,t)):dt}function pt(e,t){return w((n,r)=>{let o=0;n.subscribe(D(r,i=>e.call(t,i,o++)&&r.next(i)))})}function _u(e){return w((t,n)=>{let r=!1,o=null,i=null,s=!1,a=()=>{if(i?.unsubscribe(),i=null,r){r=!1;let c=o;o=null,n.next(c)}s&&n.complete()},u=()=>{i=null,s&&n.complete()};t.subscribe(D(n,c=>{r=!0,o=c,i||T(e(c)).subscribe(i=D(n,a,u))},()=>{s=!0,(!r||!i||i.closed)&&n.complete()}))})}function sp(e,t=lt){return _u(()=>mn(e,t))}function Jo(e){return w((t,n)=>{let r=null,o=!1,i;r=t.subscribe(D(n,void 0,void 0,s=>{i=T(e(s,Jo(e)(t))),r?(r.unsubscribe(),r=null,i.subscribe(n)):o=!0})),o&&(r.unsubscribe(),r=null,i.subscribe(n))})}function Mu(e,t,n,r,o){return(i,s)=>{let a=n,u=t,c=0;i.subscribe(D(s,l=>{let d=c++;u=a?e(u,l,d):(a=!0,l),r&&s.next(u)},o&&(()=>{a&&s.next(u),s.complete()})))}}function ap(e,t){return m(t)?Ee(e,t,1):Ee(e,1)}function up(e,t=lt){return w((n,r)=>{let o=null,i=null,s=null,a=()=>{if(o){o.unsubscribe(),o=null;let c=i;i=null,r.next(c)}};function u(){let c=s+e,l=t.now();if(l<c){o=this.schedule(void 0,c-l),r.add(o);return}a()}n.subscribe(D(r,c=>{i=c,s=t.now(),o||(o=t.schedule(u,e),r.add(o))},()=>{a(),r.complete()},void 0,()=>{i=o=null}))})}function yn(e){return w((t,n)=>{let r=!1;t.subscribe(D(n,o=>{r=!0,n.next(o)},()=>{r||n.next(e),n.complete()}))})}function Ht(e){return e<=0?()=>dt:w((t,n)=>{let r=0;t.subscribe(D(n,o=>{++r<=e&&(n.next(o),e<=r&&n.complete())}))})}function xu(){return w((e,t)=>{e.subscribe(D(t,at))})}function Xo(e){return Fe(()=>e)}function ei(e,t){return t?n=>$t(t.pipe(Ht(1),xu()),n.pipe(ei(e))):Ee((n,r)=>T(e(n,r)).pipe(Ht(1),Xo(n)))}function cp(e,t=lt){let n=mn(e,t);return ei(()=>n)}function lp(e,t=X){return e=e??dp,w((n,r)=>{let o,i=!0;n.subscribe(D(r,s=>{let a=t(s);(i||!e(o,a))&&(i=!1,o=a,r.next(s))}))})}function dp(e,t){return e===t}function mr(e=fp){return w((t,n)=>{let r=!1;t.subscribe(D(n,o=>{r=!0,n.next(o)},()=>r?n.complete():n.error(e())))})}function fp(){return new Ve}function pp(e){return w((t,n)=>{try{t.subscribe(n)}finally{n.add(e)}})}function Su(e,t){let n=arguments.length>=2;return r=>r.pipe(e?pt((o,i)=>e(o,i,r)):X,Ht(1),n?yn(t):mr(()=>new Ve))}function ti(e){return e<=0?()=>dt:w((t,n)=>{let r=[];t.subscribe(D(n,o=>{r.push(o),e<r.length&&r.shift()},()=>{for(let o of r)n.next(o);n.complete()},void 0,()=>{r=null}))})}function hp(e,t){let n=arguments.length>=2;return r=>r.pipe(e?pt((o,i)=>e(o,i,r)):X,ti(1),n?yn(t):mr(()=>new Ve))}function gp(e,t){return w(Mu(e,t,arguments.length>=2,!0))}function ri(e={}){let{connector:t=()=>new ge,resetOnError:n=!0,resetOnComplete:r=!0,resetOnRefCountZero:o=!0}=e;return i=>{let s,a,u,c=0,l=!1,d=!1,p=()=>{a?.unsubscribe(),a=void 0},f=()=>{p(),s=u=void 0,l=d=!1},h=()=>{let g=s;f(),g?.unsubscribe()};return w((g,M)=>{c++,!d&&!l&&p();let b=u=u??t();M.add(()=>{c--,c===0&&!d&&!l&&(a=ni(h,o))}),b.subscribe(M),!s&&c>0&&(s=new Ie({next:z=>b.next(z),error:z=>{d=!0,p(),a=ni(f,n,z),b.error(z)},complete:()=>{l=!0,p(),a=ni(f,r),b.complete()}}),T(g).subscribe(s))})(i)}}function ni(e,t,...n){if(t===!0){e();return}if(t===!1)return;let r=new Ie({next:()=>{r.unsubscribe(),e()}});return T(t(...n)).subscribe(r)}function mp(e,t,n){let r,o=!1;return e&&typeof e=="object"?{bufferSize:r=1/0,windowTime:t=1/0,refCount:o=!1,scheduler:n}=e:r=e??1/0,ri({connector:()=>new pn(r,t,n),resetOnError:!0,resetOnComplete:!1,resetOnRefCountZero:o})}function yp(e){return pt((t,n)=>e<=n)}function Tu(...e){let t=Ae(e);return w((n,r)=>{(t?$t(e,n,t):$t(e,n)).subscribe(r)})}function Dp(e,t){return w((n,r)=>{let o=null,i=0,s=!1,a=()=>s&&!o&&r.complete();n.subscribe(D(r,u=>{o?.unsubscribe();let c=0,l=i++;T(e(u,l)).subscribe(o=D(r,d=>r.next(t?t(u,d,l,c++):d),()=>{o=null,a()}))},()=>{s=!0,a()}))})}function vp(e){return w((t,n)=>{T(e).subscribe(D(n,()=>n.complete(),at)),!n.closed&&t.subscribe(n)})}function wp(e,t=!1){return w((n,r)=>{let o=0;n.subscribe(D(r,i=>{let s=e(i,o++);(s||t)&&r.next(i),!s&&r.complete()}))})}function Ip(e,t,n){let r=m(e)||t||n?{next:e,error:t,complete:n}:e;return r?w((o,i)=>{var s;(s=r.subscribe)===null||s===void 0||s.call(r);let a=!0;o.subscribe(D(i,u=>{var c;(c=r.next)===null||c===void 0||c.call(r,u),i.next(u)},()=>{var u;a=!1,(u=r.complete)===null||u===void 0||u.call(r),i.complete()},u=>{var c;a=!1,(c=r.error)===null||c===void 0||c.call(r,u),i.error(u)},()=>{var u,c;a&&((u=r.unsubscribe)===null||u===void 0||u.call(r)),(c=r.finalize)===null||c===void 0||c.call(r)}))}):X}var Dc="https://g.co/ng/security#xss",C=class extends Error{constructor(t,n){super(vc(t,n)),this.code=t}};function vc(e,t){return`${`NG0${Math.abs(e)}`}${t?": "+t:""}`}function Rn(e){return{toString:e}.toString()}var yr="__parameters__";function Ep(e){return function(...n){if(e){let r=e(...n);for(let o in r)this[o]=r[o]}}}function wc(e,t,n){return Rn(()=>{let r=Ep(t);function o(...i){if(this instanceof o)return r.apply(this,i),this;let s=new o(...i);return a.annotation=s,a;function a(u,c,l){let d=u.hasOwnProperty(yr)?u[yr]:Object.defineProperty(u,yr,{value:[]})[yr];for(;d.length<=l;)d.push(null);return(d[l]=d[l]||[]).push(s),u}}return n&&(o.prototype=Object.create(n.prototype)),o.prototype.ngMetadataName=e,o.annotationCls=o,o})}var ue=globalThis;function R(e){for(let t in e)if(e[t]===R)return t;throw Error("Could not find renamed property on target object.")}function Cp(e,t){for(let n in t)t.hasOwnProperty(n)&&!e.hasOwnProperty(n)&&(e[n]=t[n])}function te(e){if(typeof e=="string")return e;if(Array.isArray(e))return"["+e.map(te).join(", ")+"]";if(e==null)return""+e;if(e.overriddenName)return`${e.overriddenName}`;if(e.name)return`${e.name}`;let t=e.toString();if(t==null)return""+t;let n=t.indexOf(`
`);return n===-1?t:t.substring(0,n)}function wi(e,t){return e==null||e===""?t===null?"":t:t==null||t===""?e:e+" "+t}var bp=R({__forward_ref__:R});function Ic(e){return e.__forward_ref__=Ic,e.toString=function(){return te(this())},e}function ee(e){return Ec(e)?e():e}function Ec(e){return typeof e=="function"&&e.hasOwnProperty(bp)&&e.__forward_ref__===Ic}function k(e){return{token:e.token,providedIn:e.providedIn||null,factory:e.factory,value:void 0}}function Rs(e){return{providers:e.providers||[],imports:e.imports||[]}}function eo(e){return Nu(e,Cc)||Nu(e,bc)}function Zx(e){return eo(e)!==null}function Nu(e,t){return e.hasOwnProperty(t)?e[t]:null}function _p(e){let t=e&&(e[Cc]||e[bc]);return t||null}function Au(e){return e&&(e.hasOwnProperty(Ou)||e.hasOwnProperty(Mp))?e[Ou]:null}var Cc=R({\u0275prov:R}),Ou=R({\u0275inj:R}),bc=R({ngInjectableDef:R}),Mp=R({ngInjectorDef:R}),O=class{constructor(t,n){this._desc=t,this.ngMetadataName="InjectionToken",this.\u0275prov=void 0,typeof n=="number"?this.__NG_ELEMENT_ID__=n:n!==void 0&&(this.\u0275prov=k({token:this,providedIn:n.providedIn||"root",factory:n.factory}))}get multi(){return this}toString(){return`InjectionToken ${this._desc}`}};function _c(e){return e&&!!e.\u0275providers}var xp=R({\u0275cmp:R}),Sp=R({\u0275dir:R}),Tp=R({\u0275pipe:R}),Np=R({\u0275mod:R}),Nr=R({\u0275fac:R}),Dn=R({__NG_ELEMENT_ID__:R}),Fu=R({__NG_ENV_ID__:R});function ce(e){return typeof e=="string"?e:e==null?"":String(e)}function Ap(e){return typeof e=="function"?e.name||e.toString():typeof e=="object"&&e!=null&&typeof e.type=="function"?e.type.name||e.type.toString():ce(e)}function Op(e,t){let n=t?`. Dependency path: ${t.join(" > ")} > ${e}`:"";throw new C(-200,e)}function Ps(e,t){throw new C(-201,!1)}var S=function(e){return e[e.Default=0]="Default",e[e.Host=1]="Host",e[e.Self=2]="Self",e[e.SkipSelf=4]="SkipSelf",e[e.Optional=8]="Optional",e}(S||{}),Ii;function Mc(){return Ii}function oe(e){let t=Ii;return Ii=e,t}function xc(e,t,n){let r=eo(e);if(r&&r.providedIn=="root")return r.value===void 0?r.value=r.factory():r.value;if(n&S.Optional)return null;if(t!==void 0)return t;Ps(e,"Injector")}var Fp={},wn=Fp,Ei="__NG_DI_FLAG__",Ar="ngTempTokenPath",Rp="ngTokenPath",Pp=/\n/gm,kp="\u0275",Ru="__source",qt;function Lp(){return qt}function Qe(e){let t=qt;return qt=e,t}function jp(e,t=S.Default){if(qt===void 0)throw new C(-203,!1);return qt===null?xc(e,void 0,t):qt.get(e,t&S.Optional?null:void 0,t)}function H(e,t=S.Default){return(Mc()||jp)(ee(e),t)}function F(e,t=S.Default){return H(e,to(t))}function to(e){return typeof e>"u"||typeof e=="number"?e:0|(e.optional&&8)|(e.host&&1)|(e.self&&2)|(e.skipSelf&&4)}function Ci(e){let t=[];for(let n=0;n<e.length;n++){let r=ee(e[n]);if(Array.isArray(r)){if(r.length===0)throw new C(900,!1);let o,i=S.Default;for(let s=0;s<r.length;s++){let a=r[s],u=Vp(a);typeof u=="number"?u===-1?o=a.token:i|=u:o=a}t.push(H(o,i))}else t.push(H(r))}return t}function Sc(e,t){return e[Ei]=t,e.prototype[Ei]=t,e}function Vp(e){return e[Ei]}function Bp(e,t,n,r){let o=e[Ar];throw t[Ru]&&o.unshift(t[Ru]),e.message=$p(`
`+e.message,o,n,r),e[Rp]=o,e[Ar]=null,e}function $p(e,t,n,r=null){e=e&&e.charAt(0)===`
`&&e.charAt(1)==kp?e.slice(2):e;let o=te(t);if(Array.isArray(t))o=t.map(te).join(" -> ");else if(typeof t=="object"){let i=[];for(let s in t)if(t.hasOwnProperty(s)){let a=t[s];i.push(s+":"+(typeof a=="string"?JSON.stringify(a):te(a)))}o=`{${i.join(", ")}}`}return`${n}${r?"("+r+")":""}[${o}]: ${e.replace(Pp,`
  `)}`}var Tc=Sc(wc("Optional"),8);var Nc=Sc(wc("SkipSelf"),4);function yt(e,t){let n=e.hasOwnProperty(Nr);return n?e[Nr]:null}function Hp(e,t,n){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let o=e[r],i=t[r];if(n&&(o=n(o),i=n(i)),i!==o)return!1}return!0}function Up(e){return e.flat(Number.POSITIVE_INFINITY)}function ks(e,t){e.forEach(n=>Array.isArray(n)?ks(n,t):t(n))}function Ac(e,t,n){t>=e.length?e.push(n):e.splice(t,0,n)}function Or(e,t){return t>=e.length-1?e.pop():e.splice(t,1)[0]}function Gp(e,t){let n=[];for(let r=0;r<e;r++)n.push(t);return n}function zp(e,t,n,r){let o=e.length;if(o==t)e.push(n,r);else if(o===1)e.push(r,e[0]),e[0]=n;else{for(o--,e.push(e[o-1],e[o]);o>t;){let i=o-2;e[o]=e[i],o--}e[t]=n,e[t+1]=r}}function no(e,t,n){let r=Pn(e,t);return r>=0?e[r|1]=n:(r=~r,zp(e,r,t,n)),r}function oi(e,t){let n=Pn(e,t);if(n>=0)return e[n|1]}function Pn(e,t){return Wp(e,t,1)}function Wp(e,t,n){let r=0,o=e.length>>n;for(;o!==r;){let i=r+(o-r>>1),s=e[i<<n];if(t===s)return i<<n;s>t?o=i:r=i+1}return~(o<<n)}var Qt={},ie=[],Fr=new O(""),Oc=new O("",-1),Fc=new O(""),Rr=class{get(t,n=wn){if(n===wn){let r=new Error(`NullInjectorError: No provider for ${te(t)}!`);throw r.name="NullInjectorError",r}return n}},Rc=function(e){return e[e.OnPush=0]="OnPush",e[e.Default=1]="Default",e}(Rc||{}),In=function(e){return e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom",e}(In||{}),Ke=function(e){return e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform",e}(Ke||{});function qp(e,t,n){let r=e.length;for(;;){let o=e.indexOf(t,n);if(o===-1)return o;if(o===0||e.charCodeAt(o-1)<=32){let i=t.length;if(o+i===r||e.charCodeAt(o+i)<=32)return o}n=o+1}}function bi(e,t,n){let r=0;for(;r<n.length;){let o=n[r];if(typeof o=="number"){if(o!==0)break;r++;let i=n[r++],s=n[r++],a=n[r++];e.setAttribute(t,s,a,i)}else{let i=o,s=n[++r];Yp(i)?e.setProperty(t,i,s):e.setAttribute(t,i,s),r++}}return r}function Pc(e){return e===3||e===4||e===6}function Yp(e){return e.charCodeAt(0)===64}function En(e,t){if(!(t===null||t.length===0))if(e===null||e.length===0)e=t.slice();else{let n=-1;for(let r=0;r<t.length;r++){let o=t[r];typeof o=="number"?n=o:n===0||(n===-1||n===2?Pu(e,n,o,null,t[++r]):Pu(e,n,o,null,null))}}return e}function Pu(e,t,n,r,o){let i=0,s=e.length;if(t===-1)s=-1;else for(;i<e.length;){let a=e[i++];if(typeof a=="number"){if(a===t){s=-1;break}else if(a>t){s=i-1;break}}}for(;i<e.length;){let a=e[i];if(typeof a=="number")break;if(a===n){if(r===null){o!==null&&(e[i+1]=o);return}else if(r===e[i+1]){e[i+2]=o;return}}i++,r!==null&&i++,o!==null&&i++}s!==-1&&(e.splice(s,0,t),i=s+1),e.splice(i++,0,n),r!==null&&e.splice(i++,0,r),o!==null&&e.splice(i++,0,o)}var kc="ng-template";function Qp(e,t,n,r){let o=0;if(r){for(;o<t.length&&typeof t[o]=="string";o+=2)if(t[o]==="class"&&qp(t[o+1].toLowerCase(),n,0)!==-1)return!0}else if(Ls(e))return!1;if(o=t.indexOf(1,o),o>-1){let i;for(;++o<t.length&&typeof(i=t[o])=="string";)if(i.toLowerCase()===n)return!0}return!1}function Ls(e){return e.type===4&&e.value!==kc}function Zp(e,t,n){let r=e.type===4&&!n?kc:e.value;return t===r}function Kp(e,t,n){let r=4,o=e.attrs,i=o!==null?eh(o):0,s=!1;for(let a=0;a<t.length;a++){let u=t[a];if(typeof u=="number"){if(!s&&!Ce(r)&&!Ce(u))return!1;if(s&&Ce(u))continue;s=!1,r=u|r&1;continue}if(!s)if(r&4){if(r=2|r&1,u!==""&&!Zp(e,u,n)||u===""&&t.length===1){if(Ce(r))return!1;s=!0}}else if(r&8){if(o===null||!Qp(e,o,u,n)){if(Ce(r))return!1;s=!0}}else{let c=t[++a],l=Jp(u,o,Ls(e),n);if(l===-1){if(Ce(r))return!1;s=!0;continue}if(c!==""){let d;if(l>i?d="":d=o[l+1].toLowerCase(),r&2&&c!==d){if(Ce(r))return!1;s=!0}}}}return Ce(r)||s}function Ce(e){return(e&1)===0}function Jp(e,t,n,r){if(t===null)return-1;let o=0;if(r||!n){let i=!1;for(;o<t.length;){let s=t[o];if(s===e)return o;if(s===3||s===6)i=!0;else if(s===1||s===2){let a=t[++o];for(;typeof a=="string";)a=t[++o];continue}else{if(s===4)break;if(s===0){o+=4;continue}}o+=i?1:2}return-1}else return th(t,e)}function Lc(e,t,n=!1){for(let r=0;r<t.length;r++)if(Kp(e,t[r],n))return!0;return!1}function Xp(e){let t=e.attrs;if(t!=null){let n=t.indexOf(5);if(!(n&1))return t[n+1]}return null}function eh(e){for(let t=0;t<e.length;t++){let n=e[t];if(Pc(n))return t}return e.length}function th(e,t){let n=e.indexOf(4);if(n>-1)for(n++;n<e.length;){let r=e[n];if(typeof r=="number")return-1;if(r===t)return n;n++}return-1}function nh(e,t){e:for(let n=0;n<t.length;n++){let r=t[n];if(e.length===r.length){for(let o=0;o<e.length;o++)if(e[o]!==r[o])continue e;return!0}}return!1}function ku(e,t){return e?":not("+t.trim()+")":t}function rh(e){let t=e[0],n=1,r=2,o="",i=!1;for(;n<e.length;){let s=e[n];if(typeof s=="string")if(r&2){let a=e[++n];o+="["+s+(a.length>0?'="'+a+'"':"")+"]"}else r&8?o+="."+s:r&4&&(o+=" "+s);else o!==""&&!Ce(s)&&(t+=ku(i,o),o=""),r=s,i=i||!Ce(r);n++}return o!==""&&(t+=ku(i,o)),t}function oh(e){return e.map(rh).join(",")}function ih(e){let t=[],n=[],r=1,o=2;for(;r<e.length;){let i=e[r];if(typeof i=="string")o===2?i!==""&&t.push(i,e[++r]):o===8&&n.push(i);else{if(!Ce(o))break;o=i}r++}return{attrs:t,classes:n}}function Kx(e){return Rn(()=>{let t=$c(e),n=ln(it({},t),{decls:e.decls,vars:e.vars,template:e.template,consts:e.consts||null,ngContentSelectors:e.ngContentSelectors,onPush:e.changeDetection===Rc.OnPush,directiveDefs:null,pipeDefs:null,dependencies:t.standalone&&e.dependencies||null,getStandaloneInjector:null,signals:e.signals??!1,data:e.data||{},encapsulation:e.encapsulation||In.Emulated,styles:e.styles||ie,_:null,schemas:e.schemas||null,tView:null,id:""});Hc(n);let r=e.dependencies;return n.directiveDefs=ju(r,!1),n.pipeDefs=ju(r,!0),n.id=ch(n),n})}function sh(e){return Je(e)||jc(e)}function ah(e){return e!==null}function js(e){return Rn(()=>({type:e.type,bootstrap:e.bootstrap||ie,declarations:e.declarations||ie,imports:e.imports||ie,exports:e.exports||ie,transitiveCompileScopes:null,schemas:e.schemas||null,id:e.id||null}))}function Lu(e,t){if(e==null)return Qt;let n={};for(let r in e)if(e.hasOwnProperty(r)){let o=e[r],i,s,a=Ke.None;Array.isArray(o)?(a=o[0],i=o[1],s=o[2]??i):(i=o,s=o),t?(n[i]=a!==Ke.None?[r,a]:r,t[i]=s):n[i]=r}return n}function rn(e){return Rn(()=>{let t=$c(e);return Hc(t),t})}function on(e){return{type:e.type,name:e.name,factory:null,pure:e.pure!==!1,standalone:e.standalone===!0,onDestroy:e.type.prototype.ngOnDestroy||null}}function Je(e){return e[xp]||null}function jc(e){return e[Sp]||null}function Vc(e){return e[Tp]||null}function uh(e){let t=Je(e)||jc(e)||Vc(e);return t!==null?t.standalone:!1}function Bc(e,t){let n=e[Np]||null;if(!n&&t===!0)throw new Error(`Type ${te(e)} does not have '\u0275mod' property.`);return n}function $c(e){let t={};return{type:e.type,providersResolver:null,factory:null,hostBindings:e.hostBindings||null,hostVars:e.hostVars||0,hostAttrs:e.hostAttrs||null,contentQueries:e.contentQueries||null,declaredInputs:t,inputTransforms:null,inputConfig:e.inputs||Qt,exportAs:e.exportAs||null,standalone:e.standalone===!0,signals:e.signals===!0,selectors:e.selectors||ie,viewQuery:e.viewQuery||null,features:e.features||null,setInput:null,findHostDirectiveDefs:null,hostDirectives:null,inputs:Lu(e.inputs,t),outputs:Lu(e.outputs),debugInfo:null}}function Hc(e){e.features?.forEach(t=>t(e))}function ju(e,t){if(!e)return null;let n=t?Vc:sh;return()=>(typeof e=="function"?e():e).map(r=>n(r)).filter(ah)}function ch(e){let t=0,n=[e.selectors,e.ngContentSelectors,e.hostVars,e.hostAttrs,e.consts,e.vars,e.decls,e.encapsulation,e.standalone,e.signals,e.exportAs,JSON.stringify(e.inputs),JSON.stringify(e.outputs),Object.getOwnPropertyNames(e.type.prototype),!!e.contentQueries,!!e.viewQuery].join("|");for(let o of n)t=Math.imul(31,t)+o.charCodeAt(0)<<0;return t+=**********,"c"+t}function Jx(e){return{\u0275providers:e}}function lh(...e){return{\u0275providers:Uc(!0,e),\u0275fromNgModule:!0}}function Uc(e,...t){let n=[],r=new Set,o,i=s=>{n.push(s)};return ks(t,s=>{let a=s;_i(a,i,[],r)&&(o||=[],o.push(a))}),o!==void 0&&Gc(o,i),n}function Gc(e,t){for(let n=0;n<e.length;n++){let{ngModule:r,providers:o}=e[n];Vs(o,i=>{t(i,r)})}}function _i(e,t,n,r){if(e=ee(e),!e)return!1;let o=null,i=Au(e),s=!i&&Je(e);if(!i&&!s){let u=e.ngModule;if(i=Au(u),i)o=u;else return!1}else{if(s&&!s.standalone)return!1;o=e}let a=r.has(o);if(s){if(a)return!1;if(r.add(o),s.dependencies){let u=typeof s.dependencies=="function"?s.dependencies():s.dependencies;for(let c of u)_i(c,t,n,r)}}else if(i){if(i.imports!=null&&!a){r.add(o);let c;try{ks(i.imports,l=>{_i(l,t,n,r)&&(c||=[],c.push(l))})}finally{}c!==void 0&&Gc(c,t)}if(!a){let c=yt(o)||(()=>new o);t({provide:o,useFactory:c,deps:ie},o),t({provide:Fc,useValue:o,multi:!0},o),t({provide:Fr,useValue:()=>H(o),multi:!0},o)}let u=i.providers;if(u!=null&&!a){let c=e;Vs(u,l=>{t(l,c)})}}else return!1;return o!==e&&e.providers!==void 0}function Vs(e,t){for(let n of e)_c(n)&&(n=n.\u0275providers),Array.isArray(n)?Vs(n,t):t(n)}var dh=R({provide:String,useValue:R});function zc(e){return e!==null&&typeof e=="object"&&dh in e}function fh(e){return!!(e&&e.useExisting)}function ph(e){return!!(e&&e.useFactory)}function Zt(e){return typeof e=="function"}function hh(e){return!!e.useClass}var Wc=new O(""),br={},gh={},ii;function Bs(){return ii===void 0&&(ii=new Rr),ii}var Xe=class{},Cn=class extends Xe{get destroyed(){return this._destroyed}constructor(t,n,r,o){super(),this.parent=n,this.source=r,this.scopes=o,this.records=new Map,this._ngOnDestroyHooks=new Set,this._onDestroyHooks=[],this._destroyed=!1,xi(t,s=>this.processProvider(s)),this.records.set(Oc,Ut(void 0,this)),o.has("environment")&&this.records.set(Xe,Ut(void 0,this));let i=this.records.get(Wc);i!=null&&typeof i.value=="string"&&this.scopes.add(i.value),this.injectorDefTypes=new Set(this.get(Fc,ie,S.Self))}destroy(){this.assertNotDestroyed(),this._destroyed=!0;let t=_(null);try{for(let r of this._ngOnDestroyHooks)r.ngOnDestroy();let n=this._onDestroyHooks;this._onDestroyHooks=[];for(let r of n)r()}finally{this.records.clear(),this._ngOnDestroyHooks.clear(),this.injectorDefTypes.clear(),_(t)}}onDestroy(t){return this.assertNotDestroyed(),this._onDestroyHooks.push(t),()=>this.removeOnDestroy(t)}runInContext(t){this.assertNotDestroyed();let n=Qe(this),r=oe(void 0),o;try{return t()}finally{Qe(n),oe(r)}}get(t,n=wn,r=S.Default){if(this.assertNotDestroyed(),t.hasOwnProperty(Fu))return t[Fu](this);r=to(r);let o,i=Qe(this),s=oe(void 0);try{if(!(r&S.SkipSelf)){let u=this.records.get(t);if(u===void 0){let c=wh(t)&&eo(t);c&&this.injectableDefInScope(c)?u=Ut(Mi(t),br):u=null,this.records.set(t,u)}if(u!=null)return this.hydrate(t,u)}let a=r&S.Self?Bs():this.parent;return n=r&S.Optional&&n===wn?null:n,a.get(t,n)}catch(a){if(a.name==="NullInjectorError"){if((a[Ar]=a[Ar]||[]).unshift(te(t)),i)throw a;return Bp(a,t,"R3InjectorError",this.source)}else throw a}finally{oe(s),Qe(i)}}resolveInjectorInitializers(){let t=_(null),n=Qe(this),r=oe(void 0),o;try{let i=this.get(Fr,ie,S.Self);for(let s of i)s()}finally{Qe(n),oe(r),_(t)}}toString(){let t=[],n=this.records;for(let r of n.keys())t.push(te(r));return`R3Injector[${t.join(", ")}]`}assertNotDestroyed(){if(this._destroyed)throw new C(205,!1)}processProvider(t){t=ee(t);let n=Zt(t)?t:ee(t&&t.provide),r=yh(t);if(!Zt(t)&&t.multi===!0){let o=this.records.get(n);o||(o=Ut(void 0,br,!0),o.factory=()=>Ci(o.multi),this.records.set(n,o)),n=t,o.multi.push(t)}this.records.set(n,r)}hydrate(t,n){let r=_(null);try{return n.value===br&&(n.value=gh,n.value=n.factory()),typeof n.value=="object"&&n.value&&vh(n.value)&&this._ngOnDestroyHooks.add(n.value),n.value}finally{_(r)}}injectableDefInScope(t){if(!t.providedIn)return!1;let n=ee(t.providedIn);return typeof n=="string"?n==="any"||this.scopes.has(n):this.injectorDefTypes.has(n)}removeOnDestroy(t){let n=this._onDestroyHooks.indexOf(t);n!==-1&&this._onDestroyHooks.splice(n,1)}};function Mi(e){let t=eo(e),n=t!==null?t.factory:yt(e);if(n!==null)return n;if(e instanceof O)throw new C(204,!1);if(e instanceof Function)return mh(e);throw new C(204,!1)}function mh(e){if(e.length>0)throw new C(204,!1);let n=_p(e);return n!==null?()=>n.factory(e):()=>new e}function yh(e){if(zc(e))return Ut(void 0,e.useValue);{let t=qc(e);return Ut(t,br)}}function qc(e,t,n){let r;if(Zt(e)){let o=ee(e);return yt(o)||Mi(o)}else if(zc(e))r=()=>ee(e.useValue);else if(ph(e))r=()=>e.useFactory(...Ci(e.deps||[]));else if(fh(e))r=()=>H(ee(e.useExisting));else{let o=ee(e&&(e.useClass||e.provide));if(Dh(e))r=()=>new o(...Ci(e.deps));else return yt(o)||Mi(o)}return r}function Ut(e,t,n=!1){return{factory:e,value:t,multi:n?[]:void 0}}function Dh(e){return!!e.deps}function vh(e){return e!==null&&typeof e=="object"&&typeof e.ngOnDestroy=="function"}function wh(e){return typeof e=="function"||typeof e=="object"&&e instanceof O}function xi(e,t){for(let n of e)Array.isArray(n)?xi(n,t):n&&_c(n)?xi(n.\u0275providers,t):t(n)}function Ih(e,t){e instanceof Cn&&e.assertNotDestroyed();let n,r=Qe(e),o=oe(void 0);try{return t()}finally{Qe(r),oe(o)}}function Yc(){return Mc()!==void 0||Lp()!=null}function Eh(e){if(!Yc())throw new C(-203,!1)}function Ch(e){let t=ue.ng;if(t&&t.\u0275compilerFacade)return t.\u0275compilerFacade;throw new Error("JIT compiler unavailable")}function bh(e){return typeof e=="function"}var Le=0,I=1,v=2,q=3,Me=4,de=5,Kt=6,bn=7,K=8,Jt=9,xe=10,j=11,_n=12,Vu=13,sn=14,me=15,kn=16,Gt=17,Be=18,ro=19,Qc=20,Ze=21,si=22,Dt=23,Y=25,$s=1;var vt=7,Pr=8,Xt=9,J=10,Hs=function(e){return e[e.None=0]="None",e[e.HasTransplantedViews=2]="HasTransplantedViews",e}(Hs||{});function gt(e){return Array.isArray(e)&&typeof e[$s]=="object"}function Ue(e){return Array.isArray(e)&&e[$s]===!0}function Us(e){return(e.flags&4)!==0}function oo(e){return e.componentOffset>-1}function io(e){return(e.flags&1)===1}function $e(e){return!!e.template}function _h(e){return(e[v]&512)!==0}var Si=class{constructor(t,n,r){this.previousValue=t,this.currentValue=n,this.firstChange=r}isFirstChange(){return this.firstChange}};function Zc(e,t,n,r){t!==null?t.applyValueToInputSignal(t,r):e[n]=r}function Gs(){return Kc}function Kc(e){return e.type.prototype.ngOnChanges&&(e.setInput=xh),Mh}Gs.ngInherit=!0;function Mh(){let e=Xc(this),t=e?.current;if(t){let n=e.previous;if(n===Qt)e.previous=t;else for(let r in t)n[r]=t[r];e.current=null,this.ngOnChanges(t)}}function xh(e,t,n,r,o){let i=this.declaredInputs[r],s=Xc(e)||Sh(e,{previous:Qt,current:null}),a=s.current||(s.current={}),u=s.previous,c=u[i];a[i]=new Si(c&&c.currentValue,n,u===Qt),Zc(e,t,o,n)}var Jc="__ngSimpleChanges__";function Xc(e){return e[Jc]||null}function Sh(e,t){return e[Jc]=t}var Bu=null;var Re=function(e,t,n){Bu?.(e,t,n)},el="svg",Th="math",Nh=!1;function Ah(){return Nh}function ke(e){for(;Array.isArray(e);)e=e[Le];return e}function Oh(e){for(;Array.isArray(e);){if(typeof e[$s]=="object")return e;e=e[Le]}return null}function tl(e,t){return ke(t[e])}function ye(e,t){return ke(t[e.index])}function zs(e,t){return e.data[t]}function Ws(e,t){return e[t]}function nt(e,t){let n=t[e];return gt(n)?n:n[Le]}function Fh(e){return(e[v]&4)===4}function qs(e){return(e[v]&128)===128}function Rh(e){return Ue(e[q])}function en(e,t){return t==null?null:e[t]}function nl(e){e[Gt]=0}function Ph(e){e[v]&1024||(e[v]|=1024,qs(e)&&Mn(e))}function kh(e,t){for(;e>0;)t=t[sn],e--;return t}function Ys(e){return!!(e[v]&9216||e[Dt]?.dirty)}function Ti(e){e[xe].changeDetectionScheduler?.notify(1),Ys(e)?Mn(e):e[v]&64&&(Ah()?(e[v]|=1024,Mn(e)):e[xe].changeDetectionScheduler?.notify())}function Mn(e){e[xe].changeDetectionScheduler?.notify();let t=xn(e);for(;t!==null&&!(t[v]&8192||(t[v]|=8192,!qs(t)));)t=xn(t)}function rl(e,t){if((e[v]&256)===256)throw new C(911,!1);e[Ze]===null&&(e[Ze]=[]),e[Ze].push(t)}function Lh(e,t){if(e[Ze]===null)return;let n=e[Ze].indexOf(t);n!==-1&&e[Ze].splice(n,1)}function xn(e){let t=e[q];return Ue(t)?t[q]:t}var E={lFrame:ll(null),bindingsEnabled:!0,skipHydrationRootTNode:null};function jh(){return E.lFrame.elementDepthCount}function Vh(){E.lFrame.elementDepthCount++}function Bh(){E.lFrame.elementDepthCount--}function ol(){return E.bindingsEnabled}function il(){return E.skipHydrationRootTNode!==null}function $h(e){return E.skipHydrationRootTNode===e}function Hh(){E.skipHydrationRootTNode=null}function y(){return E.lFrame.lView}function V(){return E.lFrame.tView}function Xx(e){return E.lFrame.contextLView=e,e[K]}function eS(e){return E.lFrame.contextLView=null,e}function Q(){let e=sl();for(;e!==null&&e.type===64;)e=e.parent;return e}function sl(){return E.lFrame.currentTNode}function Uh(){let e=E.lFrame,t=e.currentTNode;return e.isParent?t:t.parent}function xt(e,t){let n=E.lFrame;n.currentTNode=e,n.isParent=t}function Qs(){return E.lFrame.isParent}function Zs(){E.lFrame.isParent=!1}function Gh(){return E.lFrame.contextLView}function Ln(){let e=E.lFrame,t=e.bindingRootIndex;return t===-1&&(t=e.bindingRootIndex=e.tView.bindingStartIndex),t}function Ks(){return E.lFrame.bindingIndex}function zh(e){return E.lFrame.bindingIndex=e}function rt(){return E.lFrame.bindingIndex++}function jn(e){let t=E.lFrame,n=t.bindingIndex;return t.bindingIndex=t.bindingIndex+e,n}function Wh(){return E.lFrame.inI18n}function qh(e,t){let n=E.lFrame;n.bindingIndex=n.bindingRootIndex=e,Ni(t)}function Yh(){return E.lFrame.currentDirectiveIndex}function Ni(e){E.lFrame.currentDirectiveIndex=e}function Js(e){let t=E.lFrame.currentDirectiveIndex;return t===-1?null:e[t]}function al(){return E.lFrame.currentQueryIndex}function Xs(e){E.lFrame.currentQueryIndex=e}function Qh(e){let t=e[I];return t.type===2?t.declTNode:t.type===1?e[de]:null}function ul(e,t,n){if(n&S.SkipSelf){let o=t,i=e;for(;o=o.parent,o===null&&!(n&S.Host);)if(o=Qh(i),o===null||(i=i[sn],o.type&10))break;if(o===null)return!1;t=o,e=i}let r=E.lFrame=cl();return r.currentTNode=t,r.lView=e,!0}function ea(e){let t=cl(),n=e[I];E.lFrame=t,t.currentTNode=n.firstChild,t.lView=e,t.tView=n,t.contextLView=e,t.bindingIndex=n.bindingStartIndex,t.inI18n=!1}function cl(){let e=E.lFrame,t=e===null?null:e.child;return t===null?ll(e):t}function ll(e){let t={currentTNode:null,isParent:!0,lView:null,tView:null,selectedIndex:-1,contextLView:null,elementDepthCount:0,currentNamespace:null,currentDirectiveIndex:-1,bindingRootIndex:-1,bindingIndex:-1,currentQueryIndex:0,parent:e,child:null,inI18n:!1};return e!==null&&(e.child=t),t}function dl(){let e=E.lFrame;return E.lFrame=e.parent,e.currentTNode=null,e.lView=null,e}var fl=dl;function ta(){let e=dl();e.isParent=!0,e.tView=null,e.selectedIndex=-1,e.contextLView=null,e.elementDepthCount=0,e.currentDirectiveIndex=-1,e.currentNamespace=null,e.bindingRootIndex=-1,e.bindingIndex=-1,e.currentQueryIndex=0}function Zh(e){return(E.lFrame.contextLView=kh(e,E.lFrame.contextLView))[K]}function Se(){return E.lFrame.selectedIndex}function wt(e){E.lFrame.selectedIndex=e}function an(){let e=E.lFrame;return zs(e.tView,e.selectedIndex)}function tS(){E.lFrame.currentNamespace=el}function nS(){Kh()}function Kh(){E.lFrame.currentNamespace=null}function Jh(){return E.lFrame.currentNamespace}var pl=!0;function so(){return pl}function ao(e){pl=e}function Xh(e,t,n){let{ngOnChanges:r,ngOnInit:o,ngDoCheck:i}=t.type.prototype;if(r){let s=Kc(t);(n.preOrderHooks??=[]).push(e,s),(n.preOrderCheckHooks??=[]).push(e,s)}o&&(n.preOrderHooks??=[]).push(0-e,o),i&&((n.preOrderHooks??=[]).push(e,i),(n.preOrderCheckHooks??=[]).push(e,i))}function uo(e,t){for(let n=t.directiveStart,r=t.directiveEnd;n<r;n++){let i=e.data[n].type.prototype,{ngAfterContentInit:s,ngAfterContentChecked:a,ngAfterViewInit:u,ngAfterViewChecked:c,ngOnDestroy:l}=i;s&&(e.contentHooks??=[]).push(-n,s),a&&((e.contentHooks??=[]).push(n,a),(e.contentCheckHooks??=[]).push(n,a)),u&&(e.viewHooks??=[]).push(-n,u),c&&((e.viewHooks??=[]).push(n,c),(e.viewCheckHooks??=[]).push(n,c)),l!=null&&(e.destroyHooks??=[]).push(n,l)}}function _r(e,t,n){hl(e,t,3,n)}function Mr(e,t,n,r){(e[v]&3)===n&&hl(e,t,n,r)}function ai(e,t){let n=e[v];(n&3)===t&&(n&=16383,n+=1,e[v]=n)}function hl(e,t,n,r){let o=r!==void 0?e[Gt]&65535:0,i=r??-1,s=t.length-1,a=0;for(let u=o;u<s;u++)if(typeof t[u+1]=="number"){if(a=t[u],r!=null&&a>=r)break}else t[u]<0&&(e[Gt]+=65536),(a<i||i==-1)&&(eg(e,n,t,u),e[Gt]=(e[Gt]&**********)+u+2),u++}function $u(e,t){Re(4,e,t);let n=_(null);try{t.call(e)}finally{_(n),Re(5,e,t)}}function eg(e,t,n,r){let o=n[r]<0,i=n[r+1],s=o?-n[r]:n[r],a=e[s];o?e[v]>>14<e[Gt]>>16&&(e[v]&3)===t&&(e[v]+=16384,$u(a,i)):$u(a,i)}var Yt=-1,It=class{constructor(t,n,r){this.factory=t,this.resolving=!1,this.canSeeViewProviders=n,this.injectImpl=r}};function tg(e){return e instanceof It}function ng(e){return(e.flags&8)!==0}function rg(e){return(e.flags&16)!==0}function gl(e){return e!==Yt}function kr(e){return e&32767}function og(e){return e>>16}function Lr(e,t){let n=og(e),r=t;for(;n>0;)r=r[sn],n--;return r}var Ai=!0;function jr(e){let t=Ai;return Ai=e,t}var ig=256,ml=ig-1,yl=5,sg=0,Pe={};function ag(e,t,n){let r;typeof n=="string"?r=n.charCodeAt(0)||0:n.hasOwnProperty(Dn)&&(r=n[Dn]),r==null&&(r=n[Dn]=sg++);let o=r&ml,i=1<<o;t.data[e+(o>>yl)]|=i}function Vr(e,t){let n=Dl(e,t);if(n!==-1)return n;let r=t[I];r.firstCreatePass&&(e.injectorIndex=t.length,ui(r.data,e),ui(t,null),ui(r.blueprint,null));let o=na(e,t),i=e.injectorIndex;if(gl(o)){let s=kr(o),a=Lr(o,t),u=a[I].data;for(let c=0;c<8;c++)t[i+c]=a[s+c]|u[s+c]}return t[i+8]=o,i}function ui(e,t){e.push(0,0,0,0,0,0,0,0,t)}function Dl(e,t){return e.injectorIndex===-1||e.parent&&e.parent.injectorIndex===e.injectorIndex||t[e.injectorIndex+8]===null?-1:e.injectorIndex}function na(e,t){if(e.parent&&e.parent.injectorIndex!==-1)return e.parent.injectorIndex;let n=0,r=null,o=t;for(;o!==null;){if(r=Cl(o),r===null)return Yt;if(n++,o=o[sn],r.injectorIndex!==-1)return r.injectorIndex|n<<16}return Yt}function Oi(e,t,n){ag(e,t,n)}function ug(e,t){if(t==="class")return e.classes;if(t==="style")return e.styles;let n=e.attrs;if(n){let r=n.length,o=0;for(;o<r;){let i=n[o];if(Pc(i))break;if(i===0)o=o+2;else if(typeof i=="number")for(o++;o<r&&typeof n[o]=="string";)o++;else{if(i===t)return n[o+1];o=o+2}}}return null}function vl(e,t,n){if(n&S.Optional||e!==void 0)return e;Ps(t,"NodeInjector")}function wl(e,t,n,r){if(n&S.Optional&&r===void 0&&(r=null),!(n&(S.Self|S.Host))){let o=e[Jt],i=oe(void 0);try{return o?o.get(t,r,n&S.Optional):xc(t,r,n&S.Optional)}finally{oe(i)}}return vl(r,t,n)}function Il(e,t,n,r=S.Default,o){if(e!==null){if(t[v]&2048&&!(r&S.Self)){let s=fg(e,t,n,r,Pe);if(s!==Pe)return s}let i=El(e,t,n,r,Pe);if(i!==Pe)return i}return wl(t,n,r,o)}function El(e,t,n,r,o){let i=lg(n);if(typeof i=="function"){if(!ul(t,e,r))return r&S.Host?vl(o,n,r):wl(t,n,r,o);try{let s;if(s=i(r),s==null&&!(r&S.Optional))Ps(n);else return s}finally{fl()}}else if(typeof i=="number"){let s=null,a=Dl(e,t),u=Yt,c=r&S.Host?t[me][de]:null;for((a===-1||r&S.SkipSelf)&&(u=a===-1?na(e,t):t[a+8],u===Yt||!Uu(r,!1)?a=-1:(s=t[I],a=kr(u),t=Lr(u,t)));a!==-1;){let l=t[I];if(Hu(i,a,l.data)){let d=cg(a,t,n,s,r,c);if(d!==Pe)return d}u=t[a+8],u!==Yt&&Uu(r,t[I].data[a+8]===c)&&Hu(i,a,t)?(s=l,a=kr(u),t=Lr(u,t)):a=-1}}return o}function cg(e,t,n,r,o,i){let s=t[I],a=s.data[e+8],u=r==null?oo(a)&&Ai:r!=s&&(a.type&3)!==0,c=o&S.Host&&i===a,l=xr(a,s,n,u,c);return l!==null?Et(t,s,l,a):Pe}function xr(e,t,n,r,o){let i=e.providerIndexes,s=t.data,a=i&1048575,u=e.directiveStart,c=e.directiveEnd,l=i>>20,d=r?a:a+l,p=o?a+l:c;for(let f=d;f<p;f++){let h=s[f];if(f<u&&n===h||f>=u&&h.type===n)return f}if(o){let f=s[u];if(f&&$e(f)&&f.type===n)return u}return null}function Et(e,t,n,r){let o=e[n],i=t.data;if(tg(o)){let s=o;s.resolving&&Op(Ap(i[n]));let a=jr(s.canSeeViewProviders);s.resolving=!0;let u,c=s.injectImpl?oe(s.injectImpl):null,l=ul(e,r,S.Default);try{o=e[n]=s.factory(void 0,i,e,r),t.firstCreatePass&&n>=r.directiveStart&&Xh(n,i[n],t)}finally{c!==null&&oe(c),jr(a),s.resolving=!1,fl()}}return o}function lg(e){if(typeof e=="string")return e.charCodeAt(0)||0;let t=e.hasOwnProperty(Dn)?e[Dn]:void 0;return typeof t=="number"?t>=0?t&ml:dg:t}function Hu(e,t,n){let r=1<<e;return!!(n[t+(e>>yl)]&r)}function Uu(e,t){return!(e&S.Self)&&!(e&S.Host&&t)}var mt=class{constructor(t,n){this._tNode=t,this._lView=n}get(t,n,r){return Il(this._tNode,this._lView,t,to(r),n)}};function dg(){return new mt(Q(),y())}function rS(e){return Rn(()=>{let t=e.prototype.constructor,n=t[Nr]||Fi(t),r=Object.prototype,o=Object.getPrototypeOf(e.prototype).constructor;for(;o&&o!==r;){let i=o[Nr]||Fi(o);if(i&&i!==n)return i;o=Object.getPrototypeOf(o)}return i=>new i})}function Fi(e){return Ec(e)?()=>{let t=Fi(ee(e));return t&&t()}:yt(e)}function fg(e,t,n,r,o){let i=e,s=t;for(;i!==null&&s!==null&&s[v]&2048&&!(s[v]&512);){let a=El(i,s,n,r|S.Self,Pe);if(a!==Pe)return a;let u=i.parent;if(!u){let c=s[Qc];if(c){let l=c.get(n,Pe,r);if(l!==Pe)return l}u=Cl(s),s=s[sn]}i=u}return o}function Cl(e){let t=e[I],n=t.type;return n===2?t.declTNode:n===1?e[de]:null}function pg(e){return ug(Q(),e)}function Gu(e,t=null,n=null,r){let o=bl(e,t,n,r);return o.resolveInjectorInitializers(),o}function bl(e,t=null,n=null,r,o=new Set){let i=[n||ie,lh(e)];return r=r||(typeof e=="object"?void 0:te(e)),new Cn(i,t||Bs(),r||null,o)}var St=(()=>{class e{static{this.THROW_IF_NOT_FOUND=wn}static{this.NULL=new Rr}static create(n,r){if(Array.isArray(n))return Gu({name:""},r,n,"");{let o=n.name??"";return Gu({name:o},n.parent,n.providers,o)}}static{this.\u0275prov=k({token:e,providedIn:"any",factory:()=>H(Oc)})}static{this.__NG_ELEMENT_ID__=-1}}return e})();var hg="ngOriginalError";function ci(e){return e[hg]}var Ct=class{constructor(){this._console=console}handleError(t){let n=this._findOriginalError(t);this._console.error("ERROR",t),n&&this._console.error("ORIGINAL ERROR",n)}_findOriginalError(t){let n=t&&ci(t);for(;n&&ci(n);)n=ci(n);return n||null}},_l=new O("",{providedIn:"root",factory:()=>F(Ct).handleError.bind(void 0)}),ra=(()=>{class e{static{this.__NG_ELEMENT_ID__=gg}static{this.__NG_ENV_ID__=n=>n}}return e})(),Ri=class extends ra{constructor(t){super(),this._lView=t}onDestroy(t){return rl(this._lView,t),()=>Lh(this._lView,t)}};function gg(){return new Ri(y())}function mg(){return un(Q(),y())}function un(e,t){return new ot(ye(e,t))}var ot=(()=>{class e{constructor(n){this.nativeElement=n}static{this.__NG_ELEMENT_ID__=mg}}return e})();function yg(e){return e instanceof ot?e.nativeElement:e}var Pi=class extends ge{constructor(t=!1){super(),this.destroyRef=void 0,this.__isAsync=t,Yc()&&(this.destroyRef=F(ra,{optional:!0})??void 0)}emit(t){let n=_(null);try{super.next(t)}finally{_(n)}}subscribe(t,n,r){let o=t,i=n||(()=>null),s=r;if(t&&typeof t=="object"){let u=t;o=u.next?.bind(u),i=u.error?.bind(u),s=u.complete?.bind(u)}this.__isAsync&&(i=li(i),o&&(o=li(o)),s&&(s=li(s)));let a=super.subscribe({next:o,error:i,complete:s});return t instanceof $&&t.add(a),a}};function li(e){return t=>{setTimeout(e,void 0,t)}}var _e=Pi;function Dg(){return this._results[Symbol.iterator]()}var ki=class e{get changes(){return this._changes??=new _e}constructor(t=!1){this._emitDistinctChangesOnly=t,this.dirty=!0,this._onDirty=void 0,this._results=[],this._changesDetected=!1,this._changes=void 0,this.length=0,this.first=void 0,this.last=void 0;let n=e.prototype;n[Symbol.iterator]||(n[Symbol.iterator]=Dg)}get(t){return this._results[t]}map(t){return this._results.map(t)}filter(t){return this._results.filter(t)}find(t){return this._results.find(t)}reduce(t,n){return this._results.reduce(t,n)}forEach(t){this._results.forEach(t)}some(t){return this._results.some(t)}toArray(){return this._results.slice()}toString(){return this._results.toString()}reset(t,n){this.dirty=!1;let r=Up(t);(this._changesDetected=!Hp(this._results,r,n))&&(this._results=r,this.length=r.length,this.last=r[this.length-1],this.first=r[0])}notifyOnChanges(){this._changes!==void 0&&(this._changesDetected||!this._emitDistinctChangesOnly)&&this._changes.emit(this)}onDirty(t){this._onDirty=t}setDirty(){this.dirty=!0,this._onDirty?.()}destroy(){this._changes!==void 0&&(this._changes.complete(),this._changes.unsubscribe())}};function Ml(e){return(e.flags&128)===128}var xl=new Map,vg=0;function wg(){return vg++}function Ig(e){xl.set(e[ro],e)}function Eg(e){xl.delete(e[ro])}var zu="__ngContext__";function et(e,t){gt(t)?(e[zu]=t[ro],Ig(t)):e[zu]=t}function Sl(e){return Nl(e[_n])}function Tl(e){return Nl(e[Me])}function Nl(e){for(;e!==null&&!Ue(e);)e=e[Me];return e}var Li;function oS(e){Li=e}function Cg(){if(Li!==void 0)return Li;if(typeof document<"u")return document;throw new C(210,!1)}var iS=new O("",{providedIn:"root",factory:()=>bg}),bg="ng",_g=new O(""),oa=new O("",{providedIn:"platform",factory:()=>"unknown"});var sS=new O(""),aS=new O("",{providedIn:"root",factory:()=>Cg().body?.querySelector("[ngCspNonce]")?.getAttribute("ngCspNonce")||null});var Mg="h",xg="b";var Sg=()=>null;function ia(e,t,n=!1){return Sg(e,t,n)}var Al=!1,Tg=new O("",{providedIn:"root",factory:()=>Al});var Dr;function Ol(){if(Dr===void 0&&(Dr=null,ue.trustedTypes))try{Dr=ue.trustedTypes.createPolicy("angular",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return Dr}function co(e){return Ol()?.createHTML(e)||e}function Ng(e){return Ol()?.createScriptURL(e)||e}var vr;function Ag(){if(vr===void 0&&(vr=null,ue.trustedTypes))try{vr=ue.trustedTypes.createPolicy("angular#unsafe-bypass",{createHTML:e=>e,createScript:e=>e,createScriptURL:e=>e})}catch{}return vr}function Wu(e){return Ag()?.createScriptURL(e)||e}var He=class{constructor(t){this.changingThisBreaksApplicationSecurity=t}toString(){return`SafeValue must use [property]=binding: ${this.changingThisBreaksApplicationSecurity} (see ${Dc})`}},ji=class extends He{getTypeName(){return"HTML"}},Vi=class extends He{getTypeName(){return"Style"}},Bi=class extends He{getTypeName(){return"Script"}},$i=class extends He{getTypeName(){return"URL"}},Hi=class extends He{getTypeName(){return"ResourceURL"}};function Vn(e){return e instanceof He?e.changingThisBreaksApplicationSecurity:e}function Fl(e,t){let n=Og(e);if(n!=null&&n!==t){if(n==="ResourceURL"&&t==="URL")return!0;throw new Error(`Required a safe ${t}, got a ${n} (see ${Dc})`)}return n===t}function Og(e){return e instanceof He&&e.getTypeName()||null}function uS(e){return new ji(e)}function cS(e){return new Vi(e)}function lS(e){return new Bi(e)}function dS(e){return new $i(e)}function fS(e){return new Hi(e)}function Fg(e){let t=new Gi(e);return Rg()?new Ui(t):t}var Ui=class{constructor(t){this.inertDocumentHelper=t}getInertBodyElement(t){t="<body><remove></remove>"+t;try{let n=new window.DOMParser().parseFromString(co(t),"text/html").body;return n===null?this.inertDocumentHelper.getInertBodyElement(t):(n.removeChild(n.firstChild),n)}catch{return null}}},Gi=class{constructor(t){this.defaultDoc=t,this.inertDocument=this.defaultDoc.implementation.createHTMLDocument("sanitization-inert")}getInertBodyElement(t){let n=this.inertDocument.createElement("template");return n.innerHTML=co(t),n}};function Rg(){try{return!!new window.DOMParser().parseFromString(co(""),"text/html")}catch{return!1}}var Pg=/^(?!javascript:)(?:[a-z0-9+.-]+:|[^&:\/?#]*(?:[\/?#]|$))/i;function Rl(e){return e=String(e),e.match(Pg)?e:"unsafe:"+e}function Ge(e){let t={};for(let n of e.split(","))t[n]=!0;return t}function Bn(...e){let t={};for(let n of e)for(let r in n)n.hasOwnProperty(r)&&(t[r]=!0);return t}var Pl=Ge("area,br,col,hr,img,wbr"),kl=Ge("colgroup,dd,dt,li,p,tbody,td,tfoot,th,thead,tr"),Ll=Ge("rp,rt"),kg=Bn(Ll,kl),Lg=Bn(kl,Ge("address,article,aside,blockquote,caption,center,del,details,dialog,dir,div,dl,figure,figcaption,footer,h1,h2,h3,h4,h5,h6,header,hgroup,hr,ins,main,map,menu,nav,ol,pre,section,summary,table,ul")),jg=Bn(Ll,Ge("a,abbr,acronym,audio,b,bdi,bdo,big,br,cite,code,del,dfn,em,font,i,img,ins,kbd,label,map,mark,picture,q,ruby,rp,rt,s,samp,small,source,span,strike,strong,sub,sup,time,track,tt,u,var,video")),qu=Bn(Pl,Lg,jg,kg),jl=Ge("background,cite,href,itemtype,longdesc,poster,src,xlink:href"),Vg=Ge("abbr,accesskey,align,alt,autoplay,axis,bgcolor,border,cellpadding,cellspacing,class,clear,color,cols,colspan,compact,controls,coords,datetime,default,dir,download,face,headers,height,hidden,hreflang,hspace,ismap,itemscope,itemprop,kind,label,lang,language,loop,media,muted,nohref,nowrap,open,preload,rel,rev,role,rows,rowspan,rules,scope,scrolling,shape,size,sizes,span,srclang,srcset,start,summary,tabindex,target,title,translate,type,usemap,valign,value,vspace,width"),Bg=Ge("aria-activedescendant,aria-atomic,aria-autocomplete,aria-busy,aria-checked,aria-colcount,aria-colindex,aria-colspan,aria-controls,aria-current,aria-describedby,aria-details,aria-disabled,aria-dropeffect,aria-errormessage,aria-expanded,aria-flowto,aria-grabbed,aria-haspopup,aria-hidden,aria-invalid,aria-keyshortcuts,aria-label,aria-labelledby,aria-level,aria-live,aria-modal,aria-multiline,aria-multiselectable,aria-orientation,aria-owns,aria-placeholder,aria-posinset,aria-pressed,aria-readonly,aria-relevant,aria-required,aria-roledescription,aria-rowcount,aria-rowindex,aria-rowspan,aria-selected,aria-setsize,aria-sort,aria-valuemax,aria-valuemin,aria-valuenow,aria-valuetext"),$g=Bn(jl,Vg,Bg),Hg=Ge("script,style,template"),zi=class{constructor(){this.sanitizedSomething=!1,this.buf=[]}sanitizeChildren(t){let n=t.firstChild,r=!0,o=[];for(;n;){if(n.nodeType===Node.ELEMENT_NODE?r=this.startElement(n):n.nodeType===Node.TEXT_NODE?this.chars(n.nodeValue):this.sanitizedSomething=!0,r&&n.firstChild){o.push(n),n=zg(n);continue}for(;n;){n.nodeType===Node.ELEMENT_NODE&&this.endElement(n);let i=Gg(n);if(i){n=i;break}n=o.pop()}}return this.buf.join("")}startElement(t){let n=Yu(t).toLowerCase();if(!qu.hasOwnProperty(n))return this.sanitizedSomething=!0,!Hg.hasOwnProperty(n);this.buf.push("<"),this.buf.push(n);let r=t.attributes;for(let o=0;o<r.length;o++){let i=r.item(o),s=i.name,a=s.toLowerCase();if(!$g.hasOwnProperty(a)){this.sanitizedSomething=!0;continue}let u=i.value;jl[a]&&(u=Rl(u)),this.buf.push(" ",s,'="',Qu(u),'"')}return this.buf.push(">"),!0}endElement(t){let n=Yu(t).toLowerCase();qu.hasOwnProperty(n)&&!Pl.hasOwnProperty(n)&&(this.buf.push("</"),this.buf.push(n),this.buf.push(">"))}chars(t){this.buf.push(Qu(t))}};function Ug(e,t){return(e.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)!==Node.DOCUMENT_POSITION_CONTAINED_BY}function Gg(e){let t=e.nextSibling;if(t&&e!==t.previousSibling)throw Vl(t);return t}function zg(e){let t=e.firstChild;if(t&&Ug(e,t))throw Vl(t);return t}function Yu(e){let t=e.nodeName;return typeof t=="string"?t:"FORM"}function Vl(e){return new Error(`Failed to sanitize html because the element is clobbered: ${e.outerHTML}`)}var Wg=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,qg=/([^\#-~ |!])/g;function Qu(e){return e.replace(/&/g,"&amp;").replace(Wg,function(t){let n=t.charCodeAt(0),r=t.charCodeAt(1);return"&#"+((n-55296)*1024+(r-56320)+65536)+";"}).replace(qg,function(t){return"&#"+t.charCodeAt(0)+";"}).replace(/</g,"&lt;").replace(/>/g,"&gt;")}var wr;function pS(e,t){let n=null;try{wr=wr||Fg(e);let r=t?String(t):"";n=wr.getInertBodyElement(r);let o=5,i=r;do{if(o===0)throw new Error("Failed to sanitize html because the input is unstable");o--,r=i,i=n.innerHTML,n=wr.getInertBodyElement(r)}while(r!==i);let a=new zi().sanitizeChildren(Zu(n)||n);return co(a)}finally{if(n){let r=Zu(n)||n;for(;r.firstChild;)r.removeChild(r.firstChild)}}}function Zu(e){return"content"in e&&Yg(e)?e.content:null}function Yg(e){return e.nodeType===Node.ELEMENT_NODE&&e.nodeName==="TEMPLATE"}var sa=function(e){return e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL",e}(sa||{});function Qg(e){let t=Bl();return t?t.sanitize(sa.URL,e)||"":Fl(e,"URL")?Vn(e):Rl(ce(e))}function Zg(e){let t=Bl();if(t)return Wu(t.sanitize(sa.RESOURCE_URL,e)||"");if(Fl(e,"ResourceURL"))return Wu(Vn(e));throw new C(904,!1)}function hS(e){return Ng(e[0])}function Kg(e,t){return t==="src"&&(e==="embed"||e==="frame"||e==="iframe"||e==="media"||e==="script")||t==="href"&&(e==="base"||e==="link")?Zg:Qg}function gS(e,t,n){return Kg(t,n)(e)}function Bl(){let e=y();return e&&e[xe].sanitizer}var Jg=/^>|^->|<!--|-->|--!>|<!-$/g,Xg=/(<|>)/g,em="\u200B$1\u200B";function tm(e){return e.replace(Jg,t=>t.replace(Xg,em))}function mS(e){return e.ownerDocument}function $l(e){return e instanceof Function?e():e}function nm(e){return(e??F(St)).get(oa)==="browser"}var Sn=function(e){return e[e.Important=1]="Important",e[e.DashCase=2]="DashCase",e}(Sn||{}),rm;function aa(e,t){return rm(e,t)}function zt(e,t,n,r,o){if(r!=null){let i,s=!1;Ue(r)?i=r:gt(r)&&(s=!0,r=r[Le]);let a=ke(r);e===0&&n!==null?o==null?Wl(t,n,a):Br(t,n,a,o||null,!0):e===1&&n!==null?Br(t,n,a,o||null,!0):e===2?vm(t,a,s):e===3&&t.destroyNode(a),i!=null&&Im(t,e,i,n,o)}}function om(e,t){return e.createText(t)}function im(e,t,n){e.setValue(t,n)}function sm(e,t){return e.createComment(tm(t))}function Hl(e,t,n){return e.createElement(t,n)}function am(e,t){Ul(e,t),t[Le]=null,t[de]=null}function um(e,t,n,r,o,i){r[Le]=o,r[de]=t,po(e,r,n,1,o,i)}function Ul(e,t){t[xe].changeDetectionScheduler?.notify(1),po(e,t,t[j],2,null,null)}function cm(e){let t=e[_n];if(!t)return di(e[I],e);for(;t;){let n=null;if(gt(t))n=t[_n];else{let r=t[J];r&&(n=r)}if(!n){for(;t&&!t[Me]&&t!==e;)gt(t)&&di(t[I],t),t=t[q];t===null&&(t=e),gt(t)&&di(t[I],t),n=t&&t[Me]}t=n}}function lm(e,t,n,r){let o=J+r,i=n.length;r>0&&(n[o-1][Me]=t),r<i-J?(t[Me]=n[o],Ac(n,J+r,t)):(n.push(t),t[Me]=null),t[q]=n;let s=t[kn];s!==null&&n!==s&&dm(s,t);let a=t[Be];a!==null&&a.insertView(e),Ti(t),t[v]|=128}function dm(e,t){let n=e[Xt],o=t[q][q][me];t[me]!==o&&(e[v]|=Hs.HasTransplantedViews),n===null?e[Xt]=[t]:n.push(t)}function Gl(e,t){let n=e[Xt],r=n.indexOf(t);n.splice(r,1)}function Tn(e,t){if(e.length<=J)return;let n=J+t,r=e[n];if(r){let o=r[kn];o!==null&&o!==e&&Gl(o,r),t>0&&(e[n-1][Me]=r[Me]);let i=Or(e,J+t);am(r[I],r);let s=i[Be];s!==null&&s.detachView(i[I]),r[q]=null,r[Me]=null,r[v]&=-129}return r}function lo(e,t){if(!(t[v]&256)){let n=t[j];n.destroyNode&&po(e,t,n,3,null,null),cm(t)}}function di(e,t){if(t[v]&256)return;let n=_(null);try{t[v]&=-129,t[v]|=256,t[Dt]&&Ja(t[Dt]),pm(e,t),fm(e,t),t[I].type===1&&t[j].destroy();let r=t[kn];if(r!==null&&Ue(t[q])){r!==t[q]&&Gl(r,t);let o=t[Be];o!==null&&o.detachView(e)}Eg(t)}finally{_(n)}}function fm(e,t){let n=e.cleanup,r=t[bn];if(n!==null)for(let i=0;i<n.length-1;i+=2)if(typeof n[i]=="string"){let s=n[i+3];s>=0?r[s]():r[-s].unsubscribe(),i+=2}else{let s=r[n[i+1]];n[i].call(s)}r!==null&&(t[bn]=null);let o=t[Ze];if(o!==null){t[Ze]=null;for(let i=0;i<o.length;i++){let s=o[i];s()}}}function pm(e,t){let n;if(e!=null&&(n=e.destroyHooks)!=null)for(let r=0;r<n.length;r+=2){let o=t[n[r]];if(!(o instanceof It)){let i=n[r+1];if(Array.isArray(i))for(let s=0;s<i.length;s+=2){let a=o[i[s]],u=i[s+1];Re(4,a,u);try{u.call(a)}finally{Re(5,a,u)}}else{Re(4,o,i);try{i.call(o)}finally{Re(5,o,i)}}}}}function zl(e,t,n){return hm(e,t.parent,n)}function hm(e,t,n){let r=t;for(;r!==null&&r.type&40;)t=r,r=t.parent;if(r===null)return n[Le];{let{componentOffset:o}=r;if(o>-1){let{encapsulation:i}=e.data[r.directiveStart+o];if(i===In.None||i===In.Emulated)return null}return ye(r,n)}}function Br(e,t,n,r,o){e.insertBefore(t,n,r,o)}function Wl(e,t,n){e.appendChild(t,n)}function Ku(e,t,n,r,o){r!==null?Br(e,t,n,r,o):Wl(e,t,n)}function gm(e,t,n,r){e.removeChild(t,n,r)}function ua(e,t){return e.parentNode(t)}function mm(e,t){return e.nextSibling(t)}function ql(e,t,n){return Dm(e,t,n)}function ym(e,t,n){return e.type&40?ye(e,n):null}var Dm=ym,Ju;function fo(e,t,n,r){let o=zl(e,r,t),i=t[j],s=r.parent||t[de],a=ql(s,r,t);if(o!=null)if(Array.isArray(n))for(let u=0;u<n.length;u++)Ku(i,o,n[u],a,!1);else Ku(i,o,n,a,!1);Ju!==void 0&&Ju(i,r,t,n,o)}function Sr(e,t){if(t!==null){let n=t.type;if(n&3)return ye(t,e);if(n&4)return Wi(-1,e[t.index]);if(n&8){let r=t.child;if(r!==null)return Sr(e,r);{let o=e[t.index];return Ue(o)?Wi(-1,o):ke(o)}}else{if(n&32)return aa(t,e)()||ke(e[t.index]);{let r=Yl(e,t);if(r!==null){if(Array.isArray(r))return r[0];let o=xn(e[me]);return Sr(o,r)}else return Sr(e,t.next)}}}return null}function Yl(e,t){if(t!==null){let r=e[me][de],o=t.projection;return r.projection[o]}return null}function Wi(e,t){let n=J+e+1;if(n<t.length){let r=t[n],o=r[I].firstChild;if(o!==null)return Sr(r,o)}return t[vt]}function vm(e,t,n){let r=ua(e,t);r&&gm(e,r,t,n)}function ca(e,t,n,r,o,i,s){for(;n!=null;){let a=r[n.index],u=n.type;if(s&&t===0&&(a&&et(ke(a),r),n.flags|=2),(n.flags&32)!==32)if(u&8)ca(e,t,n.child,r,o,i,!1),zt(t,e,o,a,i);else if(u&32){let c=aa(n,r),l;for(;l=c();)zt(t,e,o,l,i);zt(t,e,o,a,i)}else u&16?Ql(e,t,r,n,o,i):zt(t,e,o,a,i);n=s?n.projectionNext:n.next}}function po(e,t,n,r,o,i){ca(n,r,e.firstChild,t,o,i,!1)}function wm(e,t,n){let r=t[j],o=zl(e,n,t),i=n.parent||t[de],s=ql(i,n,t);Ql(r,0,t,n,o,s)}function Ql(e,t,n,r,o,i){let s=n[me],u=s[de].projection[r.projection];if(Array.isArray(u))for(let c=0;c<u.length;c++){let l=u[c];zt(t,e,o,l,i)}else{let c=u,l=s[q];Ml(r)&&(c.flags|=128),ca(e,t,c,l,o,i,!0)}}function Im(e,t,n,r,o){let i=n[vt],s=ke(n);i!==s&&zt(t,e,r,i,o);for(let a=J;a<n.length;a++){let u=n[a];po(u[I],u,e,t,r,i)}}function Em(e,t,n,r,o){if(t)o?e.addClass(n,r):e.removeClass(n,r);else{let i=r.indexOf("-")===-1?void 0:Sn.DashCase;o==null?e.removeStyle(n,r,i):(typeof o=="string"&&o.endsWith("!important")&&(o=o.slice(0,-10),i|=Sn.Important),e.setStyle(n,r,o,i))}}function Cm(e,t,n){e.setAttribute(t,"style",n)}function Zl(e,t,n){n===""?e.removeAttribute(t,"class"):e.setAttribute(t,"class",n)}function Kl(e,t,n){let{mergedAttrs:r,classes:o,styles:i}=n;r!==null&&bi(e,t,r),o!==null&&Zl(e,t,o),i!==null&&Cm(e,t,i)}var Z={};function yS(e=1){Jl(V(),y(),Se()+e,!1)}function Jl(e,t,n,r){if(!r)if((t[v]&3)===3){let i=e.preOrderCheckHooks;i!==null&&_r(t,i,n)}else{let i=e.preOrderHooks;i!==null&&Mr(t,i,0,n)}wt(n)}function U(e,t=S.Default){let n=y();if(n===null)return H(e,t);let r=Q();return Il(r,n,ee(e),t)}function DS(){let e="invalid";throw new Error(e)}function Xl(e,t,n,r,o,i){let s=_(null);try{let a=null;o&Ke.SignalBased&&(a=t[r][ko]),a!==null&&a.transformFn!==void 0&&(i=a.transformFn(i)),o&Ke.HasDecoratorInputTransform&&(i=e.inputTransforms[r].call(t,i)),e.setInput!==null?e.setInput(t,a,i,n,r):Zc(t,a,r,i)}finally{_(s)}}function bm(e,t){let n=e.hostBindingOpCodes;if(n!==null)try{for(let r=0;r<n.length;r++){let o=n[r];if(o<0)wt(~o);else{let i=o,s=n[++r],a=n[++r];qh(s,i);let u=t[i];a(2,u)}}}finally{wt(-1)}}function ho(e,t,n,r,o,i,s,a,u,c,l){let d=t.blueprint.slice();return d[Le]=o,d[v]=r|4|128|8|64,(c!==null||e&&e[v]&2048)&&(d[v]|=2048),nl(d),d[q]=d[sn]=e,d[K]=n,d[xe]=s||e&&e[xe],d[j]=a||e&&e[j],d[Jt]=u||e&&e[Jt]||null,d[de]=i,d[ro]=wg(),d[Kt]=l,d[Qc]=c,d[me]=t.type==2?e[me]:d,d}function cn(e,t,n,r,o){let i=e.data[t];if(i===null)i=_m(e,t,n,r,o),Wh()&&(i.flags|=32);else if(i.type&64){i.type=n,i.value=r,i.attrs=o;let s=Uh();i.injectorIndex=s===null?-1:s.injectorIndex}return xt(i,!0),i}function _m(e,t,n,r,o){let i=sl(),s=Qs(),a=s?i:i&&i.parent,u=e.data[t]=Am(e,a,n,t,r,o);return e.firstChild===null&&(e.firstChild=u),i!==null&&(s?i.child==null&&u.parent!==null&&(i.child=u):i.next===null&&(i.next=u,u.prev=i)),u}function ed(e,t,n,r){if(n===0)return-1;let o=t.length;for(let i=0;i<n;i++)t.push(r),e.blueprint.push(r),e.data.push(null);return o}function td(e,t,n,r,o){let i=Se(),s=r&2;try{wt(-1),s&&t.length>Y&&Jl(e,t,Y,!1),Re(s?2:0,o),n(r,o)}finally{wt(i),Re(s?3:1,o)}}function la(e,t,n){if(Us(t)){let r=_(null);try{let o=t.directiveStart,i=t.directiveEnd;for(let s=o;s<i;s++){let a=e.data[s];if(a.contentQueries){let u=n[s];a.contentQueries(1,u,s)}}}finally{_(r)}}}function da(e,t,n){ol()&&(Lm(e,t,n,ye(n,t)),(n.flags&64)===64&&od(e,t,n))}function fa(e,t,n=ye){let r=t.localNames;if(r!==null){let o=t.index+1;for(let i=0;i<r.length;i+=2){let s=r[i+1],a=s===-1?n(t,e):e[s];e[o++]=a}}}function nd(e){let t=e.tView;return t===null||t.incompleteFirstPass?e.tView=pa(1,null,e.template,e.decls,e.vars,e.directiveDefs,e.pipeDefs,e.viewQuery,e.schemas,e.consts,e.id):t}function pa(e,t,n,r,o,i,s,a,u,c,l){let d=Y+r,p=d+o,f=Mm(d,p),h=typeof c=="function"?c():c;return f[I]={type:e,blueprint:f,template:n,queries:null,viewQuery:a,declTNode:t,data:f.slice().fill(null,d),bindingStartIndex:d,expandoStartIndex:p,hostBindingOpCodes:null,firstCreatePass:!0,firstUpdatePass:!0,staticViewQueries:!1,staticContentQueries:!1,preOrderHooks:null,preOrderCheckHooks:null,contentHooks:null,contentCheckHooks:null,viewHooks:null,viewCheckHooks:null,destroyHooks:null,cleanup:null,contentQueries:null,components:null,directiveRegistry:typeof i=="function"?i():i,pipeRegistry:typeof s=="function"?s():s,firstChild:null,schemas:u,consts:h,incompleteFirstPass:!1,ssrId:l}}function Mm(e,t){let n=[];for(let r=0;r<t;r++)n.push(r<e?null:Z);return n}function xm(e,t,n,r){let i=r.get(Tg,Al)||n===In.ShadowDom,s=e.selectRootElement(t,i);return Sm(s),s}function Sm(e){Tm(e)}var Tm=()=>null;function Nm(e,t,n,r){let o=ad(t);o.push(n),e.firstCreatePass&&ud(e).push(r,o.length-1)}function Am(e,t,n,r,o,i){let s=t?t.injectorIndex:-1,a=0;return il()&&(a|=128),{type:n,index:r,insertBeforeIndex:null,injectorIndex:s,directiveStart:-1,directiveEnd:-1,directiveStylingLast:-1,componentOffset:-1,propertyBindings:null,flags:a,providerIndexes:0,value:o,attrs:i,mergedAttrs:null,localNames:null,initialInputs:void 0,inputs:null,outputs:null,tView:null,next:null,prev:null,projectionNext:null,child:null,parent:t,projection:null,styles:null,stylesWithoutHost:null,residualStyles:void 0,classes:null,classesWithoutHost:null,residualClasses:void 0,classBindings:0,styleBindings:0}}function Xu(e,t,n,r,o){for(let i in t){if(!t.hasOwnProperty(i))continue;let s=t[i];if(s===void 0)continue;r??={};let a,u=Ke.None;Array.isArray(s)?(a=s[0],u=s[1]):a=s;let c=i;if(o!==null){if(!o.hasOwnProperty(i))continue;c=o[i]}e===0?ec(r,n,c,a,u):ec(r,n,c,a)}return r}function ec(e,t,n,r,o){let i;e.hasOwnProperty(n)?(i=e[n]).push(t,r):i=e[n]=[t,r],o!==void 0&&i.push(o)}function Om(e,t,n){let r=t.directiveStart,o=t.directiveEnd,i=e.data,s=t.attrs,a=[],u=null,c=null;for(let l=r;l<o;l++){let d=i[l],p=n?n.get(d):null,f=p?p.inputs:null,h=p?p.outputs:null;u=Xu(0,d.inputs,l,u,f),c=Xu(1,d.outputs,l,c,h);let g=u!==null&&s!==null&&!Ls(t)?Ym(u,l,s):null;a.push(g)}u!==null&&(u.hasOwnProperty("class")&&(t.flags|=8),u.hasOwnProperty("style")&&(t.flags|=16)),t.initialInputs=a,t.inputs=u,t.outputs=c}function Fm(e){return e==="class"?"className":e==="for"?"htmlFor":e==="formaction"?"formAction":e==="innerHtml"?"innerHTML":e==="readonly"?"readOnly":e==="tabindex"?"tabIndex":e}function $n(e,t,n,r,o,i,s,a){let u=ye(t,n),c=t.inputs,l;!a&&c!=null&&(l=c[r])?(ga(e,n,l,r,o),oo(t)&&Rm(n,t.index)):t.type&3?(r=Fm(r),o=s!=null?s(o,t.value||"",r):o,i.setProperty(u,r,o)):t.type&12}function Rm(e,t){let n=nt(t,e);n[v]&16||(n[v]|=64)}function ha(e,t,n,r){if(ol()){let o=r===null?null:{"":-1},i=Vm(e,n),s,a;i===null?s=a=null:[s,a]=i,s!==null&&rd(e,t,n,s,o,a),o&&Bm(n,r,o)}n.mergedAttrs=En(n.mergedAttrs,n.attrs)}function rd(e,t,n,r,o,i){for(let c=0;c<r.length;c++)Oi(Vr(n,t),e,r[c].type);Hm(n,e.data.length,r.length);for(let c=0;c<r.length;c++){let l=r[c];l.providersResolver&&l.providersResolver(l)}let s=!1,a=!1,u=ed(e,t,r.length,null);for(let c=0;c<r.length;c++){let l=r[c];n.mergedAttrs=En(n.mergedAttrs,l.hostAttrs),Um(e,n,t,u,l),$m(u,l,o),l.contentQueries!==null&&(n.flags|=4),(l.hostBindings!==null||l.hostAttrs!==null||l.hostVars!==0)&&(n.flags|=64);let d=l.type.prototype;!s&&(d.ngOnChanges||d.ngOnInit||d.ngDoCheck)&&((e.preOrderHooks??=[]).push(n.index),s=!0),!a&&(d.ngOnChanges||d.ngDoCheck)&&((e.preOrderCheckHooks??=[]).push(n.index),a=!0),u++}Om(e,n,i)}function Pm(e,t,n,r,o){let i=o.hostBindings;if(i){let s=e.hostBindingOpCodes;s===null&&(s=e.hostBindingOpCodes=[]);let a=~t.index;km(s)!=a&&s.push(a),s.push(n,r,i)}}function km(e){let t=e.length;for(;t>0;){let n=e[--t];if(typeof n=="number"&&n<0)return n}return 0}function Lm(e,t,n,r){let o=n.directiveStart,i=n.directiveEnd;oo(n)&&Gm(t,n,e.data[o+n.componentOffset]),e.firstCreatePass||Vr(n,t),et(r,t);let s=n.initialInputs;for(let a=o;a<i;a++){let u=e.data[a],c=Et(t,e,a,n);if(et(c,t),s!==null&&qm(t,a-o,c,u,n,s),$e(u)){let l=nt(n.index,t);l[K]=Et(t,e,a,n)}}}function od(e,t,n){let r=n.directiveStart,o=n.directiveEnd,i=n.index,s=Yh();try{wt(i);for(let a=r;a<o;a++){let u=e.data[a],c=t[a];Ni(a),(u.hostBindings!==null||u.hostVars!==0||u.hostAttrs!==null)&&jm(u,c)}}finally{wt(-1),Ni(s)}}function jm(e,t){e.hostBindings!==null&&e.hostBindings(1,t)}function Vm(e,t){let n=e.directiveRegistry,r=null,o=null;if(n)for(let i=0;i<n.length;i++){let s=n[i];if(Lc(t,s.selectors,!1))if(r||(r=[]),$e(s))if(s.findHostDirectiveDefs!==null){let a=[];o=o||new Map,s.findHostDirectiveDefs(s,a,o),r.unshift(...a,s);let u=a.length;qi(e,t,u)}else r.unshift(s),qi(e,t,0);else o=o||new Map,s.findHostDirectiveDefs?.(s,r,o),r.push(s)}return r===null?null:[r,o]}function qi(e,t,n){t.componentOffset=n,(e.components??=[]).push(t.index)}function Bm(e,t,n){if(t){let r=e.localNames=[];for(let o=0;o<t.length;o+=2){let i=n[t[o+1]];if(i==null)throw new C(-301,!1);r.push(t[o],i)}}}function $m(e,t,n){if(n){if(t.exportAs)for(let r=0;r<t.exportAs.length;r++)n[t.exportAs[r]]=e;$e(t)&&(n[""]=e)}}function Hm(e,t,n){e.flags|=1,e.directiveStart=t,e.directiveEnd=t+n,e.providerIndexes=t}function Um(e,t,n,r,o){e.data[r]=o;let i=o.factory||(o.factory=yt(o.type,!0)),s=new It(i,$e(o),U);e.blueprint[r]=s,n[r]=s,Pm(e,t,r,ed(e,n,o.hostVars,Z),o)}function Gm(e,t,n){let r=ye(t,e),o=nd(n),i=e[xe].rendererFactory,s=16;n.signals?s=4096:n.onPush&&(s=64);let a=go(e,ho(e,o,null,s,r,t,null,i.createRenderer(r,n),null,null,null));e[t.index]=a}function zm(e,t,n,r,o,i){let s=ye(e,t);Wm(t[j],s,i,e.value,n,r,o)}function Wm(e,t,n,r,o,i,s){if(i==null)e.removeAttribute(t,o,n);else{let a=s==null?ce(i):s(i,r||"",o);e.setAttribute(t,o,a,n)}}function qm(e,t,n,r,o,i){let s=i[t];if(s!==null)for(let a=0;a<s.length;){let u=s[a++],c=s[a++],l=s[a++],d=s[a++];Xl(r,n,u,c,l,d)}}function Ym(e,t,n){let r=null,o=0;for(;o<n.length;){let i=n[o];if(i===0){o+=4;continue}else if(i===5){o+=2;continue}if(typeof i=="number")break;if(e.hasOwnProperty(i)){r===null&&(r=[]);let s=e[i];for(let a=0;a<s.length;a+=3)if(s[a]===t){r.push(i,s[a+1],s[a+2],n[o+1]);break}}o+=2}return r}function id(e,t,n,r){return[e,!0,0,t,null,r,null,n,null,null]}function sd(e,t){let n=e.contentQueries;if(n!==null){let r=_(null);try{for(let o=0;o<n.length;o+=2){let i=n[o],s=n[o+1];if(s!==-1){let a=e.data[s];Xs(i),a.contentQueries(2,t[s],s)}}}finally{_(r)}}}function go(e,t){return e[_n]?e[Vu][Me]=t:e[_n]=t,e[Vu]=t,t}function Yi(e,t,n){Xs(0);let r=_(null);try{t(e,n)}finally{_(r)}}function ad(e){return e[bn]||(e[bn]=[])}function ud(e){return e.cleanup||(e.cleanup=[])}function cd(e,t,n){return(e===null||$e(e))&&(n=Oh(n[t.index])),n[j]}function ld(e,t){let n=e[Jt],r=n?n.get(Ct,null):null;r&&r.handleError(t)}function ga(e,t,n,r,o){for(let i=0;i<n.length;){let s=n[i++],a=n[i++],u=n[i++],c=t[s],l=e.data[s];Xl(l,c,r,a,u,o)}}function mo(e,t,n){let r=tl(t,e);im(e[j],r,n)}function Qm(e,t){let n=nt(t,e),r=n[I];Zm(r,n);let o=n[Le];o!==null&&n[Kt]===null&&(n[Kt]=ia(o,n[Jt])),ma(r,n,n[K])}function Zm(e,t){for(let n=t.length;n<e.blueprint.length;n++)t.push(e.blueprint[n])}function ma(e,t,n){ea(t);try{let r=e.viewQuery;r!==null&&Yi(1,r,n);let o=e.template;o!==null&&td(e,t,o,1,n),e.firstCreatePass&&(e.firstCreatePass=!1),t[Be]?.finishViewCreation(e),e.staticContentQueries&&sd(e,t),e.staticViewQueries&&Yi(2,e.viewQuery,n);let i=e.components;i!==null&&Km(t,i)}catch(r){throw e.firstCreatePass&&(e.incompleteFirstPass=!0,e.firstCreatePass=!1),r}finally{t[v]&=-5,ta()}}function Km(e,t){for(let n=0;n<t.length;n++)Qm(e,t[n])}function yo(e,t,n,r){let o=_(null);try{let i=t.tView,a=e[v]&4096?4096:16,u=ho(e,i,n,a,null,t,null,null,r?.injector??null,r?.embeddedViewInjector??null,r?.dehydratedView??null),c=e[t.index];u[kn]=c;let l=e[Be];return l!==null&&(u[Be]=l.createEmbeddedView(i)),ma(i,u,n),u}finally{_(o)}}function dd(e,t){let n=J+t;if(n<e.length)return e[n]}function Nn(e,t){return!t||t.firstChild===null||Ml(e)}function Do(e,t,n,r=!0){let o=t[I];if(lm(o,t,e,n),r){let s=Wi(n,e),a=t[j],u=ua(a,e[vt]);u!==null&&um(o,e[de],a,t,u,s)}let i=t[Kt];i!==null&&i.firstChild!==null&&(i.firstChild=null)}function fd(e,t){let n=Tn(e,t);return n!==void 0&&lo(n[I],n),n}function $r(e,t,n,r,o=!1){for(;n!==null;){let i=t[n.index];i!==null&&r.push(ke(i)),Ue(i)&&Jm(i,r);let s=n.type;if(s&8)$r(e,t,n.child,r);else if(s&32){let a=aa(n,t),u;for(;u=a();)r.push(u)}else if(s&16){let a=Yl(t,n);if(Array.isArray(a))r.push(...a);else{let u=xn(t[me]);$r(u[I],u,a,r,!0)}}n=o?n.projectionNext:n.next}return r}function Jm(e,t){for(let n=J;n<e.length;n++){let r=e[n],o=r[I].firstChild;o!==null&&$r(r[I],r,o,t)}e[vt]!==e[Le]&&t.push(e[vt])}var pd=[];function Xm(e){return e[Dt]??ey(e)}function ey(e){let t=pd.pop()??Object.create(ny);return t.lView=e,t}function ty(e){e.lView[Dt]!==e&&(e.lView=null,pd.push(e))}var ny=ln(it({},Qa),{consumerIsAlwaysLive:!0,consumerMarkedDirty:e=>{Mn(e.lView)},consumerOnSignalRead(){this.lView[Dt]=this}}),hd=100;function gd(e,t=!0,n=0){let r=e[xe],o=r.rendererFactory,i=!1;i||o.begin?.();try{ry(e,n)}catch(s){throw t&&ld(e,s),s}finally{i||(o.end?.(),r.inlineEffectRunner?.flush())}}function ry(e,t){Qi(e,t);let n=0;for(;Ys(e);){if(n===hd)throw new C(103,!1);n++,Qi(e,1)}}function oy(e,t,n,r){let o=t[v];if((o&256)===256)return;let i=!1;!i&&t[xe].inlineEffectRunner?.flush(),ea(t);let s=null,a=null;!i&&iy(e)&&(a=Xm(t),s=Za(a));try{nl(t),zh(e.bindingStartIndex),n!==null&&td(e,t,n,2,r);let u=(o&3)===3;if(!i)if(u){let d=e.preOrderCheckHooks;d!==null&&_r(t,d,null)}else{let d=e.preOrderHooks;d!==null&&Mr(t,d,0,null),ai(t,0)}if(sy(t),md(t,0),e.contentQueries!==null&&sd(e,t),!i)if(u){let d=e.contentCheckHooks;d!==null&&_r(t,d)}else{let d=e.contentHooks;d!==null&&Mr(t,d,1),ai(t,1)}bm(e,t);let c=e.components;c!==null&&Dd(t,c,0);let l=e.viewQuery;if(l!==null&&Yi(2,l,r),!i)if(u){let d=e.viewCheckHooks;d!==null&&_r(t,d)}else{let d=e.viewHooks;d!==null&&Mr(t,d,2),ai(t,2)}if(e.firstUpdatePass===!0&&(e.firstUpdatePass=!1),t[si]){for(let d of t[si])d();t[si]=null}i||(t[v]&=-73)}catch(u){throw Mn(t),u}finally{a!==null&&(Ka(a,s),ty(a)),ta()}}function iy(e){return e.type!==2}function md(e,t){for(let n=Sl(e);n!==null;n=Tl(n))for(let r=J;r<n.length;r++){let o=n[r];yd(o,t)}}function sy(e){for(let t=Sl(e);t!==null;t=Tl(t)){if(!(t[v]&Hs.HasTransplantedViews))continue;let n=t[Xt];for(let r=0;r<n.length;r++){let o=n[r],i=o[q];Ph(o)}}}function ay(e,t,n){let r=nt(t,e);yd(r,n)}function yd(e,t){qs(e)&&Qi(e,t)}function Qi(e,t){let r=e[I],o=e[v],i=e[Dt],s=!!(t===0&&o&16);if(s||=!!(o&64&&t===0),s||=!!(o&1024),s||=!!(i?.dirty&&Lo(i)),i&&(i.dirty=!1),e[v]&=-9217,s)oy(r,e,r.template,e[K]);else if(o&8192){md(e,1);let a=r.components;a!==null&&Dd(e,a,1)}}function Dd(e,t,n){for(let r=0;r<t.length;r++)ay(e,t[r],n)}function ya(e){for(e[xe].changeDetectionScheduler?.notify();e;){e[v]|=64;let t=xn(e);if(_h(e)&&!t)return e;e=t}return null}var bt=class{get rootNodes(){let t=this._lView,n=t[I];return $r(n,t,n.firstChild,[])}constructor(t,n,r=!0){this._lView=t,this._cdRefInjectingView=n,this.notifyErrorHandler=r,this._appRef=null,this._attachedToViewContainer=!1}get context(){return this._lView[K]}set context(t){this._lView[K]=t}get destroyed(){return(this._lView[v]&256)===256}destroy(){if(this._appRef)this._appRef.detachView(this);else if(this._attachedToViewContainer){let t=this._lView[q];if(Ue(t)){let n=t[Pr],r=n?n.indexOf(this):-1;r>-1&&(Tn(t,r),Or(n,r))}this._attachedToViewContainer=!1}lo(this._lView[I],this._lView)}onDestroy(t){rl(this._lView,t)}markForCheck(){ya(this._cdRefInjectingView||this._lView)}detach(){this._lView[v]&=-129}reattach(){Ti(this._lView),this._lView[v]|=128}detectChanges(){this._lView[v]|=1024,gd(this._lView,this.notifyErrorHandler)}checkNoChanges(){}attachToViewContainerRef(){if(this._appRef)throw new C(902,!1);this._attachedToViewContainer=!0}detachFromAppRef(){this._appRef=null,Ul(this._lView[I],this._lView)}attachToAppRef(t){if(this._attachedToViewContainer)throw new C(902,!1);this._appRef=t,Ti(this._lView)}},_t=(()=>{class e{static{this.__NG_ELEMENT_ID__=ly}}return e})(),uy=_t,cy=class extends uy{constructor(t,n,r){super(),this._declarationLView=t,this._declarationTContainer=n,this.elementRef=r}get ssrId(){return this._declarationTContainer.tView?.ssrId||null}createEmbeddedView(t,n){return this.createEmbeddedViewImpl(t,n)}createEmbeddedViewImpl(t,n,r){let o=yo(this._declarationLView,this._declarationTContainer,t,{embeddedViewInjector:n,dehydratedView:r});return new bt(o)}};function ly(){return vo(Q(),y())}function vo(e,t){return e.type&4?new cy(t,e,un(e,t)):null}var wS=new RegExp(`^(\\d+)*(${xg}|${Mg})*(.*)`);var dy=()=>null;function An(e,t){return dy(e,t)}var Hr=class{},Zi=class{},Ur=class{};function fy(e){let t=Error(`No component factory found for ${te(e)}.`);return t[py]=e,t}var py="ngComponent";var Ki=class{resolveComponentFactory(t){throw fy(t)}},wo=(()=>{class e{static{this.NULL=new Ki}}return e})(),Ji=class{},Io=(()=>{class e{constructor(){this.destroyNode=null}static{this.__NG_ELEMENT_ID__=()=>hy()}}return e})();function hy(){let e=y(),t=Q(),n=nt(t.index,e);return(gt(n)?n:e)[j]}var gy=(()=>{class e{static{this.\u0275prov=k({token:e,providedIn:"root",factory:()=>null})}}return e})(),fi={};var tc=new Set;function Hn(e){tc.has(e)||(tc.add(e),performance?.mark?.("mark_feature_usage",{detail:{feature:e}}))}function nc(...e){}function my(){let e=typeof ue.requestAnimationFrame=="function",t=ue[e?"requestAnimationFrame":"setTimeout"],n=ue[e?"cancelAnimationFrame":"clearTimeout"];if(typeof Zone<"u"&&t&&n){let r=t[Zone.__symbol__("OriginalDelegate")];r&&(t=r);let o=n[Zone.__symbol__("OriginalDelegate")];o&&(n=o)}return{nativeRequestAnimationFrame:t,nativeCancelAnimationFrame:n}}var le=class e{constructor({enableLongStackTrace:t=!1,shouldCoalesceEventChangeDetection:n=!1,shouldCoalesceRunChangeDetection:r=!1}){if(this.hasPendingMacrotasks=!1,this.hasPendingMicrotasks=!1,this.isStable=!0,this.onUnstable=new _e(!1),this.onMicrotaskEmpty=new _e(!1),this.onStable=new _e(!1),this.onError=new _e(!1),typeof Zone>"u")throw new C(908,!1);Zone.assertZonePatched();let o=this;o._nesting=0,o._outer=o._inner=Zone.current,Zone.TaskTrackingZoneSpec&&(o._inner=o._inner.fork(new Zone.TaskTrackingZoneSpec)),t&&Zone.longStackTraceZoneSpec&&(o._inner=o._inner.fork(Zone.longStackTraceZoneSpec)),o.shouldCoalesceEventChangeDetection=!r&&n,o.shouldCoalesceRunChangeDetection=r,o.lastRequestAnimationFrameId=-1,o.nativeRequestAnimationFrame=my().nativeRequestAnimationFrame,vy(o)}static isInAngularZone(){return typeof Zone<"u"&&Zone.current.get("isAngularZone")===!0}static assertInAngularZone(){if(!e.isInAngularZone())throw new C(909,!1)}static assertNotInAngularZone(){if(e.isInAngularZone())throw new C(909,!1)}run(t,n,r){return this._inner.run(t,n,r)}runTask(t,n,r,o){let i=this._inner,s=i.scheduleEventTask("NgZoneEvent: "+o,t,yy,nc,nc);try{return i.runTask(s,n,r)}finally{i.cancelTask(s)}}runGuarded(t,n,r){return this._inner.runGuarded(t,n,r)}runOutsideAngular(t){return this._outer.run(t)}},yy={};function Da(e){if(e._nesting==0&&!e.hasPendingMicrotasks&&!e.isStable)try{e._nesting++,e.onMicrotaskEmpty.emit(null)}finally{if(e._nesting--,!e.hasPendingMicrotasks)try{e.runOutsideAngular(()=>e.onStable.emit(null))}finally{e.isStable=!0}}}function Dy(e){e.isCheckStableRunning||e.lastRequestAnimationFrameId!==-1||(e.lastRequestAnimationFrameId=e.nativeRequestAnimationFrame.call(ue,()=>{e.fakeTopEventTask||(e.fakeTopEventTask=Zone.root.scheduleEventTask("fakeTopEventTask",()=>{e.lastRequestAnimationFrameId=-1,Xi(e),e.isCheckStableRunning=!0,Da(e),e.isCheckStableRunning=!1},void 0,()=>{},()=>{})),e.fakeTopEventTask.invoke()}),Xi(e))}function vy(e){let t=()=>{Dy(e)};e._inner=e._inner.fork({name:"angular",properties:{isAngularZone:!0},onInvokeTask:(n,r,o,i,s,a)=>{if(wy(a))return n.invokeTask(o,i,s,a);try{return rc(e),n.invokeTask(o,i,s,a)}finally{(e.shouldCoalesceEventChangeDetection&&i.type==="eventTask"||e.shouldCoalesceRunChangeDetection)&&t(),oc(e)}},onInvoke:(n,r,o,i,s,a,u)=>{try{return rc(e),n.invoke(o,i,s,a,u)}finally{e.shouldCoalesceRunChangeDetection&&t(),oc(e)}},onHasTask:(n,r,o,i)=>{n.hasTask(o,i),r===o&&(i.change=="microTask"?(e._hasPendingMicrotasks=i.microTask,Xi(e),Da(e)):i.change=="macroTask"&&(e.hasPendingMacrotasks=i.macroTask))},onHandleError:(n,r,o,i)=>(n.handleError(o,i),e.runOutsideAngular(()=>e.onError.emit(i)),!1)})}function Xi(e){e._hasPendingMicrotasks||(e.shouldCoalesceEventChangeDetection||e.shouldCoalesceRunChangeDetection)&&e.lastRequestAnimationFrameId!==-1?e.hasPendingMicrotasks=!0:e.hasPendingMicrotasks=!1}function rc(e){e._nesting++,e.isStable&&(e.isStable=!1,e.onUnstable.emit(null))}function oc(e){e._nesting--,Da(e)}var es=class{constructor(){this.hasPendingMicrotasks=!1,this.hasPendingMacrotasks=!1,this.isStable=!0,this.onUnstable=new _e,this.onMicrotaskEmpty=new _e,this.onStable=new _e,this.onError=new _e}run(t,n,r){return t.apply(n,r)}runGuarded(t,n,r){return t.apply(n,r)}runOutsideAngular(t){return t()}runTask(t,n,r,o){return t.apply(n,r)}};function wy(e){return!Array.isArray(e)||e.length!==1?!1:e[0].data?.__ignore_ng_zone__===!0}function Iy(e="zone.js",t){return e==="noop"?new es:e==="zone.js"?new le(t):e}var Wt=function(e){return e[e.EarlyRead=0]="EarlyRead",e[e.Write=1]="Write",e[e.MixedReadWrite=2]="MixedReadWrite",e[e.Read=3]="Read",e}(Wt||{}),Ey={destroy(){}};function Cy(e,t){!t&&Eh(Cy);let n=t?.injector??F(St);if(!nm(n))return Ey;Hn("NgAfterNextRender");let r=n.get(va),o=r.handler??=new ns,i=t?.phase??Wt.MixedReadWrite,s=()=>{o.unregister(u),a()},a=n.get(ra).onDestroy(s),u=Ih(n,()=>new ts(i,()=>{s(),e()}));return o.register(u),{destroy:s}}var ts=class{constructor(t,n){this.phase=t,this.callbackFn=n,this.zone=F(le),this.errorHandler=F(Ct,{optional:!0}),F(Hr,{optional:!0})?.notify(1)}invoke(){try{this.zone.runOutsideAngular(this.callbackFn)}catch(t){this.errorHandler?.handleError(t)}}},ns=class{constructor(){this.executingCallbacks=!1,this.buckets={[Wt.EarlyRead]:new Set,[Wt.Write]:new Set,[Wt.MixedReadWrite]:new Set,[Wt.Read]:new Set},this.deferredCallbacks=new Set}register(t){(this.executingCallbacks?this.deferredCallbacks:this.buckets[t.phase]).add(t)}unregister(t){this.buckets[t.phase].delete(t),this.deferredCallbacks.delete(t)}execute(){this.executingCallbacks=!0;for(let t of Object.values(this.buckets))for(let n of t)n.invoke();this.executingCallbacks=!1;for(let t of this.deferredCallbacks)this.buckets[t.phase].add(t);this.deferredCallbacks.clear()}destroy(){for(let t of Object.values(this.buckets))t.clear();this.deferredCallbacks.clear()}},va=(()=>{class e{constructor(){this.handler=null,this.internalCallbacks=[]}execute(){this.executeInternalCallbacks(),this.handler?.execute()}executeInternalCallbacks(){let n=[...this.internalCallbacks];this.internalCallbacks.length=0;for(let r of n)r()}ngOnDestroy(){this.handler?.destroy(),this.handler=null,this.internalCallbacks.length=0}static{this.\u0275prov=k({token:e,providedIn:"root",factory:()=>new e})}}return e})();function Gr(e,t,n){let r=n?e.styles:null,o=n?e.classes:null,i=0;if(t!==null)for(let s=0;s<t.length;s++){let a=t[s];if(typeof a=="number")i=a;else if(i==1)o=wi(o,a);else if(i==2){let u=a,c=t[++s];r=wi(r,u+": "+c+";")}}n?e.styles=r:e.stylesWithoutHost=r,n?e.classes=o:e.classesWithoutHost=o}var zr=class extends wo{constructor(t){super(),this.ngModule=t}resolveComponentFactory(t){let n=Je(t);return new tn(n,this.ngModule)}};function ic(e){let t=[];for(let n in e){if(!e.hasOwnProperty(n))continue;let r=e[n];r!==void 0&&t.push({propName:Array.isArray(r)?r[0]:r,templateName:n})}return t}function by(e){let t=e.toLowerCase();return t==="svg"?el:t==="math"?Th:null}var rs=class{constructor(t,n){this.injector=t,this.parentInjector=n}get(t,n,r){r=to(r);let o=this.injector.get(t,fi,r);return o!==fi||n===fi?o:this.parentInjector.get(t,n,r)}},tn=class extends Ur{get inputs(){let t=this.componentDef,n=t.inputTransforms,r=ic(t.inputs);if(n!==null)for(let o of r)n.hasOwnProperty(o.propName)&&(o.transform=n[o.propName]);return r}get outputs(){return ic(this.componentDef.outputs)}constructor(t,n){super(),this.componentDef=t,this.ngModule=n,this.componentType=t.type,this.selector=oh(t.selectors),this.ngContentSelectors=t.ngContentSelectors?t.ngContentSelectors:[],this.isBoundToModule=!!n}create(t,n,r,o){let i=_(null);try{o=o||this.ngModule;let s=o instanceof Xe?o:o?.injector;s&&this.componentDef.getStandaloneInjector!==null&&(s=this.componentDef.getStandaloneInjector(s)||s);let a=s?new rs(t,s):t,u=a.get(Ji,null);if(u===null)throw new C(407,!1);let c=a.get(gy,null),l=a.get(va,null),d=a.get(Hr,null),p={rendererFactory:u,sanitizer:c,inlineEffectRunner:null,afterRenderEventManager:l,changeDetectionScheduler:d},f=u.createRenderer(null,this.componentDef),h=this.componentDef.selectors[0][0]||"div",g=r?xm(f,r,this.componentDef.encapsulation,a):Hl(f,h,by(h)),M=512;this.componentDef.signals?M|=4096:this.componentDef.onPush||(M|=16);let b=null;g!==null&&(b=ia(g,a,!0));let z=pa(0,null,null,1,0,null,null,null,null,null,null),W=ho(null,z,null,M,null,null,p,f,a,null,b);ea(W);let ae,je;try{let ve=this.componentDef,Nt,Ro=null;ve.findHostDirectiveDefs?(Nt=[],Ro=new Map,ve.findHostDirectiveDefs(ve,Nt,Ro),Nt.push(ve)):Nt=[ve];let Df=_y(W,g),vf=My(Df,g,ve,Nt,W,p,f);je=zs(z,Y),g&&Ty(f,ve,g,r),n!==void 0&&Ny(je,this.ngContentSelectors,n),ae=Sy(vf,ve,Nt,Ro,W,[Ay]),ma(z,W,null)}finally{ta()}return new os(this.componentType,ae,un(je,W),W,je)}finally{_(i)}}},os=class extends Zi{constructor(t,n,r,o,i){super(),this.location=r,this._rootLView=o,this._tNode=i,this.previousInputValues=null,this.instance=n,this.hostView=this.changeDetectorRef=new bt(o,void 0,!1),this.componentType=t}setInput(t,n){let r=this._tNode.inputs,o;if(r!==null&&(o=r[t])){if(this.previousInputValues??=new Map,this.previousInputValues.has(t)&&Object.is(this.previousInputValues.get(t),n))return;let i=this._rootLView;ga(i[I],i,o,t,n),this.previousInputValues.set(t,n);let s=nt(this._tNode.index,i);ya(s)}}get injector(){return new mt(this._tNode,this._rootLView)}destroy(){this.hostView.destroy()}onDestroy(t){this.hostView.onDestroy(t)}};function _y(e,t){let n=e[I],r=Y;return e[r]=t,cn(n,r,2,"#host",null)}function My(e,t,n,r,o,i,s){let a=o[I];xy(r,e,t,s);let u=null;t!==null&&(u=ia(t,o[Jt]));let c=i.rendererFactory.createRenderer(t,n),l=16;n.signals?l=4096:n.onPush&&(l=64);let d=ho(o,nd(n),null,l,o[e.index],e,i,c,null,null,u);return a.firstCreatePass&&qi(a,e,r.length-1),go(o,d),o[e.index]=d}function xy(e,t,n,r){for(let o of e)t.mergedAttrs=En(t.mergedAttrs,o.hostAttrs);t.mergedAttrs!==null&&(Gr(t,t.mergedAttrs,!0),n!==null&&Kl(r,n,t))}function Sy(e,t,n,r,o,i){let s=Q(),a=o[I],u=ye(s,o);rd(a,o,s,n,null,r);for(let l=0;l<n.length;l++){let d=s.directiveStart+l,p=Et(o,a,d,s);et(p,o)}od(a,o,s),u&&et(u,o);let c=Et(o,a,s.directiveStart+s.componentOffset,s);if(e[K]=o[K]=c,i!==null)for(let l of i)l(c,t);return la(a,s,o),c}function Ty(e,t,n,r){if(r)bi(e,n,["ng-version","17.3.4"]);else{let{attrs:o,classes:i}=ih(t.selectors[0]);o&&bi(e,n,o),i&&i.length>0&&Zl(e,n,i.join(" "))}}function Ny(e,t,n){let r=e.projection=[];for(let o=0;o<t.length;o++){let i=n[o];r.push(i!=null?Array.from(i):null)}}function Ay(){let e=Q();uo(y()[I],e)}var Tt=(()=>{class e{static{this.__NG_ELEMENT_ID__=Oy}}return e})();function Oy(){let e=Q();return wd(e,y())}var Fy=Tt,vd=class extends Fy{constructor(t,n,r){super(),this._lContainer=t,this._hostTNode=n,this._hostLView=r}get element(){return un(this._hostTNode,this._hostLView)}get injector(){return new mt(this._hostTNode,this._hostLView)}get parentInjector(){let t=na(this._hostTNode,this._hostLView);if(gl(t)){let n=Lr(t,this._hostLView),r=kr(t),o=n[I].data[r+8];return new mt(o,n)}else return new mt(null,this._hostLView)}clear(){for(;this.length>0;)this.remove(this.length-1)}get(t){let n=sc(this._lContainer);return n!==null&&n[t]||null}get length(){return this._lContainer.length-J}createEmbeddedView(t,n,r){let o,i;typeof r=="number"?o=r:r!=null&&(o=r.index,i=r.injector);let s=An(this._lContainer,t.ssrId),a=t.createEmbeddedViewImpl(n||{},i,s);return this.insertImpl(a,o,Nn(this._hostTNode,s)),a}createComponent(t,n,r,o,i){let s=t&&!bh(t),a;if(s)a=n;else{let h=n||{};a=h.index,r=h.injector,o=h.projectableNodes,i=h.environmentInjector||h.ngModuleRef}let u=s?t:new tn(Je(t)),c=r||this.parentInjector;if(!i&&u.ngModule==null){let g=(s?c:this.parentInjector).get(Xe,null);g&&(i=g)}let l=Je(u.componentType??{}),d=An(this._lContainer,l?.id??null),p=d?.firstChild??null,f=u.create(c,o,p,i);return this.insertImpl(f.hostView,a,Nn(this._hostTNode,d)),f}insert(t,n){return this.insertImpl(t,n,!0)}insertImpl(t,n,r){let o=t._lView;if(Rh(o)){let a=this.indexOf(t);if(a!==-1)this.detach(a);else{let u=o[q],c=new vd(u,u[de],u[q]);c.detach(c.indexOf(t))}}let i=this._adjustIndex(n),s=this._lContainer;return Do(s,o,i,r),t.attachToViewContainerRef(),Ac(pi(s),i,t),t}move(t,n){return this.insert(t,n)}indexOf(t){let n=sc(this._lContainer);return n!==null?n.indexOf(t):-1}remove(t){let n=this._adjustIndex(t,-1),r=Tn(this._lContainer,n);r&&(Or(pi(this._lContainer),n),lo(r[I],r))}detach(t){let n=this._adjustIndex(t,-1),r=Tn(this._lContainer,n);return r&&Or(pi(this._lContainer),n)!=null?new bt(r):null}_adjustIndex(t,n=0){return t??this.length+n}};function sc(e){return e[Pr]}function pi(e){return e[Pr]||(e[Pr]=[])}function wd(e,t){let n,r=t[e.index];return Ue(r)?n=r:(n=id(r,t,null,e),t[e.index]=n,go(t,n)),Py(n,t,e,r),new vd(n,e,t)}function Ry(e,t){let n=e[j],r=n.createComment(""),o=ye(t,e),i=ua(n,o);return Br(n,i,r,mm(n,o),!1),r}var Py=jy,ky=()=>!1;function Ly(e,t,n){return ky(e,t,n)}function jy(e,t,n,r){if(e[vt])return;let o;n.type&8?o=ke(r):o=Ry(t,n),e[vt]=o}var is=class e{constructor(t){this.queryList=t,this.matches=null}clone(){return new e(this.queryList)}setDirty(){this.queryList.setDirty()}},ss=class e{constructor(t=[]){this.queries=t}createEmbeddedView(t){let n=t.queries;if(n!==null){let r=t.contentQueries!==null?t.contentQueries[0]:n.length,o=[];for(let i=0;i<r;i++){let s=n.getByIndex(i),a=this.queries[s.indexInDeclarationView];o.push(a.clone())}return new e(o)}return null}insertView(t){this.dirtyQueriesWithMatches(t)}detachView(t){this.dirtyQueriesWithMatches(t)}finishViewCreation(t){this.dirtyQueriesWithMatches(t)}dirtyQueriesWithMatches(t){for(let n=0;n<this.queries.length;n++)wa(t,n).matches!==null&&this.queries[n].setDirty()}},Wr=class{constructor(t,n,r=null){this.flags=n,this.read=r,typeof t=="string"?this.predicate=Wy(t):this.predicate=t}},as=class e{constructor(t=[]){this.queries=t}elementStart(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].elementStart(t,n)}elementEnd(t){for(let n=0;n<this.queries.length;n++)this.queries[n].elementEnd(t)}embeddedTView(t){let n=null;for(let r=0;r<this.length;r++){let o=n!==null?n.length:0,i=this.getByIndex(r).embeddedTView(t,o);i&&(i.indexInDeclarationView=r,n!==null?n.push(i):n=[i])}return n!==null?new e(n):null}template(t,n){for(let r=0;r<this.queries.length;r++)this.queries[r].template(t,n)}getByIndex(t){return this.queries[t]}get length(){return this.queries.length}track(t){this.queries.push(t)}},us=class e{constructor(t,n=-1){this.metadata=t,this.matches=null,this.indexInDeclarationView=-1,this.crossesNgTemplate=!1,this._appliesToNextNode=!0,this._declarationNodeIndex=n}elementStart(t,n){this.isApplyingToNode(n)&&this.matchTNode(t,n)}elementEnd(t){this._declarationNodeIndex===t.index&&(this._appliesToNextNode=!1)}template(t,n){this.elementStart(t,n)}embeddedTView(t,n){return this.isApplyingToNode(t)?(this.crossesNgTemplate=!0,this.addMatch(-t.index,n),new e(this.metadata)):null}isApplyingToNode(t){if(this._appliesToNextNode&&(this.metadata.flags&1)!==1){let n=this._declarationNodeIndex,r=t.parent;for(;r!==null&&r.type&8&&r.index!==n;)r=r.parent;return n===(r!==null?r.index:-1)}return this._appliesToNextNode}matchTNode(t,n){let r=this.metadata.predicate;if(Array.isArray(r))for(let o=0;o<r.length;o++){let i=r[o];this.matchTNodeWithReadOption(t,n,Vy(n,i)),this.matchTNodeWithReadOption(t,n,xr(n,t,i,!1,!1))}else r===_t?n.type&4&&this.matchTNodeWithReadOption(t,n,-1):this.matchTNodeWithReadOption(t,n,xr(n,t,r,!1,!1))}matchTNodeWithReadOption(t,n,r){if(r!==null){let o=this.metadata.read;if(o!==null)if(o===ot||o===Tt||o===_t&&n.type&4)this.addMatch(n.index,-2);else{let i=xr(n,t,o,!1,!1);i!==null&&this.addMatch(n.index,i)}else this.addMatch(n.index,r)}}addMatch(t,n){this.matches===null?this.matches=[t,n]:this.matches.push(t,n)}};function Vy(e,t){let n=e.localNames;if(n!==null){for(let r=0;r<n.length;r+=2)if(n[r]===t)return n[r+1]}return null}function By(e,t){return e.type&11?un(e,t):e.type&4?vo(e,t):null}function $y(e,t,n,r){return n===-1?By(t,e):n===-2?Hy(e,t,r):Et(e,e[I],n,t)}function Hy(e,t,n){if(n===ot)return un(t,e);if(n===_t)return vo(t,e);if(n===Tt)return wd(t,e)}function Id(e,t,n,r){let o=t[Be].queries[r];if(o.matches===null){let i=e.data,s=n.matches,a=[];for(let u=0;s!==null&&u<s.length;u+=2){let c=s[u];if(c<0)a.push(null);else{let l=i[c];a.push($y(t,l,s[u+1],n.metadata.read))}}o.matches=a}return o.matches}function cs(e,t,n,r){let o=e.queries.getByIndex(n),i=o.matches;if(i!==null){let s=Id(e,t,o,n);for(let a=0;a<i.length;a+=2){let u=i[a];if(u>0)r.push(s[a/2]);else{let c=i[a+1],l=t[-u];for(let d=J;d<l.length;d++){let p=l[d];p[kn]===p[q]&&cs(p[I],p,c,r)}if(l[Xt]!==null){let d=l[Xt];for(let p=0;p<d.length;p++){let f=d[p];cs(f[I],f,c,r)}}}}}return r}function Uy(e,t){return e[Be].queries[t].queryList}function Ed(e,t,n){let r=new ki((n&4)===4);return Nm(e,t,r,r.destroy),(t[Be]??=new ss).queries.push(new is(r))-1}function Gy(e,t,n){let r=V();return r.firstCreatePass&&(Cd(r,new Wr(e,t,n),-1),(t&2)===2&&(r.staticViewQueries=!0)),Ed(r,y(),t)}function zy(e,t,n,r){let o=V();if(o.firstCreatePass){let i=Q();Cd(o,new Wr(t,n,r),i.index),qy(o,e),(n&2)===2&&(o.staticContentQueries=!0)}return Ed(o,y(),n)}function Wy(e){return e.split(",").map(t=>t.trim())}function Cd(e,t,n){e.queries===null&&(e.queries=new as),e.queries.track(new us(t,n))}function qy(e,t){let n=e.contentQueries||(e.contentQueries=[]),r=n.length?n[n.length-1]:-1;t!==r&&n.push(e.queries.length-1,t)}function wa(e,t){return e.queries.getByIndex(t)}function Yy(e,t){let n=e[I],r=wa(n,t);return r.crossesNgTemplate?cs(n,e,t,[]):Id(n,e,r,t)}function Qy(e){return typeof e=="function"&&e[ko]!==void 0}function bd(e){return Qy(e)&&typeof e.set=="function"}function Zy(e){let t=[],n=new Map;function r(o){let i=n.get(o);if(!i){let s=e(o);n.set(o,i=s.then(eD))}return i}return qr.forEach((o,i)=>{let s=[];o.templateUrl&&s.push(r(o.templateUrl).then(c=>{o.template=c}));let a=typeof o.styles=="string"?[o.styles]:o.styles||[];if(o.styles=a,o.styleUrl&&o.styleUrls?.length)throw new Error("@Component cannot define both `styleUrl` and `styleUrls`. Use `styleUrl` if the component has one stylesheet, or `styleUrls` if it has multiple");if(o.styleUrls?.length){let c=o.styles.length,l=o.styleUrls;o.styleUrls.forEach((d,p)=>{a.push(""),s.push(r(d).then(f=>{a[c+p]=f,l.splice(l.indexOf(d),1),l.length==0&&(o.styleUrls=void 0)}))})}else o.styleUrl&&s.push(r(o.styleUrl).then(c=>{a.push(c),o.styleUrl=void 0}));let u=Promise.all(s).then(()=>tD(i));t.push(u)}),Jy(),Promise.all(t).then(()=>{})}var qr=new Map,Ky=new Set;function Jy(){let e=qr;return qr=new Map,e}function Xy(){return qr.size===0}function eD(e){return typeof e=="string"?e:e.text()}function tD(e){Ky.delete(e)}function nD(e){return Object.getPrototypeOf(e.prototype).constructor}function rD(e){let t=nD(e.type),n=!0,r=[e];for(;t;){let o;if($e(e))o=t.\u0275cmp||t.\u0275dir;else{if(t.\u0275cmp)throw new C(903,!1);o=t.\u0275dir}if(o){if(n){r.push(o);let s=e;s.inputs=Ir(e.inputs),s.inputTransforms=Ir(e.inputTransforms),s.declaredInputs=Ir(e.declaredInputs),s.outputs=Ir(e.outputs);let a=o.hostBindings;a&&uD(e,a);let u=o.viewQuery,c=o.contentQueries;if(u&&sD(e,u),c&&aD(e,c),oD(e,o),Cp(e.outputs,o.outputs),$e(o)&&o.data.animation){let l=e.data;l.animation=(l.animation||[]).concat(o.data.animation)}}let i=o.features;if(i)for(let s=0;s<i.length;s++){let a=i[s];a&&a.ngInherit&&a(e),a===rD&&(n=!1)}}t=Object.getPrototypeOf(t)}iD(r)}function oD(e,t){for(let n in t.inputs){if(!t.inputs.hasOwnProperty(n)||e.inputs.hasOwnProperty(n))continue;let r=t.inputs[n];if(r!==void 0&&(e.inputs[n]=r,e.declaredInputs[n]=t.declaredInputs[n],t.inputTransforms!==null)){let o=Array.isArray(r)?r[0]:r;if(!t.inputTransforms.hasOwnProperty(o))continue;e.inputTransforms??={},e.inputTransforms[o]=t.inputTransforms[o]}}}function iD(e){let t=0,n=null;for(let r=e.length-1;r>=0;r--){let o=e[r];o.hostVars=t+=o.hostVars,o.hostAttrs=En(o.hostAttrs,n=En(n,o.hostAttrs))}}function Ir(e){return e===Qt?{}:e===ie?[]:e}function sD(e,t){let n=e.viewQuery;n?e.viewQuery=(r,o)=>{t(r,o),n(r,o)}:e.viewQuery=t}function aD(e,t){let n=e.contentQueries;n?e.contentQueries=(r,o,i)=>{t(r,o,i),n(r,o,i)}:e.contentQueries=t}function uD(e,t){let n=e.hostBindings;n?e.hostBindings=(r,o)=>{t(r,o),n(r,o)}:e.hostBindings=t}function cD(e){let t=e.inputConfig,n={};for(let r in t)if(t.hasOwnProperty(r)){let o=t[r];Array.isArray(o)&&o[3]&&(n[r]=o[3])}e.inputTransforms=n}var tt=class{},ls=class{};var Yr=class extends tt{constructor(t,n,r){super(),this._parent=n,this._bootstrapComponents=[],this.destroyCbs=[],this.componentFactoryResolver=new zr(this);let o=Bc(t);this._bootstrapComponents=$l(o.bootstrap),this._r3Injector=bl(t,n,[{provide:tt,useValue:this},{provide:wo,useValue:this.componentFactoryResolver},...r],te(t),new Set(["environment"])),this._r3Injector.resolveInjectorInitializers(),this.instance=this._r3Injector.get(t)}get injector(){return this._r3Injector}destroy(){let t=this._r3Injector;!t.destroyed&&t.destroy(),this.destroyCbs.forEach(n=>n()),this.destroyCbs=null}onDestroy(t){this.destroyCbs.push(t)}},Qr=class extends ls{constructor(t){super(),this.moduleType=t}create(t){return new Yr(this.moduleType,t,[])}};function lD(e,t,n){return new Yr(e,t,n)}var ds=class extends tt{constructor(t){super(),this.componentFactoryResolver=new zr(this),this.instance=null;let n=new Cn([...t.providers,{provide:tt,useValue:this},{provide:wo,useValue:this.componentFactoryResolver}],t.parent||Bs(),t.debugName,new Set(["environment"]));this.injector=n,t.runEnvironmentInitializers&&n.resolveInjectorInitializers()}destroy(){this.injector.destroy()}onDestroy(t){this.injector.onDestroy(t)}};function dD(e,t,n=null){return new ds({providers:e,parent:t,debugName:n,runEnvironmentInitializers:!0}).injector}var _d=(()=>{class e{constructor(){this.taskId=0,this.pendingTasks=new Set,this.hasPendingTasks=new dn(!1)}get _hasPendingTasks(){return this.hasPendingTasks.value}add(){this._hasPendingTasks||this.hasPendingTasks.next(!0);let n=this.taskId++;return this.pendingTasks.add(n),n}remove(n){this.pendingTasks.delete(n),this.pendingTasks.size===0&&this._hasPendingTasks&&this.hasPendingTasks.next(!1)}ngOnDestroy(){this.pendingTasks.clear(),this._hasPendingTasks&&this.hasPendingTasks.next(!1)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Md(e){return Ia(e)?Array.isArray(e)||!(e instanceof Map)&&Symbol.iterator in e:!1}function fD(e,t){if(Array.isArray(e))for(let n=0;n<e.length;n++)t(e[n]);else{let n=e[Symbol.iterator](),r;for(;!(r=n.next()).done;)t(r.value)}}function Ia(e){return e!==null&&(typeof e=="function"||typeof e=="object")}function Ea(e,t,n){return e[t]=n}function pD(e,t){return e[t]}function se(e,t,n){let r=e[t];return Object.is(r,n)?!1:(e[t]=n,!0)}function On(e,t,n,r){let o=se(e,t,n);return se(e,t+1,r)||o}function hD(e,t,n,r,o){let i=On(e,t,n,r);return se(e,t+2,o)||i}function gD(e,t,n,r,o,i){let s=On(e,t,n,r);return On(e,t+2,o,i)||s}function mD(e){return(e.flags&32)===32}function yD(e,t,n,r,o,i,s,a,u){let c=t.consts,l=cn(t,e,4,s||null,en(c,a));ha(t,n,l,en(c,u)),uo(t,l);let d=l.tView=pa(2,l,r,o,i,t.directiveRegistry,t.pipeRegistry,null,t.schemas,c,null);return t.queries!==null&&(t.queries.template(t,l),d.queries=t.queries.embeddedTView(l)),l}function fs(e,t,n,r,o,i,s,a){let u=y(),c=V(),l=e+Y,d=c.firstCreatePass?yD(l,c,u,t,n,r,o,i,s):c.data[l];xt(d,!1);let p=DD(c,u,d,e);so()&&fo(c,u,p,d),et(p,u);let f=id(p,u,p,d);return u[l]=f,go(u,f),Ly(f,d,u),io(d)&&da(c,u,d),s!=null&&fa(u,d,a),fs}var DD=vD;function vD(e,t,n,r){return ao(!0),t[j].createComment("")}function wD(e,t,n,r){let o=y(),i=rt();if(se(o,i,t)){let s=V(),a=an();zm(a,o,e,t,n,r)}return wD}function Ca(e,t,n,r){return se(e,rt(),n)?t+ce(n)+r:Z}function ID(e,t,n,r,o,i){let s=Ks(),a=On(e,s,n,o);return jn(2),a?t+ce(n)+r+ce(o)+i:Z}function ED(e,t,n,r,o,i,s,a){let u=Ks(),c=hD(e,u,n,o,s);return jn(3),c?t+ce(n)+r+ce(o)+i+ce(s)+a:Z}function CD(e,t,n,r,o,i,s,a,u,c){let l=Ks(),d=gD(e,l,n,o,s,u);return jn(4),d?t+ce(n)+r+ce(o)+i+ce(s)+a+ce(u)+c:Z}function Er(e,t){return e<<17|t<<2}function Mt(e){return e>>17&32767}function bD(e){return(e&2)==2}function _D(e,t){return e&131071|t<<17}function ps(e){return e|2}function nn(e){return(e&131068)>>2}function hi(e,t){return e&-131069|t<<2}function MD(e){return(e&1)===1}function hs(e){return e|1}function xD(e,t,n,r,o,i){let s=i?t.classBindings:t.styleBindings,a=Mt(s),u=nn(s);e[r]=n;let c=!1,l;if(Array.isArray(n)){let d=n;l=d[1],(l===null||Pn(d,l)>0)&&(c=!0)}else l=n;if(o)if(u!==0){let p=Mt(e[a+1]);e[r+1]=Er(p,a),p!==0&&(e[p+1]=hi(e[p+1],r)),e[a+1]=_D(e[a+1],r)}else e[r+1]=Er(a,0),a!==0&&(e[a+1]=hi(e[a+1],r)),a=r;else e[r+1]=Er(u,0),a===0?a=r:e[u+1]=hi(e[u+1],r),u=r;c&&(e[r+1]=ps(e[r+1])),ac(e,l,r,!0),ac(e,l,r,!1),SD(t,l,e,r,i),s=Er(a,u),i?t.classBindings=s:t.styleBindings=s}function SD(e,t,n,r,o){let i=o?e.residualClasses:e.residualStyles;i!=null&&typeof t=="string"&&Pn(i,t)>=0&&(n[r+1]=hs(n[r+1]))}function ac(e,t,n,r){let o=e[n+1],i=t===null,s=r?Mt(o):nn(o),a=!1;for(;s!==0&&(a===!1||i);){let u=e[s],c=e[s+1];TD(u,t)&&(a=!0,e[s+1]=r?hs(c):ps(c)),s=r?Mt(c):nn(c)}a&&(e[n+1]=r?ps(o):hs(o))}function TD(e,t){return e===null||t==null||(Array.isArray(e)?e[1]:e)===t?!0:Array.isArray(e)&&typeof t=="string"?Pn(e,t)>=0:!1}var be={textEnd:0,key:0,keyEnd:0,value:0,valueEnd:0};function ND(e){return e.substring(be.key,be.keyEnd)}function AD(e){return OD(e),xd(e,Sd(e,0,be.textEnd))}function xd(e,t){let n=be.textEnd;return n===t?-1:(t=be.keyEnd=FD(e,be.key=t,n),Sd(e,t,n))}function OD(e){be.key=0,be.keyEnd=0,be.value=0,be.valueEnd=0,be.textEnd=e.length}function Sd(e,t,n){for(;t<n&&e.charCodeAt(t)<=32;)t++;return t}function FD(e,t,n){for(;t<n&&e.charCodeAt(t)>32;)t++;return t}function RD(e,t,n){let r=y(),o=rt();if(se(r,o,t)){let i=V(),s=an();$n(i,s,r,e,t,r[j],n,!1)}return RD}function gs(e,t,n,r,o){let i=t.inputs,s=o?"class":"style";ga(e,n,i[s],s,r)}function Td(e,t,n){return Ad(e,t,n,!1),Td}function PD(e,t){return Ad(e,t,null,!0),PD}function ES(e){Od($D,Nd,e,!0)}function Nd(e,t){for(let n=AD(t);n>=0;n=xd(t,n))no(e,ND(t),!0)}function Ad(e,t,n,r){let o=y(),i=V(),s=jn(2);if(i.firstUpdatePass&&Rd(i,e,s,r),t!==Z&&se(o,s,t)){let a=i.data[Se()];Pd(i,a,o,o[j],e,o[s+1]=UD(t,n),r,s)}}function Od(e,t,n,r){let o=V(),i=jn(2);o.firstUpdatePass&&Rd(o,null,i,r);let s=y();if(n!==Z&&se(s,i,n)){let a=o.data[Se()];if(kd(a,r)&&!Fd(o,i)){let u=r?a.classesWithoutHost:a.stylesWithoutHost;u!==null&&(n=wi(u,n||"")),gs(o,a,s,n,r)}else HD(o,a,s,s[j],s[i+1],s[i+1]=BD(e,t,n),r,i)}}function Fd(e,t){return t>=e.expandoStartIndex}function Rd(e,t,n,r){let o=e.data;if(o[n+1]===null){let i=o[Se()],s=Fd(e,n);kd(i,r)&&t===null&&!s&&(t=!1),t=kD(o,i,t,r),xD(o,i,t,n,s,r)}}function kD(e,t,n,r){let o=Js(e),i=r?t.residualClasses:t.residualStyles;if(o===null)(r?t.classBindings:t.styleBindings)===0&&(n=gi(null,e,t,n,r),n=Fn(n,t.attrs,r),i=null);else{let s=t.directiveStylingLast;if(s===-1||e[s]!==o)if(n=gi(o,e,t,n,r),i===null){let u=LD(e,t,r);u!==void 0&&Array.isArray(u)&&(u=gi(null,e,t,u[1],r),u=Fn(u,t.attrs,r),jD(e,t,r,u))}else i=VD(e,t,r)}return i!==void 0&&(r?t.residualClasses=i:t.residualStyles=i),n}function LD(e,t,n){let r=n?t.classBindings:t.styleBindings;if(nn(r)!==0)return e[Mt(r)]}function jD(e,t,n,r){let o=n?t.classBindings:t.styleBindings;e[Mt(o)]=r}function VD(e,t,n){let r,o=t.directiveEnd;for(let i=1+t.directiveStylingLast;i<o;i++){let s=e[i].hostAttrs;r=Fn(r,s,n)}return Fn(r,t.attrs,n)}function gi(e,t,n,r,o){let i=null,s=n.directiveEnd,a=n.directiveStylingLast;for(a===-1?a=n.directiveStart:a++;a<s&&(i=t[a],r=Fn(r,i.hostAttrs,o),i!==e);)a++;return e!==null&&(n.directiveStylingLast=a),r}function Fn(e,t,n){let r=n?1:2,o=-1;if(t!==null)for(let i=0;i<t.length;i++){let s=t[i];typeof s=="number"?o=s:o===r&&(Array.isArray(e)||(e=e===void 0?[]:["",e]),no(e,s,n?!0:t[++i]))}return e===void 0?null:e}function BD(e,t,n){if(n==null||n==="")return ie;let r=[],o=Vn(n);if(Array.isArray(o))for(let i=0;i<o.length;i++)e(r,o[i],!0);else if(typeof o=="object")for(let i in o)o.hasOwnProperty(i)&&e(r,i,o[i]);else typeof o=="string"&&t(r,o);return r}function $D(e,t,n){let r=String(t);r!==""&&!r.includes(" ")&&no(e,r,n)}function HD(e,t,n,r,o,i,s,a){o===Z&&(o=ie);let u=0,c=0,l=0<o.length?o[0]:null,d=0<i.length?i[0]:null;for(;l!==null||d!==null;){let p=u<o.length?o[u+1]:void 0,f=c<i.length?i[c+1]:void 0,h=null,g;l===d?(u+=2,c+=2,p!==f&&(h=d,g=f)):d===null||l!==null&&l<d?(u+=2,h=l):(c+=2,h=d,g=f),h!==null&&Pd(e,t,n,r,h,g,s,a),l=u<o.length?o[u]:null,d=c<i.length?i[c]:null}}function Pd(e,t,n,r,o,i,s,a){if(!(t.type&3))return;let u=e.data,c=u[a+1],l=MD(c)?uc(u,t,n,o,nn(c),s):void 0;if(!Zr(l)){Zr(i)||bD(c)&&(i=uc(u,null,n,o,a,s));let d=tl(Se(),n);Em(r,s,d,o,i)}}function uc(e,t,n,r,o,i){let s=t===null,a;for(;o>0;){let u=e[o],c=Array.isArray(u),l=c?u[1]:u,d=l===null,p=n[o+1];p===Z&&(p=d?ie:void 0);let f=d?oi(p,r):l===r?p:void 0;if(c&&!Zr(f)&&(f=oi(u,r)),Zr(f)&&(a=f,s))return a;let h=e[o+1];o=s?Mt(h):nn(h)}if(t!==null){let u=i?t.residualClasses:t.residualStyles;u!=null&&(a=oi(u,r))}return a}function Zr(e){return e!==void 0}function UD(e,t){return e==null||e===""||(typeof t=="string"?e=e+t:typeof e=="object"&&(e=te(Vn(e)))),e}function kd(e,t){return(e.flags&(t?8:16))!==0}function CS(e,t,n){let r=y(),o=Ca(r,e,t,n);Od(no,Nd,o,!0)}var ms=class{destroy(t){}updateValue(t,n){}swap(t,n){let r=Math.min(t,n),o=Math.max(t,n),i=this.detach(o);if(o-r>1){let s=this.detach(r);this.attach(r,i),this.attach(o,s)}else this.attach(r,i)}move(t,n){this.attach(n,this.detach(t))}};function mi(e,t,n,r,o){return e===n&&Object.is(t,r)?1:Object.is(o(e,t),o(n,r))?-1:0}function GD(e,t,n){let r,o,i=0,s=e.length-1;if(Array.isArray(t)){let a=t.length-1;for(;i<=s&&i<=a;){let u=e.at(i),c=t[i],l=mi(i,u,i,c,n);if(l!==0){l<0&&e.updateValue(i,c),i++;continue}let d=e.at(s),p=t[a],f=mi(s,d,a,p,n);if(f!==0){f<0&&e.updateValue(s,p),s--,a--;continue}let h=n(i,u),g=n(s,d),M=n(i,c);if(Object.is(M,g)){let b=n(a,p);Object.is(b,h)?(e.swap(i,s),e.updateValue(s,p),a--,s--):e.move(s,i),e.updateValue(i,c),i++;continue}if(r??=new Kr,o??=lc(e,i,s,n),ys(e,r,i,M))e.updateValue(i,c),i++,s++;else if(o.has(M))r.set(h,e.detach(i)),s--;else{let b=e.create(i,t[i]);e.attach(i,b),i++,s++}}for(;i<=a;)cc(e,r,n,i,t[i]),i++}else if(t!=null){let a=t[Symbol.iterator](),u=a.next();for(;!u.done&&i<=s;){let c=e.at(i),l=u.value,d=mi(i,c,i,l,n);if(d!==0)d<0&&e.updateValue(i,l),i++,u=a.next();else{r??=new Kr,o??=lc(e,i,s,n);let p=n(i,l);if(ys(e,r,i,p))e.updateValue(i,l),i++,s++,u=a.next();else if(!o.has(p))e.attach(i,e.create(i,l)),i++,s++,u=a.next();else{let f=n(i,c);r.set(f,e.detach(i)),s--}}}for(;!u.done;)cc(e,r,n,e.length,u.value),u=a.next()}for(;i<=s;)e.destroy(e.detach(s--));r?.forEach(a=>{e.destroy(a)})}function ys(e,t,n,r){return t!==void 0&&t.has(r)?(e.attach(n,t.get(r)),t.delete(r),!0):!1}function cc(e,t,n,r,o){if(ys(e,t,r,n(r,o)))e.updateValue(r,o);else{let i=e.create(r,o);e.attach(r,i)}}function lc(e,t,n,r){let o=new Set;for(let i=t;i<=n;i++)o.add(r(i,e.at(i)));return o}var Kr=class{constructor(){this.kvMap=new Map,this._vMap=void 0}has(t){return this.kvMap.has(t)}delete(t){if(!this.has(t))return!1;let n=this.kvMap.get(t);return this._vMap!==void 0&&this._vMap.has(n)?(this.kvMap.set(t,this._vMap.get(n)),this._vMap.delete(n)):this.kvMap.delete(t),!0}get(t){return this.kvMap.get(t)}set(t,n){if(this.kvMap.has(t)){let r=this.kvMap.get(t);this._vMap===void 0&&(this._vMap=new Map);let o=this._vMap;for(;o.has(r);)r=o.get(r);o.set(r,n)}else this.kvMap.set(t,n)}forEach(t){for(let[n,r]of this.kvMap)if(t(r,n),this._vMap!==void 0){let o=this._vMap;for(;o.has(r);)r=o.get(r),t(r,n)}}};function bS(e,t,n){Hn("NgControlFlow");let r=y(),o=rt(),i=Is(r,Y+e),s=0;if(se(r,o,t)){let a=_(null);try{if(fd(i,s),t!==-1){let u=Es(r[I],Y+t),c=An(i,u.tView.ssrId),l=yo(r,u,n,{dehydratedView:c});Do(i,l,s,Nn(u,c))}}finally{_(a)}}else{let a=dd(i,s);a!==void 0&&(a[K]=n)}}var Ds=class{constructor(t,n,r){this.lContainer=t,this.$implicit=n,this.$index=r}get $count(){return this.lContainer.length-J}};function _S(e,t){return t}var vs=class{constructor(t,n,r){this.hasEmptyBlock=t,this.trackByFn=n,this.liveCollection=r}};function MS(e,t,n,r,o,i,s,a,u,c,l,d,p){Hn("NgControlFlow");let f=u!==void 0,h=y(),g=a?s.bind(h[me][K]):s,M=new vs(f,g);h[Y+e]=M,fs(e+1,t,n,r,o,i),f&&fs(e+2,u,c,l,d,p)}var ws=class extends ms{constructor(t,n,r){super(),this.lContainer=t,this.hostLView=n,this.templateTNode=r,this.needsIndexUpdate=!1}get length(){return this.lContainer.length-J}at(t){return this.getLView(t)[K].$implicit}attach(t,n){let r=n[Kt];this.needsIndexUpdate||=t!==this.length,Do(this.lContainer,n,t,Nn(this.templateTNode,r))}detach(t){return this.needsIndexUpdate||=t!==this.length-1,zD(this.lContainer,t)}create(t,n){let r=An(this.lContainer,this.templateTNode.tView.ssrId);return yo(this.hostLView,this.templateTNode,new Ds(this.lContainer,n,t),{dehydratedView:r})}destroy(t){lo(t[I],t)}updateValue(t,n){this.getLView(t)[K].$implicit=n}reset(){this.needsIndexUpdate=!1}updateIndexes(){if(this.needsIndexUpdate)for(let t=0;t<this.length;t++)this.getLView(t)[K].$index=t}getLView(t){return WD(this.lContainer,t)}};function xS(e){let t=_(null),n=Se();try{let r=y(),o=r[I],i=r[n];if(i.liveCollection===void 0){let a=n+1,u=Is(r,a),c=Es(o,a);i.liveCollection=new ws(u,r,c)}else i.liveCollection.reset();let s=i.liveCollection;if(GD(s,e,i.trackByFn),s.updateIndexes(),i.hasEmptyBlock){let a=rt(),u=s.length===0;if(se(r,a,u)){let c=n+2,l=Is(r,c);if(u){let d=Es(o,c),p=An(l,d.tView.ssrId),f=yo(r,d,void 0,{dehydratedView:p});Do(l,f,0,Nn(d,p))}else fd(l,0)}}}finally{_(t)}}function Is(e,t){return e[t]}function zD(e,t){return Tn(e,t)}function WD(e,t){return dd(e,t)}function Es(e,t){return zs(e,t)}function qD(e,t,n,r,o,i){let s=t.consts,a=en(s,o),u=cn(t,e,2,r,a);return ha(t,n,u,en(s,i)),u.attrs!==null&&Gr(u,u.attrs,!1),u.mergedAttrs!==null&&Gr(u,u.mergedAttrs,!0),t.queries!==null&&t.queries.elementStart(t,u),u}function Ld(e,t,n,r){let o=y(),i=V(),s=Y+e,a=o[j],u=i.firstCreatePass?qD(s,i,o,t,n,r):i.data[s],c=QD(i,o,u,a,t,e);o[s]=c;let l=io(u);return xt(u,!0),Kl(a,c,u),!mD(u)&&so()&&fo(i,o,c,u),jh()===0&&et(c,o),Vh(),l&&(da(i,o,u),la(i,u,o)),r!==null&&fa(o,u),Ld}function jd(){let e=Q();Qs()?Zs():(e=e.parent,xt(e,!1));let t=e;$h(t)&&Hh(),Bh();let n=V();return n.firstCreatePass&&(uo(n,e),Us(e)&&n.queries.elementEnd(e)),t.classesWithoutHost!=null&&ng(t)&&gs(n,t,y(),t.classesWithoutHost,!0),t.stylesWithoutHost!=null&&rg(t)&&gs(n,t,y(),t.stylesWithoutHost,!1),jd}function YD(e,t,n,r){return Ld(e,t,n,r),jd(),YD}var QD=(e,t,n,r,o,i)=>(ao(!0),Hl(r,o,Jh()));function ZD(e,t,n,r,o){let i=t.consts,s=en(i,r),a=cn(t,e,8,"ng-container",s);s!==null&&Gr(a,s,!0);let u=en(i,o);return ha(t,n,a,u),t.queries!==null&&t.queries.elementStart(t,a),a}function Vd(e,t,n){let r=y(),o=V(),i=e+Y,s=o.firstCreatePass?ZD(i,o,r,t,n):o.data[i];xt(s,!0);let a=JD(o,r,s,e);return r[i]=a,so()&&fo(o,r,a,s),et(a,r),io(s)&&(da(o,r,s),la(o,s,r)),n!=null&&fa(r,s),Vd}function Bd(){let e=Q(),t=V();return Qs()?Zs():(e=e.parent,xt(e,!1)),t.firstCreatePass&&(uo(t,e),Us(e)&&t.queries.elementEnd(e)),Bd}function KD(e,t,n){return Vd(e,t,n),Bd(),KD}var JD=(e,t,n,r)=>(ao(!0),sm(t[j],""));function SS(){return y()}function XD(e,t,n){let r=y(),o=rt();if(se(r,o,t)){let i=V(),s=an();$n(i,s,r,e,t,r[j],n,!0)}return XD}function ev(e,t,n){let r=y(),o=rt();if(se(r,o,t)){let i=V(),s=an(),a=Js(i.data),u=cd(a,s,r);$n(i,s,r,e,t,u,n,!0)}return ev}var ht=void 0;function tv(e){let t=e,n=Math.floor(Math.abs(e)),r=e.toString().replace(/^[^.]*\.?/,"").length;return n===1&&r===0?1:5}var nv=["en",[["a","p"],["AM","PM"],ht],[["AM","PM"],ht,ht],[["S","M","T","W","T","F","S"],["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],["Su","Mo","Tu","We","Th","Fr","Sa"]],ht,[["J","F","M","A","M","J","J","A","S","O","N","D"],["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],["January","February","March","April","May","June","July","August","September","October","November","December"]],ht,[["B","A"],["BC","AD"],["Before Christ","Anno Domini"]],0,[6,0],["M/d/yy","MMM d, y","MMMM d, y","EEEE, MMMM d, y"],["h:mm a","h:mm:ss a","h:mm:ss a z","h:mm:ss a zzzz"],["{1}, {0}",ht,"{1} 'at' {0}",ht],[".",",",";","%","+","-","E","\xD7","\u2030","\u221E","NaN",":"],["#,##0.###","#,##0%","\xA4#,##0.00","#E0"],"USD","$","US Dollar",{},"ltr",tv],yi={};function fe(e){let t=rv(e),n=dc(t);if(n)return n;let r=t.split("-")[0];if(n=dc(r),n)return n;if(r==="en")return nv;throw new C(701,!1)}function dc(e){return e in yi||(yi[e]=ue.ng&&ue.ng.common&&ue.ng.common.locales&&ue.ng.common.locales[e]),yi[e]}var B=function(e){return e[e.LocaleId=0]="LocaleId",e[e.DayPeriodsFormat=1]="DayPeriodsFormat",e[e.DayPeriodsStandalone=2]="DayPeriodsStandalone",e[e.DaysFormat=3]="DaysFormat",e[e.DaysStandalone=4]="DaysStandalone",e[e.MonthsFormat=5]="MonthsFormat",e[e.MonthsStandalone=6]="MonthsStandalone",e[e.Eras=7]="Eras",e[e.FirstDayOfWeek=8]="FirstDayOfWeek",e[e.WeekendRange=9]="WeekendRange",e[e.DateFormat=10]="DateFormat",e[e.TimeFormat=11]="TimeFormat",e[e.DateTimeFormat=12]="DateTimeFormat",e[e.NumberSymbols=13]="NumberSymbols",e[e.NumberFormats=14]="NumberFormats",e[e.CurrencyCode=15]="CurrencyCode",e[e.CurrencySymbol=16]="CurrencySymbol",e[e.CurrencyName=17]="CurrencyName",e[e.Currencies=18]="Currencies",e[e.Directionality=19]="Directionality",e[e.PluralCase=20]="PluralCase",e[e.ExtraData=21]="ExtraData",e}(B||{});function rv(e){return e.toLowerCase().replace(/_/g,"-")}var Jr="en-US";var ov=Jr;function iv(e){typeof e=="string"&&(ov=e.toLowerCase().replace(/_/g,"-"))}function sv(e,t,n,r){let o=y(),i=V(),s=Q();return ba(i,o,o[j],s,e,t,r),sv}function av(e,t){let n=Q(),r=y(),o=V(),i=Js(o.data),s=cd(i,n,r);return ba(o,r,s,n,e,t),av}function uv(e,t,n,r){let o=e.cleanup;if(o!=null)for(let i=0;i<o.length-1;i+=2){let s=o[i];if(s===n&&o[i+1]===r){let a=t[bn],u=o[i+2];return a.length>u?a[u]:null}typeof s=="string"&&(i+=2)}return null}function ba(e,t,n,r,o,i,s){let a=io(r),c=e.firstCreatePass&&ud(e),l=t[K],d=ad(t),p=!0;if(r.type&3||s){let g=ye(r,t),M=s?s(g):g,b=d.length,z=s?ae=>s(ke(ae[r.index])):r.index,W=null;if(!s&&a&&(W=uv(e,t,o,r.index)),W!==null){let ae=W.__ngLastListenerFn__||W;ae.__ngNextListenerFn__=i,W.__ngLastListenerFn__=i,p=!1}else{i=pc(r,t,l,i,!1);let ae=n.listen(M,o,i);d.push(i,ae),c&&c.push(o,z,b,b+1)}}else i=pc(r,t,l,i,!1);let f=r.outputs,h;if(p&&f!==null&&(h=f[o])){let g=h.length;if(g)for(let M=0;M<g;M+=2){let b=h[M],z=h[M+1],je=t[b][z].subscribe(i),ve=d.length;d.push(i,je),c&&c.push(o,r.index,ve,-(ve+1))}}}function fc(e,t,n,r){let o=_(null);try{return Re(6,t,n),n(r)!==!1}catch(i){return ld(e,i),!1}finally{Re(7,t,n),_(o)}}function pc(e,t,n,r,o){return function i(s){if(s===Function)return r;let a=e.componentOffset>-1?nt(e.index,t):t;ya(a);let u=fc(t,n,r,s),c=i.__ngNextListenerFn__;for(;c;)u=fc(t,n,c,s)&&u,c=c.__ngNextListenerFn__;return o&&u===!1&&s.preventDefault(),u}}function TS(e=1){return Zh(e)}function cv(e,t){let n=null,r=Xp(e);for(let o=0;o<t.length;o++){let i=t[o];if(i==="*"){n=o;continue}if(r===null?Lc(e,i,!0):nh(r,i))return o}return n}function NS(e){let t=y()[me][de];if(!t.projection){let n=e?e.length:1,r=t.projection=Gp(n,null),o=r.slice(),i=t.child;for(;i!==null;){let s=e?cv(i,e):0;s!==null&&(o[s]?o[s].projectionNext=i:r[s]=i,o[s]=i),i=i.next}}}function AS(e,t=0,n){let r=y(),o=V(),i=cn(o,Y+e,16,null,n||null);i.projection===null&&(i.projection=t),Zs(),(!r[Kt]||il())&&(i.flags&32)!==32&&wm(o,r,i)}function lv(e,t,n){return $d(e,"",t,"",n),lv}function $d(e,t,n,r,o){let i=y(),s=Ca(i,t,n,r);if(s!==Z){let a=V(),u=an();$n(a,u,i,e,s,i[j],o,!1)}return $d}function OS(e,t,n,r){zy(e,t,n,r)}function FS(e,t,n){Gy(e,t,n)}function RS(e){let t=y(),n=V(),r=al();Xs(r+1);let o=wa(n,r);if(e.dirty&&Fh(t)===((o.metadata.flags&2)===2)){if(o.matches===null)e.reset([]);else{let i=Yy(t,r);e.reset(i,yg),e.notifyOnChanges()}return!0}return!1}function PS(){return Uy(y(),al())}function dv(e,t,n,r){n>=e.data.length&&(e.data[n]=null,e.blueprint[n]=null),t[n]=r}function kS(e){let t=Gh();return Ws(t,Y+e)}function LS(e,t=""){let n=y(),r=V(),o=e+Y,i=r.firstCreatePass?cn(r,o,1,t,null):r.data[o],s=fv(r,n,i,t,e);n[o]=s,so()&&fo(r,n,s,i),xt(i,!1)}var fv=(e,t,n,r,o)=>(ao(!0),om(t[j],r));function pv(e){return Hd("",e,""),pv}function Hd(e,t,n){let r=y(),o=Ca(r,e,t,n);return o!==Z&&mo(r,Se(),o),Hd}function hv(e,t,n,r,o){let i=y(),s=ID(i,e,t,n,r,o);return s!==Z&&mo(i,Se(),s),hv}function gv(e,t,n,r,o,i,s){let a=y(),u=ED(a,e,t,n,r,o,i,s);return u!==Z&&mo(a,Se(),u),gv}function mv(e,t,n,r,o,i,s,a,u){let c=y(),l=CD(c,e,t,n,r,o,i,s,a,u);return l!==Z&&mo(c,Se(),l),mv}function yv(e,t,n){bd(t)&&(t=t());let r=y(),o=rt();if(se(r,o,t)){let i=V(),s=an();$n(i,s,r,e,t,r[j],n,!1)}return yv}function jS(e,t){let n=bd(e);return n&&e.set(t),n}function Dv(e,t){let n=y(),r=V(),o=Q();return ba(r,n,n[j],o,e,t),Dv}function vv(e,t,n){let r=V();if(r.firstCreatePass){let o=$e(e);Cs(n,r.data,r.blueprint,o,!0),Cs(t,r.data,r.blueprint,o,!1)}}function Cs(e,t,n,r,o){if(e=ee(e),Array.isArray(e))for(let i=0;i<e.length;i++)Cs(e[i],t,n,r,o);else{let i=V(),s=y(),a=Q(),u=Zt(e)?e:ee(e.provide),c=qc(e),l=a.providerIndexes&1048575,d=a.directiveStart,p=a.providerIndexes>>20;if(Zt(e)||!e.multi){let f=new It(c,o,U),h=vi(u,t,o?l:l+p,d);h===-1?(Oi(Vr(a,s),i,u),Di(i,e,t.length),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(f),s.push(f)):(n[h]=f,s[h]=f)}else{let f=vi(u,t,l+p,d),h=vi(u,t,l,l+p),g=f>=0&&n[f],M=h>=0&&n[h];if(o&&!M||!o&&!g){Oi(Vr(a,s),i,u);let b=Ev(o?Iv:wv,n.length,o,r,c);!o&&M&&(n[h].providerFactory=b),Di(i,e,t.length,0),t.push(u),a.directiveStart++,a.directiveEnd++,o&&(a.providerIndexes+=1048576),n.push(b),s.push(b)}else{let b=Ud(n[o?h:f],c,!o&&r);Di(i,e,f>-1?f:h,b)}!o&&r&&M&&n[h].componentProviders++}}}function Di(e,t,n,r){let o=Zt(t),i=hh(t);if(o||i){let u=(i?ee(t.useClass):t).prototype.ngOnDestroy;if(u){let c=e.destroyHooks||(e.destroyHooks=[]);if(!o&&t.multi){let l=c.indexOf(n);l===-1?c.push(n,[r,u]):c[l+1].push(r,u)}else c.push(n,u)}}}function Ud(e,t,n){return n&&e.componentProviders++,e.multi.push(t)-1}function vi(e,t,n,r){for(let o=n;o<r;o++)if(t[o]===e)return o;return-1}function wv(e,t,n,r){return bs(this.multi,[])}function Iv(e,t,n,r){let o=this.multi,i;if(this.providerFactory){let s=this.providerFactory.componentProviders,a=Et(n,n[I],this.providerFactory.index,r);i=a.slice(0,s),bs(o,i);for(let u=s;u<a.length;u++)i.push(a[u])}else i=[],bs(o,i);return i}function bs(e,t){for(let n=0;n<e.length;n++){let r=e[n];t.push(r())}return t}function Ev(e,t,n,r,o){let i=new It(e,n,U);return i.multi=[],i.index=t,i.componentProviders=0,Ud(i,o,r&&!n),i}function VS(e,t=[]){return n=>{n.providersResolver=(r,o)=>vv(r,o?o(e):e,t)}}var Cv=(()=>{class e{constructor(n){this._injector=n,this.cachedInjectors=new Map}getOrCreateStandaloneInjector(n){if(!n.standalone)return null;if(!this.cachedInjectors.has(n)){let r=Uc(!1,n.type),o=r.length>0?dD([r],this._injector,`Standalone[${n.type.name}]`):null;this.cachedInjectors.set(n,o)}return this.cachedInjectors.get(n)}ngOnDestroy(){try{for(let n of this.cachedInjectors.values())n!==null&&n.destroy()}finally{this.cachedInjectors.clear()}}static{this.\u0275prov=k({token:e,providedIn:"environment",factory:()=>new e(H(Xe))})}}return e})();function BS(e){Hn("NgStandalone"),e.getStandaloneInjector=t=>t.get(Cv).getOrCreateStandaloneInjector(e)}function $S(e,t,n){let r=Ln()+e,o=y();return o[r]===Z?Ea(o,r,n?t.call(n):t()):pD(o,r)}function HS(e,t,n,r){return zd(y(),Ln(),e,t,n,r)}function US(e,t,n,r,o){return Wd(y(),Ln(),e,t,n,r,o)}function Gd(e,t){let n=e[t];return n===Z?void 0:n}function zd(e,t,n,r,o,i){let s=t+n;return se(e,s,o)?Ea(e,s+1,i?r.call(i,o):r(o)):Gd(e,s+1)}function Wd(e,t,n,r,o,i,s){let a=t+n;return On(e,a,o,i)?Ea(e,a+2,s?r.call(s,o,i):r(o,i)):Gd(e,a+2)}function GS(e,t){let n=V(),r,o=e+Y;n.firstCreatePass?(r=bv(t,n.pipeRegistry),n.data[o]=r,r.onDestroy&&(n.destroyHooks??=[]).push(o,r.onDestroy)):r=n.data[o];let i=r.factory||(r.factory=yt(r.type,!0)),s,a=oe(U);try{let u=jr(!1),c=i();return jr(u),dv(n,y(),o,c),c}finally{oe(a)}}function bv(e,t){if(t)for(let n=t.length-1;n>=0;n--){let r=t[n];if(e===r.name)return r}}function zS(e,t,n){let r=e+Y,o=y(),i=Ws(o,r);return qd(o,r)?zd(o,Ln(),t,i.transform,n,i):i.transform(n)}function WS(e,t,n,r){let o=e+Y,i=y(),s=Ws(i,o);return qd(i,o)?Wd(i,Ln(),t,s.transform,n,r,s):s.transform(n,r)}function qd(e,t){return e[I].data[t].pure}function qS(e,t){return vo(e,t)}var Cr=null;function _v(e){Cr!==null&&(e.defaultEncapsulation!==Cr.defaultEncapsulation||e.preserveWhitespaces!==Cr.preserveWhitespaces)||(Cr=e)}var YS=(()=>{class e{log(n){console.log(n)}warn(n){console.warn(n)}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();var Mv=new O(""),xv=new O(""),QS=(()=>{class e{constructor(n,r,o){this._ngZone=n,this.registry=r,this._pendingCount=0,this._isZoneStable=!0,this._callbacks=[],this.taskTrackingZone=null,_a||(Tv(o),o.addToWindow(r)),this._watchAngularEvents(),n.run(()=>{this.taskTrackingZone=typeof Zone>"u"?null:Zone.current.get("TaskTrackingZone")})}_watchAngularEvents(){this._ngZone.onUnstable.subscribe({next:()=>{this._isZoneStable=!1}}),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.subscribe({next:()=>{le.assertNotInAngularZone(),queueMicrotask(()=>{this._isZoneStable=!0,this._runCallbacksIfReady()})}})})}increasePendingRequestCount(){return this._pendingCount+=1,this._pendingCount}decreasePendingRequestCount(){if(this._pendingCount-=1,this._pendingCount<0)throw new Error("pending async requests below zero");return this._runCallbacksIfReady(),this._pendingCount}isStable(){return this._isZoneStable&&this._pendingCount===0&&!this._ngZone.hasPendingMacrotasks}_runCallbacksIfReady(){if(this.isStable())queueMicrotask(()=>{for(;this._callbacks.length!==0;){let n=this._callbacks.pop();clearTimeout(n.timeoutId),n.doneCb()}});else{let n=this.getPendingTasks();this._callbacks=this._callbacks.filter(r=>r.updateCb&&r.updateCb(n)?(clearTimeout(r.timeoutId),!1):!0)}}getPendingTasks(){return this.taskTrackingZone?this.taskTrackingZone.macroTasks.map(n=>({source:n.source,creationLocation:n.creationLocation,data:n.data})):[]}addCallback(n,r,o){let i=-1;r&&r>0&&(i=setTimeout(()=>{this._callbacks=this._callbacks.filter(s=>s.timeoutId!==i),n()},r)),this._callbacks.push({doneCb:n,timeoutId:i,updateCb:o})}whenStable(n,r,o){if(o&&!this.taskTrackingZone)throw new Error('Task tracking zone is required when passing an update callback to whenStable(). Is "zone.js/plugins/task-tracking" loaded?');this.addCallback(n,r,o),this._runCallbacksIfReady()}getPendingRequestCount(){return this._pendingCount}registerApplication(n){this.registry.registerApplication(n,this)}unregisterApplication(n){this.registry.unregisterApplication(n)}findProviders(n,r,o){return[]}static{this.\u0275fac=function(r){return new(r||e)(H(le),H(Sv),H(xv))}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac})}}return e})(),Sv=(()=>{class e{constructor(){this._applications=new Map}registerApplication(n,r){this._applications.set(n,r)}unregisterApplication(n){this._applications.delete(n)}unregisterAllApplications(){this._applications.clear()}getTestability(n){return this._applications.get(n)||null}getAllTestabilities(){return Array.from(this._applications.values())}getAllRootElements(){return Array.from(this._applications.keys())}findTestabilityInTree(n,r=!0){return _a?.findTestabilityInTree(this,n,r)??null}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})();function Tv(e){_a=e}var _a;function Eo(e){return!!e&&typeof e.then=="function"}function Ma(e){return!!e&&typeof e.subscribe=="function"}var Nv=new O(""),Yd=(()=>{class e{constructor(){this.initialized=!1,this.done=!1,this.donePromise=new Promise((n,r)=>{this.resolve=n,this.reject=r}),this.appInits=F(Nv,{optional:!0})??[]}runInitializers(){if(this.initialized)return;let n=[];for(let o of this.appInits){let i=o();if(Eo(i))n.push(i);else if(Ma(i)){let s=new Promise((a,u)=>{i.subscribe({complete:a,error:u})});n.push(s)}}let r=()=>{this.done=!0,this.resolve()};Promise.all(n).then(()=>{r()}).catch(o=>{this.reject(o)}),n.length===0&&r(),this.initialized=!0}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Av=new O("");function Ov(){Xa(()=>{throw new C(600,!1)})}function Fv(e){return e.isBoundToModule}function Rv(e,t,n){try{let r=n();return Eo(r)?r.catch(o=>{throw t.runOutsideAngular(()=>e.handleError(o)),o}):r}catch(r){throw t.runOutsideAngular(()=>e.handleError(r)),r}}function Qd(e,t){return Array.isArray(t)?t.reduce(Qd,e):it(it({},e),t)}var xa=(()=>{class e{constructor(){this._bootstrapListeners=[],this._runningTick=!1,this._destroyed=!1,this._destroyListeners=[],this._views=[],this.internalErrorHandler=F(_l),this.afterRenderEffectManager=F(va),this.externalTestViews=new Set,this.beforeRender=new ge,this.afterTick=new ge,this.componentTypes=[],this.components=[],this.isStable=F(_d).hasPendingTasks.pipe(Fe(n=>!n)),this._injector=F(Xe)}get destroyed(){return this._destroyed}get injector(){return this._injector}bootstrap(n,r){let o=n instanceof Ur;if(!this._injector.get(Yd).done){let p=!o&&uh(n),f=!1;throw new C(405,f)}let s;o?s=n:s=this._injector.get(wo).resolveComponentFactory(n),this.componentTypes.push(s.componentType);let a=Fv(s)?void 0:this._injector.get(tt),u=r||s.selector,c=s.create(St.NULL,[],u,a),l=c.location.nativeElement,d=c.injector.get(Mv,null);return d?.registerApplication(l),c.onDestroy(()=>{this.detachView(c.hostView),Tr(this.components,c),d?.unregisterApplication(l)}),this._loadComponent(c),c}tick(){this._tick(!0)}_tick(n){if(this._runningTick)throw new C(101,!1);let r=_(null);try{this._runningTick=!0,this.detectChangesInAttachedViews(n)}catch(o){this.internalErrorHandler(o)}finally{this.afterTick.next(),this._runningTick=!1,_(r)}}detectChangesInAttachedViews(n){let r=0,o=this.afterRenderEffectManager;for(;;){if(r===hd)throw new C(103,!1);if(n){let i=r===0;this.beforeRender.next(i);for(let{_lView:s,notifyErrorHandler:a}of this._views)Pv(s,i,a)}if(r++,o.executeInternalCallbacks(),![...this.externalTestViews.keys(),...this._views].some(({_lView:i})=>_s(i))&&(o.execute(),![...this.externalTestViews.keys(),...this._views].some(({_lView:i})=>_s(i))))break}}attachView(n){let r=n;this._views.push(r),r.attachToAppRef(this)}detachView(n){let r=n;Tr(this._views,r),r.detachFromAppRef()}_loadComponent(n){this.attachView(n.hostView),this.tick(),this.components.push(n);let r=this._injector.get(Av,[]);[...this._bootstrapListeners,...r].forEach(o=>o(n))}ngOnDestroy(){if(!this._destroyed)try{this._destroyListeners.forEach(n=>n()),this._views.slice().forEach(n=>n.destroy())}finally{this._destroyed=!0,this._views=[],this._bootstrapListeners=[],this._destroyListeners=[]}}onDestroy(n){return this._destroyListeners.push(n),()=>Tr(this._destroyListeners,n)}destroy(){if(this._destroyed)throw new C(406,!1);let n=this._injector;n.destroy&&!n.destroyed&&n.destroy()}get viewCount(){return this._views.length}warnIfDestroyed(){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Tr(e,t){let n=e.indexOf(t);n>-1&&e.splice(n,1)}function Pv(e,t,n){!t&&!_s(e)||kv(e,n,t)}function _s(e){return Ys(e)}function kv(e,t,n){let r;n?(r=0,e[v]|=1024):e[v]&64?r=0:r=1,gd(e,t,r)}var Ms=class{constructor(t,n){this.ngModuleFactory=t,this.componentFactories=n}},ZS=(()=>{class e{compileModuleSync(n){return new Qr(n)}compileModuleAsync(n){return Promise.resolve(this.compileModuleSync(n))}compileModuleAndAllComponentsSync(n){let r=this.compileModuleSync(n),o=Bc(n),i=$l(o.declarations).reduce((s,a)=>{let u=Je(a);return u&&s.push(new tn(u)),s},[]);return new Ms(r,i)}compileModuleAndAllComponentsAsync(n){return Promise.resolve(this.compileModuleAndAllComponentsSync(n))}clearCache(){}clearCacheFor(n){}getModuleId(n){}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),Lv=new O("");function jv(e,t,n){let r=new Qr(n);return Promise.resolve(r)}function hc(e){for(let t=e.length-1;t>=0;t--)if(e[t]!==void 0)return e[t]}var Vv=(()=>{class e{constructor(){this.zone=F(le),this.applicationRef=F(xa)}initialize(){this._onMicrotaskEmptySubscription||(this._onMicrotaskEmptySubscription=this.zone.onMicrotaskEmpty.subscribe({next:()=>{this.zone.run(()=>{this.applicationRef.tick()})}}))}ngOnDestroy(){this._onMicrotaskEmptySubscription?.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Bv(e){return[{provide:le,useFactory:e},{provide:Fr,multi:!0,useFactory:()=>{let t=F(Vv,{optional:!0});return()=>t.initialize()}},{provide:Fr,multi:!0,useFactory:()=>{let t=F(Uv);return()=>{t.initialize()}}},{provide:_l,useFactory:$v}]}function $v(){let e=F(le),t=F(Ct);return n=>e.runOutsideAngular(()=>t.handleError(n))}function Hv(e){return{enableLongStackTrace:!1,shouldCoalesceEventChangeDetection:e?.eventCoalescing??!1,shouldCoalesceRunChangeDetection:e?.runCoalescing??!1}}var Uv=(()=>{class e{constructor(){this.subscription=new $,this.initialized=!1,this.zone=F(le),this.pendingTasks=F(_d)}initialize(){if(this.initialized)return;this.initialized=!0;let n=null;!this.zone.isStable&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(n=this.pendingTasks.add()),this.zone.runOutsideAngular(()=>{this.subscription.add(this.zone.onStable.subscribe(()=>{le.assertNotInAngularZone(),queueMicrotask(()=>{n!==null&&!this.zone.hasPendingMacrotasks&&!this.zone.hasPendingMicrotasks&&(this.pendingTasks.remove(n),n=null)})}))}),this.subscription.add(this.zone.onUnstable.subscribe(()=>{le.assertInAngularZone(),n??=this.pendingTasks.add()}))}ngOnDestroy(){this.subscription.unsubscribe()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})();function Gv(){return typeof $localize<"u"&&$localize.locale||Jr}var Un=new O("",{providedIn:"root",factory:()=>F(Un,S.Optional|S.SkipSelf)||Gv()});var Zd=new O(""),Kd=(()=>{class e{constructor(n){this._injector=n,this._modules=[],this._destroyListeners=[],this._destroyed=!1}bootstrapModuleFactory(n,r){let o=Iy(r?.ngZone,Hv({eventCoalescing:r?.ngZoneEventCoalescing,runCoalescing:r?.ngZoneRunCoalescing}));return o.run(()=>{let i=lD(n.moduleType,this.injector,Bv(()=>o)),s=i.injector.get(Ct,null);return o.runOutsideAngular(()=>{let a=o.onError.subscribe({next:u=>{s.handleError(u)}});i.onDestroy(()=>{Tr(this._modules,i),a.unsubscribe()})}),Rv(s,o,()=>{let a=i.injector.get(Yd);return a.runInitializers(),a.donePromise.then(()=>{let u=i.injector.get(Un,Jr);return iv(u||Jr),this._moduleDoBootstrap(i),i})})})}bootstrapModule(n,r=[]){let o=Qd({},r);return jv(this.injector,o,n).then(i=>this.bootstrapModuleFactory(i,o))}_moduleDoBootstrap(n){let r=n.injector.get(xa);if(n._bootstrapComponents.length>0)n._bootstrapComponents.forEach(o=>r.bootstrap(o));else if(n.instance.ngDoBootstrap)n.instance.ngDoBootstrap(r);else throw new C(-403,!1);this._modules.push(n)}onDestroy(n){this._destroyListeners.push(n)}get injector(){return this._injector}destroy(){if(this._destroyed)throw new C(404,!1);this._modules.slice().forEach(r=>r.destroy()),this._destroyListeners.forEach(r=>r());let n=this._injector.get(Zd,null);n&&(n.forEach(r=>r()),n.clear()),this._destroyed=!0}get destroyed(){return this._destroyed}static{this.\u0275fac=function(r){return new(r||e)(H(St))}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"platform"})}}return e})(),vn=null,Jd=new O("");function zv(e){if(vn&&!vn.get(Jd,!1))throw new C(400,!1);Ov(),vn=e;let t=e.get(Kd);return Qv(e),t}function Wv(e,t,n=[]){let r=`Platform: ${t}`,o=new O(r);return(i=[])=>{let s=Xd();if(!s||s.injector.get(Jd,!1)){let a=[...n,...i,{provide:o,useValue:!0}];e?e(a):zv(qv(a,r))}return Yv(o)}}function qv(e=[],t){return St.create({name:t,providers:[{provide:Wc,useValue:"platform"},{provide:Zd,useValue:new Set([()=>vn=null])},...e]})}function Yv(e){let t=Xd();if(!t)throw new C(401,!1);return t}function Xd(){return vn?.get(Kd)??null}function Qv(e){e.get(_g,null)?.forEach(n=>n())}var Sa=(()=>{class e{static{this.__NG_ELEMENT_ID__=Zv}}return e})();function Zv(e){return Kv(Q(),y(),(e&16)===16)}function Kv(e,t,n){if(oo(e)&&!n){let r=nt(e.index,t);return new bt(r,r)}else if(e.type&47){let r=t[me];return new bt(r,t)}return null}var xs=class{constructor(){}supports(t){return Md(t)}create(t){return new Ss(t)}},Jv=(e,t)=>t,Ss=class{constructor(t){this.length=0,this._linkedRecords=null,this._unlinkedRecords=null,this._previousItHead=null,this._itHead=null,this._itTail=null,this._additionsHead=null,this._additionsTail=null,this._movesHead=null,this._movesTail=null,this._removalsHead=null,this._removalsTail=null,this._identityChangesHead=null,this._identityChangesTail=null,this._trackByFn=t||Jv}forEachItem(t){let n;for(n=this._itHead;n!==null;n=n._next)t(n)}forEachOperation(t){let n=this._itHead,r=this._removalsHead,o=0,i=null;for(;n||r;){let s=!r||n&&n.currentIndex<gc(r,o,i)?n:r,a=gc(s,o,i),u=s.currentIndex;if(s===r)o--,r=r._nextRemoved;else if(n=n._next,s.previousIndex==null)o++;else{i||(i=[]);let c=a-o,l=u-o;if(c!=l){for(let p=0;p<c;p++){let f=p<i.length?i[p]:i[p]=0,h=f+p;l<=h&&h<c&&(i[p]=f+1)}let d=s.previousIndex;i[d]=l-c}}a!==u&&t(s,a,u)}}forEachPreviousItem(t){let n;for(n=this._previousItHead;n!==null;n=n._nextPrevious)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachMovedItem(t){let n;for(n=this._movesHead;n!==null;n=n._nextMoved)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}forEachIdentityChange(t){let n;for(n=this._identityChangesHead;n!==null;n=n._nextIdentityChange)t(n)}diff(t){if(t==null&&(t=[]),!Md(t))throw new C(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._itHead,r=!1,o,i,s;if(Array.isArray(t)){this.length=t.length;for(let a=0;a<this.length;a++)i=t[a],s=this._trackByFn(a,i),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,i,s,a),r=!0):(r&&(n=this._verifyReinsertion(n,i,s,a)),Object.is(n.item,i)||this._addIdentityChange(n,i)),n=n._next}else o=0,fD(t,a=>{s=this._trackByFn(o,a),n===null||!Object.is(n.trackById,s)?(n=this._mismatch(n,a,s,o),r=!0):(r&&(n=this._verifyReinsertion(n,a,s,o)),Object.is(n.item,a)||this._addIdentityChange(n,a)),n=n._next,o++}),this.length=o;return this._truncate(n),this.collection=t,this.isDirty}get isDirty(){return this._additionsHead!==null||this._movesHead!==null||this._removalsHead!==null||this._identityChangesHead!==null}_reset(){if(this.isDirty){let t;for(t=this._previousItHead=this._itHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._additionsHead;t!==null;t=t._nextAdded)t.previousIndex=t.currentIndex;for(this._additionsHead=this._additionsTail=null,t=this._movesHead;t!==null;t=t._nextMoved)t.previousIndex=t.currentIndex;this._movesHead=this._movesTail=null,this._removalsHead=this._removalsTail=null,this._identityChangesHead=this._identityChangesTail=null}}_mismatch(t,n,r,o){let i;return t===null?i=this._itTail:(i=t._prev,this._remove(t)),t=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._reinsertAfter(t,i,o)):(t=this._linkedRecords===null?null:this._linkedRecords.get(r,o),t!==null?(Object.is(t.item,n)||this._addIdentityChange(t,n),this._moveAfter(t,i,o)):t=this._addAfter(new Ts(n,r),i,o)),t}_verifyReinsertion(t,n,r,o){let i=this._unlinkedRecords===null?null:this._unlinkedRecords.get(r,null);return i!==null?t=this._reinsertAfter(i,t._prev,o):t.currentIndex!=o&&(t.currentIndex=o,this._addToMoves(t,o)),t}_truncate(t){for(;t!==null;){let n=t._next;this._addToRemovals(this._unlink(t)),t=n}this._unlinkedRecords!==null&&this._unlinkedRecords.clear(),this._additionsTail!==null&&(this._additionsTail._nextAdded=null),this._movesTail!==null&&(this._movesTail._nextMoved=null),this._itTail!==null&&(this._itTail._next=null),this._removalsTail!==null&&(this._removalsTail._nextRemoved=null),this._identityChangesTail!==null&&(this._identityChangesTail._nextIdentityChange=null)}_reinsertAfter(t,n,r){this._unlinkedRecords!==null&&this._unlinkedRecords.remove(t);let o=t._prevRemoved,i=t._nextRemoved;return o===null?this._removalsHead=i:o._nextRemoved=i,i===null?this._removalsTail=o:i._prevRemoved=o,this._insertAfter(t,n,r),this._addToMoves(t,r),t}_moveAfter(t,n,r){return this._unlink(t),this._insertAfter(t,n,r),this._addToMoves(t,r),t}_addAfter(t,n,r){return this._insertAfter(t,n,r),this._additionsTail===null?this._additionsTail=this._additionsHead=t:this._additionsTail=this._additionsTail._nextAdded=t,t}_insertAfter(t,n,r){let o=n===null?this._itHead:n._next;return t._next=o,t._prev=n,o===null?this._itTail=t:o._prev=t,n===null?this._itHead=t:n._next=t,this._linkedRecords===null&&(this._linkedRecords=new Xr),this._linkedRecords.put(t),t.currentIndex=r,t}_remove(t){return this._addToRemovals(this._unlink(t))}_unlink(t){this._linkedRecords!==null&&this._linkedRecords.remove(t);let n=t._prev,r=t._next;return n===null?this._itHead=r:n._next=r,r===null?this._itTail=n:r._prev=n,t}_addToMoves(t,n){return t.previousIndex===n||(this._movesTail===null?this._movesTail=this._movesHead=t:this._movesTail=this._movesTail._nextMoved=t),t}_addToRemovals(t){return this._unlinkedRecords===null&&(this._unlinkedRecords=new Xr),this._unlinkedRecords.put(t),t.currentIndex=null,t._nextRemoved=null,this._removalsTail===null?(this._removalsTail=this._removalsHead=t,t._prevRemoved=null):(t._prevRemoved=this._removalsTail,this._removalsTail=this._removalsTail._nextRemoved=t),t}_addIdentityChange(t,n){return t.item=n,this._identityChangesTail===null?this._identityChangesTail=this._identityChangesHead=t:this._identityChangesTail=this._identityChangesTail._nextIdentityChange=t,t}},Ts=class{constructor(t,n){this.item=t,this.trackById=n,this.currentIndex=null,this.previousIndex=null,this._nextPrevious=null,this._prev=null,this._next=null,this._prevDup=null,this._nextDup=null,this._prevRemoved=null,this._nextRemoved=null,this._nextAdded=null,this._nextMoved=null,this._nextIdentityChange=null}},Ns=class{constructor(){this._head=null,this._tail=null}add(t){this._head===null?(this._head=this._tail=t,t._nextDup=null,t._prevDup=null):(this._tail._nextDup=t,t._prevDup=this._tail,t._nextDup=null,this._tail=t)}get(t,n){let r;for(r=this._head;r!==null;r=r._nextDup)if((n===null||n<=r.currentIndex)&&Object.is(r.trackById,t))return r;return null}remove(t){let n=t._prevDup,r=t._nextDup;return n===null?this._head=r:n._nextDup=r,r===null?this._tail=n:r._prevDup=n,this._head===null}},Xr=class{constructor(){this.map=new Map}put(t){let n=t.trackById,r=this.map.get(n);r||(r=new Ns,this.map.set(n,r)),r.add(t)}get(t,n){let r=t,o=this.map.get(r);return o?o.get(t,n):null}remove(t){let n=t.trackById;return this.map.get(n).remove(t)&&this.map.delete(n),t}get isEmpty(){return this.map.size===0}clear(){this.map.clear()}};function gc(e,t,n){let r=e.previousIndex;if(r===null)return r;let o=0;return n&&r<n.length&&(o=n[r]),r+t+o}var As=class{constructor(){}supports(t){return t instanceof Map||Ia(t)}create(){return new Os}},Os=class{constructor(){this._records=new Map,this._mapHead=null,this._appendAfter=null,this._previousMapHead=null,this._changesHead=null,this._changesTail=null,this._additionsHead=null,this._additionsTail=null,this._removalsHead=null,this._removalsTail=null}get isDirty(){return this._additionsHead!==null||this._changesHead!==null||this._removalsHead!==null}forEachItem(t){let n;for(n=this._mapHead;n!==null;n=n._next)t(n)}forEachPreviousItem(t){let n;for(n=this._previousMapHead;n!==null;n=n._nextPrevious)t(n)}forEachChangedItem(t){let n;for(n=this._changesHead;n!==null;n=n._nextChanged)t(n)}forEachAddedItem(t){let n;for(n=this._additionsHead;n!==null;n=n._nextAdded)t(n)}forEachRemovedItem(t){let n;for(n=this._removalsHead;n!==null;n=n._nextRemoved)t(n)}diff(t){if(!t)t=new Map;else if(!(t instanceof Map||Ia(t)))throw new C(900,!1);return this.check(t)?this:null}onDestroy(){}check(t){this._reset();let n=this._mapHead;if(this._appendAfter=null,this._forEach(t,(r,o)=>{if(n&&n.key===o)this._maybeAddToChanges(n,r),this._appendAfter=n,n=n._next;else{let i=this._getOrCreateRecordForKey(o,r);n=this._insertBeforeOrAppend(n,i)}}),n){n._prev&&(n._prev._next=null),this._removalsHead=n;for(let r=n;r!==null;r=r._nextRemoved)r===this._mapHead&&(this._mapHead=null),this._records.delete(r.key),r._nextRemoved=r._next,r.previousValue=r.currentValue,r.currentValue=null,r._prev=null,r._next=null}return this._changesTail&&(this._changesTail._nextChanged=null),this._additionsTail&&(this._additionsTail._nextAdded=null),this.isDirty}_insertBeforeOrAppend(t,n){if(t){let r=t._prev;return n._next=t,n._prev=r,t._prev=n,r&&(r._next=n),t===this._mapHead&&(this._mapHead=n),this._appendAfter=t,t}return this._appendAfter?(this._appendAfter._next=n,n._prev=this._appendAfter):this._mapHead=n,this._appendAfter=n,null}_getOrCreateRecordForKey(t,n){if(this._records.has(t)){let o=this._records.get(t);this._maybeAddToChanges(o,n);let i=o._prev,s=o._next;return i&&(i._next=s),s&&(s._prev=i),o._next=null,o._prev=null,o}let r=new Fs(t);return this._records.set(t,r),r.currentValue=n,this._addToAdditions(r),r}_reset(){if(this.isDirty){let t;for(this._previousMapHead=this._mapHead,t=this._previousMapHead;t!==null;t=t._next)t._nextPrevious=t._next;for(t=this._changesHead;t!==null;t=t._nextChanged)t.previousValue=t.currentValue;for(t=this._additionsHead;t!=null;t=t._nextAdded)t.previousValue=t.currentValue;this._changesHead=this._changesTail=null,this._additionsHead=this._additionsTail=null,this._removalsHead=null}}_maybeAddToChanges(t,n){Object.is(n,t.currentValue)||(t.previousValue=t.currentValue,t.currentValue=n,this._addToChanges(t))}_addToAdditions(t){this._additionsHead===null?this._additionsHead=this._additionsTail=t:(this._additionsTail._nextAdded=t,this._additionsTail=t)}_addToChanges(t){this._changesHead===null?this._changesHead=this._changesTail=t:(this._changesTail._nextChanged=t,this._changesTail=t)}_forEach(t,n){t instanceof Map?t.forEach(n):Object.keys(t).forEach(r=>n(t[r],r))}},Fs=class{constructor(t){this.key=t,this.previousValue=null,this.currentValue=null,this._nextPrevious=null,this._next=null,this._prev=null,this._nextAdded=null,this._nextRemoved=null,this._nextChanged=null}};function mc(){return new Ta([new xs])}var Ta=(()=>{class e{static{this.\u0275prov=k({token:e,providedIn:"root",factory:mc})}constructor(n){this.factories=n}static create(n,r){if(r!=null){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||mc()),deps:[[e,new Nc,new Tc]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r!=null)return r;throw new C(901,!1)}}return e})();function yc(){return new Na([new As])}var Na=(()=>{class e{static{this.\u0275prov=k({token:e,providedIn:"root",factory:yc})}constructor(n){this.factories=n}static create(n,r){if(r){let o=r.factories.slice();n=n.concat(o)}return new e(n)}static extend(n){return{provide:e,useFactory:r=>e.create(n,r||yc()),deps:[[e,new Nc,new Tc]]}}find(n){let r=this.factories.find(o=>o.supports(n));if(r)return r;throw new C(901,!1)}}return e})();var KS=Wv(null,"core",[]),JS=(()=>{class e{constructor(n){}static{this.\u0275fac=function(r){return new(r||e)(H(xa))}}static{this.\u0275mod=js({type:e})}static{this.\u0275inj=Rs({})}}return e})();function Xv(e){return typeof e=="boolean"?e:e!=null&&e!=="false"}function ew(e,t=NaN){return!isNaN(parseFloat(e))&&!isNaN(Number(e))?Number(e):t}function Aa(e){let t=_(null);try{return e()}finally{_(t)}}function XS(e){let t=Je(e);if(!t)return null;let n=new tn(t);return{get selector(){return n.selector},get type(){return n.componentType},get inputs(){return n.inputs},get outputs(){return n.outputs},get ngContentSelectors(){return n.ngContentSelectors},get isStandalone(){return t.standalone},get isSignal(){return t.signals}}}var lf=null;function Oa(){return lf}function vT(e){lf??=e}var ef=class{};var za=new O(""),Wa=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:()=>F(nw),providedIn:"platform"})}}return e})(),wT=new O(""),nw=(()=>{class e extends Wa{constructor(){super(),this._doc=F(za),this._location=window.location,this._history=window.history}getBaseHrefFromDOM(){return Oa().getBaseHref(this._doc)}onPopState(n){let r=Oa().getGlobalEventTarget(this._doc,"window");return r.addEventListener("popstate",n,!1),()=>r.removeEventListener("popstate",n)}onHashChange(n){let r=Oa().getGlobalEventTarget(this._doc,"window");return r.addEventListener("hashchange",n,!1),()=>r.removeEventListener("hashchange",n)}get href(){return this._location.href}get protocol(){return this._location.protocol}get hostname(){return this._location.hostname}get port(){return this._location.port}get pathname(){return this._location.pathname}get search(){return this._location.search}get hash(){return this._location.hash}set pathname(n){this._location.pathname=n}pushState(n,r,o){this._history.pushState(n,r,o)}replaceState(n,r,o){this._history.replaceState(n,r,o)}forward(){this._history.forward()}back(){this._history.back()}historyGo(n=0){this._history.go(n)}getState(){return this._history.state}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:()=>new e,providedIn:"platform"})}}return e})();function qa(e,t){if(e.length==0)return t;if(t.length==0)return e;let n=0;return e.endsWith("/")&&n++,t.startsWith("/")&&n++,n==2?e+t.substring(1):n==1?e+t:e+"/"+t}function tf(e){let t=e.match(/#|\?|$/),n=t&&t.index||e.length,r=n-(e[n-1]==="/"?1:0);return e.slice(0,r)+e.slice(n)}function We(e){return e&&e[0]!=="?"?"?"+e:e}var Fo=(()=>{class e{historyGo(n){throw new Error("")}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275prov=k({token:e,factory:()=>F(rw),providedIn:"root"})}}return e})(),df=new O(""),rw=(()=>{class e extends Fo{constructor(n,r){super(),this._platformLocation=n,this._removeListenerFns=[],this._baseHref=r??this._platformLocation.getBaseHrefFromDOM()??F(za).location?.origin??""}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}prepareExternalUrl(n){return qa(this._baseHref,n)}path(n=!1){let r=this._platformLocation.pathname+We(this._platformLocation.search),o=this._platformLocation.hash;return o&&n?`${r}${o}`:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+We(i));this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+We(i));this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(H(Wa),H(df,8))}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac,providedIn:"root"})}}return e})(),IT=(()=>{class e extends Fo{constructor(n,r){super(),this._platformLocation=n,this._baseHref="",this._removeListenerFns=[],r!=null&&(this._baseHref=r)}ngOnDestroy(){for(;this._removeListenerFns.length;)this._removeListenerFns.pop()()}onPopState(n){this._removeListenerFns.push(this._platformLocation.onPopState(n),this._platformLocation.onHashChange(n))}getBaseHref(){return this._baseHref}path(n=!1){let r=this._platformLocation.hash??"#";return r.length>0?r.substring(1):r}prepareExternalUrl(n){let r=qa(this._baseHref,n);return r.length>0?"#"+r:r}pushState(n,r,o,i){let s=this.prepareExternalUrl(o+We(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.pushState(n,r,s)}replaceState(n,r,o,i){let s=this.prepareExternalUrl(o+We(i));s.length==0&&(s=this._platformLocation.pathname),this._platformLocation.replaceState(n,r,s)}forward(){this._platformLocation.forward()}back(){this._platformLocation.back()}getState(){return this._platformLocation.getState()}historyGo(n=0){this._platformLocation.historyGo?.(n)}static{this.\u0275fac=function(r){return new(r||e)(H(Wa),H(df,8))}}static{this.\u0275prov=k({token:e,factory:e.\u0275fac})}}return e})(),ow=(()=>{class e{constructor(n){this._subject=new _e,this._urlChangeListeners=[],this._urlChangeSubscription=null,this._locationStrategy=n;let r=this._locationStrategy.getBaseHref();this._basePath=aw(tf(nf(r))),this._locationStrategy.onPopState(o=>{this._subject.emit({url:this.path(!0),pop:!0,state:o.state,type:o.type})})}ngOnDestroy(){this._urlChangeSubscription?.unsubscribe(),this._urlChangeListeners=[]}path(n=!1){return this.normalize(this._locationStrategy.path(n))}getState(){return this._locationStrategy.getState()}isCurrentPathEqualTo(n,r=""){return this.path()==this.normalize(n+We(r))}normalize(n){return e.stripTrailingSlash(sw(this._basePath,nf(n)))}prepareExternalUrl(n){return n&&n[0]!=="/"&&(n="/"+n),this._locationStrategy.prepareExternalUrl(n)}go(n,r="",o=null){this._locationStrategy.pushState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+We(r)),o)}replaceState(n,r="",o=null){this._locationStrategy.replaceState(o,"",n,r),this._notifyUrlChangeListeners(this.prepareExternalUrl(n+We(r)),o)}forward(){this._locationStrategy.forward()}back(){this._locationStrategy.back()}historyGo(n=0){this._locationStrategy.historyGo?.(n)}onUrlChange(n){return this._urlChangeListeners.push(n),this._urlChangeSubscription??=this.subscribe(r=>{this._notifyUrlChangeListeners(r.url,r.state)}),()=>{let r=this._urlChangeListeners.indexOf(n);this._urlChangeListeners.splice(r,1),this._urlChangeListeners.length===0&&(this._urlChangeSubscription?.unsubscribe(),this._urlChangeSubscription=null)}}_notifyUrlChangeListeners(n="",r){this._urlChangeListeners.forEach(o=>o(n,r))}subscribe(n,r,o){return this._subject.subscribe({next:n,error:r,complete:o})}static{this.normalizeQueryParams=We}static{this.joinWithSlash=qa}static{this.stripTrailingSlash=tf}static{this.\u0275fac=function(r){return new(r||e)(H(Fo))}}static{this.\u0275prov=k({token:e,factory:()=>iw(),providedIn:"root"})}}return e})();function iw(){return new ow(H(Fo))}function sw(e,t){if(!e||!t.startsWith(e))return t;let n=t.substring(e.length);return n===""||["/",";","?","#"].includes(n[0])?n:t}function nf(e){return e.replace(/\/index.html$/,"")}function aw(e){if(new RegExp("^(https?:)?//").test(e)){let[,n]=e.split(/\/\/[^\/]+/);return n}return e}var ff=function(e){return e[e.Decimal=0]="Decimal",e[e.Percent=1]="Percent",e[e.Currency=2]="Currency",e[e.Scientific=3]="Scientific",e}(ff||{});var ne=function(e){return e[e.Format=0]="Format",e[e.Standalone=1]="Standalone",e}(ne||{}),P=function(e){return e[e.Narrow=0]="Narrow",e[e.Abbreviated=1]="Abbreviated",e[e.Wide=2]="Wide",e[e.Short=3]="Short",e}(P||{}),pe=function(e){return e[e.Short=0]="Short",e[e.Medium=1]="Medium",e[e.Long=2]="Long",e[e.Full=3]="Full",e}(pe||{}),he={Decimal:0,Group:1,List:2,PercentSign:3,PlusSign:4,MinusSign:5,Exponential:6,SuperscriptingExponent:7,PerMille:8,Infinity:9,NaN:10,TimeSeparator:11,CurrencyDecimal:12,CurrencyGroup:13};function uw(e){return fe(e)[B.LocaleId]}function cw(e,t,n){let r=fe(e),o=[r[B.DayPeriodsFormat],r[B.DayPeriodsStandalone]],i=De(o,t);return De(i,n)}function lw(e,t,n){let r=fe(e),o=[r[B.DaysFormat],r[B.DaysStandalone]],i=De(o,t);return De(i,n)}function dw(e,t,n){let r=fe(e),o=[r[B.MonthsFormat],r[B.MonthsStandalone]],i=De(o,t);return De(i,n)}function fw(e,t){let r=fe(e)[B.Eras];return De(r,t)}function Co(e,t){let n=fe(e);return De(n[B.DateFormat],t)}function bo(e,t){let n=fe(e);return De(n[B.TimeFormat],t)}function _o(e,t){let r=fe(e)[B.DateTimeFormat];return De(r,t)}function qe(e,t){let n=fe(e),r=n[B.NumberSymbols][t];if(typeof r>"u"){if(t===he.CurrencyDecimal)return n[B.NumberSymbols][he.Decimal];if(t===he.CurrencyGroup)return n[B.NumberSymbols][he.Group]}return r}function pw(e,t){return fe(e)[B.NumberFormats][t]}function pf(e){if(!e[B.ExtraData])throw new Error(`Missing extra locale data for the locale "${e[B.LocaleId]}". Use "registerLocaleData" to load new data. See the "I18n guide" on angular.io to know more.`)}function hw(e){let t=fe(e);return pf(t),(t[B.ExtraData][2]||[]).map(r=>typeof r=="string"?Fa(r):[Fa(r[0]),Fa(r[1])])}function gw(e,t,n){let r=fe(e);pf(r);let o=[r[B.ExtraData][0],r[B.ExtraData][1]],i=De(o,t)||[];return De(i,n)||[]}function De(e,t){for(let n=t;n>-1;n--)if(typeof e[n]<"u")return e[n];throw new Error("Locale data API: locale data undefined")}function Fa(e){let[t,n]=e.split(":");return{hours:+t,minutes:+n}}var mw=/^(\d{4,})-?(\d\d)-?(\d\d)(?:T(\d\d)(?::?(\d\d)(?::?(\d\d)(?:\.(\d+))?)?)?(Z|([+-])(\d\d):?(\d\d))?)?$/,Mo={},yw=/((?:[^BEGHLMOSWYZabcdhmswyz']+)|(?:'(?:[^']|'')*')|(?:G{1,5}|y{1,4}|Y{1,4}|M{1,5}|L{1,5}|w{1,2}|W{1}|d{1,2}|E{1,6}|c{1,6}|a{1,5}|b{1,5}|B{1,5}|h{1,2}|H{1,2}|m{1,2}|s{1,2}|S{1,3}|z{1,4}|Z{1,5}|O{1,4}))([\s\S]*)/,Ye=function(e){return e[e.Short=0]="Short",e[e.ShortGMT=1]="ShortGMT",e[e.Long=2]="Long",e[e.Extended=3]="Extended",e}(Ye||{}),A=function(e){return e[e.FullYear=0]="FullYear",e[e.Month=1]="Month",e[e.Date=2]="Date",e[e.Hours=3]="Hours",e[e.Minutes=4]="Minutes",e[e.Seconds=5]="Seconds",e[e.FractionalSeconds=6]="FractionalSeconds",e[e.Day=7]="Day",e}(A||{}),N=function(e){return e[e.DayPeriods=0]="DayPeriods",e[e.Days=1]="Days",e[e.Months=2]="Months",e[e.Eras=3]="Eras",e}(N||{});function Dw(e,t,n,r){let o=xw(e);t=ze(n,t)||t;let s=[],a;for(;t;)if(a=yw.exec(t),a){s=s.concat(a.slice(1));let l=s.pop();if(!l)break;t=l}else{s.push(t);break}let u=o.getTimezoneOffset();r&&(u=gf(r,u),o=Mw(o,r,!0));let c="";return s.forEach(l=>{let d=bw(l);c+=d?d(o,n,u):l==="''"?"'":l.replace(/(^'|'$)/g,"").replace(/''/g,"'")}),c}function Ao(e,t,n){let r=new Date(0);return r.setFullYear(e,t,n),r.setHours(0,0,0),r}function ze(e,t){let n=uw(e);if(Mo[n]??={},Mo[n][t])return Mo[n][t];let r="";switch(t){case"shortDate":r=Co(e,pe.Short);break;case"mediumDate":r=Co(e,pe.Medium);break;case"longDate":r=Co(e,pe.Long);break;case"fullDate":r=Co(e,pe.Full);break;case"shortTime":r=bo(e,pe.Short);break;case"mediumTime":r=bo(e,pe.Medium);break;case"longTime":r=bo(e,pe.Long);break;case"fullTime":r=bo(e,pe.Full);break;case"short":let o=ze(e,"shortTime"),i=ze(e,"shortDate");r=xo(_o(e,pe.Short),[o,i]);break;case"medium":let s=ze(e,"mediumTime"),a=ze(e,"mediumDate");r=xo(_o(e,pe.Medium),[s,a]);break;case"long":let u=ze(e,"longTime"),c=ze(e,"longDate");r=xo(_o(e,pe.Long),[u,c]);break;case"full":let l=ze(e,"fullTime"),d=ze(e,"fullDate");r=xo(_o(e,pe.Full),[l,d]);break}return r&&(Mo[n][t]=r),r}function xo(e,t){return t&&(e=e.replace(/\{([^}]+)}/g,function(n,r){return t!=null&&r in t?t[r]:n})),e}function Te(e,t,n="-",r,o){let i="";(e<0||o&&e<=0)&&(o?e=-e+1:(e=-e,i=n));let s=String(e);for(;s.length<t;)s="0"+s;return r&&(s=s.slice(s.length-t)),i+s}function vw(e,t){return Te(e,3).substring(0,t)}function G(e,t,n=0,r=!1,o=!1){return function(i,s){let a=ww(e,i);if((n>0||a>-n)&&(a+=n),e===A.Hours)a===0&&n===-12&&(a=12);else if(e===A.FractionalSeconds)return vw(a,t);let u=qe(s,he.MinusSign);return Te(a,t,u,r,o)}}function ww(e,t){switch(e){case A.FullYear:return t.getFullYear();case A.Month:return t.getMonth();case A.Date:return t.getDate();case A.Hours:return t.getHours();case A.Minutes:return t.getMinutes();case A.Seconds:return t.getSeconds();case A.FractionalSeconds:return t.getMilliseconds();case A.Day:return t.getDay();default:throw new Error(`Unknown DateType value "${e}".`)}}function L(e,t,n=ne.Format,r=!1){return function(o,i){return Iw(o,i,e,t,n,r)}}function Iw(e,t,n,r,o,i){switch(n){case N.Months:return dw(t,o,r)[e.getMonth()];case N.Days:return lw(t,o,r)[e.getDay()];case N.DayPeriods:let s=e.getHours(),a=e.getMinutes();if(i){let c=hw(t),l=gw(t,o,r),d=c.findIndex(p=>{if(Array.isArray(p)){let[f,h]=p,g=s>=f.hours&&a>=f.minutes,M=s<h.hours||s===h.hours&&a<h.minutes;if(f.hours<h.hours){if(g&&M)return!0}else if(g||M)return!0}else if(p.hours===s&&p.minutes===a)return!0;return!1});if(d!==-1)return l[d]}return cw(t,o,r)[s<12?0:1];case N.Eras:return fw(t,r)[e.getFullYear()<=0?0:1];default:let u=n;throw new Error(`unexpected translation type ${u}`)}}function So(e){return function(t,n,r){let o=-1*r,i=qe(n,he.MinusSign),s=o>0?Math.floor(o/60):Math.ceil(o/60);switch(e){case Ye.Short:return(o>=0?"+":"")+Te(s,2,i)+Te(Math.abs(o%60),2,i);case Ye.ShortGMT:return"GMT"+(o>=0?"+":"")+Te(s,1,i);case Ye.Long:return"GMT"+(o>=0?"+":"")+Te(s,2,i)+":"+Te(Math.abs(o%60),2,i);case Ye.Extended:return r===0?"Z":(o>=0?"+":"")+Te(s,2,i)+":"+Te(Math.abs(o%60),2,i);default:throw new Error(`Unknown zone width "${e}"`)}}}var Ew=0,No=4;function Cw(e){let t=Ao(e,Ew,1).getDay();return Ao(e,0,1+(t<=No?No:No+7)-t)}function hf(e){let t=e.getDay(),n=t===0?-3:No-t;return Ao(e.getFullYear(),e.getMonth(),e.getDate()+n)}function Ra(e,t=!1){return function(n,r){let o;if(t){let i=new Date(n.getFullYear(),n.getMonth(),1).getDay()-1,s=n.getDate();o=1+Math.floor((s+i)/7)}else{let i=hf(n),s=Cw(i.getFullYear()),a=i.getTime()-s.getTime();o=1+Math.round(a/6048e5)}return Te(o,e,qe(r,he.MinusSign))}}function To(e,t=!1){return function(n,r){let i=hf(n).getFullYear();return Te(i,e,qe(r,he.MinusSign),t)}}var Pa={};function bw(e){if(Pa[e])return Pa[e];let t;switch(e){case"G":case"GG":case"GGG":t=L(N.Eras,P.Abbreviated);break;case"GGGG":t=L(N.Eras,P.Wide);break;case"GGGGG":t=L(N.Eras,P.Narrow);break;case"y":t=G(A.FullYear,1,0,!1,!0);break;case"yy":t=G(A.FullYear,2,0,!0,!0);break;case"yyy":t=G(A.FullYear,3,0,!1,!0);break;case"yyyy":t=G(A.FullYear,4,0,!1,!0);break;case"Y":t=To(1);break;case"YY":t=To(2,!0);break;case"YYY":t=To(3);break;case"YYYY":t=To(4);break;case"M":case"L":t=G(A.Month,1,1);break;case"MM":case"LL":t=G(A.Month,2,1);break;case"MMM":t=L(N.Months,P.Abbreviated);break;case"MMMM":t=L(N.Months,P.Wide);break;case"MMMMM":t=L(N.Months,P.Narrow);break;case"LLL":t=L(N.Months,P.Abbreviated,ne.Standalone);break;case"LLLL":t=L(N.Months,P.Wide,ne.Standalone);break;case"LLLLL":t=L(N.Months,P.Narrow,ne.Standalone);break;case"w":t=Ra(1);break;case"ww":t=Ra(2);break;case"W":t=Ra(1,!0);break;case"d":t=G(A.Date,1);break;case"dd":t=G(A.Date,2);break;case"c":case"cc":t=G(A.Day,1);break;case"ccc":t=L(N.Days,P.Abbreviated,ne.Standalone);break;case"cccc":t=L(N.Days,P.Wide,ne.Standalone);break;case"ccccc":t=L(N.Days,P.Narrow,ne.Standalone);break;case"cccccc":t=L(N.Days,P.Short,ne.Standalone);break;case"E":case"EE":case"EEE":t=L(N.Days,P.Abbreviated);break;case"EEEE":t=L(N.Days,P.Wide);break;case"EEEEE":t=L(N.Days,P.Narrow);break;case"EEEEEE":t=L(N.Days,P.Short);break;case"a":case"aa":case"aaa":t=L(N.DayPeriods,P.Abbreviated);break;case"aaaa":t=L(N.DayPeriods,P.Wide);break;case"aaaaa":t=L(N.DayPeriods,P.Narrow);break;case"b":case"bb":case"bbb":t=L(N.DayPeriods,P.Abbreviated,ne.Standalone,!0);break;case"bbbb":t=L(N.DayPeriods,P.Wide,ne.Standalone,!0);break;case"bbbbb":t=L(N.DayPeriods,P.Narrow,ne.Standalone,!0);break;case"B":case"BB":case"BBB":t=L(N.DayPeriods,P.Abbreviated,ne.Format,!0);break;case"BBBB":t=L(N.DayPeriods,P.Wide,ne.Format,!0);break;case"BBBBB":t=L(N.DayPeriods,P.Narrow,ne.Format,!0);break;case"h":t=G(A.Hours,1,-12);break;case"hh":t=G(A.Hours,2,-12);break;case"H":t=G(A.Hours,1);break;case"HH":t=G(A.Hours,2);break;case"m":t=G(A.Minutes,1);break;case"mm":t=G(A.Minutes,2);break;case"s":t=G(A.Seconds,1);break;case"ss":t=G(A.Seconds,2);break;case"S":t=G(A.FractionalSeconds,1);break;case"SS":t=G(A.FractionalSeconds,2);break;case"SSS":t=G(A.FractionalSeconds,3);break;case"Z":case"ZZ":case"ZZZ":t=So(Ye.Short);break;case"ZZZZZ":t=So(Ye.Extended);break;case"O":case"OO":case"OOO":case"z":case"zz":case"zzz":t=So(Ye.ShortGMT);break;case"OOOO":case"ZZZZ":case"zzzz":t=So(Ye.Long);break;default:return null}return Pa[e]=t,t}function gf(e,t){e=e.replace(/:/g,"");let n=Date.parse("Jan 01, 1970 00:00:00 "+e)/6e4;return isNaN(n)?t:n}function _w(e,t){return e=new Date(e.getTime()),e.setMinutes(e.getMinutes()+t),e}function Mw(e,t,n){let r=n?-1:1,o=e.getTimezoneOffset(),i=gf(t,o);return _w(e,r*(i-o))}function xw(e){if(rf(e))return e;if(typeof e=="number"&&!isNaN(e))return new Date(e);if(typeof e=="string"){if(e=e.trim(),/^(\d{4}(-\d{1,2}(-\d{1,2})?)?)$/.test(e)){let[o,i=1,s=1]=e.split("-").map(a=>+a);return Ao(o,i-1,s)}let n=parseFloat(e);if(!isNaN(e-n))return new Date(n);let r;if(r=e.match(mw))return Sw(r)}let t=new Date(e);if(!rf(t))throw new Error(`Unable to convert "${e}" into a date`);return t}function Sw(e){let t=new Date(0),n=0,r=0,o=e[8]?t.setUTCFullYear:t.setFullYear,i=e[8]?t.setUTCHours:t.setHours;e[9]&&(n=Number(e[9]+e[10]),r=Number(e[9]+e[11])),o.call(t,Number(e[1]),Number(e[2])-1,Number(e[3]));let s=Number(e[4]||0)-n,a=Number(e[5]||0)-r,u=Number(e[6]||0),c=Math.floor(parseFloat("0."+(e[7]||0))*1e3);return i.call(t,s,a,u,c),t}function rf(e){return e instanceof Date&&!isNaN(e.valueOf())}var Tw=/^(\d+)?\.((\d+)(-(\d+))?)?$/,of=22,Oo=".",Gn="0",Nw=";",Aw=",",ka="#";function Ow(e,t,n,r,o,i,s=!1){let a="",u=!1;if(!isFinite(e))a=qe(n,he.Infinity);else{let c=kw(e);s&&(c=Pw(c));let l=t.minInt,d=t.minFrac,p=t.maxFrac;if(i){let z=i.match(Tw);if(z===null)throw new Error(`${i} is not a valid digit info`);let W=z[1],ae=z[3],je=z[5];W!=null&&(l=La(W)),ae!=null&&(d=La(ae)),je!=null?p=La(je):ae!=null&&d>p&&(p=d)}Lw(c,d,p);let f=c.digits,h=c.integerLen,g=c.exponent,M=[];for(u=f.every(z=>!z);h<l;h++)f.unshift(0);for(;h<0;h++)f.unshift(0);h>0?M=f.splice(h,f.length):(M=f,f=[0]);let b=[];for(f.length>=t.lgSize&&b.unshift(f.splice(-t.lgSize,f.length).join(""));f.length>t.gSize;)b.unshift(f.splice(-t.gSize,f.length).join(""));f.length&&b.unshift(f.join("")),a=b.join(qe(n,r)),M.length&&(a+=qe(n,o)+M.join("")),g&&(a+=qe(n,he.Exponential)+"+"+g)}return e<0&&!u?a=t.negPre+a+t.negSuf:a=t.posPre+a+t.posSuf,a}function Fw(e,t,n){let r=pw(t,ff.Decimal),o=Rw(r,qe(t,he.MinusSign));return Ow(e,o,t,he.Group,he.Decimal,n)}function Rw(e,t="-"){let n={minInt:1,minFrac:0,maxFrac:0,posPre:"",posSuf:"",negPre:"",negSuf:"",gSize:0,lgSize:0},r=e.split(Nw),o=r[0],i=r[1],s=o.indexOf(Oo)!==-1?o.split(Oo):[o.substring(0,o.lastIndexOf(Gn)+1),o.substring(o.lastIndexOf(Gn)+1)],a=s[0],u=s[1]||"";n.posPre=a.substring(0,a.indexOf(ka));for(let l=0;l<u.length;l++){let d=u.charAt(l);d===Gn?n.minFrac=n.maxFrac=l+1:d===ka?n.maxFrac=l+1:n.posSuf+=d}let c=a.split(Aw);if(n.gSize=c[1]?c[1].length:0,n.lgSize=c[2]||c[1]?(c[2]||c[1]).length:0,i){let l=o.length-n.posPre.length-n.posSuf.length,d=i.indexOf(ka);n.negPre=i.substring(0,d).replace(/'/g,""),n.negSuf=i.slice(d+l).replace(/'/g,"")}else n.negPre=t+n.posPre,n.negSuf=n.posSuf;return n}function Pw(e){if(e.digits[0]===0)return e;let t=e.digits.length-e.integerLen;return e.exponent?e.exponent+=2:(t===0?e.digits.push(0,0):t===1&&e.digits.push(0),e.integerLen+=2),e}function kw(e){let t=Math.abs(e)+"",n=0,r,o,i,s,a;for((o=t.indexOf(Oo))>-1&&(t=t.replace(Oo,"")),(i=t.search(/e/i))>0?(o<0&&(o=i),o+=+t.slice(i+1),t=t.substring(0,i)):o<0&&(o=t.length),i=0;t.charAt(i)===Gn;i++);if(i===(a=t.length))r=[0],o=1;else{for(a--;t.charAt(a)===Gn;)a--;for(o-=i,r=[],s=0;i<=a;i++,s++)r[s]=Number(t.charAt(i))}return o>of&&(r=r.splice(0,of-1),n=o-1,o=1),{digits:r,exponent:n,integerLen:o}}function Lw(e,t,n){if(t>n)throw new Error(`The minimum number of digits after fraction (${t}) is higher than the maximum (${n}).`);let r=e.digits,o=r.length-e.integerLen,i=Math.min(Math.max(t,o),n),s=i+e.integerLen,a=r[s];if(s>0){r.splice(Math.max(e.integerLen,s));for(let d=s;d<r.length;d++)r[d]=0}else{o=Math.max(0,o),e.integerLen=1,r.length=Math.max(1,s=i+1),r[0]=0;for(let d=1;d<s;d++)r[d]=0}if(a>=5)if(s-1<0){for(let d=0;d>s;d--)r.unshift(0),e.integerLen++;r.unshift(1),e.integerLen++}else r[s-1]++;for(;o<Math.max(0,i);o++)r.push(0);let u=i!==0,c=t+e.integerLen,l=r.reduceRight(function(d,p,f,h){return p=p+d,h[f]=p<10?p:p-10,u&&(h[f]===0&&f>=c?h.pop():u=!1),p>=10?1:0},0);l&&(r.unshift(l),e.integerLen++)}function La(e){let t=parseInt(e);if(isNaN(t))throw new Error("Invalid integer literal when parsing "+e);return t}function ET(e,t){t=encodeURIComponent(t);for(let n of e.split(";")){let r=n.indexOf("="),[o,i]=r==-1?[n,""]:[n.slice(0,r),n.slice(r+1)];if(o.trim()===t)return decodeURIComponent(i)}return null}var ja=/\s+/,sf=[],CT=(()=>{class e{constructor(n,r){this._ngEl=n,this._renderer=r,this.initialClasses=sf,this.stateMap=new Map}set klass(n){this.initialClasses=n!=null?n.trim().split(ja):sf}set ngClass(n){this.rawClass=typeof n=="string"?n.trim().split(ja):n}ngDoCheck(){for(let r of this.initialClasses)this._updateState(r,!0);let n=this.rawClass;if(Array.isArray(n)||n instanceof Set)for(let r of n)this._updateState(r,!0);else if(n!=null)for(let r of Object.keys(n))this._updateState(r,!!n[r]);this._applyStateDiff()}_updateState(n,r){let o=this.stateMap.get(n);o!==void 0?(o.enabled!==r&&(o.changed=!0,o.enabled=r),o.touched=!0):this.stateMap.set(n,{enabled:r,changed:!0,touched:!0})}_applyStateDiff(){for(let n of this.stateMap){let r=n[0],o=n[1];o.changed?(this._toggleClass(r,o.enabled),o.changed=!1):o.touched||(o.enabled&&this._toggleClass(r,!1),this.stateMap.delete(r)),o.touched=!1}}_toggleClass(n,r){n=n.trim(),n.length>0&&n.split(ja).forEach(o=>{r?this._renderer.addClass(this._ngEl.nativeElement,o):this._renderer.removeClass(this._ngEl.nativeElement,o)})}static{this.\u0275fac=function(r){return new(r||e)(U(ot),U(Io))}}static{this.\u0275dir=rn({type:e,selectors:[["","ngClass",""]],inputs:{klass:[Ke.None,"class","klass"],ngClass:"ngClass"},standalone:!0})}}return e})();var Va=class{constructor(t,n,r,o){this.$implicit=t,this.ngForOf=n,this.index=r,this.count=o}get first(){return this.index===0}get last(){return this.index===this.count-1}get even(){return this.index%2===0}get odd(){return!this.even}},bT=(()=>{class e{set ngForOf(n){this._ngForOf=n,this._ngForOfDirty=!0}set ngForTrackBy(n){this._trackByFn=n}get ngForTrackBy(){return this._trackByFn}constructor(n,r,o){this._viewContainer=n,this._template=r,this._differs=o,this._ngForOf=null,this._ngForOfDirty=!0,this._differ=null}set ngForTemplate(n){n&&(this._template=n)}ngDoCheck(){if(this._ngForOfDirty){this._ngForOfDirty=!1;let n=this._ngForOf;if(!this._differ&&n)if(0)try{}catch{}else this._differ=this._differs.find(n).create(this.ngForTrackBy)}if(this._differ){let n=this._differ.diff(this._ngForOf);n&&this._applyChanges(n)}}_applyChanges(n){let r=this._viewContainer;n.forEachOperation((o,i,s)=>{if(o.previousIndex==null)r.createEmbeddedView(this._template,new Va(o.item,this._ngForOf,-1,-1),s===null?void 0:s);else if(s==null)r.remove(i===null?void 0:i);else if(i!==null){let a=r.get(i);r.move(a,s),af(a,o)}});for(let o=0,i=r.length;o<i;o++){let a=r.get(o).context;a.index=o,a.count=i,a.ngForOf=this._ngForOf}n.forEachIdentityChange(o=>{let i=r.get(o.currentIndex);af(i,o)})}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(U(Tt),U(_t),U(Ta))}}static{this.\u0275dir=rn({type:e,selectors:[["","ngFor","","ngForOf",""]],inputs:{ngForOf:"ngForOf",ngForTrackBy:"ngForTrackBy",ngForTemplate:"ngForTemplate"},standalone:!0})}}return e})();function af(e,t){e.context.$implicit=t.item}var _T=(()=>{class e{constructor(n,r){this._viewContainer=n,this._context=new Ba,this._thenTemplateRef=null,this._elseTemplateRef=null,this._thenViewRef=null,this._elseViewRef=null,this._thenTemplateRef=r}set ngIf(n){this._context.$implicit=this._context.ngIf=n,this._updateView()}set ngIfThen(n){uf("ngIfThen",n),this._thenTemplateRef=n,this._thenViewRef=null,this._updateView()}set ngIfElse(n){uf("ngIfElse",n),this._elseTemplateRef=n,this._elseViewRef=null,this._updateView()}_updateView(){this._context.$implicit?this._thenViewRef||(this._viewContainer.clear(),this._elseViewRef=null,this._thenTemplateRef&&(this._thenViewRef=this._viewContainer.createEmbeddedView(this._thenTemplateRef,this._context))):this._elseViewRef||(this._viewContainer.clear(),this._thenViewRef=null,this._elseTemplateRef&&(this._elseViewRef=this._viewContainer.createEmbeddedView(this._elseTemplateRef,this._context)))}static ngTemplateContextGuard(n,r){return!0}static{this.\u0275fac=function(r){return new(r||e)(U(Tt),U(_t))}}static{this.\u0275dir=rn({type:e,selectors:[["","ngIf",""]],inputs:{ngIf:"ngIf",ngIfThen:"ngIfThen",ngIfElse:"ngIfElse"},standalone:!0})}}return e})(),Ba=class{constructor(){this.$implicit=null,this.ngIf=null}};function uf(e,t){if(!!!(!t||t.createEmbeddedView))throw new Error(`${e} must be a TemplateRef, but received '${te(t)}'.`)}var MT=(()=>{class e{constructor(n,r,o){this._ngEl=n,this._differs=r,this._renderer=o,this._ngStyle=null,this._differ=null}set ngStyle(n){this._ngStyle=n,!this._differ&&n&&(this._differ=this._differs.find(n).create())}ngDoCheck(){if(this._differ){let n=this._differ.diff(this._ngStyle);n&&this._applyChanges(n)}}_setStyle(n,r){let[o,i]=n.split("."),s=o.indexOf("-")===-1?void 0:Sn.DashCase;r!=null?this._renderer.setStyle(this._ngEl.nativeElement,o,i?`${r}${i}`:r,s):this._renderer.removeStyle(this._ngEl.nativeElement,o,s)}_applyChanges(n){n.forEachRemovedItem(r=>this._setStyle(r.key,null)),n.forEachAddedItem(r=>this._setStyle(r.key,r.currentValue)),n.forEachChangedItem(r=>this._setStyle(r.key,r.currentValue))}static{this.\u0275fac=function(r){return new(r||e)(U(ot),U(Na),U(Io))}}static{this.\u0275dir=rn({type:e,selectors:[["","ngStyle",""]],inputs:{ngStyle:"ngStyle"},standalone:!0})}}return e})(),xT=(()=>{class e{constructor(n){this._viewContainerRef=n,this._viewRef=null,this.ngTemplateOutletContext=null,this.ngTemplateOutlet=null,this.ngTemplateOutletInjector=null}ngOnChanges(n){if(this._shouldRecreateView(n)){let r=this._viewContainerRef;if(this._viewRef&&r.remove(r.indexOf(this._viewRef)),!this.ngTemplateOutlet){this._viewRef=null;return}let o=this._createContextForwardProxy();this._viewRef=r.createEmbeddedView(this.ngTemplateOutlet,o,{injector:this.ngTemplateOutletInjector??void 0})}}_shouldRecreateView(n){return!!n.ngTemplateOutlet||!!n.ngTemplateOutletInjector}_createContextForwardProxy(){return new Proxy({},{set:(n,r,o)=>this.ngTemplateOutletContext?Reflect.set(this.ngTemplateOutletContext,r,o):!1,get:(n,r,o)=>{if(this.ngTemplateOutletContext)return Reflect.get(this.ngTemplateOutletContext,r,o)}})}static{this.\u0275fac=function(r){return new(r||e)(U(Tt))}}static{this.\u0275dir=rn({type:e,selectors:[["","ngTemplateOutlet",""]],inputs:{ngTemplateOutletContext:"ngTemplateOutletContext",ngTemplateOutlet:"ngTemplateOutlet",ngTemplateOutletInjector:"ngTemplateOutletInjector"},standalone:!0,features:[Gs]})}}return e})();function zn(e,t){return new C(2100,!1)}var $a=class{createSubscription(t,n){return Aa(()=>t.subscribe({next:n,error:r=>{throw r}}))}dispose(t){Aa(()=>t.unsubscribe())}},Ha=class{createSubscription(t,n){return t.then(n,r=>{throw r})}dispose(t){}},jw=new Ha,Vw=new $a,ST=(()=>{class e{constructor(n){this._latestValue=null,this.markForCheckOnValueUpdate=!0,this._subscription=null,this._obj=null,this._strategy=null,this._ref=n}ngOnDestroy(){this._subscription&&this._dispose(),this._ref=null}transform(n){if(!this._obj){if(n)try{this.markForCheckOnValueUpdate=!1,this._subscribe(n)}finally{this.markForCheckOnValueUpdate=!0}return this._latestValue}return n!==this._obj?(this._dispose(),this.transform(n)):this._latestValue}_subscribe(n){this._obj=n,this._strategy=this._selectStrategy(n),this._subscription=this._strategy.createSubscription(n,r=>this._updateLatestValue(n,r))}_selectStrategy(n){if(Eo(n))return jw;if(Ma(n))return Vw;throw zn(e,n)}_dispose(){this._strategy.dispose(this._subscription),this._latestValue=null,this._subscription=null,this._obj=null}_updateLatestValue(n,r){n===this._obj&&(this._latestValue=r,this.markForCheckOnValueUpdate&&this._ref?.markForCheck())}static{this.\u0275fac=function(r){return new(r||e)(U(Sa,16))}}static{this.\u0275pipe=on({name:"async",type:e,pure:!1,standalone:!0})}}return e})();var Bw=/(?:[0-9A-Za-z\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0560-\u0588\u05D0-\u05EA\u05EF-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u0860-\u086A\u0870-\u0887\u0889-\u088E\u08A0-\u08C9\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u09FC\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C5D\u0C60\u0C61\u0C80\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D04-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D54-\u0D56\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16F1-\u16F8\u1700-\u1711\u171F-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1878\u1880-\u1884\u1887-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4C\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1C80-\u1C88\u1C90-\u1CBA\u1CBD-\u1CBF\u1CE9-\u1CEC\u1CEE-\u1CF3\u1CF5\u1CF6\u1CFA\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2119-\u211D\u2124\u2126\u2128\u212A-\u212D\u212F-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2183\u2184\u2C00-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2E2F\u3005\u3006\u3031-\u3035\u303B\u303C\u3041-\u3096\u309D-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312F\u3131-\u318E\u31A0-\u31BF\u31F0-\u31FF\u3400-\u4DBF\u4E00-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6E5\uA717-\uA71F\uA722-\uA788\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA8FE\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB69\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF2D-\uDF40\uDF42-\uDF49\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF]|\uD801[\uDC00-\uDC9D\uDCB0-\uDCD3\uDCD8-\uDCFB\uDD00-\uDD27\uDD30-\uDD63\uDD70-\uDD7A\uDD7C-\uDD8A\uDD8C-\uDD92\uDD94\uDD95\uDD97-\uDDA1\uDDA3-\uDDB1\uDDB3-\uDDB9\uDDBB\uDDBC\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE35\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2\uDD00-\uDD23\uDE80-\uDEA9\uDEB0\uDEB1\uDF00-\uDF1C\uDF27\uDF30-\uDF45\uDF70-\uDF81\uDFB0-\uDFC4\uDFE0-\uDFF6]|\uD804[\uDC03-\uDC37\uDC71\uDC72\uDC75\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD44\uDD47\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC00-\uDC34\uDC47-\uDC4A\uDC5F-\uDC61\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDEB8\uDF00-\uDF1A\uDF40-\uDF46]|\uD806[\uDC00-\uDC2B\uDCA0-\uDCDF\uDCFF-\uDD06\uDD09\uDD0C-\uDD13\uDD15\uDD16\uDD18-\uDD2F\uDD3F\uDD41\uDDA0-\uDDA7\uDDAA-\uDDD0\uDDE1\uDDE3\uDE00\uDE0B-\uDE32\uDE3A\uDE50\uDE5C-\uDE89\uDE9D\uDEB0-\uDEF8]|\uD807[\uDC00-\uDC08\uDC0A-\uDC2E\uDC40\uDC72-\uDC8F\uDD00-\uDD06\uDD08\uDD09\uDD0B-\uDD30\uDD46\uDD60-\uDD65\uDD67\uDD68\uDD6A-\uDD89\uDD98\uDEE0-\uDEF2\uDFB0]|\uD808[\uDC00-\uDF99]|\uD809[\uDC80-\uDD43]|\uD80B[\uDF90-\uDFF0]|[\uD80C\uD81C-\uD820\uD822\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE70-\uDEBE\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDE40-\uDE7F\uDF00-\uDF4A\uDF50\uDF93-\uDF9F\uDFE0\uDFE1\uDFE3]|\uD821[\uDC00-\uDFF7]|\uD823[\uDC00-\uDCD5\uDD00-\uDD08]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00-\uDD22\uDD50-\uDD52\uDD64-\uDD67\uDD70-\uDEFB]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD837[\uDF00-\uDF1E]|\uD838[\uDD00-\uDD2C\uDD37-\uDD3D\uDD4E\uDE90-\uDEAD\uDEC0-\uDEEB]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]|\uD83A[\uDC00-\uDCC4\uDD00-\uDD43\uDD4B]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF38\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A])\S*/g,TT=(()=>{class e{transform(n){if(n==null)return null;if(typeof n!="string")throw zn(e,n);return n.replace(Bw,r=>r[0].toUpperCase()+r.slice(1).toLowerCase())}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275pipe=on({name:"titlecase",type:e,pure:!0,standalone:!0})}}return e})(),NT=(()=>{class e{transform(n){if(n==null)return null;if(typeof n!="string")throw zn(e,n);return n.toUpperCase()}static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275pipe=on({name:"uppercase",type:e,pure:!0,standalone:!0})}}return e})(),$w="mediumDate",Hw=new O(""),Uw=new O(""),AT=(()=>{class e{constructor(n,r,o){this.locale=n,this.defaultTimezone=r,this.defaultOptions=o}transform(n,r,o,i){if(n==null||n===""||n!==n)return null;try{let s=r??this.defaultOptions?.dateFormat??$w,a=o??this.defaultOptions?.timezone??this.defaultTimezone??void 0;return Dw(n,s,i||this.locale,a)}catch(s){throw zn(e,s.message)}}static{this.\u0275fac=function(r){return new(r||e)(U(Un,16),U(Hw,24),U(Uw,24))}}static{this.\u0275pipe=on({name:"date",type:e,pure:!0,standalone:!0})}}return e})();var OT=(()=>{class e{constructor(n){this._locale=n}transform(n,r,o){if(!Gw(n))return null;o||=this._locale;try{let i=zw(n);return Fw(i,o,r)}catch(i){throw zn(e,i.message)}}static{this.\u0275fac=function(r){return new(r||e)(U(Un,16))}}static{this.\u0275pipe=on({name:"number",type:e,pure:!0,standalone:!0})}}return e})();function Gw(e){return!(e==null||e===""||e!==e)}function zw(e){if(typeof e=="string"&&!isNaN(Number(e)-parseFloat(e)))return Number(e);if(typeof e!="number")throw new Error(`${e} is not a number`);return e}var FT=(()=>{class e{static{this.\u0275fac=function(r){return new(r||e)}}static{this.\u0275mod=js({type:e})}static{this.\u0275inj=Rs({})}}return e})(),Ww="browser",qw="server";function Yw(e){return e===Ww}function RT(e){return e===qw}var PT=(()=>{class e{static{this.\u0275prov=k({token:e,providedIn:"root",factory:()=>Yw(F(oa))?new Ua(F(za),window):new Ga})}}return e})(),Ua=class{constructor(t,n){this.document=t,this.window=n,this.offset=()=>[0,0]}setOffset(t){Array.isArray(t)?this.offset=()=>t:this.offset=t}getScrollPosition(){return[this.window.scrollX,this.window.scrollY]}scrollToPosition(t){this.window.scrollTo(t[0],t[1])}scrollToAnchor(t){let n=Qw(this.document,t);n&&(this.scrollToElement(n),n.focus())}setHistoryScrollRestoration(t){this.window.history.scrollRestoration=t}scrollToElement(t){let n=t.getBoundingClientRect(),r=n.left+this.window.pageXOffset,o=n.top+this.window.pageYOffset,i=this.offset();this.window.scrollTo(r-i[0],o-i[1])}};function Qw(e,t){let n=e.getElementById(t)||e.getElementsByName(t)[0];if(n)return n;if(typeof e.createTreeWalker=="function"&&e.body&&typeof e.body.attachShadow=="function"){let r=e.createTreeWalker(e.body,NodeFilter.SHOW_ELEMENT),o=r.currentNode;for(;o;){let i=o.shadowRoot;if(i){let s=i.getElementById(t)||i.querySelector(`[name="${t}"]`);if(s)return s}o=r.nextNode()}}return null}var Ga=class{setOffset(t){}getScrollPosition(){return[0,0]}scrollToPosition(t){}scrollToAnchor(t){}setHistoryScrollRestoration(t){}},cf=class{};var Ne=function(e){return e[e.State=0]="State",e[e.Transition=1]="Transition",e[e.Sequence=2]="Sequence",e[e.Group=3]="Group",e[e.Animate=4]="Animate",e[e.Keyframes=5]="Keyframes",e[e.Style=6]="Style",e[e.Trigger=7]="Trigger",e[e.Reference=8]="Reference",e[e.AnimateChild=9]="AnimateChild",e[e.AnimateRef=10]="AnimateRef",e[e.Query=11]="Query",e[e.Stagger=12]="Stagger",e}(Ne||{}),jT="*";function VT(e,t){return{type:Ne.Trigger,name:e,definitions:t,options:{}}}function BT(e,t=null){return{type:Ne.Animate,styles:t,timings:e}}function $T(e,t=null){return{type:Ne.Group,steps:e,options:t}}function HT(e,t=null){return{type:Ne.Sequence,steps:e,options:t}}function UT(e){return{type:Ne.Style,styles:e,offset:null}}function GT(e,t,n){return{type:Ne.State,name:e,styles:t,options:n}}function zT(e){return{type:Ne.Keyframes,steps:e}}function WT(e,t,n=null){return{type:Ne.Transition,expr:e,animation:t,options:n}}function qT(e=null){return{type:Ne.AnimateChild,options:e}}function YT(e,t,n=null){return{type:Ne.Query,selector:e,animation:t,options:n}}var mf=class{constructor(t=0,n=0){this._onDoneFns=[],this._onStartFns=[],this._onDestroyFns=[],this._originalOnDoneFns=[],this._originalOnStartFns=[],this._started=!1,this._destroyed=!1,this._finished=!1,this._position=0,this.parentPlayer=null,this.totalTime=t+n}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}onStart(t){this._originalOnStartFns.push(t),this._onStartFns.push(t)}onDone(t){this._originalOnDoneFns.push(t),this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}init(){}play(){this.hasStarted()||(this._onStart(),this.triggerMicrotask()),this._started=!0}triggerMicrotask(){queueMicrotask(()=>this._onFinish())}_onStart(){this._onStartFns.forEach(t=>t()),this._onStartFns=[]}pause(){}restart(){}finish(){this._onFinish()}destroy(){this._destroyed||(this._destroyed=!0,this.hasStarted()||this._onStart(),this.finish(),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this._started=!1,this._finished=!1,this._onStartFns=this._originalOnStartFns,this._onDoneFns=this._originalOnDoneFns}setPosition(t){this._position=this.totalTime?t*this.totalTime:1}getPosition(){return this.totalTime?this._position/this.totalTime:1}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},yf=class{constructor(t){this._onDoneFns=[],this._onStartFns=[],this._finished=!1,this._started=!1,this._destroyed=!1,this._onDestroyFns=[],this.parentPlayer=null,this.totalTime=0,this.players=t;let n=0,r=0,o=0,i=this.players.length;i==0?queueMicrotask(()=>this._onFinish()):this.players.forEach(s=>{s.onDone(()=>{++n==i&&this._onFinish()}),s.onDestroy(()=>{++r==i&&this._onDestroy()}),s.onStart(()=>{++o==i&&this._onStart()})}),this.totalTime=this.players.reduce((s,a)=>Math.max(s,a.totalTime),0)}_onFinish(){this._finished||(this._finished=!0,this._onDoneFns.forEach(t=>t()),this._onDoneFns=[])}init(){this.players.forEach(t=>t.init())}onStart(t){this._onStartFns.push(t)}_onStart(){this.hasStarted()||(this._started=!0,this._onStartFns.forEach(t=>t()),this._onStartFns=[])}onDone(t){this._onDoneFns.push(t)}onDestroy(t){this._onDestroyFns.push(t)}hasStarted(){return this._started}play(){this.parentPlayer||this.init(),this._onStart(),this.players.forEach(t=>t.play())}pause(){this.players.forEach(t=>t.pause())}restart(){this.players.forEach(t=>t.restart())}finish(){this._onFinish(),this.players.forEach(t=>t.finish())}destroy(){this._onDestroy()}_onDestroy(){this._destroyed||(this._destroyed=!0,this._onFinish(),this.players.forEach(t=>t.destroy()),this._onDestroyFns.forEach(t=>t()),this._onDestroyFns=[])}reset(){this.players.forEach(t=>t.reset()),this._destroyed=!1,this._finished=!1,this._started=!1}setPosition(t){let n=t*this.totalTime;this.players.forEach(r=>{let o=r.totalTime?Math.min(1,n/r.totalTime):1;r.setPosition(o)})}getPosition(){let t=this.players.reduce((n,r)=>n===null||r.totalTime>n.totalTime?r:n,null);return t!=null?t.getPosition():0}beforeDestroy(){this.players.forEach(t=>{t.beforeDestroy&&t.beforeDestroy()})}triggerCallback(t){let n=t=="start"?this._onStartFns:this._onDoneFns;n.forEach(r=>r()),n.length=0}},QT="!";export{$ as a,xf as b,x as c,Yo as d,Qo as e,ge as f,dn as g,pn as h,Nf as i,dt as j,Oe as k,jf as l,Vf as m,Bf as n,Ve as o,$f as p,Fe as q,Qf as r,Ee as s,gn as t,$t as u,Kf as v,Jf as w,Ko as x,mn as y,ip as z,pt as A,sp as B,Jo as C,ap as D,up as E,yn as F,Ht as G,Xo as H,cp as I,lp as J,pp as K,Su as L,ti as M,hp as N,gp as O,ri as P,mp as Q,yp as R,Tu as S,Dp as T,vp as U,wp as V,Ip as W,C as X,ue as Y,Ic as Z,k as _,Rs as $,Zx as aa,O as ba,S as ca,H as da,F as ea,Tc as fa,Nc as ga,In as ha,Ke as ia,Kx as ja,js as ka,rn as la,Jx as ma,Wc as na,Xe as oa,Ih as pa,Gs as qa,Xx as ra,eS as sa,tS as ta,nS as ua,rS as va,pg as wa,St as xa,Ct as ya,ot as za,_e as Aa,ki as Ba,oS as Ca,iS as Da,_g as Ea,oa as Fa,sS as Ga,aS as Ha,Vn as Ia,Fl as Ja,uS as Ka,cS as La,lS as Ma,dS as Na,fS as Oa,Rl as Pa,pS as Qa,sa as Ra,Qg as Sa,hS as Ta,gS as Ua,mS as Va,Sn as Wa,yS as Xa,U as Ya,DS as Za,_t as _a,Hr as $a,wo as ab,Ji as bb,Io as cb,Hn as db,le as eb,Cy as fb,Tt as gb,rD as hb,cD as ib,ls as jb,dD as kb,_d as lb,fs as mb,wD as nb,RD as ob,Td as pb,PD as qb,ES as rb,CS as sb,bS as tb,_S as ub,MS as vb,xS as wb,Ld as xb,jd as yb,YD as zb,Vd as Ab,Bd as Bb,KD as Cb,SS as Db,XD as Eb,ev as Fb,sv as Gb,av as Hb,TS as Ib,NS as Jb,AS as Kb,lv as Lb,$d as Mb,OS as Nb,FS as Ob,RS as Pb,PS as Qb,kS as Rb,LS as Sb,pv as Tb,Hd as Ub,hv as Vb,gv as Wb,mv as Xb,yv as Yb,jS as Zb,Dv as _b,VS as $b,BS as ac,$S as bc,HS as cc,US as dc,GS as ec,zS as fc,WS as gc,qS as hc,YS as ic,Mv as jc,xv as kc,QS as lc,Sv as mc,Eo as nc,Nv as oc,Av as pc,xa as qc,ZS as rc,Un as sc,Wv as tc,Sa as uc,Ta as vc,KS as wc,JS as xc,Xv as yc,ew as zc,XS as Ac,Oa as Bc,vT as Cc,ef as Dc,za as Ec,wT as Fc,Fo as Gc,rw as Hc,IT as Ic,ow as Jc,ET as Kc,CT as Lc,bT as Mc,_T as Nc,MT as Oc,xT as Pc,ST as Qc,TT as Rc,NT as Sc,AT as Tc,OT as Uc,FT as Vc,Ww as Wc,Yw as Xc,RT as Yc,PT as Zc,cf as _c,Ne as $c,jT as ad,VT as bd,BT as cd,$T as dd,HT as ed,UT as fd,GT as gd,zT as hd,WT as id,qT as jd,YT as kd,mf as ld,yf as md,QT as nd};
