<!-- <div class="row ">
  <div class="col-md-3 text-right">
    <button mat-raised-button color="primary" class="">Print</button>
  </div>
 </div> -->

 <div class="container mt-4">
  <div class="d-flex justify-content-end">
    <div class="col-md-3 text-right">
      <button mat-raised-button color="primary" (click)="printPage()">Print</button>
    </div>
   </div>
 </div>

<div id="print-root">
  <div class="invoice mt-2" tabindex="0">

      <section class="container-fluid">
        <div class="company-header">
          <div class="title">M/S. RACHIN EXPORTS</div>
          <div class="sub mt-2">SUREKA ESTATE, MAHANTH SHIWALA, MIRZAPUR, U.P. INDIA</div>
          <div class="sub">Mobile: 6390335666</div>
        </div>


        <div class="container">
          <div class="d-flex justify-content-between">
            <div class="main-div">
              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 90px;">Issue No</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                  <span class="px-3 fw-medium">{{ orderIssueData.Br_issueNo }}</span
                  >
                </p>
              </div>
              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 90px;">Name</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                   <span class="px-3 fw-medium">{{ orderIssueData.weaver.name | uppercase}}</span>

                </p>
              </div>
           <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 90px;">Address</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                   <span class="px-3 fw-medium">{{orderIssueData.weaver.address}}</span>
                </p>
              </div>

              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 90px;">Aadhaar No.</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                   <span class="px-3 fw-medium">{{ orderIssueData?.weaver?.aadhaarDetails?.aadhaarNo }}</span>
                </p>
              </div>
            </div>
            <div class="main-div">
              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 80px;">Issue Date</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                  <span class="px-3 fw-medium">{{ orderIssueData.date | date:'dd-MM-yyyy'}}</span>
                </p>
              </div>
              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 80px;">Phone No</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                  <span class="px-3 fw-medium">{{ orderIssueData.weaver.contactNo }}</span>
                </p>
              </div>
              <div class="sub-div d-flex align-items-center" style="min-width: 320px;">
                <p style="width: 80px;">Map No.</p>
                <p style="width: 10px; text-align: right;">:</p>
                <p>
                  <span class="px-3 fw-medium">{{ orderIssueData.MapOrderNo }}</span>
                </p>
              </div>
            </div>
          </div>
        </div>


      </section>
      <section class="container-fluid mt-5">
        <table>
          <tr class="tablecontent2">
            <th>Order No</th>
            <th>Quality</th>
            <th>Design</th>
            <th>Colour</th>
            <th>Size</th>
            <th>Khap Size</th>
            <th>Area</th>
            <th>Pcs</th>
            <th>Rate</th>
            <th>Amount</th>
          </tr>
          <tr class="tablecontent2">


            <td>{{ orderIssueData.buyerOrder.orderNo }}</td>
            <td>{{ orderIssueData.quality.quality }}</td>
            <td>{{ orderIssueData.design.design }}</td>
            <td>{{ orderIssueData.borderColour }}</td>
            <td>{{ orderIssueData.size.sizeInYard }}</td>
            <td>{{ orderIssueData.khapSize }}</td>

            <td>
              {{ orderIssueData.area }}
            </td>

            <td>{{ orderIssueData.pcs }}</td>
            <td>{{ orderIssueData.rate | number:'1.2-3'}}</td>
            <td>{{ orderIssueData.amount | number:'1.0-0' }}.00 </td>

          </tr>
        </table>
      </section>
      <section class="container-fluid my-5">
        <table style="border: 2" class="tablecontent5">
          <tr class="tablecontent3">
            <th></th>
            <th>Kati</th>
            <th>Tana</th>
            <th>Soot</th>
            <th>Tharri</th>
            <th>Silk</th>
            <th>Other</th>
          </tr>
          <!-- Lagat row -->
          <tr class="tablecontent3">
             <th>Lagat</th>
            <td>{{ materialsTotals.kati.lagat | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.tana.lagat | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.soot.lagat | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.thari.lagat | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.silk.lagat | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.other.lagat | number:'1.3-3' }}</td>
          </tr>
          <!-- Total Issue row -->
          <tr class="tablecontent3">
            <th>
              Total Issue
              <button type="button" class="info-chip" mat-icon-button (click)="openChallanDetailsModal()" matTooltip="Challan Issued Details">
                <mat-icon>info</mat-icon>
              </button>
            </th>
            <td>{{ materialsTotals.kati.issue | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.tana.issue | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.soot.issue | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.thari.issue | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.silk.issue | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.other.issue | number:'1.3-3' }}</td>
          </tr>
          <!-- Receive row -->
          <tr class="tablecontent3">
            <th>Receive</th>
            <td>{{ materialsTotals.kati.receive | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.tana.receive | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.soot.receive | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.thari.receive | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.silk.receive | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.other.receive | number:'1.3-3' }}</td>
          </tr>
          <!-- Total Lagat row -->
          <tr class="tablecontent3">
            <th>Total Lagat</th>
            <td>{{ materialsTotals.kati.tLagat | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.tana.tLagat | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.soot.tLagat | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.thari.tLagat | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.silk.tLagat | number:'1.3-3' }}</td>
            <td>{{ materialsTotals.other.tLagat | number:'1.3-3' }}</td>
          </tr>
        </table>
      </section>



      <section class="container-fluid tablecontent4 mt-4">
        <h6>Note:</h6>
        <table  class="tableborder">
          <tr>
            <td>1.Without Map Carpet will not be Received</td>
            <th>Map No.</th>
            <td>:<span class="px-2">{{ orderIssueData?.MapOrderNo || 0 }}</span></td>
          </tr>
          <tr>
            <td>2. Color Fault Responsibility would be of Weaver</td>
            <th>Issue Date</th>
            <td>:<span class="px-2">{{ orderIssueData?.date | date:'dd-MM-yyyy' }}</span></td>
          </tr>
          <tr>
            <td>3.Bis</td>
            <th>Rec. Date</th>
            <td>:<span class="px-2">{{ recDate | date:'dd-MM-yyyy' }}</span></td>
          </tr>
          <tr>
            <td>4. Butan</td>
            <th>Carpet No.</th>
            <td>:<span class="px-5">{{ carpetNo || 0 }}</span></td>
          </tr>
          <tr>
            <td>5. Map Received Yes/No</td>
            <th>Butan</th>
            <td>:</td>
          </tr>
          <tr>
            <td>6. Colour on frings Penality on Weaver</td>
            <th>Biss</th>
            <td>:</td>
          </tr>
          <tr>
            <td>7. Khap in width 1' & 3/4' in length per sq.yds</td>
            <th>Weight</th>
            <td><span class="px-2">{{ weight || '' }}</span></td>
          </tr>
          <tr>
            <td>8. width and length of Boder size should be same</td>
            <th></th>
            <td></td>
          </tr>
          <tr>
            <td>9. Langari on Weaving Rs. 500 per sq.yds Penality</td>
            <th></th>
            <td></td>
          </tr>
          <tr>
            <td>10. Kinara:Down Up Right Left</td>
            <th></th>
            <td></td>
          </tr>
          <tr>
            <th class="pt-5">Auth. Signature</th>
            <th class="pt-5">Signature</th>
          </tr>
        </table>
      </section>


      <div class="center justify-content-center text-center page-break-after">
        <table
        class="my-5 mx-auto"
        style="
          width: 50%;
          border: 2px solid black;
          border-radius: 4px;
          text-align: center;
        "
      >
        <tr>
          <th>
            Less then the Age of 14 Years Weaving of carpet is illeagle<br />All
            Responsibility would be Contractorc or Weaver of the Carpet
          </th>
        </tr>
      </table>
      </div>


  </div>

  <!-- Second page wrapper -->
  <div class="invoice mt-2">
    <!-- Kati Receive details should be on the 2nd page ONLY -->
    <section class="container-fluid" *ngIf="materialReceives.length">
      <h6 class="text-center mb-2 kati-title">Kati Receive Details</h6>
      <table class="tablecontent2 kati-table">
        <tr>
          <th>Sr.No</th>
          <th>Colour</th>
          <th>Lagat</th>
          <th>Carpet Lagat</th>
          <th>T. Issued</th>
          <th>Receive</th>
          <th>Total Lagat</th>
        </tr>
        <ng-container *ngFor="let rec of materialReceives; let rid = index">
          <tr *ngFor="let row of rec.katiData; let i = index">
            <td>{{ i + 1 }}</td>
            <td>{{ row?.colour?.newColor ? (row?.colour?.newColor + ' - ' + (row?.colour?.companyColorCode || '') + ' - ' + (row?.colour?.remark || '')) : (row?.colour?.name || row?.colour) }}</td>
            <td>{{ row?.lagat }}</td>
            <td>{{ row?.carpetLagat }}</td>
            <td>{{ row?.tIssued || row?.issueValue || '-' }}</td>
            <td>{{ row?.receiveValue }}</td>
            <td>{{ row?.tLagat }}</td>
          </tr>
        </ng-container>
      </table>
    </section>
  </div>
</div>
