import { Component, OnInit, AfterViewInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { ManufactureService } from '../../../../services/manufacture.service';
import Swal from 'sweetalert2';

interface MaterialReceiveDoc {
  _id: string;
  challanNo: string;
  date: string | Date;
  weaver: any; // populated
  issueNo: any; // populated
  issueAreaInYard: string;
  materials: any;
}

interface TableRow {
  srNo: number;
  weaver: string;
  issueNo: string;
  challanNo: string;
  date: string;
  quality: string;
  design: string;
  colour: string;
  size: string;
  pcs: string;
  area: string;
  kati: string;
  tana: string;
  soot: string;
  thari: string;
  silk: string;
  other: string;
}

@Component({
  selector: 'app-view-material-receive',
  templateUrl: './view-material-receive.component.html',
  styleUrl: './view-material-receive.component.css'
})
export class ViewMaterialReceiveComponent implements OnInit, AfterViewInit {
  @ViewChild(MatPaginator) paginator!: MatPaginator;
  @ViewChild(MatSort) sort!: MatSort;

  filterForm!: FormGroup;
  dataSource = new MatTableDataSource<TableRow>([]);
  displayedColumns: string[] = [
    'srNo', 'challanNo', 'date', 'weaver', 'issueNo',
    'quality', 'design', 'colour', 'size', 'pcs', 'area',
    'kati', 'tana', 'soot', 'thari', 'silk', 'other', 'actions'
  ];

  receiveList: MaterialReceiveDoc[] = [];
  weaverList: any[] = [];
  issueList: any[] = [];
  filteredData: TableRow[] = [];

  constructor(private fb: FormBuilder, private manufactureService: ManufactureService) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.loadData();
  }

  ngAfterViewInit(): void {
    this.setupDataSource();
  }

  initializeForm(): void {
    this.filterForm = this.fb.group({
      weaver: [''],
      issueNo: [''],
      fromDate: [''],
      toDate: ['']
    });
    this.filterForm.valueChanges.subscribe(() => this.applyFilters());
  }

  setupDataSource(): void {
    this.dataSource.paginator = this.paginator;
    this.dataSource.sort = this.sort;
  }

  loadData(): void {
    this.manufactureService.getAllMaterialReceives().subscribe({
      next: (data: any) => {
        this.receiveList = data || [];
        this.extractFiltersData();
        this.processTableData();
      },
      error: (error) => {
        console.error('Error loading material receives:', error);
        Swal.fire('Error', 'Failed to load material receive data', 'error');
      }
    });
  }

  extractFiltersData(): void {
    const weavers = new Set<string>();
    const issues = new Set<string>();

    this.receiveList.forEach(item => {
      const w = item.weaver;
      if (w) {
        if (typeof w === 'object' && w.name) weavers.add(w.name);
        else if (typeof w === 'string') weavers.add(w);
      }
      const i = item.issueNo;
      if (i) {
        if (typeof i === 'object' && i.Br_issueNo) issues.add(i.Br_issueNo);
        else if (typeof i === 'string') issues.add(i);
      }
    });

    this.weaverList = Array.from(weavers).map(name => ({ name }));
    this.issueList = Array.from(issues).map(Br_issueNo => ({ Br_issueNo }));
  }

  processTableData(): void {
    this.filteredData = [];
    let sr = 1;

    const sorted = [...this.receiveList].sort((a, b) => {
      const n = (val: string) => {
        if (!val) return 0; const m = val.includes('-') ? parseInt(val.split('-')[1]) : parseInt(val.replace(/\D/g, ''));
        return isNaN(m) ? 0 : m;
      };
      return n(b.challanNo || '') - n(a.challanNo || '');
    });

    sorted.forEach(rec => {
      const row: TableRow = {
        srNo: sr++,
        weaver: this.getWeaverName(rec.weaver),
        issueNo: this.getIssueNumber(rec.issueNo),
        challanNo: rec.challanNo,
        date: this.formatDate(rec.date),
        quality: this.getPopulatedValue(rec.issueNo?.quality, 'quality'),
        design: this.getPopulatedValue(rec.issueNo?.design, 'design'),
        colour: this.getPopulatedValue(rec.issueNo, 'borderColour'),
        size: this.getPopulatedValue(rec.issueNo?.size, 'sizeInYard'),
        pcs: this.getPopulatedValue(rec.issueNo, 'pcs'),
        area: rec.issueAreaInYard || this.getPopulatedValue(rec.issueNo, 'area'),
        kati: this.getMaterialReceiveValue(rec.materials?.kati),
        tana: this.getMaterialReceiveValue(rec.materials?.tana),
        soot: this.getMaterialReceiveValue(rec.materials?.soot),
        thari: this.getMaterialReceiveValue(rec.materials?.thari),
        silk: this.getMaterialReceiveValue(rec.materials?.silk),
        other: this.getMaterialReceiveValue(rec.materials?.other),
      };
      this.filteredData.push(row);
    });

    this.dataSource.data = this.filteredData;
  }

  getWeaverName(weaver: any): string {
    if (typeof weaver === 'object' && weaver?.name) {
      const branchCode = weaver.branchCode || 'K';
      return `${branchCode} - ${weaver.name}`;
    } else if (typeof weaver === 'string') {
      return weaver.includes(' - ') ? weaver : `K - ${weaver}`;
    }
    return 'N/A';
  }

  getIssueNumber(issueNo: any): string {
    if (typeof issueNo === 'object' && issueNo?.Br_issueNo) return issueNo.Br_issueNo;
    if (typeof issueNo === 'string') return issueNo;
    return 'N/A';
  }

  formatDate(date: any): string {
    if (!date) return 'N/A';
    try { return new Date(date).toLocaleDateString('en-GB'); } catch { return date.toString(); }
  }

  getPopulatedValue(issueNo: any, field: string): string {
    if (typeof issueNo === 'object' && issueNo?.[field]) {
      const v = issueNo[field];
      if (typeof v === 'object' && v?.name) return v.name;
      if (typeof v === 'object' && v?.sizeInYard) return v.sizeInYard;
      if (typeof v === 'string' || typeof v === 'number') return String(v);
    }
    return 'N/A';
  }

  getMaterialReceiveValue(material: any): string {
    if (!material) return '0.000';
    // Show only Receive value per requirement
    const v = parseFloat(material.receive);
    return isNaN(v) ? '0.000' : v.toFixed(3);
  }

  onEdit(row: any): void {
    // Navigate to Material Receive in edit mode with the receive doc id
    const rec = this.receiveList.find(r => (r.challanNo === row.challanNo));
    if (!rec) return;
    window.location.href = `/admin/material-receive?editMode=true&editId=${rec._id}`;
  }

  onPrint(row: any): void {
    // Open print page for the corresponding issue id
    const rec = this.receiveList.find(r => (r.challanNo === row.challanNo));
    const issueId = (rec?.issueNo && (rec.issueNo._id || rec.issueNo)) || '';
    if (issueId) {
      window.location.href = `/admin/material-receive-print/${issueId}`;
    }
  }

  onDelete(row: any): void {
    const rec = this.receiveList.find(r => (r.challanNo === row.challanNo));
    if (!rec) return;
    Swal.fire({
      title: 'Delete this record?',
      text: `Challan No: ${rec.challanNo}`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonText: 'Delete'
    }).then(res => {
      if (res.isConfirmed) {
        this.manufactureService.deleteMaterialReceive(rec._id).subscribe({
          next: () => {
            Swal.fire('Deleted', 'Material receive deleted', 'success');
            this.loadData();
          },
          error: () => Swal.fire('Error', 'Failed to delete', 'error')
        });
      }
    });
  }

  applyFilters(): void {
    const f = this.filterForm.value;
    let data = [...this.filteredData];

    if (f.weaver) data = data.filter(x => x.weaver.toLowerCase().includes(f.weaver.toLowerCase()));
    if (f.issueNo) data = data.filter(x => x.issueNo.toLowerCase().includes(f.issueNo.toLowerCase()));

    if (f.fromDate) {
      const from = new Date(f.fromDate);
      data = data.filter(x => new Date(x.date.split('/').reverse().join('-')) >= from);
    }
    if (f.toDate) {
      const to = new Date(f.toDate);
      data = data.filter(x => new Date(x.date.split('/').reverse().join('-')) <= to);
    }

    this.dataSource.data = data;
  }

  applyFilter(event: Event): void {
    const filterValue = (event.target as HTMLInputElement).value;
    this.dataSource.filter = filterValue.trim().toLowerCase();
    if (this.dataSource.paginator) this.dataSource.paginator.firstPage();
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.dataSource.data = this.filteredData;
  }
}
