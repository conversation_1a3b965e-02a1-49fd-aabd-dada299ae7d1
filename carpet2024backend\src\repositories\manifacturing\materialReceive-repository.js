const MaterialReceive = require('../../model/phase-4/materialReceive-model');

const createMaterialReceive = async (data) => {
  const doc = new MaterialReceive(data);
  return await doc.save();
};

const getAllMaterialReceives = async () => {
  return await MaterialReceive.find()
    .populate({
      path: 'weaver',
      populate: [{ path: 'branch' }]
    })
    .populate({
      path: 'issueNo',
      populate: [
        { path: 'quality' },
        { path: 'design' },
        { path: 'branch' },
        { path: 'size' },
        { path: 'buyerOrder' },
        { path: 'weaver' },
      ]
    })
    .populate('materials.kati.description')
    .populate('materials.tana.description')
    .populate('materials.soot.description')
    .populate('materials.thari.description')
    .populate('materials.silk.description')
    .populate('materials.other.description')
    .populate('katiData.colour')
    .sort({ createdAt: -1 });
};

const getMaterialReceiveById = async (id) => {
  return await MaterialReceive.findById(id);
};

const updateMaterialReceive = async (id, data) => {
  return await MaterialReceive.findByIdAndUpdate(id, data, { new: true });
};

const deleteMaterialReceive = async (id) => {
  return await MaterialReceive.findByIdAndDelete(id);
};

const getMaterialReceivesByChallanNo = async (challanNo) => {
  return await MaterialReceive.findOne({ challanNo });
};

const getMaterialReceivesByWeaver = async (weaver) => {
  return await MaterialReceive.find({ weaver }).sort({ createdAt: -1 });
};

const getMaterialReceivesByIssueNo = async (issueNoId) => {
  return await MaterialReceive.find({ issueNo: issueNoId })
    .populate('katiData.colour')
    .sort({ createdAt: -1 });
};

module.exports = {
  createMaterialReceive,
  getAllMaterialReceives,
  getMaterialReceiveById,
  updateMaterialReceive,
  deleteMaterialReceive,
  getMaterialReceivesByChallanNo,
  getMaterialReceivesByWeaver,
  getMaterialReceivesByIssueNo,
};

