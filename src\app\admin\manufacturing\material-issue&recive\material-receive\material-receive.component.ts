import { Component, OnInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { MatTableDataSource } from '@angular/material/table';
import { MatDialog } from '@angular/material/dialog';
import Swal from 'sweetalert2';
import { ManufactureService } from '../../../../services/manufacture.service';
import { MasterService } from '../../../../services/master.service';
import { KatiReceiveModalComponent } from './modal/kati-receive-modal/kati-receive-modal.component';


@Component({
  selector: 'app-material-receive',
  templateUrl: './material-receive.component.html',
  styleUrl: './material-receive.component.css'
})
export class MaterialReceiveComponent implements OnInit {
  materialReceiveForm!: FormGroup;
  list: any[] = []; // weavers list
  weavers: any[] = []; // weavers list (alias for compatibility)
  availableIssues: any[] = []; // carpet order issues
  carpetOrderIssues: any[] = []; // all carpet order issues
  materialIssueList: any[] = []; // material issues for challan details
  materialLagatList: any[] = []; // material lagats for descriptions and lagats
  selectedIssueData: any = null;
  // Keep selected material lagat (for description ObjectIds at save time)
  selectedMaterialLagat: any = null;
  // Keep last saved Kati receive rows with original colour objects for payload
  katiReceiveDataForSave: any[] = [];

  displayedColumns: string[] = ['issueNo', 'quality', 'design', 'colour', 'size', 'pcs', 'area'];
  dataSource = new MatTableDataSource<any>([]);
  isEditMode: any = false;
  private editId: string | null = null;

  // Kati challan data table (view like Material Issue) - now expanded to include Receive & Total Lagat
  katiChallanDisplayedColumns: string[] = ['srNo', 'colour', 'lagat', 'carpetLagat', 'issue', 'receive', 'totalLagat'];
  katiChallanDataSource = new MatTableDataSource<any>([]);
  get hasKatiRows(): boolean { return (this.katiChallanDataSource?.data?.length || 0) > 0; }
  totalKatiChallanLagat: number = 0;
  totalKatiChallanCarpetLagat: number = 0;
  totalKatiChallanIssued: number = 0;
  totalKatiChallanReceive: number = 0;
  totalKatiChallanTotalLagat: number = 0;

  printAfterSave: boolean = false;

  constructor(
    private router: Router,
    private route: ActivatedRoute,
    private fb: FormBuilder,
    private manufactureService: ManufactureService,
    private masterService: MasterService,
    private dialog: MatDialog
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    // Load data in proper sequence
    this.loadCarpetOrderIssues();
    this.loadMaterialIssuesAndWeavers();
    this.loadMaterialLagats();

    // Check if we're in edit mode
    this.route.queryParams.subscribe(params => {
      if (params['editMode'] === 'true' && params['editId']) {
        console.log('🔍 Edit mode detected, ID:', params['editId']);
        this.isEditMode = true;
        this.editId = params['editId'];
        this.loadEditData(params['editId']);
      } else {
        this.isEditMode = false;
        this.generateChallanNo();
      }
    });
  }

  initializeForm(): void {
    this.materialReceiveForm = this.fb.group({
      challanNo: ['', Validators.required],
      date: [new Date().toISOString().split('T')[0], Validators.required],
      weaver: ['', Validators.required],
      issueNo: ['', Validators.required],

      // Material descriptions (readonly - from issue)
      katiDescription: [''],
      tanaDescription: [''],
      sootDescription: [''],
      thariDescription: [''],
      silkDescription: [''],
      otherDescription: [''],

      // Material lagats (readonly - from issue)
      katiLagat: [''],
      tanaLagat: [''],
      sootLagat: [''],
      thariLagat: [''],
      silkLagat: [''],
      otherLagat: [''],

      // Material issues (readonly - from issue)
      katiIssued: [''],
      tanaIssued: [''],
      sootIssued: [''],
      thariIssued: [''],
      silkIssued: [''],
      otherIssued: [''],

      // Material receives (user input)
      katiReceive: [''],
      tanaReceive: [''],
      sootReceive: [''],
      thariReceive: [''],
      silkReceive: [''],
      otherReceive: [''],

      // Material TLagat (calculated)
      katiTLagat: [''],
      tanaTLagat: [''],
      sootTLagat: [''],
      thariTLagat: [''],
      silkTLagat: [''],
      otherTLagat: ['']
    });

    // Add value change listeners for receive fields to calculate TLagat
    this.setupTLagatCalculations();
  }

  setupTLagatCalculations(): void {
    const materialTypes = ['kati', 'tana', 'soot', 'thari', 'silk', 'other'];

    const to3 = (val: any) => {
      const n = parseFloat(val);
      return isNaN(n) ? '0.000' : n.toFixed(3);
    };

    materialTypes.forEach(type => {
      this.materialReceiveForm.get(`${type}Receive`)?.valueChanges.subscribe(receiveValue => {
        // Use the correct Issued fields from the form
        const issueControl = this.materialReceiveForm.get(`${type}Issued`);
        const issueValue = parseFloat(issueControl?.value || '0') || 0;
        const recNum = parseFloat(receiveValue || '0') || 0;
        const TLagat = Math.max(0, issueValue - recNum);
        this.materialReceiveForm.patchValue({
          [`${type}TLagat`]: to3(TLagat)
        }, { emitEvent: false });
      });
    });
  }

  setDigitReceive(val: any, fieldName: string) {
    let _val = parseFloat(val.target.value);
    if (!isNaN(_val)) {
      val.target.value = _val.toFixed(3);
      this.materialReceiveForm.get(fieldName)?.patchValue(_val.toFixed(3));

      // Calculate TLagat for the corresponding material type
      this.calculateTLagat(fieldName);
    }
  }

  calculateTLagat(receiveFieldName: string) {
    // Map receive field names to their corresponding issue and TLagat field names
    const fieldMapping: { [key: string]: { issue: string, TLagat: string } } = {
      'katiReceive': { issue: 'katiIssued', TLagat: 'katiTLagat' },
      'tanaReceive': { issue: 'tanaIssued', TLagat: 'tanaTLagat' },
      'sootReceive': { issue: 'sootIssued', TLagat: 'sootTLagat' },
      'thariReceive': { issue: 'thariIssued', TLagat: 'thariTLagat' },
      'silkReceive': { issue: 'silkIssued', TLagat: 'silkTLagat' },
      'otherReceive': { issue: 'otherIssued', TLagat: 'otherTLagat' }
    };

    const mapping = fieldMapping[receiveFieldName];
    if (mapping) {
      const receiveValue = parseFloat(this.materialReceiveForm.get(receiveFieldName)?.value || '0');

      // Only calculate TLagat if receive value is entered (greater than 0)
      if (receiveValue > 0) {
        const issueValue = parseFloat(this.materialReceiveForm.get(mapping.issue)?.value || '0');
        const TLagat = Math.max(0, issueValue - receiveValue); // Ensure TLagat is not negative
        this.materialReceiveForm.get(mapping.TLagat)?.patchValue(this.formatToThreeDecimals(TLagat));

        console.log(`✅ Calculated TLagat for ${receiveFieldName}:`, {
          issue: issueValue,
          receive: receiveValue,
          TLagat: TLagat
        });
      } else {
        // If no receive value, clear the TLagat field
        this.materialReceiveForm.get(mapping.TLagat)?.patchValue('');
      }
    }
  }

  openKatiReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('kati', 'Kati');
  }

  openTanaReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('tana', 'Tana');
  }

  openSootReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('soot', 'Soot');
  }

  openThariReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('thari', 'Thari');
  }

  openSilkReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('silk', 'Silk');
  }

  openOtherReceivedDetailsModal(): void {
    this.openMaterialReceiveModal('other', 'Other');
  }

  private openMaterialReceiveModal(materialType: string, materialName: string): void {
    console.log(`🔍 Opening ${materialName} Receive Modal...`);

    // Check if weaver and issue are selected
    const selectedWeaver = this.materialReceiveForm.get('weaver')?.value;
    const selectedIssue = this.materialReceiveForm.get('issueNo')?.value;

    if (!selectedWeaver || !selectedIssue) {
      Swal.fire('Warning', 'Please select weaver and issue number first', 'warning');
      return;
    }

    // Find the selected weaver and issue objects
    // Note: form stores weaver NAME and issueNo KEY, so match accordingly
    const weaverObj = this.list.find(w => w.name === selectedWeaver)?.weaver
      || this.weavers.find(w => w.name === selectedWeaver || w._id === selectedWeaver);
    const issueObj = this.availableIssues.find(i => i.issueNoKey === selectedIssue || i._id === selectedIssue);

    if (!weaverObj || !issueObj) {
      Swal.fire('Error', 'Selected weaver or issue not found', 'error');
      return;
    }

    // Compute area again for the selected issue (areaInYard * pcs)
    const carpetOrderIssue = issueObj;
    let areaNum = 0;
    try {
      const areaInYard = parseFloat(carpetOrderIssue?.size?.areaInYard || '0') || 0;
      const pcs = parseInt(carpetOrderIssue?.pcs || '0', 10) || 0;
      areaNum = +(areaInYard * pcs).toFixed(3);
    } catch {}

    const dialogRef = this.dialog.open(KatiReceiveModalComponent, {
      width: '90%',
      maxWidth: '1200px',
      height: '80%',
      data: {
        weaver: weaverObj,
        issueNo: issueObj,
        materialType: materialType,
        materialName: materialName,
        materialLagatList: this.materialLagatList,
        carpetOrderIssue: issueObj,
        area: areaNum,
        isEditMode: !!(this.katiReceiveDataForSave && this.katiReceiveDataForSave.length),
        existingReceiveData: this.katiReceiveDataForSave
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result && (result.totalReceive !== undefined)) {
        console.log(`✅ ${materialName} receive data received:`, result);

        // Update the corresponding receive field with total
        const receiveFieldName = `${materialType}Receive`;
        this.materialReceiveForm.patchValue({
          [receiveFieldName]: (parseFloat(result.totalReceive || '0') || 0).toFixed(3)
        });

        // Keep raw data for save (to send colour ObjectIds)
        if (materialType === 'kati') {
          this.katiReceiveDataForSave = result.materialReceiveData || [];
        }

        // Trigger TLagat calculation
        this.calculateTLagat(receiveFieldName);

        // Update Kati Data Table using returned rows from modal
        if (materialType === 'kati') {
          const rows = result.tableRows || [];
          this.katiChallanDataSource.data = rows as any;
          // Totals
          this.totalKatiChallanLagat = rows.reduce((s: number, r: any) => s + (parseFloat(r.lagat as any) || 0), 0);
          this.totalKatiChallanCarpetLagat = rows.reduce((s: number, r: any) => s + (parseFloat(r.carpetLagat as any) || 0), 0);
          this.totalKatiChallanIssued = rows.reduce((s: number, r: any) => s + (parseFloat(r.tIssued as any) || 0), 0);
          this.totalKatiChallanReceive = rows.reduce((s: number, r: any) => s + (parseFloat(r.receive as any) || 0), 0);
          this.totalKatiChallanTotalLagat = rows.reduce((s: number, r: any) => s + (parseFloat(r.totalLagat as any) || 0), 0);
        }
      }
    });
  }

  generateChallanNo(): void {
    // Always generate the next highest challan number based on existing Material Receives
    this.manufactureService.getAllMaterialReceives().subscribe(
      (value: any) => {
        let nextChallanNo = 'MR-2400001';
        if (Array.isArray(value) && value.length) {
          let maxNum = 2400000; // so first becomes 2400001
          (value as any[]).forEach((item: any) => {
            const c = item?.challanNo as string;
            if (c) {
              if (c.includes('-')) {
                const parts = c.split('-');
                const num = parseInt(parts[1]);
                if (!isNaN(num) && num > maxNum) maxNum = num;
              } else {
                // fallback: strip non-digits
                const num = parseInt(String(c).replace(/\D/g, ''));
                if (!isNaN(num) && num > maxNum) maxNum = num;
              }
            }
          });
          const nextSequentialNum = maxNum + 1;
          nextChallanNo = `MR-${nextSequentialNum}`;
          console.log('🔍 Generated next challan number:', nextChallanNo);
        } else {
          console.log('🔍 No existing material receives found, using default starting number');
        }
        this.materialReceiveForm.patchValue({ challanNo: nextChallanNo });
      },
      (err) => {
        console.error('Error fetching material receives for challan number:', err);
        // fallback
        this.materialReceiveForm.patchValue({ challanNo: 'MR-2400001' });
      }
    );
  }

  loadMaterialIssuesAndWeavers(): void {
    console.log('🚀 Starting loadMaterialIssuesAndWeavers...');

    // First load material issues
    this.manufactureService.getAllMaterialIssues().subscribe({
      next: (materialIssues: any) => {
        console.log('🔍 Material issues loaded:', materialIssues?.length || 0);
        this.materialIssueList = materialIssues || [];

        // Then load all weavers and filter them
        this.masterService.getAllWeaverEmployee().subscribe({
          next: (allWeavers: any) => {
            console.log('🔍 All weavers loaded:', allWeavers?.length || 0);
            this.filterWeaversBasedOnMaterialIssues(allWeavers || []);
          },
          error: (error: any) => {
            console.error('❌ Error loading weavers:', error);
          }
        });
      },
      error: (error: any) => {
        console.error('❌ Error loading material issues:', error);
      }
    });
  }

  navigateToMaterialIssue(): void {
    console.log('Navigating to Material Issue Component');
    this.router.navigate(['/admin/material-issue']);
  }

  loadWeavers(): void {
    // First load all weavers, then filter based on material issues
    this.masterService.getAllWeaverEmployee().subscribe({
      next: (response: any) => {
        console.log('🔍 All weavers loaded:', response);
        const allWeavers = response || [];

        // Filter weavers that exist in materialIssueList
        this.filterWeaversBasedOnMaterialIssues(allWeavers);
      },
      error: (error: any) => {
        console.error('❌ Error loading weavers:', error);
      }
    });
  }

  filterWeaversBasedOnMaterialIssues(allWeavers: any[]): void {
    console.log('🔍 Starting weaver filtering...');
    console.log('🔍 All weavers count:', allWeavers.length);
    console.log('🔍 Material issues count:', this.materialIssueList.length);
    console.log('🔍 Carpet order issues count:', this.carpetOrderIssues.length);

    // Get unique weaver IDs from all three collections

    // 1. From material issues
    const weaverIdsInMaterialIssues = [...new Set(
      this.materialIssueList.map(issue => issue.weaver?._id || issue.weaver)
    )];

    // 2. From carpet order issues
    const weaverIdsInCarpetOrderIssues = [...new Set(
      this.carpetOrderIssues.map(issue => issue.weaver?._id || issue.weaver)
    )];

    // 3. Find intersection - weavers that exist in both collections
    const commonWeaverIds = weaverIdsInMaterialIssues.filter(id =>
      weaverIdsInCarpetOrderIssues.includes(id)
    );

    console.log('🔍 Weaver IDs in material issues:', weaverIdsInMaterialIssues);
    console.log('🔍 Weaver IDs in carpet order issues:', weaverIdsInCarpetOrderIssues);
    console.log('🔍 Common weaver IDs:', commonWeaverIds);

    // Filter weavers that exist in both collections
    const filteredWeavers = allWeavers.filter(weaver =>
      commonWeaverIds.includes(weaver._id)
    );

    this.list = filteredWeavers.map((weaver: any) => ({
      name: weaver.name,
      displayName: `${weaver.branch?.branchCode || 'K'} - ${weaver.name}`,
      weaver: weaver
    }));

    // Also populate weavers array for modal compatibility
    this.weavers = filteredWeavers;

    console.log('🔍 Filtered weavers for dropdown:', this.list.length);
  }

  loadCarpetOrderIssues(): void {
    this.manufactureService.getsOrderIssueList().subscribe({
      next: (response: any) => {
        console.log('🔍 Carpet order issues loaded:', response);
        this.carpetOrderIssues = response || [];
      },
      error: (error: any) => {
        console.error('❌ Error loading carpet order issues:', error);
      }
    });
  }



  loadEditData(editId: string): void {
    console.log('🔍 Loading edit data for ID:', editId);
    this.manufactureService.getMaterialReceiveById(editId).subscribe({
      next: (doc: any) => {
        if (!doc) return;
        // Patch basic fields
        const dateStr = doc.date ? new Date(doc.date).toISOString().slice(0,10) : new Date().toISOString().slice(0,10);
        this.materialReceiveForm.patchValue({
          challanNo: doc.challanNo,
          date: dateStr,
          weaver: (doc.weaver?.name || doc.weaver || ''),
          issueNo: (doc.issueNo?._id || doc.issueNo || ''),
          // issues
          katiIssued: this.formatToThreeDecimals(doc.materials?.kati?.issue),
          tanaIssued: this.formatToThreeDecimals(doc.materials?.tana?.issue),
          sootIssued: this.formatToThreeDecimals(doc.materials?.soot?.issue),
          thariIssued: this.formatToThreeDecimals(doc.materials?.thari?.issue),
          silkIssued: this.formatToThreeDecimals(doc.materials?.silk?.issue),
          otherIssued: this.formatToThreeDecimals(doc.materials?.other?.issue),
          // receives
          katiReceive: this.formatToThreeDecimals(doc.materials?.kati?.receive),
          tanaReceive: this.formatToThreeDecimals(doc.materials?.tana?.receive),
          sootReceive: this.formatToThreeDecimals(doc.materials?.soot?.receive),
          thariReceive: this.formatToThreeDecimals(doc.materials?.thari?.receive),
          silkReceive: this.formatToThreeDecimals(doc.materials?.silk?.receive),
          otherReceive: this.formatToThreeDecimals(doc.materials?.other?.receive),
          // TLagat
          katiTLagat: this.formatToThreeDecimals(doc.materials?.kati?.tLagat),
          tanaTLagat: this.formatToThreeDecimals(doc.materials?.tana?.tLagat),
          sootTLagat: this.formatToThreeDecimals(doc.materials?.soot?.tLagat),
          thariTLagat: this.formatToThreeDecimals(doc.materials?.thari?.tLagat),
          silkTLagat: this.formatToThreeDecimals(doc.materials?.silk?.tLagat),
          otherTLagat: this.formatToThreeDecimals(doc.materials?.other?.tLagat),
        });

        // Prefill Kati table for view
        const rows = (doc.katiData || []).map((r: any, idx: number) => ({
          srNo: idx + 1,
          colourText: r?.colour?.newColor ? `${r.colour?.newColor || ''} - ${r.colour?.companyColorCode || ''} - ${r.colour?.remark || ''}` : (r?.colour?.name || r?.colour || ''),
          lagat: parseFloat(r.lagat || '0') || 0,
          carpetLagat: parseFloat(r.carpetLagat || '0') || 0,
          tIssued: parseFloat(r.tIssued || '0') || 0,
          receive: parseFloat(r.receiveValue || '0') || 0,
          totalLagat: parseFloat(r.tLagat || '0') || 0,
        }));
        // Do not render Kati table in edit mode on receive screen
        this.katiChallanDataSource.data = [];
        this.totalKatiChallanLagat = 0;
        this.totalKatiChallanCarpetLagat = 0;
        this.totalKatiChallanIssued = 0;
        this.totalKatiChallanReceive = 0;
        this.totalKatiChallanTotalLagat = 0;

        // Keep raw data for save payload reuse
        this.katiReceiveDataForSave = (doc.katiData || []).map((r: any) => ({
          colour: r.colour,
          lagat: r.lagat,
          carpetLagat: r.carpetLagat,
          tIssued: r.tIssued,
          receiveValue: r.receiveValue,
          tLagat: r.tLagat
        }));

        // Try to set selectedIssueData so print routing works
        const issueId = (doc.issueNo?._id || doc.issueNo);
        const matchIssue = this.carpetOrderIssues.find((x:any) => x._id === issueId);
        if (matchIssue) {
          this.selectedIssueData = {
            issueNoKey: matchIssue._id,
            issueNoDisplay: matchIssue.Br_issueNo,
            challanDetails: [],
            carpetOrderIssue: matchIssue
          };
          // Also populate descriptions/lagats for header
          const areaInYard = parseFloat(matchIssue?.size?.areaInYard || '0') || 0;
          const pcs = parseInt(matchIssue?.pcs || '0', 10) || 0;
          const areaNum = +(areaInYard * pcs).toFixed(3);
          this.dataSource.data = [{
            issueNo: matchIssue.Br_issueNo,
            quality: this.getDisplayText(matchIssue?.quality?.quality),
            design: this.getDisplayText(matchIssue?.design?.design),
            colour: this.getDisplayText(matchIssue?.borderColour),
            size: this.getDisplayText(matchIssue?.size?.sizeInYard),
            pcs: matchIssue?.pcs || 0,
            area: `${areaNum.toFixed(2)} Yd`,
            calculatedArea: areaNum
          }];
          this.populateMaterialDescriptionsAndLagats(matchIssue, areaNum);
        }
      },
      error: (err) => {
        console.error('❌ Failed to load material receive for edit:', err);
      }
    });
  }

  populateFormWithEditData(data: any): void {
    console.log('🔍 Populating form with edit data:', data);
    // Implementation for edit mode
  }

  onWeaverChange(): void {
    const selectedWeaverName = this.materialReceiveForm.get('weaver')?.value;
    console.log('🔍 Weaver selected:', selectedWeaverName);

    if (selectedWeaverName) {
      // Get selected weaver object
      const selectedWeaver = this.list.find(w => w.name === selectedWeaverName)?.weaver;
      const selectedWeaverId = selectedWeaver?._id;

      console.log('🔍 Selected weaver ID:', selectedWeaverId);

      // Filter material issues for this weaver
      const weaverMaterialIssues = this.materialIssueList.filter((materialIssue: any) => {
        const materialWeaverId = typeof materialIssue.weaver === 'object' ?
          materialIssue.weaver._id : materialIssue.weaver;
        return materialWeaverId === selectedWeaverId;
      });

      console.log('🔍 Weaver material issues found:', weaverMaterialIssues);

      // Get unique Br_issueNo from material issues for this weaver
      const weaverIssueNumbers = [...new Set(
        weaverMaterialIssues
          .filter(mi => mi.issueNo && mi.issueNo.Br_issueNo)
          .map(mi => mi.issueNo.Br_issueNo)
      )];

      console.log('🔍 Weaver issue numbers from material issues:', weaverIssueNumbers);

      // Filter carpet order issues that match both weaver and have material issued
      this.availableIssues = this.carpetOrderIssues.filter((issue: any) => {
        const issueWeaverId = typeof issue.weaver === 'object' ? issue.weaver._id : issue.weaver;
        const matchesWeaver = issueWeaverId === selectedWeaverId;
        const hasMaterialIssued = weaverIssueNumbers.includes(issue.Br_issueNo);

        return matchesWeaver && hasMaterialIssued;
      }).map((issue: any) => {
        // Add challan count and other details
        const challanDetails = weaverMaterialIssues.filter(mi =>
          mi.issueNo && mi.issueNo.Br_issueNo === issue.Br_issueNo
        );

        return {
          ...issue,
          issueNoKey: issue._id,
          issueNoDisplay: issue.Br_issueNo,
          challanCount: challanDetails.length,
          challanDetails: challanDetails,
          carpetOrderIssue: issue
        };
      });

      console.log('✅ Available issues for weaver:', this.availableIssues);

      // Clear issue selection
      this.materialReceiveForm.patchValue({ issueNo: '' });
      this.selectedIssueData = null;
      this.dataSource.data = [];
    }
  }

  onIssueChange(): void {
    const selectedIssueNoKey = this.materialReceiveForm.get('issueNo')?.value;
    console.log('🔍 Issue selected:', selectedIssueNoKey);

    if (selectedIssueNoKey) {
      // Find the selected issue data using the key
      this.selectedIssueData = this.availableIssues.find((issue: any) =>
        issue.issueNoKey === selectedIssueNoKey
      );

      if (this.selectedIssueData) {
        console.log('✅ Selected issue data:', this.selectedIssueData);
        this.populateIssueData();
      }
    }
  }

  populateIssueData(): void {
    if (!this.selectedIssueData) return;

    const carpetOrderIssue = this.selectedIssueData.carpetOrderIssue;

    // Calculate area = areaInYard * pcs (from size object)
    let calculatedArea = 0;
    if (carpetOrderIssue.size && carpetOrderIssue.size.areaInYard && carpetOrderIssue.pcs) {
      calculatedArea = parseFloat(carpetOrderIssue.size.areaInYard) * parseInt(carpetOrderIssue.pcs);
    }

    // Format area display: "5.00 Yd "

    const areaDisplay = `${calculatedArea.toFixed(2)} Yd`;

    // Populate issue details table
    const issueDetails = [{
      issueNo: this.selectedIssueData.issueNoDisplay,
      quality: this.getDisplayText(carpetOrderIssue?.quality?.quality),
      design: this.getDisplayText(carpetOrderIssue?.design?.design),
      colour: this.getDisplayText(carpetOrderIssue?.borderColour),
      size: this.getDisplayText(carpetOrderIssue?.size?.sizeInYard),
      pcs: carpetOrderIssue?.pcs || 0,
      area: areaDisplay,
      calculatedArea: calculatedArea // Store for lagat calculations
    }];

    this.dataSource.data = issueDetails;

    // Populate material descriptions and lagats from materialLagat collection
    this.populateMaterialDescriptionsAndLagats(carpetOrderIssue, calculatedArea);

    // Populate material data from challan details
    if (this.selectedIssueData.challanDetails && this.selectedIssueData.challanDetails.length > 0) {
      // Aggregate material data for form totals
      const aggregatedMaterials = {
        kati: { description: '', lagat: 0, issue: 0 },
        tana: { description: '', lagat: 0, issue: 0 },
        soot: { description: '', lagat: 0, issue: 0 },
        thari: { description: '', lagat: 0, issue: 0 },
        silk: { description: '', lagat: 0, issue: 0 },
        other: { description: '', lagat: 0, issue: 0 }
      };

      this.selectedIssueData.challanDetails.forEach((challan: any) => {
        if (challan.materials) {
          const materialTypes = ['kati', 'tana', 'soot', 'thari', 'silk', 'other'];
          materialTypes.forEach(type => {
            if (challan.materials[type]) {
              const issueValue = parseFloat(challan.materials[type].issue || '0');
              (aggregatedMaterials as any)[type].issue += issueValue;

              if (!(aggregatedMaterials as any)[type].description && challan.materials[type].description) {
                (aggregatedMaterials as any)[type].description = this.getDisplayText(challan.materials[type].description);
              }
              if (!(aggregatedMaterials as any)[type].lagat && challan.materials[type].lagat) {
                (aggregatedMaterials as any)[type].lagat = parseFloat(challan.materials[type].lagat || '0');
              }
            }
          });
        }
      });

      // Populate form with aggregated material data (only issues and TLagat, not descriptions/lagats)
      this.materialReceiveForm.patchValue({

        // Issues (with .000 format)
        katiIssued: this.formatToThreeDecimals(aggregatedMaterials.kati.issue),
        tanaIssued: this.formatToThreeDecimals(aggregatedMaterials.tana.issue),
        sootIssued: this.formatToThreeDecimals(aggregatedMaterials.soot.issue),
        thariIssued: this.formatToThreeDecimals(aggregatedMaterials.thari.issue),
        silkIssued: this.formatToThreeDecimals(aggregatedMaterials.silk.issue),
        otherIssued: this.formatToThreeDecimals(aggregatedMaterials.other.issue),


        // TLagat (initially empty, calculated when receive is entered)
        katiTLagat: '',
        tanaTLagat: '',
        sootTLagat: '',
        thariTLagat: '',
        silkTLagat: '',
        otherTLagat: ''
      });

      // Do not prepare Kati challan data table on receive screen
      this.katiChallanDataSource.data = [];
      this.totalKatiChallanLagat = 0;
      this.totalKatiChallanCarpetLagat = 0;
      this.totalKatiChallanIssued = 0;
    }
  }

  private prepareKatiChallanDataTable(): void {
    if (!this.selectedIssueData || !this.selectedIssueData.challanDetails) {
      this.katiChallanDataSource.data = [];
      this.totalKatiChallanLagat = 0;
      this.totalKatiChallanCarpetLagat = 0;
      this.totalKatiChallanIssued = 0;
      return;
    }

    const areaRow = this.dataSource.data[0];
    const area = areaRow?.calculatedArea || 0;

    const rowsMap = new Map<string, any>();

    this.selectedIssueData.challanDetails.forEach((challan: any) => {
      if (Array.isArray(challan.katiData)) {
        challan.katiData.forEach((r: any) => {
          const colour = r.colour || {};
          const colourId = colour._id || colour.id || colour;
          const colourText = colour.newColor
            ? `${colour.newColor || ''} - ${colour.companyColorCode || ''} - ${colour.remark || ''}`
            : (colour.name || '');
          const lagat = parseFloat(r.lagat || '0') || 0;
          const issued = parseFloat(r.toIssueValue || r.issueValue || '0') || 0;

          const key = String(colourId);
          if (!rowsMap.has(key)) {
            rowsMap.set(key, {
              colourText,
              lagat,
              carpetLagat: +(lagat * area).toFixed(3),
              tIssued: 0
            });
          }
          const row = rowsMap.get(key);
          row.tIssued += issued;
        });
      }
    });

    const rows = Array.from(rowsMap.values()).map((r: any, idx: number) => ({
      srNo: idx + 1,
      colourText: r.colourText,
      lagat: r.lagat,
      carpetLagat: r.carpetLagat,
      tIssued: +(r.tIssued.toFixed(3))
    }));

    this.katiChallanDataSource.data = rows as any;
    this.totalKatiChallanLagat = rows.reduce((s: number, r: any) => s + (parseFloat(r.lagat as any) || 0), 0);
    this.totalKatiChallanCarpetLagat = rows.reduce((s: number, r: any) => s + (parseFloat(r.carpetLagat as any) || 0), 0);
    this.totalKatiChallanIssued = rows.reduce((s: number, r: any) => s + (parseFloat(r.tIssued as any) || 0), 0);
  }

  formatToThreeDecimals(value: any): string {
    if (!value || value === '' || value === null || value === undefined) {
      return '0.000';
    }
    const numValue = parseFloat(value.toString());
    if (isNaN(numValue)) {
      return '0.000';
    }
    return numValue.toFixed(3);
  }

  calculateLagat(lagat: number, area: number): string {
    if (!lagat || !area) return '0.000';
    const calculatedLagat = lagat * area;
    return calculatedLagat.toFixed(3);
  }

  getDisplayText(field: any): string {
    if (!field) return '';
    if (typeof field === 'string') return field;
    if (typeof field === 'object') {
      // For size object, show sizeInYard
      if (field.sizeInYard) {
        return field.sizeInYard;
      }
      return field.name || field.title || field.Description || field.description || '';
    }
    return '';
  }

  onSave(): void {
    if (this.materialReceiveForm.valid) {
      const formData = this.materialReceiveForm.value;
      console.log('💾 Saving material receive data:', formData);

      // Validate receive quantities don't exceed issue quantities
      const materialTypes = ['kati', 'tana', 'soot', 'thari', 'silk', 'other'];
      let validationError = false;

      for (const type of materialTypes) {
        // Use the correct field names (Issued) and coerce to numbers safely
        const issueValue = parseFloat(formData[`${type}Issued`]) || 0;
        const receiveValue = parseFloat(formData[`${type}Receive`]) || 0;

        if (receiveValue > issueValue) {
          Swal.fire({
            title: 'Validation Error!',
            text: `${type.charAt(0).toUpperCase() + type.slice(1)} receive quantity cannot exceed issue quantity.`,
            icon: 'warning',
            confirmButtonText: 'OK'
          });
          validationError = true;
          break;
        }
      }

      if (!validationError) {
        // Prepare payload matching backend MaterialReceive model
        const payload = this.buildMaterialReceivePayload(formData);
        console.log('Prepared payload for save:', payload);

        // Call backend API to save Material Receive
        const save$ = this.isEditMode && this.editId
          ? this.manufactureService.updateMaterialReceive(this.editId, payload)
          : this.manufactureService.addMaterialReceive(payload);

        save$.subscribe({
          next: (response: any) => {
            console.log('✅ Material Receive saved:', response);
            Swal.fire({
              title: 'Success!',
              text: `Material Receive saved successfully!\nChallan No: ${formData.challanNo}`,
              icon: 'success',
              confirmButtonText: 'OK'
            }).then(() => {
              if (this.printAfterSave) {
                this.printAfterSave = false;
                // Navigate to print with the selected issue id
                let issueId: string | null = this.selectedIssueData?.carpetOrderIssue?._id || null;
                if (!issueId) {
                  const selectedKey = this.materialReceiveForm.get('issueNo')?.value;
                  const match = this.availableIssues.find((i: any) => i.issueNoKey === selectedKey);
                  issueId = match?._id || null;
                }
                if (issueId) {
                  this.router.navigate([`/admin/material-receive-print/${issueId}`]);
                } else {
                  this.onClear();
                }
              } else {
                this.onClear();
              }
            });
          },
          error: (error: any) => {
            console.error('❌ Error saving Material Receive:', error);
            Swal.fire('Error', error?.error?.message || 'Failed to save Material Receive', 'error');
          }
        });
      }
    } else {
      this.showValidationErrors();
    }
  }

  onClear(): void {
    this.materialReceiveForm.reset();
    this.initializeForm();
    this.selectedIssueData = null;
    this.dataSource.data = [];
    this.generateChallanNo();
  }

  private buildMaterialReceivePayload(formData: any) {
    // Resolve ObjectIds for weaver and issue from selectedIssueData and list
    const weaverObj = this.list.find(w => w.name === formData.weaver)?.weaver || this.weavers.find((w: any) => w.name === formData.weaver || w._id === formData.weaver);
    const issueObj = this.availableIssues.find(i => i.issueNoKey === formData.issueNo || i._id === formData.issueNo);

    // Map descriptions to ObjectIds isn’t strictly required here because backend fields are optional
    const toStr = (v: any) => (v !== undefined && v !== null ? String(v) : '');

    // Build materials block matching backend model
    const materials = {
      kati: {
        description: undefined,
        lagat: toStr(formData.katiLagat),
        issue: toStr(formData.katiIssued),
        receive: toStr(formData.katiReceive),
        tLagat: toStr(formData.katiTLagat)
      },
      tana: {
        description: undefined,
        lagat: toStr(formData.tanaLagat),
        issue: toStr(formData.tanaIssued),
        receive: toStr(formData.tanaReceive),
        tLagat: toStr(formData.tanaTLagat)
      },
      soot: {
        description: undefined,
        lagat: toStr(formData.sootLagat),
        issue: toStr(formData.sootIssued),
        receive: toStr(formData.sootReceive),
        tLagat: toStr(formData.sootTLagat)
      },
      thari: {
        description: undefined,
        lagat: toStr(formData.thariLagat),
        issue: toStr(formData.thariIssued),
        receive: toStr(formData.thariReceive),
        tLagat: toStr(formData.thariTLagat)
      },
      silk: {
        description: undefined,
        lagat: toStr(formData.silkLagat),
        issue: toStr(formData.silkIssued),
        receive: toStr(formData.silkReceive),
        tLagat: toStr(formData.silkTLagat)
      },
      other: {
        description: undefined,
        lagat: toStr(formData.otherLagat),
        issue: toStr(formData.otherIssued),
        receive: toStr(formData.otherReceive),
        tLagat: toStr(formData.otherTLagat)
      },
    } as any;

    // Build Kati rows from the last saved modal payload to ensure colour ObjectIds are sent
    // Guard against empty colour objects {} which cause Cast to ObjectId errors in backend
    const katiRows = (this.katiReceiveDataForSave || [])
      .map((row: any) => {
        const colourVal = row?.colour;
        const colourId = typeof colourVal === 'object' ? (colourVal?._id || colourVal?.id || null) : colourVal;
        if (!colourId) {
          // Skip rows that don't have a valid colour id
          return null;
        }
        return {
          colour: colourId,
          lagat: toStr(row?.lagat),
          carpetLagat: toStr(row?.carpetLagat),
          tIssued: toStr(row?.tIssued),
          receiveValue: toStr(row?.receiveValue),
          tLagat: toStr(row?.tLagat)
        };
      })
      .filter((r: any) => !!r);

    // Helper to extract id from selected material lagat
    const getId = (obj: any) => (obj && (obj._id || obj.id)) || undefined;

    const areaStr = this.dataSource?.data?.[0]?.calculatedArea?.toString() || '';
    const issueAreaStr = areaStr && !/\bYd\b/i.test(areaStr) ? `${areaStr} Yd` : areaStr;
    return {
      challanNo: formData.challanNo,
      date: formData.date,
      weaver: weaverObj?._id || formData.weaver,
      issueNo: (this.selectedIssueData?.carpetOrderIssue?._id) || issueObj?._id || formData.issueNo,
      issueAreaInYard: issueAreaStr,
      materials: {
        kati: { ...materials.kati, description: getId(this.selectedMaterialLagat?.katiDescription) },
        tana: { ...materials.tana, description: getId(this.selectedMaterialLagat?.tanaDescription) },
        soot: { ...materials.soot, description: getId(this.selectedMaterialLagat?.sootDescription) },
        thari: { ...materials.thari, description: getId(this.selectedMaterialLagat?.tharriDescription) },
        silk: { ...materials.silk, description: getId(this.selectedMaterialLagat?.silkDescription) },
        other: { ...materials.other, description: getId(this.selectedMaterialLagat?.item?.[0]?.description) }
      },
      katiData: katiRows
    };
  }

  showValidationErrors(): void {
    Swal.fire({
      title: 'Validation Error!',
      text: 'Please fill all required fields correctly.',
      icon: 'warning',
      confirmButtonText: 'OK'
    });
  }

  // Save current data then open print
  saveAndPrint(): void {
    this.printAfterSave = true;
    this.onSave();
  }

  printCurrentReceive(): void {
    // Use selectedIssueData if available to get the issue id; else fallback to selected form values
    let issueId: string | null = this.selectedIssueData?.carpetOrderIssue?._id || null;
    if (!issueId) {
      const selectedKey = this.materialReceiveForm.get('issueNo')?.value;
      const match = this.availableIssues.find((i: any) => i.issueNoKey === selectedKey);
      issueId = match?._id || null;
    }
    if (issueId) {
      this.router.navigate([`/admin/material-receive-print/${issueId}`]);
    }
  }




  openChallanDetailsModal(): void {
    if (!this.selectedIssueData || !this.selectedIssueData.challanDetails) {
      console.warn('No challan details available');
      return;
    }

    // Import the ChallanModalComponent
    import('./modal/challan-modal/challan-modal.component').then(({ ChallanModalComponent }) => {
      const dialogRef = this.dialog.open(ChallanModalComponent, {
        width: '1200px',
        data: {
          issueNo: this.selectedIssueData.issueNoDisplay,
          challanDetails: this.selectedIssueData.challanDetails
        }
      });

      dialogRef.afterClosed().subscribe(() => {
        console.log('Challan modal closed');
      });
    });
  }





  loadMaterialLagats(): void {
    console.log('🚀 Loading Material Lagats...');
    this.masterService.getsMaterialLagat().subscribe({
      next: (data: any) => {
        console.log('✅ Material Lagat Data:', data);
        this.materialLagatList = data;
      },
      error: (error: any) => {
        console.error('❌ Error loading material lagats:', error);
      }
    });
  }

  findMaterialLagatByQualityDesignColor(quality: any, design: any, color: any): any {
    if (!this.materialLagatList || this.materialLagatList.length === 0) {
      return null;
    }

    return this.materialLagatList.find((lagat: any) => {
      const qualityMatch = lagat.quality?._id === quality?._id || lagat.quality?._id === quality;
      const designMatch = lagat.AddDesign?._id === design?._id || lagat.AddDesign?._id === design;
      const colorMatch = lagat.Color?._id === color?._id || lagat.Color?._id === color;

      return qualityMatch && designMatch && colorMatch;
    });
  }

  populateMaterialDescriptionsAndLagats(carpetOrderIssue: any, calculatedArea: number): void {
    console.log('🔍 Populating material descriptions and lagats...');

    // Find matching material lagat record
    const materialLagat = this.findMaterialLagatByQualityDesignColor(
      carpetOrderIssue.quality,
      carpetOrderIssue.design,
      carpetOrderIssue.borderColour
    );

    if (materialLagat) {
      console.log('✅ Found matching material lagat:', materialLagat);
      this.selectedMaterialLagat = materialLagat; // keep for payload ids

      // Populate descriptions and lagats
      this.materialReceiveForm.patchValue({
        // Descriptions (from materialLagat collection)
        katiDescription: this.getDisplayText(materialLagat.katiDescription),
        tanaDescription: this.getDisplayText(materialLagat.tanaDescription),
        sootDescription: this.getDisplayText(materialLagat.sootDescription),
        thariDescription: this.getDisplayText(materialLagat.tharriDescription),
        silkDescription: this.getDisplayText(materialLagat.silkDescription),
        otherDescription: materialLagat.item && materialLagat.item.length > 0 ?
          this.getDisplayText(materialLagat.item[0].description) : '',

        // Lagats (calculated as lagat * area)
        katiLagat: this.calculateLagat(parseFloat(materialLagat.katiLagat || '0'), calculatedArea),
        tanaLagat: this.calculateLagat(parseFloat(materialLagat.tanaLagat || '0'), calculatedArea),
        sootLagat: this.calculateLagat(parseFloat(materialLagat.sootLagat || '0'), calculatedArea),
        thariLagat: this.calculateLagat(parseFloat(materialLagat.tharriLagat || '0'), calculatedArea),
        silkLagat: this.calculateLagat(parseFloat(materialLagat.silkLagat || '0'), calculatedArea),
        otherLagat: materialLagat.item && materialLagat.item.length > 0 ?
          this.calculateLagat(parseFloat(materialLagat.item[0].lagat || '0'), calculatedArea) : '0.000'
      });


    } else {
      console.log('⚠️ No matching material lagat found for quality/design/color combination');
      this.selectedMaterialLagat = null;
    }
  }

}
