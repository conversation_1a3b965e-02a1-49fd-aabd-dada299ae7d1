<div class="container mt-5">
  <section>
    <fieldset>
      <legend><b>View Material Receives</b></legend>

      <!-- Filters -->
      <form [formGroup]="filterForm" class="mb-4">
        <div class="row">
          <div class="search-width">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Search</mat-label>
              <input matInput (keyup)="applyFilter($event)" placeholder="Search in table...">
            </mat-form-field>
          </div>

          <div class="col-md-3">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Weaver</mat-label>
              <mat-select formControlName="weaver">
                <mat-option value="">All Weavers</mat-option>
                <mat-option *ngFor="let w of weaverList" [value]="w.name">{{ w.name }}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="issues-width">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>Issue No</mat-label>
              <mat-select formControlName="issueNo">
                <mat-option value="">All Issues</mat-option>
                <mat-option *ngFor="let i of issueList" [value]="i.Br_issueNo">{{ i.Br_issueNo }}</mat-option>
              </mat-select>
            </mat-form-field>
          </div>

          <div class="fromDate-width">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>From Date</mat-label>
              <input matInput [matDatepicker]="fromPicker" formControlName="fromDate">
              <mat-datepicker-toggle matIconSuffix [for]="fromPicker"></mat-datepicker-toggle>
              <mat-datepicker #fromPicker></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="toDate-width">
            <mat-form-field appearance="outline" class="w-100">
              <mat-label>To Date</mat-label>
              <input matInput [matDatepicker]="toPicker" formControlName="toDate">
              <mat-datepicker-toggle matIconSuffix [for]="toPicker"></mat-datepicker-toggle>
              <mat-datepicker #toPicker></mat-datepicker>
            </mat-form-field>
          </div>

          <div class="clearB-width ">
            <button mat-raised-button color="warn" type="button" class="mt-2" (click)="clearFilters()">Clear</button>
          </div>
        </div>
      </form>

      <!-- Table -->
 
        <div class="col-md-12 scroll-container">
          <table mat-table [dataSource]="dataSource" matSort class="mat-elevation-z8" style="width:max-content;">
            <!-- Columns -->
            <ng-container matColumnDef="srNo">
              <th mat-header-cell *matHeaderCellDef>Sr.No</th>
              <td mat-cell *matCellDef="let e">{{ e.srNo }}</td>
            </ng-container>

         

            <ng-container matColumnDef="issueNo">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Issue No</th>
              <td mat-cell *matCellDef="let e">{{ e.issueNo }}</td>
            </ng-container>

               <ng-container matColumnDef="weaver">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Weaver</th>
              <td mat-cell *matCellDef="let e">{{ e.weaver }}</td>
            </ng-container>

            <ng-container matColumnDef="challanNo">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Challan No</th>
              <td mat-cell *matCellDef="let e">{{ e.challanNo }}</td>
            </ng-container>

            <ng-container matColumnDef="date">
              <th mat-header-cell *matHeaderCellDef mat-sort-header>Date</th>
              <td mat-cell *matCellDef="let e">{{ e.date }}</td>
            </ng-container>

            <ng-container matColumnDef="quality">
              <th mat-header-cell *matHeaderCellDef>Quality</th>
              <td mat-cell *matCellDef="let e">{{ e.quality }}</td>
            </ng-container>

            <ng-container matColumnDef="design">
              <th mat-header-cell *matHeaderCellDef>Design</th>
              <td mat-cell *matCellDef="let e">{{ e.design }}</td>
            </ng-container>

            <ng-container matColumnDef="colour">
              <th mat-header-cell *matHeaderCellDef>Colour</th>
              <td mat-cell *matCellDef="let e">{{ e.colour }}</td>
            </ng-container>

            <ng-container matColumnDef="size">
              <th mat-header-cell *matHeaderCellDef>Size</th>
              <td mat-cell *matCellDef="let e">{{ e.size }}</td>
            </ng-container>

            <ng-container matColumnDef="pcs">
              <th mat-header-cell *matHeaderCellDef>Pcs</th>
              <td mat-cell *matCellDef="let e">{{ e.pcs }}</td>
            </ng-container>

            <ng-container matColumnDef="area">
              <th mat-header-cell *matHeaderCellDef>Area</th>
              <td mat-cell *matCellDef="let e">{{ e.area }}</td>
            </ng-container>

            <ng-container matColumnDef="kati">
              <th mat-header-cell *matHeaderCellDef>Kati Received</th>
              <td mat-cell *matCellDef="let e">{{ e.kati }}</td>
            </ng-container>

            <ng-container matColumnDef="tana">
              <th mat-header-cell *matHeaderCellDef>Tana Received</th>
              <td mat-cell *matCellDef="let e">{{ e.tana }}</td>
            </ng-container>

            <ng-container matColumnDef="soot">
              <th mat-header-cell *matHeaderCellDef>Soot Received</th>
              <td mat-cell *matCellDef="let e">{{ e.soot }}</td>
            </ng-container>

            <ng-container matColumnDef="thari">
              <th mat-header-cell *matHeaderCellDef>Tharri Received</th>
              <td mat-cell *matCellDef="let e">{{ e.thari }}</td>
            </ng-container>

            <ng-container matColumnDef="silk">
              <th mat-header-cell *matHeaderCellDef>Silk Received</th>
              <td mat-cell *matCellDef="let e">{{ e.silk }}</td>
            </ng-container>

            <ng-container matColumnDef="other">
              <th mat-header-cell *matHeaderCellDef>Other Received</th>
              <td mat-cell *matCellDef="let e">{{ e.other }}</td>
            </ng-container>

            <!-- Actions Column -->
            <ng-container matColumnDef="actions">
              <th mat-header-cell *matHeaderCellDef>Action</th>
              <td mat-cell *matCellDef="let e">
                <button mat-icon-button color="primary" matTooltip="Edit" (click)="onEdit(e)"><mat-icon>edit</mat-icon></button>
                <button mat-icon-button color="primary" matTooltip="Print" (click)="onPrint(e)"><mat-icon>print</mat-icon></button>
                <button mat-icon-button color="warn" matTooltip="Delete" (click)="onDelete(e)"><mat-icon>delete</mat-icon></button>
              </td>
            </ng-container>

            <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
            <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>

            <tr class="mat-row" *matNoDataRow>
              <td class="mat-cell" colspan="17" style="text-align:center; padding:20px;">
                No data matching the filter
              </td>
            </tr>
          </table>
        </div>
    
      <mat-paginator [pageSizeOptions]="[10,25,50,100]" aria-label="Select page of material receives"></mat-paginator>
    </fieldset>
  </section>
</div>

