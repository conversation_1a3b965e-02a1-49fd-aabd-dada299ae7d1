const materialReceiveService = require('../../services/manifacturing/materialReceive-service');

const createMaterialReceive = async (req, res) => {
  try {
    const payload = req.body;
    const created = await materialReceiveService.createMaterialReceive(payload);
    res.status(201).json(created);
  } catch (error) {
    console.error('Controller: Error creating material receive:', error.message);
    res.status(400).json({ success: false, message: error.message, error: error.message });
  }
};

const getAllMaterialReceives = async (req, res) => {
  try {
    const items = await materialReceiveService.getAllMaterialReceives();
    res.status(200).json(items);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message, error: error.message }); 
  }
};

const getMaterialReceiveById = async (req, res) => {
  try {
    const item = await materialReceiveService.getMaterialReceiveById(req.params.id);
    if (!item) return res.status(404).json({ success: false, message: 'Not found' });
    res.status(200).json(item);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message, error: error.message });
  }
};

const updateMaterialReceive = async (req, res) => {
  try {
    const updated = await materialReceiveService.updateMaterialReceive(req.params.id, req.body);
    res.status(200).json(updated);
  } catch (error) {
    res.status(400).json({ success: false, message: error.message, error: error.message });
  }
};

const deleteMaterialReceive = async (req, res) => {
  try {
    const deleted = await materialReceiveService.deleteMaterialReceive(req.params.id);
    res.status(200).json(deleted);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message, error: error.message });
  }
};

const getMaterialReceivesByChallanNo = async (req, res) => {
  try {
    const doc = await materialReceiveService.getMaterialReceivesByChallanNo(req.params.challanNo);
    res.status(200).json(doc);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message, error: error.message });
  }
};

const getMaterialReceivesByWeaver = async (req, res) => {
  try {
    const docs = await materialReceiveService.getMaterialReceivesByWeaver(req.params.weaver);
    res.status(200).json(docs);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message, error: error.message });
  }
};

const getMaterialReceivesByIssueNo = async (req, res) => {
  try {
    const docs = await materialReceiveService.getMaterialReceivesByIssueNo(req.params.issueNoId);
    res.status(200).json(docs);
  } catch (error) {
    res.status(500).json({ success: false, message: error.message, error: error.message });
  }
};

module.exports = {
  createMaterialReceive,
  getAllMaterialReceives,
  getMaterialReceiveById,
  updateMaterialReceive,
  deleteMaterialReceive,
  getMaterialReceivesByChallanNo,
  getMaterialReceivesByWeaver,
  getMaterialReceivesByIssueNo,
};

