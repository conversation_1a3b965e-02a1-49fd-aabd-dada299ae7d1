import{H as wl,L as Ht,M as bl,S as yl,a as $t,b as io,c as gl,k as zt,p as no,s as vl}from"./chunk-UCMEJ5RL.js";import{$ as te,$b as G,A as Re,Aa as _e,Ac as al,Bc as yt,C as Vt,Ca as La,Cc as ll,D as Ke,Da as Cn,Db as Ja,Dc as dl,Ea as Va,Eb as bi,Ec as se,F as zr,Fa as gt,Fc as cl,G as jt,Ga as ja,Gb as De,Gc as xi,H as Sa,Ha as Yr,Hc as ul,Ia as Ut,Ib as We,Ic as hl,Ja as Bt,Jb as Kr,Jc as _i,K as mt,Ka as Ua,Kb as Oe,Kc as Rn,L as Qe,La as Ba,M as Hr,Ma as $a,N as Da,Na as za,Nb as Ye,O as Ia,Oa as Ha,Ob as it,<PERSON> as qa,Pb as re,Pc as fl,<PERSON> as Ra,<PERSON>a as Ga,Qb as oe,<PERSON> as vt,Rb as Qr,<PERSON> as Ta,Sb as el,T as ye,Tb as tl,U as Je,Ua as Wa,Vc as Tn,W as K,Wa as gi,Wc as ml,X as N,Xa as ne,Y as et,Ya as v,Yc as kn,Z as xe,Za as Ya,Zc as pl,_ as D,_c as On,a as vn,aa as ka,ac as yi,b as xa,ba as _,bb as Xa,bd as xl,c as wn,ca as qr,cb as vi,cd as _l,d as jr,da as M,e as Ur,ea as y,eb as V,f as X,fa as Gr,fb as Za,fd as ro,g as Me,ga as Oa,gb as Ka,gd as Cl,ha as mi,hb as Q,hc as il,ia as j,ib as Xr,ic as En,id as El,j as Le,ja as pi,jb as Qa,jc as nl,k as Z,ka as ie,kb as Zr,kc as An,l as x,la as T,lb as wi,lc as Mn,m as fi,ma as yn,mb as me,mc as Jr,n as _a,na as Fa,nb as ke,nc as Sn,o as Ca,oa as pt,ob as tt,oc as eo,pa as Ve,pc as to,q as F,qa as Te,qb as Se,qc as Dn,r as bn,ra as Pa,rc as In,s as be,sa as Na,t as Br,tb as pe,tc as rl,u as Ea,uc as wt,v as $r,va as je,w as Aa,wa as xn,wc as ol,xa as _n,xb as de,xc as sl,ya as Wr,yb as ce,yc as bt,z as Ma,za as fe,zb as Ge}from"./chunk-TSVGDZRC.js";import{a as w,b as z,d as rf}from"./chunk-CWTPBX7D.js";var Dv=rf((He,ys)=>{"use strict";(function(t,i){typeof He=="object"&&typeof ys<"u"?ys.exports=i():typeof define=="function"&&define.amd?define(i):(t=typeof globalThis<"u"?globalThis:t||self,t.Sweetalert2=i())})(He,function(){"use strict";function t(o,s,l){if(typeof o=="function"?o===s:o.has(s))return arguments.length<3?s:l;throw new TypeError("Private element is not present on this object")}function i(o,s){if(s.has(o))throw new TypeError("Cannot initialize the same private elements twice on an object")}function e(o,s){return o.get(t(o,s))}function n(o,s,l){i(o,s),s.set(o,l)}function r(o,s,l){return o.set(t(o,s),l),l}let a=100,d={},h=()=>{d.previousActiveElement instanceof HTMLElement?(d.previousActiveElement.focus(),d.previousActiveElement=null):document.body&&document.body.focus()},u=o=>new Promise(s=>{if(!o)return s();let l=window.scrollX,c=window.scrollY;d.restoreFocusTimeout=setTimeout(()=>{h(),s()},a),window.scrollTo(l,c)}),p="swal2-",f=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","default-outline","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error","draggable","dragging"].reduce((o,s)=>(o[s]=p+s,o),{}),B=["success","warning","info","question","error"].reduce((o,s)=>(o[s]=p+s,o),{}),ae="SweetAlert2:",S=o=>o.charAt(0).toUpperCase()+o.slice(1),E=o=>{console.warn(`${ae} ${typeof o=="object"?o.join(" "):o}`)},$=o=>{console.error(`${ae} ${o}`)},oi=[],J=o=>{oi.includes(o)||(oi.push(o),E(o))},St=function(o){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:null;J(`"${o}" is deprecated and will be removed in the next major release.${s?` Use "${s}" instead.`:""}`)},Dt=o=>typeof o=="function"?o():o,si=o=>o&&typeof o.toPromise=="function",ai=o=>si(o)?o.toPromise():Promise.resolve(o),xr=o=>o&&Promise.resolve(o)===o,le=()=>document.body.querySelector(`.${f.container}`),li=o=>{let s=le();return s?s.querySelector(o):null},ge=o=>li(`.${o}`),O=()=>ge(f.popup),It=()=>ge(f.icon),Sc=()=>ge(f["icon-content"]),xs=()=>ge(f.title),_r=()=>ge(f["html-container"]),_s=()=>ge(f.image),Cr=()=>ge(f["progress-steps"]),nn=()=>ge(f["validation-message"]),Pe=()=>li(`.${f.actions} .${f.confirm}`),Rt=()=>li(`.${f.actions} .${f.cancel}`),ct=()=>li(`.${f.actions} .${f.deny}`),Dc=()=>ge(f["input-label"]),Tt=()=>li(`.${f.loader}`),di=()=>ge(f.actions),Cs=()=>ge(f.footer),rn=()=>ge(f["timer-progress-bar"]),Er=()=>ge(f.close),Ic=`
  a[href],
  area[href],
  input:not([disabled]),
  select:not([disabled]),
  textarea:not([disabled]),
  button:not([disabled]),
  iframe,
  object,
  embed,
  [tabindex="0"],
  [contenteditable],
  audio[controls],
  video[controls],
  summary
`,Ar=()=>{let o=O();if(!o)return[];let s=o.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),l=Array.from(s).sort((g,A)=>{let R=parseInt(g.getAttribute("tabindex")||"0"),U=parseInt(A.getAttribute("tabindex")||"0");return R>U?1:R<U?-1:0}),c=o.querySelectorAll(Ic),m=Array.from(c).filter(g=>g.getAttribute("tabindex")!=="-1");return[...new Set(l.concat(m))].filter(g=>he(g))},Mr=()=>qe(document.body,f.shown)&&!qe(document.body,f["toast-shown"])&&!qe(document.body,f["no-backdrop"]),on=()=>{let o=O();return o?qe(o,f.toast):!1},Rc=()=>{let o=O();return o?o.hasAttribute("data-loading"):!1},ve=(o,s)=>{if(o.textContent="",s){let c=new DOMParser().parseFromString(s,"text/html"),m=c.querySelector("head");m&&Array.from(m.childNodes).forEach(A=>{o.appendChild(A)});let g=c.querySelector("body");g&&Array.from(g.childNodes).forEach(A=>{A instanceof HTMLVideoElement||A instanceof HTMLAudioElement?o.appendChild(A.cloneNode(!0)):o.appendChild(A)})}},qe=(o,s)=>{if(!s)return!1;let l=s.split(/\s+/);for(let c=0;c<l.length;c++)if(!o.classList.contains(l[c]))return!1;return!0},Tc=(o,s)=>{Array.from(o.classList).forEach(l=>{!Object.values(f).includes(l)&&!Object.values(B).includes(l)&&!Object.values(s.showClass||{}).includes(l)&&o.classList.remove(l)})},we=(o,s,l)=>{if(Tc(o,s),!s.customClass)return;let c=s.customClass[l];if(c){if(typeof c!="string"&&!c.forEach){E(`Invalid type of customClass.${l}! Expected string or iterable object, got "${typeof c}"`);return}I(o,c)}},sn=(o,s)=>{if(!s)return null;switch(s){case"select":case"textarea":case"file":return o.querySelector(`.${f.popup} > .${f[s]}`);case"checkbox":return o.querySelector(`.${f.popup} > .${f.checkbox} input`);case"radio":return o.querySelector(`.${f.popup} > .${f.radio} input:checked`)||o.querySelector(`.${f.popup} > .${f.radio} input:first-child`);case"range":return o.querySelector(`.${f.popup} > .${f.range} input`);default:return o.querySelector(`.${f.popup} > .${f.input}`)}},Es=o=>{if(o.focus(),o.type!=="file"){let s=o.value;o.value="",o.value=s}},As=(o,s,l)=>{!o||!s||(typeof s=="string"&&(s=s.split(/\s+/).filter(Boolean)),s.forEach(c=>{Array.isArray(o)?o.forEach(m=>{l?m.classList.add(c):m.classList.remove(c)}):l?o.classList.add(c):o.classList.remove(c)}))},I=(o,s)=>{As(o,s,!0)},Ae=(o,s)=>{As(o,s,!1)},Xe=(o,s)=>{let l=Array.from(o.children);for(let c=0;c<l.length;c++){let m=l[c];if(m instanceof HTMLElement&&qe(m,s))return m}},ut=(o,s,l)=>{l===`${parseInt(l)}`&&(l=parseInt(l)),l||parseInt(l)===0?o.style.setProperty(s,typeof l=="number"?`${l}px`:l):o.style.removeProperty(s)},W=function(o){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"flex";o&&(o.style.display=s)},ee=o=>{o&&(o.style.display="none")},Sr=function(o){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"block";o&&new MutationObserver(()=>{ci(o,o.innerHTML,s)}).observe(o,{childList:!0,subtree:!0})},Ms=(o,s,l,c)=>{let m=o.querySelector(s);m&&m.style.setProperty(l,c)},ci=function(o,s){let l=arguments.length>2&&arguments[2]!==void 0?arguments[2]:"flex";s?W(o,l):ee(o)},he=o=>!!(o&&(o.offsetWidth||o.offsetHeight||o.getClientRects().length)),kc=()=>!he(Pe())&&!he(ct())&&!he(Rt()),Ss=o=>o.scrollHeight>o.clientHeight,Ds=o=>{let s=window.getComputedStyle(o),l=parseFloat(s.getPropertyValue("animation-duration")||"0"),c=parseFloat(s.getPropertyValue("transition-duration")||"0");return l>0||c>0},Dr=function(o){let s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!1,l=rn();l&&he(l)&&(s&&(l.style.transition="none",l.style.width="100%"),setTimeout(()=>{l.style.transition=`width ${o/1e3}s linear`,l.style.width="0%"},10))},Oc=()=>{let o=rn();if(!o)return;let s=parseInt(window.getComputedStyle(o).width);o.style.removeProperty("transition"),o.style.width="100%";let l=parseInt(window.getComputedStyle(o).width),c=s/l*100;o.style.width=`${c}%`},Fc=()=>typeof window>"u"||typeof document>"u",Pc=`
 <div aria-labelledby="${f.title}" aria-describedby="${f["html-container"]}" class="${f.popup}" tabindex="-1">
   <button type="button" class="${f.close}"></button>
   <ul class="${f["progress-steps"]}"></ul>
   <div class="${f.icon}"></div>
   <img class="${f.image}" />
   <h2 class="${f.title}" id="${f.title}"></h2>
   <div class="${f["html-container"]}" id="${f["html-container"]}"></div>
   <input class="${f.input}" id="${f.input}" />
   <input type="file" class="${f.file}" />
   <div class="${f.range}">
     <input type="range" />
     <output></output>
   </div>
   <select class="${f.select}" id="${f.select}"></select>
   <div class="${f.radio}"></div>
   <label class="${f.checkbox}">
     <input type="checkbox" id="${f.checkbox}" />
     <span class="${f.label}"></span>
   </label>
   <textarea class="${f.textarea}" id="${f.textarea}"></textarea>
   <div class="${f["validation-message"]}" id="${f["validation-message"]}"></div>
   <div class="${f.actions}">
     <div class="${f.loader}"></div>
     <button type="button" class="${f.confirm}"></button>
     <button type="button" class="${f.deny}"></button>
     <button type="button" class="${f.cancel}"></button>
   </div>
   <div class="${f.footer}"></div>
   <div class="${f["timer-progress-bar-container"]}">
     <div class="${f["timer-progress-bar"]}"></div>
   </div>
 </div>
`.replace(/(^|\n)\s*/g,""),Nc=()=>{let o=le();return o?(o.remove(),Ae([document.documentElement,document.body],[f["no-backdrop"],f["toast-shown"],f["has-column"]]),!0):!1},ht=()=>{d.currentInstance.resetValidationMessage()},Lc=()=>{let o=O(),s=Xe(o,f.input),l=Xe(o,f.file),c=o.querySelector(`.${f.range} input`),m=o.querySelector(`.${f.range} output`),g=Xe(o,f.select),A=o.querySelector(`.${f.checkbox} input`),R=Xe(o,f.textarea);s.oninput=ht,l.onchange=ht,g.onchange=ht,A.onchange=ht,R.oninput=ht,c.oninput=()=>{ht(),m.value=c.value},c.onchange=()=>{ht(),m.value=c.value}},Vc=o=>typeof o=="string"?document.querySelector(o):o,jc=o=>{let s=O();s.setAttribute("role",o.toast?"alert":"dialog"),s.setAttribute("aria-live",o.toast?"polite":"assertive"),o.toast||s.setAttribute("aria-modal","true")},Uc=o=>{window.getComputedStyle(o).direction==="rtl"&&I(le(),f.rtl)},Bc=o=>{let s=Nc();if(Fc()){$("SweetAlert2 requires document to initialize");return}let l=document.createElement("div");l.className=f.container,s&&I(l,f["no-transition"]),ve(l,Pc),l.dataset.swal2Theme=o.theme;let c=Vc(o.target);c.appendChild(l),jc(o),Uc(c),Lc()},Ir=(o,s)=>{o instanceof HTMLElement?s.appendChild(o):typeof o=="object"?$c(o,s):o&&ve(s,o)},$c=(o,s)=>{o.jquery?zc(s,o):ve(s,o.toString())},zc=(o,s)=>{if(o.textContent="",0 in s)for(let l=0;l in s;l++)o.appendChild(s[l].cloneNode(!0));else o.appendChild(s.cloneNode(!0))},Hc=(o,s)=>{let l=di(),c=Tt();!l||!c||(!s.showConfirmButton&&!s.showDenyButton&&!s.showCancelButton?ee(l):W(l),we(l,s,"actions"),qc(l,c,s),ve(c,s.loaderHtml||""),we(c,s,"loader"))};function qc(o,s,l){let c=Pe(),m=ct(),g=Rt();!c||!m||!g||(Rr(c,"confirm",l),Rr(m,"deny",l),Rr(g,"cancel",l),Gc(c,m,g,l),l.reverseButtons&&(l.toast?(o.insertBefore(g,c),o.insertBefore(m,c)):(o.insertBefore(g,s),o.insertBefore(m,s),o.insertBefore(c,s))))}function Gc(o,s,l,c){if(!c.buttonsStyling){Ae([o,s,l],f.styled);return}I([o,s,l],f.styled),c.confirmButtonColor&&(o.style.backgroundColor=c.confirmButtonColor,I(o,f["default-outline"])),c.denyButtonColor&&(s.style.backgroundColor=c.denyButtonColor,I(s,f["default-outline"])),c.cancelButtonColor&&(l.style.backgroundColor=c.cancelButtonColor,I(l,f["default-outline"]))}function Rr(o,s,l){let c=S(s);ci(o,l[`show${c}Button`],"inline-block"),ve(o,l[`${s}ButtonText`]||""),o.setAttribute("aria-label",l[`${s}ButtonAriaLabel`]||""),o.className=f[s],we(o,l,`${s}Button`)}let Wc=(o,s)=>{let l=Er();l&&(ve(l,s.closeButtonHtml||""),we(l,s,"closeButton"),ci(l,s.showCloseButton),l.setAttribute("aria-label",s.closeButtonAriaLabel||""))},Yc=(o,s)=>{let l=le();l&&(Xc(l,s.backdrop),Zc(l,s.position),Kc(l,s.grow),we(l,s,"container"))};function Xc(o,s){typeof s=="string"?o.style.background=s:s||I([document.documentElement,document.body],f["no-backdrop"])}function Zc(o,s){s&&(s in f?I(o,f[s]):(E('The "position" parameter is not valid, defaulting to "center"'),I(o,f.center)))}function Kc(o,s){s&&I(o,f[`grow-${s}`])}var L={innerParams:new WeakMap,domCache:new WeakMap};let Qc=["input","file","range","select","radio","checkbox","textarea"],Jc=(o,s)=>{let l=O();if(!l)return;let c=L.innerParams.get(o),m=!c||s.input!==c.input;Qc.forEach(g=>{let A=Xe(l,f[g]);A&&(iu(g,s.inputAttributes),A.className=f[g],m&&ee(A))}),s.input&&(m&&eu(s),nu(s))},eu=o=>{if(!o.input)return;if(!H[o.input]){$(`Unexpected type of input! Expected ${Object.keys(H).join(" | ")}, got "${o.input}"`);return}let s=Is(o.input);if(!s)return;let l=H[o.input](s,o);W(s),o.inputAutoFocus&&setTimeout(()=>{Es(l)})},tu=o=>{for(let s=0;s<o.attributes.length;s++){let l=o.attributes[s].name;["id","type","value","style"].includes(l)||o.removeAttribute(l)}},iu=(o,s)=>{let l=O();if(!l)return;let c=sn(l,o);if(c){tu(c);for(let m in s)c.setAttribute(m,s[m])}},nu=o=>{if(!o.input)return;let s=Is(o.input);s&&we(s,o,"input")},Tr=(o,s)=>{!o.placeholder&&s.inputPlaceholder&&(o.placeholder=s.inputPlaceholder)},ui=(o,s,l)=>{if(l.inputLabel){let c=document.createElement("label"),m=f["input-label"];c.setAttribute("for",o.id),c.className=m,typeof l.customClass=="object"&&I(c,l.customClass.inputLabel),c.innerText=l.inputLabel,s.insertAdjacentElement("beforebegin",c)}},Is=o=>{let s=O();if(s)return Xe(s,f[o]||f.input)},an=(o,s)=>{["string","number"].includes(typeof s)?o.value=`${s}`:xr(s)||E(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof s}"`)},H={};H.text=H.email=H.password=H.number=H.tel=H.url=H.search=H.date=H["datetime-local"]=H.time=H.week=H.month=(o,s)=>(an(o,s.inputValue),ui(o,o,s),Tr(o,s),o.type=s.input,o),H.file=(o,s)=>(ui(o,o,s),Tr(o,s),o),H.range=(o,s)=>{let l=o.querySelector("input"),c=o.querySelector("output");return an(l,s.inputValue),l.type=s.input,an(c,s.inputValue),ui(l,o,s),o},H.select=(o,s)=>{if(o.textContent="",s.inputPlaceholder){let l=document.createElement("option");ve(l,s.inputPlaceholder),l.value="",l.disabled=!0,l.selected=!0,o.appendChild(l)}return ui(o,o,s),o},H.radio=o=>(o.textContent="",o),H.checkbox=(o,s)=>{let l=sn(O(),"checkbox");l.value="1",l.checked=!!s.inputValue;let c=o.querySelector("span");return ve(c,s.inputPlaceholder||s.inputLabel),l},H.textarea=(o,s)=>{an(o,s.inputValue),Tr(o,s),ui(o,o,s);let l=c=>parseInt(window.getComputedStyle(c).marginLeft)+parseInt(window.getComputedStyle(c).marginRight);return setTimeout(()=>{if("MutationObserver"in window){let c=parseInt(window.getComputedStyle(O()).width),m=()=>{if(!document.body.contains(o))return;let g=o.offsetWidth+l(o);g>c?O().style.width=`${g}px`:ut(O(),"width",s.width)};new MutationObserver(m).observe(o,{attributes:!0,attributeFilter:["style"]})}}),o};let ru=(o,s)=>{let l=_r();l&&(Sr(l),we(l,s,"htmlContainer"),s.html?(Ir(s.html,l),W(l,"block")):s.text?(l.textContent=s.text,W(l,"block")):ee(l),Jc(o,s))},ou=(o,s)=>{let l=Cs();l&&(Sr(l),ci(l,s.footer,"block"),s.footer&&Ir(s.footer,l),we(l,s,"footer"))},su=(o,s)=>{let l=L.innerParams.get(o),c=It();if(!c)return;if(l&&s.icon===l.icon){ks(c,s),Rs(c,s);return}if(!s.icon&&!s.iconHtml){ee(c);return}if(s.icon&&Object.keys(B).indexOf(s.icon)===-1){$(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${s.icon}"`),ee(c);return}W(c),ks(c,s),Rs(c,s),I(c,s.showClass&&s.showClass.icon),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",Ts)},Rs=(o,s)=>{for(let[l,c]of Object.entries(B))s.icon!==l&&Ae(o,c);I(o,s.icon&&B[s.icon]),du(o,s),Ts(),we(o,s,"icon")},Ts=()=>{let o=O();if(!o)return;let s=window.getComputedStyle(o).getPropertyValue("background-color"),l=o.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let c=0;c<l.length;c++)l[c].style.backgroundColor=s},au=`
  <div class="swal2-success-circular-line-left"></div>
  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>
  <div class="swal2-success-circular-line-right"></div>
`,lu=`
  <span class="swal2-x-mark">
    <span class="swal2-x-mark-line-left"></span>
    <span class="swal2-x-mark-line-right"></span>
  </span>
`,ks=(o,s)=>{if(!s.icon&&!s.iconHtml)return;let l=o.innerHTML,c="";s.iconHtml?c=Os(s.iconHtml):s.icon==="success"?(c=au,l=l.replace(/ style=".*?"/g,"")):s.icon==="error"?c=lu:s.icon&&(c=Os({question:"?",warning:"!",info:"i"}[s.icon])),l.trim()!==c.trim()&&ve(o,c)},du=(o,s)=>{if(s.iconColor){o.style.color=s.iconColor,o.style.borderColor=s.iconColor;for(let l of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])Ms(o,l,"background-color",s.iconColor);Ms(o,".swal2-success-ring","border-color",s.iconColor)}},Os=o=>`<div class="${f["icon-content"]}">${o}</div>`,cu=(o,s)=>{let l=_s();if(l){if(!s.imageUrl){ee(l);return}W(l,""),l.setAttribute("src",s.imageUrl),l.setAttribute("alt",s.imageAlt||""),ut(l,"width",s.imageWidth),ut(l,"height",s.imageHeight),l.className=f.image,we(l,s,"image")}},kr=!1,Fs=0,Ps=0,Ns=0,Ls=0,uu=o=>{o.addEventListener("mousedown",ln),document.body.addEventListener("mousemove",dn),o.addEventListener("mouseup",cn),o.addEventListener("touchstart",ln),document.body.addEventListener("touchmove",dn),o.addEventListener("touchend",cn)},hu=o=>{o.removeEventListener("mousedown",ln),document.body.removeEventListener("mousemove",dn),o.removeEventListener("mouseup",cn),o.removeEventListener("touchstart",ln),document.body.removeEventListener("touchmove",dn),o.removeEventListener("touchend",cn)},ln=o=>{let s=O();if(o.target===s||It().contains(o.target)){kr=!0;let l=Vs(o);Fs=l.clientX,Ps=l.clientY,Ns=parseInt(s.style.insetInlineStart)||0,Ls=parseInt(s.style.insetBlockStart)||0,I(s,"swal2-dragging")}},dn=o=>{let s=O();if(kr){let{clientX:l,clientY:c}=Vs(o);s.style.insetInlineStart=`${Ns+(l-Fs)}px`,s.style.insetBlockStart=`${Ls+(c-Ps)}px`}},cn=()=>{let o=O();kr=!1,Ae(o,"swal2-dragging")},Vs=o=>{let s=0,l=0;return o.type.startsWith("mouse")?(s=o.clientX,l=o.clientY):o.type.startsWith("touch")&&(s=o.touches[0].clientX,l=o.touches[0].clientY),{clientX:s,clientY:l}},fu=(o,s)=>{let l=le(),c=O();if(!(!l||!c)){if(s.toast){ut(l,"width",s.width),c.style.width="100%";let m=Tt();m&&c.insertBefore(m,It())}else ut(c,"width",s.width);ut(c,"padding",s.padding),s.color&&(c.style.color=s.color),s.background&&(c.style.background=s.background),ee(nn()),mu(c,s),s.draggable&&!s.toast?(I(c,f.draggable),uu(c)):(Ae(c,f.draggable),hu(c))}},mu=(o,s)=>{let l=s.showClass||{};o.className=`${f.popup} ${he(o)?l.popup:""}`,s.toast?(I([document.documentElement,document.body],f["toast-shown"]),I(o,f.toast)):I(o,f.modal),we(o,s,"popup"),typeof s.customClass=="string"&&I(o,s.customClass),s.icon&&I(o,f[`icon-${s.icon}`])},pu=(o,s)=>{let l=Cr();if(!l)return;let{progressSteps:c,currentProgressStep:m}=s;if(!c||c.length===0||m===void 0){ee(l);return}W(l),l.textContent="",m>=c.length&&E("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),c.forEach((g,A)=>{let R=gu(g);if(l.appendChild(R),A===m&&I(R,f["active-progress-step"]),A!==c.length-1){let U=vu(s);l.appendChild(U)}})},gu=o=>{let s=document.createElement("li");return I(s,f["progress-step"]),ve(s,o),s},vu=o=>{let s=document.createElement("li");return I(s,f["progress-step-line"]),o.progressStepsDistance&&ut(s,"width",o.progressStepsDistance),s},wu=(o,s)=>{let l=xs();l&&(Sr(l),ci(l,s.title||s.titleText,"block"),s.title&&Ir(s.title,l),s.titleText&&(l.innerText=s.titleText),we(l,s,"title"))},js=(o,s)=>{fu(o,s),Yc(o,s),pu(o,s),su(o,s),cu(o,s),wu(o,s),Wc(o,s),ru(o,s),Hc(o,s),ou(o,s);let l=O();typeof s.didRender=="function"&&l&&s.didRender(l),d.eventEmitter.emit("didRender",l)},bu=()=>he(O()),Us=()=>{var o;return(o=Pe())===null||o===void 0?void 0:o.click()},yu=()=>{var o;return(o=ct())===null||o===void 0?void 0:o.click()},xu=()=>{var o;return(o=Rt())===null||o===void 0?void 0:o.click()},kt=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Bs=o=>{o.keydownTarget&&o.keydownHandlerAdded&&(o.keydownTarget.removeEventListener("keydown",o.keydownHandler,{capture:o.keydownListenerCapture}),o.keydownHandlerAdded=!1)},_u=(o,s,l)=>{Bs(o),s.toast||(o.keydownHandler=c=>Eu(s,c,l),o.keydownTarget=s.keydownListenerCapture?window:O(),o.keydownListenerCapture=s.keydownListenerCapture,o.keydownTarget.addEventListener("keydown",o.keydownHandler,{capture:o.keydownListenerCapture}),o.keydownHandlerAdded=!0)},Or=(o,s)=>{var l;let c=Ar();if(c.length){o=o+s,o===c.length?o=0:o===-1&&(o=c.length-1),c[o].focus();return}(l=O())===null||l===void 0||l.focus()},$s=["ArrowRight","ArrowDown"],Cu=["ArrowLeft","ArrowUp"],Eu=(o,s,l)=>{o&&(s.isComposing||s.keyCode===229||(o.stopKeydownPropagation&&s.stopPropagation(),s.key==="Enter"?Au(s,o):s.key==="Tab"?Mu(s):[...$s,...Cu].includes(s.key)?Su(s.key):s.key==="Escape"&&Du(s,o,l)))},Au=(o,s)=>{if(!Dt(s.allowEnterKey))return;let l=sn(O(),s.input);if(o.target&&l&&o.target instanceof HTMLElement&&o.target.outerHTML===l.outerHTML){if(["textarea","file"].includes(s.input))return;Us(),o.preventDefault()}},Mu=o=>{let s=o.target,l=Ar(),c=-1;for(let m=0;m<l.length;m++)if(s===l[m]){c=m;break}o.shiftKey?Or(c,-1):Or(c,1),o.stopPropagation(),o.preventDefault()},Su=o=>{let s=di(),l=Pe(),c=ct(),m=Rt();if(!s||!l||!c||!m)return;let g=[l,c,m];if(document.activeElement instanceof HTMLElement&&!g.includes(document.activeElement))return;let A=$s.includes(o)?"nextElementSibling":"previousElementSibling",R=document.activeElement;if(R){for(let U=0;U<s.children.length;U++){if(R=R[A],!R)return;if(R instanceof HTMLButtonElement&&he(R))break}R instanceof HTMLButtonElement&&R.focus()}},Du=(o,s,l)=>{Dt(s.allowEscapeKey)&&(o.preventDefault(),l(kt.esc))};var Ot={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};let Iu=()=>{let o=le();Array.from(document.body.children).forEach(l=>{l.contains(o)||(l.hasAttribute("aria-hidden")&&l.setAttribute("data-previous-aria-hidden",l.getAttribute("aria-hidden")||""),l.setAttribute("aria-hidden","true"))})},zs=()=>{Array.from(document.body.children).forEach(s=>{s.hasAttribute("data-previous-aria-hidden")?(s.setAttribute("aria-hidden",s.getAttribute("data-previous-aria-hidden")||""),s.removeAttribute("data-previous-aria-hidden")):s.removeAttribute("aria-hidden")})},Hs=typeof window<"u"&&!!window.GestureEvent,Ru=()=>{if(Hs&&!qe(document.body,f.iosfix)){let o=document.body.scrollTop;document.body.style.top=`${o*-1}px`,I(document.body,f.iosfix),Tu()}},Tu=()=>{let o=le();if(!o)return;let s;o.ontouchstart=l=>{s=ku(l)},o.ontouchmove=l=>{s&&(l.preventDefault(),l.stopPropagation())}},ku=o=>{let s=o.target,l=le(),c=_r();return!l||!c||Ou(o)||Fu(o)?!1:s===l||!Ss(l)&&s instanceof HTMLElement&&s.tagName!=="INPUT"&&s.tagName!=="TEXTAREA"&&!(Ss(c)&&c.contains(s))},Ou=o=>o.touches&&o.touches.length&&o.touches[0].touchType==="stylus",Fu=o=>o.touches&&o.touches.length>1,Pu=()=>{if(qe(document.body,f.iosfix)){let o=parseInt(document.body.style.top,10);Ae(document.body,f.iosfix),document.body.style.top="",document.body.scrollTop=o*-1}},Nu=()=>{let o=document.createElement("div");o.className=f["scrollbar-measure"],document.body.appendChild(o);let s=o.getBoundingClientRect().width-o.clientWidth;return document.body.removeChild(o),s},Ft=null,Lu=o=>{Ft===null&&(document.body.scrollHeight>window.innerHeight||o==="scroll")&&(Ft=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${Ft+Nu()}px`)},Vu=()=>{Ft!==null&&(document.body.style.paddingRight=`${Ft}px`,Ft=null)};function qs(o,s,l,c){on()?Ws(o,c):(u(l).then(()=>Ws(o,c)),Bs(d)),Hs?(s.setAttribute("style","display:none !important"),s.removeAttribute("class"),s.innerHTML=""):s.remove(),Mr()&&(Vu(),Pu(),zs()),ju()}function ju(){Ae([document.documentElement,document.body],[f.shown,f["height-auto"],f["no-backdrop"],f["toast-shown"]])}function Ze(o){o=Bu(o);let s=Ot.swalPromiseResolve.get(this),l=Uu(this);this.isAwaitingPromise?o.isDismissed||(hi(this),s(o)):l&&s(o)}let Uu=o=>{let s=O();if(!s)return!1;let l=L.innerParams.get(o);if(!l||qe(s,l.hideClass.popup))return!1;Ae(s,l.showClass.popup),I(s,l.hideClass.popup);let c=le();return Ae(c,l.showClass.backdrop),I(c,l.hideClass.backdrop),$u(o,s,l),!0};function Gs(o){let s=Ot.swalPromiseReject.get(this);hi(this),s&&s(o)}let hi=o=>{o.isAwaitingPromise&&(delete o.isAwaitingPromise,L.innerParams.get(o)||o._destroy())},Bu=o=>typeof o>"u"?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},o),$u=(o,s,l)=>{var c;let m=le(),g=Ds(s);typeof l.willClose=="function"&&l.willClose(s),(c=d.eventEmitter)===null||c===void 0||c.emit("willClose",s),g?zu(o,s,m,l.returnFocus,l.didClose):qs(o,m,l.returnFocus,l.didClose)},zu=(o,s,l,c,m)=>{d.swalCloseEventFinishedCallback=qs.bind(null,o,l,c,m);let g=function(A){if(A.target===s){var R;(R=d.swalCloseEventFinishedCallback)===null||R===void 0||R.call(d),delete d.swalCloseEventFinishedCallback,s.removeEventListener("animationend",g),s.removeEventListener("transitionend",g)}};s.addEventListener("animationend",g),s.addEventListener("transitionend",g)},Ws=(o,s)=>{setTimeout(()=>{var l;typeof s=="function"&&s.bind(o.params)(),(l=d.eventEmitter)===null||l===void 0||l.emit("didClose"),o._destroy&&o._destroy()})},Pt=o=>{let s=O();if(s||new gn,s=O(),!s)return;let l=Tt();on()?ee(It()):Hu(s,o),W(l),s.setAttribute("data-loading","true"),s.setAttribute("aria-busy","true"),s.focus()},Hu=(o,s)=>{let l=di(),c=Tt();!l||!c||(!s&&he(Pe())&&(s=Pe()),W(l),s&&(ee(s),c.setAttribute("data-button-to-replace",s.className),l.insertBefore(c,s)),I([o,l],f.loading))},qu=(o,s)=>{s.input==="select"||s.input==="radio"?Zu(o,s):["text","email","number","tel","textarea"].some(l=>l===s.input)&&(si(s.inputValue)||xr(s.inputValue))&&(Pt(Pe()),Ku(o,s))},Gu=(o,s)=>{let l=o.getInput();if(!l)return null;switch(s.input){case"checkbox":return Wu(l);case"radio":return Yu(l);case"file":return Xu(l);default:return s.inputAutoTrim?l.value.trim():l.value}},Wu=o=>o.checked?1:0,Yu=o=>o.checked?o.value:null,Xu=o=>o.files&&o.files.length?o.getAttribute("multiple")!==null?o.files:o.files[0]:null,Zu=(o,s)=>{let l=O();if(!l)return;let c=m=>{s.input==="select"?Qu(l,un(m),s):s.input==="radio"&&Ju(l,un(m),s)};si(s.inputOptions)||xr(s.inputOptions)?(Pt(Pe()),ai(s.inputOptions).then(m=>{o.hideLoading(),c(m)})):typeof s.inputOptions=="object"?c(s.inputOptions):$(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof s.inputOptions}`)},Ku=(o,s)=>{let l=o.getInput();l&&(ee(l),ai(s.inputValue).then(c=>{l.value=s.input==="number"?`${parseFloat(c)||0}`:`${c}`,W(l),l.focus(),o.hideLoading()}).catch(c=>{$(`Error in inputValue promise: ${c}`),l.value="",W(l),l.focus(),o.hideLoading()}))};function Qu(o,s,l){let c=Xe(o,f.select);if(!c)return;let m=(g,A,R)=>{let U=document.createElement("option");U.value=R,ve(U,A),U.selected=Ys(R,l.inputValue),g.appendChild(U)};s.forEach(g=>{let A=g[0],R=g[1];if(Array.isArray(R)){let U=document.createElement("optgroup");U.label=A,U.disabled=!1,c.appendChild(U),R.forEach(Lt=>m(U,Lt[1],Lt[0]))}else m(c,R,A)}),c.focus()}function Ju(o,s,l){let c=Xe(o,f.radio);if(!c)return;s.forEach(g=>{let A=g[0],R=g[1],U=document.createElement("input"),Lt=document.createElement("label");U.type="radio",U.name=f.radio,U.value=A,Ys(A,l.inputValue)&&(U.checked=!0);let Vr=document.createElement("span");ve(Vr,R),Vr.className=f.label,Lt.appendChild(U),Lt.appendChild(Vr),c.appendChild(Lt)});let m=c.querySelectorAll("input");m.length&&m[0].focus()}let un=o=>{let s=[];return o instanceof Map?o.forEach((l,c)=>{let m=l;typeof m=="object"&&(m=un(m)),s.push([c,m])}):Object.keys(o).forEach(l=>{let c=o[l];typeof c=="object"&&(c=un(c)),s.push([l,c])}),s},Ys=(o,s)=>!!s&&s.toString()===o.toString(),eh=o=>{let s=L.innerParams.get(o);o.disableButtons(),s.input?Xs(o,"confirm"):Pr(o,!0)},th=o=>{let s=L.innerParams.get(o);o.disableButtons(),s.returnInputValueOnDeny?Xs(o,"deny"):Fr(o,!1)},ih=(o,s)=>{o.disableButtons(),s(kt.cancel)},Xs=(o,s)=>{let l=L.innerParams.get(o);if(!l.input){$(`The "input" parameter is needed to be set when using returnInputValueOn${S(s)}`);return}let c=o.getInput(),m=Gu(o,l);l.inputValidator?nh(o,m,s):c&&!c.checkValidity()?(o.enableButtons(),o.showValidationMessage(l.validationMessage||c.validationMessage)):s==="deny"?Fr(o,m):Pr(o,m)},nh=(o,s,l)=>{let c=L.innerParams.get(o);o.disableInput(),Promise.resolve().then(()=>ai(c.inputValidator(s,c.validationMessage))).then(g=>{o.enableButtons(),o.enableInput(),g?o.showValidationMessage(g):l==="deny"?Fr(o,s):Pr(o,s)})},Fr=(o,s)=>{let l=L.innerParams.get(o||void 0);l.showLoaderOnDeny&&Pt(ct()),l.preDeny?(o.isAwaitingPromise=!0,Promise.resolve().then(()=>ai(l.preDeny(s,l.validationMessage))).then(m=>{m===!1?(o.hideLoading(),hi(o)):o.close({isDenied:!0,value:typeof m>"u"?s:m})}).catch(m=>Ks(o||void 0,m))):o.close({isDenied:!0,value:s})},Zs=(o,s)=>{o.close({isConfirmed:!0,value:s})},Ks=(o,s)=>{o.rejectPromise(s)},Pr=(o,s)=>{let l=L.innerParams.get(o||void 0);l.showLoaderOnConfirm&&Pt(),l.preConfirm?(o.resetValidationMessage(),o.isAwaitingPromise=!0,Promise.resolve().then(()=>ai(l.preConfirm(s,l.validationMessage))).then(m=>{he(nn())||m===!1?(o.hideLoading(),hi(o)):Zs(o,typeof m>"u"?s:m)}).catch(m=>Ks(o||void 0,m))):Zs(o,s)};function hn(){let o=L.innerParams.get(this);if(!o)return;let s=L.domCache.get(this);ee(s.loader),on()?o.icon&&W(It()):rh(s),Ae([s.popup,s.actions],f.loading),s.popup.removeAttribute("aria-busy"),s.popup.removeAttribute("data-loading"),s.confirmButton.disabled=!1,s.denyButton.disabled=!1,s.cancelButton.disabled=!1}let rh=o=>{let s=o.popup.getElementsByClassName(o.loader.getAttribute("data-button-to-replace"));s.length?W(s[0],"inline-block"):kc()&&ee(o.actions)};function Qs(){let o=L.innerParams.get(this),s=L.domCache.get(this);return s?sn(s.popup,o.input):null}function Js(o,s,l){let c=L.domCache.get(o);s.forEach(m=>{c[m].disabled=l})}function ea(o,s){let l=O();if(!(!l||!o))if(o.type==="radio"){let c=l.querySelectorAll(`[name="${f.radio}"]`);for(let m=0;m<c.length;m++)c[m].disabled=s}else o.disabled=s}function ta(){Js(this,["confirmButton","denyButton","cancelButton"],!1)}function ia(){Js(this,["confirmButton","denyButton","cancelButton"],!0)}function na(){ea(this.getInput(),!1)}function ra(){ea(this.getInput(),!0)}function oa(o){let s=L.domCache.get(this),l=L.innerParams.get(this);ve(s.validationMessage,o),s.validationMessage.className=f["validation-message"],l.customClass&&l.customClass.validationMessage&&I(s.validationMessage,l.customClass.validationMessage),W(s.validationMessage);let c=this.getInput();c&&(c.setAttribute("aria-invalid","true"),c.setAttribute("aria-describedby",f["validation-message"]),Es(c),I(c,f.inputerror))}function sa(){let o=L.domCache.get(this);o.validationMessage&&ee(o.validationMessage);let s=this.getInput();s&&(s.removeAttribute("aria-invalid"),s.removeAttribute("aria-describedby"),Ae(s,f.inputerror))}let Nt={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,draggable:!1,animation:!0,theme:"light",showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0},oh=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","draggable","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","theme","willClose"],sh={allowEnterKey:void 0},ah=["allowOutsideClick","allowEnterKey","backdrop","draggable","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],aa=o=>Object.prototype.hasOwnProperty.call(Nt,o),la=o=>oh.indexOf(o)!==-1,da=o=>sh[o],lh=o=>{aa(o)||E(`Unknown parameter "${o}"`)},dh=o=>{ah.includes(o)&&E(`The parameter "${o}" is incompatible with toasts`)},ch=o=>{let s=da(o);s&&St(o,s)},ca=o=>{o.backdrop===!1&&o.allowOutsideClick&&E('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),o.theme&&!["light","dark","auto","borderless"].includes(o.theme)&&E(`Invalid theme "${o.theme}". Expected "light", "dark", "auto", or "borderless"`);for(let s in o)lh(s),o.toast&&dh(s),ch(s)};function ua(o){let s=le(),l=O(),c=L.innerParams.get(this);if(!l||qe(l,c.hideClass.popup)){E("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");return}let m=uh(o),g=Object.assign({},c,m);ca(g),s.dataset.swal2Theme=g.theme,js(this,g),L.innerParams.set(this,g),Object.defineProperties(this,{params:{value:Object.assign({},this.params,o),writable:!1,enumerable:!0}})}let uh=o=>{let s={};return Object.keys(o).forEach(l=>{la(l)?s[l]=o[l]:E(`Invalid parameter to update: ${l}`)}),s};function ha(){let o=L.domCache.get(this),s=L.innerParams.get(this);if(!s){fa(this);return}o.popup&&d.swalCloseEventFinishedCallback&&(d.swalCloseEventFinishedCallback(),delete d.swalCloseEventFinishedCallback),typeof s.didDestroy=="function"&&s.didDestroy(),d.eventEmitter.emit("didDestroy"),hh(this)}let hh=o=>{fa(o),delete o.params,delete d.keydownHandler,delete d.keydownTarget,delete d.currentInstance},fa=o=>{o.isAwaitingPromise?(Nr(L,o),o.isAwaitingPromise=!0):(Nr(Ot,o),Nr(L,o),delete o.isAwaitingPromise,delete o.disableButtons,delete o.enableButtons,delete o.getInput,delete o.disableInput,delete o.enableInput,delete o.hideLoading,delete o.disableLoading,delete o.showValidationMessage,delete o.resetValidationMessage,delete o.close,delete o.closePopup,delete o.closeModal,delete o.closeToast,delete o.rejectPromise,delete o.update,delete o._destroy)},Nr=(o,s)=>{for(let l in o)o[l].delete(s)};var fh=Object.freeze({__proto__:null,_destroy:ha,close:Ze,closeModal:Ze,closePopup:Ze,closeToast:Ze,disableButtons:ia,disableInput:ra,disableLoading:hn,enableButtons:ta,enableInput:na,getInput:Qs,handleAwaitingPromise:hi,hideLoading:hn,rejectPromise:Gs,resetValidationMessage:sa,showValidationMessage:oa,update:ua});let mh=(o,s,l)=>{o.toast?ph(o,s,l):(vh(s),wh(s),bh(o,s,l))},ph=(o,s,l)=>{s.popup.onclick=()=>{o&&(gh(o)||o.timer||o.input)||l(kt.close)}},gh=o=>!!(o.showConfirmButton||o.showDenyButton||o.showCancelButton||o.showCloseButton),fn=!1,vh=o=>{o.popup.onmousedown=()=>{o.container.onmouseup=function(s){o.container.onmouseup=()=>{},s.target===o.container&&(fn=!0)}}},wh=o=>{o.container.onmousedown=s=>{s.target===o.container&&s.preventDefault(),o.popup.onmouseup=function(l){o.popup.onmouseup=()=>{},(l.target===o.popup||l.target instanceof HTMLElement&&o.popup.contains(l.target))&&(fn=!0)}}},bh=(o,s,l)=>{s.container.onclick=c=>{if(fn){fn=!1;return}c.target===s.container&&Dt(o.allowOutsideClick)&&l(kt.backdrop)}},yh=o=>typeof o=="object"&&o.jquery,ma=o=>o instanceof Element||yh(o),xh=o=>{let s={};return typeof o[0]=="object"&&!ma(o[0])?Object.assign(s,o[0]):["title","html","icon"].forEach((l,c)=>{let m=o[c];typeof m=="string"||ma(m)?s[l]=m:m!==void 0&&$(`Unexpected type of ${l}! Expected "string" or "Element", got ${typeof m}`)}),s};function _h(){for(var o=arguments.length,s=new Array(o),l=0;l<o;l++)s[l]=arguments[l];return new this(...s)}function Ch(o){class s extends this{_main(c,m){return super._main(c,Object.assign({},o,m))}}return s}let Eh=()=>d.timeout&&d.timeout.getTimerLeft(),pa=()=>{if(d.timeout)return Oc(),d.timeout.stop()},ga=()=>{if(d.timeout){let o=d.timeout.start();return Dr(o),o}},Ah=()=>{let o=d.timeout;return o&&(o.running?pa():ga())},Mh=o=>{if(d.timeout){let s=d.timeout.increase(o);return Dr(s,!0),s}},Sh=()=>!!(d.timeout&&d.timeout.isRunning()),va=!1,Lr={};function Dh(){let o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"data-swal-template";Lr[o]=this,va||(document.body.addEventListener("click",Ih),va=!0)}let Ih=o=>{for(let s=o.target;s&&s!==document;s=s.parentNode)for(let l in Lr){let c=s.getAttribute(l);if(c){Lr[l].fire({template:c});return}}};class Rh{constructor(){this.events={}}_getHandlersByEventName(s){return typeof this.events[s]>"u"&&(this.events[s]=[]),this.events[s]}on(s,l){let c=this._getHandlersByEventName(s);c.includes(l)||c.push(l)}once(s,l){var c=this;let m=function(){c.removeListener(s,m);for(var g=arguments.length,A=new Array(g),R=0;R<g;R++)A[R]=arguments[R];l.apply(c,A)};this.on(s,m)}emit(s){for(var l=arguments.length,c=new Array(l>1?l-1:0),m=1;m<l;m++)c[m-1]=arguments[m];this._getHandlersByEventName(s).forEach(g=>{try{g.apply(this,c)}catch(A){console.error(A)}})}removeListener(s,l){let c=this._getHandlersByEventName(s),m=c.indexOf(l);m>-1&&c.splice(m,1)}removeAllListeners(s){this.events[s]!==void 0&&(this.events[s].length=0)}reset(){this.events={}}}d.eventEmitter=new Rh;var Th=Object.freeze({__proto__:null,argsToParams:xh,bindClickHandler:Dh,clickCancel:xu,clickConfirm:Us,clickDeny:yu,enableLoading:Pt,fire:_h,getActions:di,getCancelButton:Rt,getCloseButton:Er,getConfirmButton:Pe,getContainer:le,getDenyButton:ct,getFocusableElements:Ar,getFooter:Cs,getHtmlContainer:_r,getIcon:It,getIconContent:Sc,getImage:_s,getInputLabel:Dc,getLoader:Tt,getPopup:O,getProgressSteps:Cr,getTimerLeft:Eh,getTimerProgressBar:rn,getTitle:xs,getValidationMessage:nn,increaseTimer:Mh,isDeprecatedParameter:da,isLoading:Rc,isTimerRunning:Sh,isUpdatableParameter:la,isValidParameter:aa,isVisible:bu,mixin:Ch,off:(o,s)=>{if(!o){d.eventEmitter.reset();return}s?d.eventEmitter.removeListener(o,s):d.eventEmitter.removeAllListeners(o)},on:(o,s)=>{d.eventEmitter.on(o,s)},once:(o,s)=>{d.eventEmitter.once(o,s)},resumeTimer:ga,showLoading:Pt,stopTimer:pa,toggleTimer:Ah});class kh{constructor(s,l){this.callback=s,this.remaining=l,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date().getTime()-this.started.getTime()),this.remaining}increase(s){let l=this.running;return l&&this.stop(),this.remaining+=s,l&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}let wa=["swal-title","swal-html","swal-footer"],Oh=o=>{let s=typeof o.template=="string"?document.querySelector(o.template):o.template;if(!s)return{};let l=s.content;return Bh(l),Object.assign(Fh(l),Ph(l),Nh(l),Lh(l),Vh(l),jh(l),Uh(l,wa))},Fh=o=>{let s={};return Array.from(o.querySelectorAll("swal-param")).forEach(c=>{ft(c,["name","value"]);let m=c.getAttribute("name"),g=c.getAttribute("value");!m||!g||(typeof Nt[m]=="boolean"?s[m]=g!=="false":typeof Nt[m]=="object"?s[m]=JSON.parse(g):s[m]=g)}),s},Ph=o=>{let s={};return Array.from(o.querySelectorAll("swal-function-param")).forEach(c=>{let m=c.getAttribute("name"),g=c.getAttribute("value");!m||!g||(s[m]=new Function(`return ${g}`)())}),s},Nh=o=>{let s={};return Array.from(o.querySelectorAll("swal-button")).forEach(c=>{ft(c,["type","color","aria-label"]);let m=c.getAttribute("type");!m||!["confirm","cancel","deny"].includes(m)||(s[`${m}ButtonText`]=c.innerHTML,s[`show${S(m)}Button`]=!0,c.hasAttribute("color")&&(s[`${m}ButtonColor`]=c.getAttribute("color")),c.hasAttribute("aria-label")&&(s[`${m}ButtonAriaLabel`]=c.getAttribute("aria-label")))}),s},Lh=o=>{let s={},l=o.querySelector("swal-image");return l&&(ft(l,["src","width","height","alt"]),l.hasAttribute("src")&&(s.imageUrl=l.getAttribute("src")||void 0),l.hasAttribute("width")&&(s.imageWidth=l.getAttribute("width")||void 0),l.hasAttribute("height")&&(s.imageHeight=l.getAttribute("height")||void 0),l.hasAttribute("alt")&&(s.imageAlt=l.getAttribute("alt")||void 0)),s},Vh=o=>{let s={},l=o.querySelector("swal-icon");return l&&(ft(l,["type","color"]),l.hasAttribute("type")&&(s.icon=l.getAttribute("type")),l.hasAttribute("color")&&(s.iconColor=l.getAttribute("color")),s.iconHtml=l.innerHTML),s},jh=o=>{let s={},l=o.querySelector("swal-input");l&&(ft(l,["type","label","placeholder","value"]),s.input=l.getAttribute("type")||"text",l.hasAttribute("label")&&(s.inputLabel=l.getAttribute("label")),l.hasAttribute("placeholder")&&(s.inputPlaceholder=l.getAttribute("placeholder")),l.hasAttribute("value")&&(s.inputValue=l.getAttribute("value")));let c=Array.from(o.querySelectorAll("swal-input-option"));return c.length&&(s.inputOptions={},c.forEach(m=>{ft(m,["value"]);let g=m.getAttribute("value");if(!g)return;let A=m.innerHTML;s.inputOptions[g]=A})),s},Uh=(o,s)=>{let l={};for(let c in s){let m=s[c],g=o.querySelector(m);g&&(ft(g,[]),l[m.replace(/^swal-/,"")]=g.innerHTML.trim())}return l},Bh=o=>{let s=wa.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(o.children).forEach(l=>{let c=l.tagName.toLowerCase();s.includes(c)||E(`Unrecognized element <${c}>`)})},ft=(o,s)=>{Array.from(o.attributes).forEach(l=>{s.indexOf(l.name)===-1&&E([`Unrecognized attribute "${l.name}" on <${o.tagName.toLowerCase()}>.`,`${s.length?`Allowed attributes are: ${s.join(", ")}`:"To set the value, use HTML within the element."}`])})},ba=10,$h=o=>{let s=le(),l=O();typeof o.willOpen=="function"&&o.willOpen(l),d.eventEmitter.emit("willOpen",l);let m=window.getComputedStyle(document.body).overflowY;qh(s,l,o),setTimeout(()=>{zh(s,l)},ba),Mr()&&(Hh(s,o.scrollbarPadding,m),Iu()),!on()&&!d.previousActiveElement&&(d.previousActiveElement=document.activeElement),typeof o.didOpen=="function"&&setTimeout(()=>o.didOpen(l)),d.eventEmitter.emit("didOpen",l),Ae(s,f["no-transition"])},mn=o=>{let s=O();if(o.target!==s)return;let l=le();s.removeEventListener("animationend",mn),s.removeEventListener("transitionend",mn),l.style.overflowY="auto"},zh=(o,s)=>{Ds(s)?(o.style.overflowY="hidden",s.addEventListener("animationend",mn),s.addEventListener("transitionend",mn)):o.style.overflowY="auto"},Hh=(o,s,l)=>{Ru(),s&&l!=="hidden"&&Lu(l),setTimeout(()=>{o.scrollTop=0})},qh=(o,s,l)=>{I(o,l.showClass.backdrop),l.animation?(s.style.setProperty("opacity","0","important"),W(s,"grid"),setTimeout(()=>{I(s,l.showClass.popup),s.style.removeProperty("opacity")},ba)):W(s,"grid"),I([document.documentElement,document.body],f.shown),l.heightAuto&&l.backdrop&&!l.toast&&I([document.documentElement,document.body],f["height-auto"])};var ya={email:(o,s)=>/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(o)?Promise.resolve():Promise.resolve(s||"Invalid email address"),url:(o,s)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(o)?Promise.resolve():Promise.resolve(s||"Invalid URL")};function Gh(o){o.inputValidator||(o.input==="email"&&(o.inputValidator=ya.email),o.input==="url"&&(o.inputValidator=ya.url))}function Wh(o){(!o.target||typeof o.target=="string"&&!document.querySelector(o.target)||typeof o.target!="string"&&!o.target.appendChild)&&(E('Target parameter is not valid, defaulting to "body"'),o.target="body")}function Yh(o){Gh(o),o.showLoaderOnConfirm&&!o.preConfirm&&E(`showLoaderOnConfirm is set to true, but preConfirm is not defined.
showLoaderOnConfirm should be used together with preConfirm, see usage example:
https://sweetalert2.github.io/#ajax-request`),Wh(o),typeof o.title=="string"&&(o.title=o.title.split(`
`).join("<br />")),Bc(o)}let Ne;var pn=new WeakMap;class q{constructor(){if(n(this,pn,void 0),typeof window>"u")return;Ne=this;for(var s=arguments.length,l=new Array(s),c=0;c<s;c++)l[c]=arguments[c];let m=Object.freeze(this.constructor.argsToParams(l));this.params=m,this.isAwaitingPromise=!1,r(pn,this,this._main(Ne.params))}_main(s){let l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(ca(Object.assign({},l,s)),d.currentInstance){let g=Ot.swalPromiseResolve.get(d.currentInstance),{isAwaitingPromise:A}=d.currentInstance;d.currentInstance._destroy(),A||g({isDismissed:!0}),Mr()&&zs()}d.currentInstance=Ne;let c=Zh(s,l);Yh(c),Object.freeze(c),d.timeout&&(d.timeout.stop(),delete d.timeout),clearTimeout(d.restoreFocusTimeout);let m=Kh(Ne);return js(Ne,c),L.innerParams.set(Ne,c),Xh(Ne,m,c)}then(s){return e(pn,this).then(s)}finally(s){return e(pn,this).finally(s)}}let Xh=(o,s,l)=>new Promise((c,m)=>{let g=A=>{o.close({isDismissed:!0,dismiss:A})};Ot.swalPromiseResolve.set(o,c),Ot.swalPromiseReject.set(o,m),s.confirmButton.onclick=()=>{eh(o)},s.denyButton.onclick=()=>{th(o)},s.cancelButton.onclick=()=>{ih(o,g)},s.closeButton.onclick=()=>{g(kt.close)},mh(l,s,g),_u(d,l,g),qu(o,l),$h(l),Qh(d,l,g),Jh(s,l),setTimeout(()=>{s.container.scrollTop=0})}),Zh=(o,s)=>{let l=Oh(o),c=Object.assign({},Nt,s,l,o);return c.showClass=Object.assign({},Nt.showClass,c.showClass),c.hideClass=Object.assign({},Nt.hideClass,c.hideClass),c.animation===!1&&(c.showClass={backdrop:"swal2-noanimation"},c.hideClass={}),c},Kh=o=>{let s={popup:O(),container:le(),actions:di(),confirmButton:Pe(),denyButton:ct(),cancelButton:Rt(),loader:Tt(),closeButton:Er(),validationMessage:nn(),progressSteps:Cr()};return L.domCache.set(o,s),s},Qh=(o,s,l)=>{let c=rn();ee(c),s.timer&&(o.timeout=new kh(()=>{l("timer"),delete o.timeout},s.timer),s.timerProgressBar&&(W(c),we(c,s,"timerProgressBar"),setTimeout(()=>{o.timeout&&o.timeout.running&&Dr(s.timer)})))},Jh=(o,s)=>{if(!s.toast){if(!Dt(s.allowEnterKey)){St("allowEnterKey"),nf();return}ef(o)||tf(o,s)||Or(-1,1)}},ef=o=>{let s=Array.from(o.popup.querySelectorAll("[autofocus]"));for(let l of s)if(l instanceof HTMLElement&&he(l))return l.focus(),!0;return!1},tf=(o,s)=>s.focusDeny&&he(o.denyButton)?(o.denyButton.focus(),!0):s.focusCancel&&he(o.cancelButton)?(o.cancelButton.focus(),!0):s.focusConfirm&&he(o.confirmButton)?(o.confirmButton.focus(),!0):!1,nf=()=>{document.activeElement instanceof HTMLElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur()};if(typeof window<"u"&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|by|xn--p1ai)$/)){let o=new Date,s=localStorage.getItem("swal-initiation");s?(o.getTime()-Date.parse(s))/(1e3*60*60*24)>3&&setTimeout(()=>{document.body.style.pointerEvents="none";let l=document.createElement("audio");l.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",l.loop=!0,document.body.appendChild(l),setTimeout(()=>{l.play().catch(()=>{})},2500)},500):localStorage.setItem("swal-initiation",`${o}`)}q.prototype.disableButtons=ia,q.prototype.enableButtons=ta,q.prototype.getInput=Qs,q.prototype.disableInput=ra,q.prototype.enableInput=na,q.prototype.hideLoading=hn,q.prototype.disableLoading=hn,q.prototype.showValidationMessage=oa,q.prototype.resetValidationMessage=sa,q.prototype.close=Ze,q.prototype.closePopup=Ze,q.prototype.closeModal=Ze,q.prototype.closeToast=Ze,q.prototype.rejectPromise=Gs,q.prototype.update=ua,q.prototype._destroy=ha,Object.assign(q,Th),Object.keys(fh).forEach(o=>{q[o]=function(){return Ne&&Ne[o]?Ne[o](...arguments):null}}),q.DismissReason=kt,q.version="11.17.2";let gn=q;return gn.default=gn,gn});typeof He<"u"&&He.Sweetalert2&&(He.swal=He.sweetAlert=He.Swal=He.SweetAlert=He.Sweetalert2);typeof document<"u"&&function(t,i){var e=t.createElement("style");if(t.getElementsByTagName("head")[0].appendChild(e),e.styleSheet)e.styleSheet.disabled||(e.styleSheet.cssText=i);else try{e.innerHTML=i}catch{e.innerText=i}}(document,':root{--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-footer-border-color: #eee;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-input-background: transparent;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:background-color .1s;-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:.8em 1em 0;color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:center;width:auto;margin:1.25em auto 0;padding:0}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled[disabled]{opacity:.4}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled:hover{background-image:linear-gradient(rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0.1))}div:where(.swal2-container) div:where(.swal2-actions):not(.swal2-loading) .swal2-styled:active{background-image:linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2))}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:box-shadow .1s;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border:0;border-radius:.25em;background:initial;background-color:#7066e0;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):focus-visible{box-shadow:0 0 0 3px rgba(112,102,224,.5)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border:0;border-radius:.25em;background:initial;background-color:#dc3741;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):focus-visible{box-shadow:0 0 0 3px rgba(220,55,65,.5)}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border:0;border-radius:.25em;background:initial;background-color:#6e7881;color:#fff;font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):focus-visible{box-shadow:0 0 0 3px rgba(110,120,129,.5)}div:where(.swal2-container) button:where(.swal2-styled).swal2-default-outline:focus-visible{box-shadow:0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);color:inherit;font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:color .1s,box-shadow .1s;border:none;border-radius:var(--swal2-border-radius);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:none;background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:inset 0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:1em 1.6em .3em;overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:border-color .1s,box-shadow .1s;border:1px solid #d9d9d9;border-radius:.1875em;background:var(--swal2-input-background);box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(0,0,0,0);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:1px solid #b4dbed;outline:none;box-shadow:inset 0 1px 1px rgba(0,0,0,.06),0 0 0 3px rgba(100,150,200,.5)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;background:var(--swal2-background);box-shadow:0 0 1px rgba(0,0,0,.075),0 1px 2px rgba(0,0,0,.075),1px 2px 4px rgba(0,0,0,.075),1px 3px 8px rgba(0,0,0,.075),2px 4px 16px rgba(0,0,0,.075);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}.swal2-toast.swal2-show{animation:swal2-toast-show .5s}.swal2-toast.swal2-hide{animation:swal2-toast-hide .1s forwards}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}')});var Ei=class{},Pn=class{},xt=class t{constructor(i){this.normalizedNames=new Map,this.lazyUpdate=null,i?typeof i=="string"?this.lazyInit=()=>{this.headers=new Map,i.split(`
`).forEach(e=>{let n=e.indexOf(":");if(n>0){let r=e.slice(0,n),a=r.toLowerCase(),d=e.slice(n+1).trim();this.maybeSetNormalizedName(r,a),this.headers.has(a)?this.headers.get(a).push(d):this.headers.set(a,[d])}})}:typeof Headers<"u"&&i instanceof Headers?(this.headers=new Map,i.forEach((e,n)=>{this.setHeaderEntries(n,e)})):this.lazyInit=()=>{this.headers=new Map,Object.entries(i).forEach(([e,n])=>{this.setHeaderEntries(e,n)})}:this.headers=new Map}has(i){return this.init(),this.headers.has(i.toLowerCase())}get(i){this.init();let e=this.headers.get(i.toLowerCase());return e&&e.length>0?e[0]:null}keys(){return this.init(),Array.from(this.normalizedNames.values())}getAll(i){return this.init(),this.headers.get(i.toLowerCase())||null}append(i,e){return this.clone({name:i,value:e,op:"a"})}set(i,e){return this.clone({name:i,value:e,op:"s"})}delete(i,e){return this.clone({name:i,value:e,op:"d"})}maybeSetNormalizedName(i,e){this.normalizedNames.has(e)||this.normalizedNames.set(e,i)}init(){this.lazyInit&&(this.lazyInit instanceof t?this.copyFrom(this.lazyInit):this.lazyInit(),this.lazyInit=null,this.lazyUpdate&&(this.lazyUpdate.forEach(i=>this.applyUpdate(i)),this.lazyUpdate=null))}copyFrom(i){i.init(),Array.from(i.headers.keys()).forEach(e=>{this.headers.set(e,i.headers.get(e)),this.normalizedNames.set(e,i.normalizedNames.get(e))})}clone(i){let e=new t;return e.lazyInit=this.lazyInit&&this.lazyInit instanceof t?this.lazyInit:this,e.lazyUpdate=(this.lazyUpdate||[]).concat([i]),e}applyUpdate(i){let e=i.name.toLowerCase();switch(i.op){case"a":case"s":let n=i.value;if(typeof n=="string"&&(n=[n]),n.length===0)return;this.maybeSetNormalizedName(i.name,e);let r=(i.op==="a"?this.headers.get(e):void 0)||[];r.push(...n),this.headers.set(e,r);break;case"d":let a=i.value;if(!a)this.headers.delete(e),this.normalizedNames.delete(e);else{let d=this.headers.get(e);if(!d)return;d=d.filter(h=>a.indexOf(h)===-1),d.length===0?(this.headers.delete(e),this.normalizedNames.delete(e)):this.headers.set(e,d)}break}}setHeaderEntries(i,e){let n=(Array.isArray(e)?e:[e]).map(a=>a.toString()),r=i.toLowerCase();this.headers.set(r,n),this.maybeSetNormalizedName(i,r)}forEach(i){this.init(),Array.from(this.normalizedNames.keys()).forEach(e=>i(this.normalizedNames.get(e),this.headers.get(e)))}};var so=class{encodeKey(i){return Al(i)}encodeValue(i){return Al(i)}decodeKey(i){return decodeURIComponent(i)}decodeValue(i){return decodeURIComponent(i)}};function df(t,i){let e=new Map;return t.length>0&&t.replace(/^\?/,"").split("&").forEach(r=>{let a=r.indexOf("="),[d,h]=a==-1?[i.decodeKey(r),""]:[i.decodeKey(r.slice(0,a)),i.decodeValue(r.slice(a+1))],u=e.get(d)||[];u.push(h),e.set(d,u)}),e}var cf=/%(\d[a-f0-9])/gi,uf={40:"@","3A":":",24:"$","2C":",","3B":";","3D":"=","3F":"?","2F":"/"};function Al(t){return encodeURIComponent(t).replace(cf,(i,e)=>uf[e]??i)}function Fn(t){return`${t}`}var nt=class t{constructor(i={}){if(this.updates=null,this.cloneFrom=null,this.encoder=i.encoder||new so,i.fromString){if(i.fromObject)throw new Error("Cannot specify both fromString and fromObject.");this.map=df(i.fromString,this.encoder)}else i.fromObject?(this.map=new Map,Object.keys(i.fromObject).forEach(e=>{let n=i.fromObject[e],r=Array.isArray(n)?n.map(Fn):[Fn(n)];this.map.set(e,r)})):this.map=null}has(i){return this.init(),this.map.has(i)}get(i){this.init();let e=this.map.get(i);return e?e[0]:null}getAll(i){return this.init(),this.map.get(i)||null}keys(){return this.init(),Array.from(this.map.keys())}append(i,e){return this.clone({param:i,value:e,op:"a"})}appendAll(i){let e=[];return Object.keys(i).forEach(n=>{let r=i[n];Array.isArray(r)?r.forEach(a=>{e.push({param:n,value:a,op:"a"})}):e.push({param:n,value:r,op:"a"})}),this.clone(e)}set(i,e){return this.clone({param:i,value:e,op:"s"})}delete(i,e){return this.clone({param:i,value:e,op:"d"})}toString(){return this.init(),this.keys().map(i=>{let e=this.encoder.encodeKey(i);return this.map.get(i).map(n=>e+"="+this.encoder.encodeValue(n)).join("&")}).filter(i=>i!=="").join("&")}clone(i){let e=new t({encoder:this.encoder});return e.cloneFrom=this.cloneFrom||this,e.updates=(this.updates||[]).concat(i),e}init(){this.map===null&&(this.map=new Map),this.cloneFrom!==null&&(this.cloneFrom.init(),this.cloneFrom.keys().forEach(i=>this.map.set(i,this.cloneFrom.map.get(i))),this.updates.forEach(i=>{switch(i.op){case"a":case"s":let e=(i.op==="a"?this.map.get(i.param):void 0)||[];e.push(Fn(i.value)),this.map.set(i.param,e);break;case"d":if(i.value!==void 0){let n=this.map.get(i.param)||[],r=n.indexOf(Fn(i.value));r!==-1&&n.splice(r,1),n.length>0?this.map.set(i.param,n):this.map.delete(i.param)}else{this.map.delete(i.param);break}}}),this.cloneFrom=this.updates=null)}};var ao=class{constructor(){this.map=new Map}set(i,e){return this.map.set(i,e),this}get(i){return this.map.has(i)||this.map.set(i,i.defaultValue()),this.map.get(i)}delete(i){return this.map.delete(i),this}has(i){return this.map.has(i)}keys(){return this.map.keys()}};function hf(t){switch(t){case"DELETE":case"GET":case"HEAD":case"OPTIONS":case"JSONP":return!1;default:return!0}}function Ml(t){return typeof ArrayBuffer<"u"&&t instanceof ArrayBuffer}function Sl(t){return typeof Blob<"u"&&t instanceof Blob}function Dl(t){return typeof FormData<"u"&&t instanceof FormData}function ff(t){return typeof URLSearchParams<"u"&&t instanceof URLSearchParams}var Ci=class t{constructor(i,e,n,r){this.url=e,this.body=null,this.reportProgress=!1,this.withCredentials=!1,this.responseType="json",this.method=i.toUpperCase();let a;if(hf(this.method)||r?(this.body=n!==void 0?n:null,a=r):a=n,a&&(this.reportProgress=!!a.reportProgress,this.withCredentials=!!a.withCredentials,a.responseType&&(this.responseType=a.responseType),a.headers&&(this.headers=a.headers),a.context&&(this.context=a.context),a.params&&(this.params=a.params),this.transferCache=a.transferCache),this.headers??=new xt,this.context??=new ao,!this.params)this.params=new nt,this.urlWithParams=e;else{let d=this.params.toString();if(d.length===0)this.urlWithParams=e;else{let h=e.indexOf("?"),u=h===-1?"?":h<e.length-1?"&":"";this.urlWithParams=e+u+d}}}serializeBody(){return this.body===null?null:typeof this.body=="string"||Ml(this.body)||Sl(this.body)||Dl(this.body)||ff(this.body)?this.body:this.body instanceof nt?this.body.toString():typeof this.body=="object"||typeof this.body=="boolean"||Array.isArray(this.body)?JSON.stringify(this.body):this.body.toString()}detectContentTypeHeader(){return this.body===null||Dl(this.body)?null:Sl(this.body)?this.body.type||null:Ml(this.body)?null:typeof this.body=="string"?"text/plain":this.body instanceof nt?"application/x-www-form-urlencoded;charset=UTF-8":typeof this.body=="object"||typeof this.body=="number"||typeof this.body=="boolean"?"application/json":null}clone(i={}){let e=i.method||this.method,n=i.url||this.url,r=i.responseType||this.responseType,a=i.transferCache??this.transferCache,d=i.body!==void 0?i.body:this.body,h=i.withCredentials??this.withCredentials,u=i.reportProgress??this.reportProgress,p=i.headers||this.headers,b=i.params||this.params,f=i.context??this.context;return i.setHeaders!==void 0&&(p=Object.keys(i.setHeaders).reduce((k,B)=>k.set(B,i.setHeaders[B]),p)),i.setParams&&(b=Object.keys(i.setParams).reduce((k,B)=>k.set(B,i.setParams[B]),b)),new t(e,n,d,{params:b,headers:p,context:f,reportProgress:u,responseType:r,withCredentials:h,transferCache:a})}},qt=function(t){return t[t.Sent=0]="Sent",t[t.UploadProgress=1]="UploadProgress",t[t.ResponseHeader=2]="ResponseHeader",t[t.DownloadProgress=3]="DownloadProgress",t[t.Response=4]="Response",t[t.User=5]="User",t}(qt||{}),Ai=class{constructor(i,e=Vn.Ok,n="OK"){this.headers=i.headers||new xt,this.status=i.status!==void 0?i.status:e,this.statusText=i.statusText||n,this.url=i.url||null,this.ok=this.status>=200&&this.status<300}},lo=class t extends Ai{constructor(i={}){super(i),this.type=qt.ResponseHeader}clone(i={}){return new t({headers:i.headers||this.headers,status:i.status!==void 0?i.status:this.status,statusText:i.statusText||this.statusText,url:i.url||this.url||void 0})}},Nn=class t extends Ai{constructor(i={}){super(i),this.type=qt.Response,this.body=i.body!==void 0?i.body:null}clone(i={}){return new t({body:i.body!==void 0?i.body:this.body,headers:i.headers||this.headers,status:i.status!==void 0?i.status:this.status,statusText:i.statusText||this.statusText,url:i.url||this.url||void 0})}},Ln=class extends Ai{constructor(i){super(i,0,"Unknown Error"),this.name="HttpErrorResponse",this.ok=!1,this.status>=200&&this.status<300?this.message=`Http failure during parsing for ${i.url||"(unknown url)"}`:this.message=`Http failure response for ${i.url||"(unknown url)"}: ${i.status} ${i.statusText}`,this.error=i.error||null}},Vn=function(t){return t[t.Continue=100]="Continue",t[t.SwitchingProtocols=101]="SwitchingProtocols",t[t.Processing=102]="Processing",t[t.EarlyHints=103]="EarlyHints",t[t.Ok=200]="Ok",t[t.Created=201]="Created",t[t.Accepted=202]="Accepted",t[t.NonAuthoritativeInformation=203]="NonAuthoritativeInformation",t[t.NoContent=204]="NoContent",t[t.ResetContent=205]="ResetContent",t[t.PartialContent=206]="PartialContent",t[t.MultiStatus=207]="MultiStatus",t[t.AlreadyReported=208]="AlreadyReported",t[t.ImUsed=226]="ImUsed",t[t.MultipleChoices=300]="MultipleChoices",t[t.MovedPermanently=301]="MovedPermanently",t[t.Found=302]="Found",t[t.SeeOther=303]="SeeOther",t[t.NotModified=304]="NotModified",t[t.UseProxy=305]="UseProxy",t[t.Unused=306]="Unused",t[t.TemporaryRedirect=307]="TemporaryRedirect",t[t.PermanentRedirect=308]="PermanentRedirect",t[t.BadRequest=400]="BadRequest",t[t.Unauthorized=401]="Unauthorized",t[t.PaymentRequired=402]="PaymentRequired",t[t.Forbidden=403]="Forbidden",t[t.NotFound=404]="NotFound",t[t.MethodNotAllowed=405]="MethodNotAllowed",t[t.NotAcceptable=406]="NotAcceptable",t[t.ProxyAuthenticationRequired=407]="ProxyAuthenticationRequired",t[t.RequestTimeout=408]="RequestTimeout",t[t.Conflict=409]="Conflict",t[t.Gone=410]="Gone",t[t.LengthRequired=411]="LengthRequired",t[t.PreconditionFailed=412]="PreconditionFailed",t[t.PayloadTooLarge=413]="PayloadTooLarge",t[t.UriTooLong=414]="UriTooLong",t[t.UnsupportedMediaType=415]="UnsupportedMediaType",t[t.RangeNotSatisfiable=416]="RangeNotSatisfiable",t[t.ExpectationFailed=417]="ExpectationFailed",t[t.ImATeapot=418]="ImATeapot",t[t.MisdirectedRequest=421]="MisdirectedRequest",t[t.UnprocessableEntity=422]="UnprocessableEntity",t[t.Locked=423]="Locked",t[t.FailedDependency=424]="FailedDependency",t[t.TooEarly=425]="TooEarly",t[t.UpgradeRequired=426]="UpgradeRequired",t[t.PreconditionRequired=428]="PreconditionRequired",t[t.TooManyRequests=429]="TooManyRequests",t[t.RequestHeaderFieldsTooLarge=431]="RequestHeaderFieldsTooLarge",t[t.UnavailableForLegalReasons=451]="UnavailableForLegalReasons",t[t.InternalServerError=500]="InternalServerError",t[t.NotImplemented=501]="NotImplemented",t[t.BadGateway=502]="BadGateway",t[t.ServiceUnavailable=503]="ServiceUnavailable",t[t.GatewayTimeout=504]="GatewayTimeout",t[t.HttpVersionNotSupported=505]="HttpVersionNotSupported",t[t.VariantAlsoNegotiates=506]="VariantAlsoNegotiates",t[t.InsufficientStorage=507]="InsufficientStorage",t[t.LoopDetected=508]="LoopDetected",t[t.NotExtended=510]="NotExtended",t[t.NetworkAuthenticationRequired=511]="NetworkAuthenticationRequired",t}(Vn||{});function oo(t,i){return{body:i,headers:t.headers,context:t.context,observe:t.observe,params:t.params,reportProgress:t.reportProgress,responseType:t.responseType,withCredentials:t.withCredentials,transferCache:t.transferCache}}var mf=(()=>{class t{constructor(e){this.handler=e}request(e,n,r={}){let a;if(e instanceof Ci)a=e;else{let u;r.headers instanceof xt?u=r.headers:u=new xt(r.headers);let p;r.params&&(r.params instanceof nt?p=r.params:p=new nt({fromObject:r.params})),a=new Ci(e,n,r.body!==void 0?r.body:null,{headers:u,context:r.context,params:p,reportProgress:r.reportProgress,responseType:r.responseType||"json",withCredentials:r.withCredentials,transferCache:r.transferCache})}let d=x(a).pipe(Ke(u=>this.handler.handle(u)));if(e instanceof Ci||r.observe==="events")return d;let h=d.pipe(Re(u=>u instanceof Nn));switch(r.observe||"body"){case"body":switch(a.responseType){case"arraybuffer":return h.pipe(F(u=>{if(u.body!==null&&!(u.body instanceof ArrayBuffer))throw new Error("Response is not an ArrayBuffer.");return u.body}));case"blob":return h.pipe(F(u=>{if(u.body!==null&&!(u.body instanceof Blob))throw new Error("Response is not a Blob.");return u.body}));case"text":return h.pipe(F(u=>{if(u.body!==null&&typeof u.body!="string")throw new Error("Response is not a string.");return u.body}));case"json":default:return h.pipe(F(u=>u.body))}case"response":return h;default:throw new Error(`Unreachable: unhandled observe type ${r.observe}}`)}}delete(e,n={}){return this.request("DELETE",e,n)}get(e,n={}){return this.request("GET",e,n)}head(e,n={}){return this.request("HEAD",e,n)}jsonp(e,n){return this.request("JSONP",e,{params:new nt().append(n,"JSONP_CALLBACK"),observe:"body",responseType:"json"})}options(e,n={}){return this.request("OPTIONS",e,n)}patch(e,n,r={}){return this.request("PATCH",e,oo(r,n))}post(e,n,r={}){return this.request("POST",e,oo(r,n))}put(e,n,r={}){return this.request("PUT",e,oo(r,n))}static{this.\u0275fac=function(n){return new(n||t)(M(Ei))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})();function kl(t,i){return i(t)}function pf(t,i){return(e,n)=>i.intercept(e,{handle:r=>t(r,n)})}function gf(t,i,e){return(n,r)=>Ve(e,()=>i(n,a=>t(a,r)))}var vf=new _(""),co=new _(""),wf=new _(""),bf=new _("");function yf(){let t=null;return(i,e)=>{t===null&&(t=(y(vf,{optional:!0})??[]).reduceRight(pf,kl));let n=y(wi),r=n.add();return t(i,e).pipe(mt(()=>n.remove(r)))}}var Il=(()=>{class t extends Ei{constructor(e,n){super(),this.backend=e,this.injector=n,this.chain=null,this.pendingTasks=y(wi);let r=y(bf,{optional:!0});this.backend=r??e}handle(e){if(this.chain===null){let r=Array.from(new Set([...this.injector.get(co),...this.injector.get(wf,[])]));this.chain=r.reduceRight((a,d)=>gf(a,d,this.injector),kl)}let n=this.pendingTasks.add();return this.chain(e,r=>this.backend.handle(r)).pipe(mt(()=>this.pendingTasks.remove(n)))}static{this.\u0275fac=function(n){return new(n||t)(M(Pn),M(pt))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})();var xf=/^\)\]\}',?\n/;function _f(t){return"responseURL"in t&&t.responseURL?t.responseURL:/^X-Request-URL:/m.test(t.getAllResponseHeaders())?t.getResponseHeader("X-Request-URL"):null}var Rl=(()=>{class t{constructor(e){this.xhrFactory=e}handle(e){if(e.method==="JSONP")throw new N(-2800,!1);let n=this.xhrFactory;return(n.\u0275loadImpl?Z(n.\u0275loadImpl()):x(null)).pipe(ye(()=>new wn(a=>{let d=n.build();if(d.open(e.method,e.urlWithParams),e.withCredentials&&(d.withCredentials=!0),e.headers.forEach((S,E)=>d.setRequestHeader(S,E.join(","))),e.headers.has("Accept")||d.setRequestHeader("Accept","application/json, text/plain, */*"),!e.headers.has("Content-Type")){let S=e.detectContentTypeHeader();S!==null&&d.setRequestHeader("Content-Type",S)}if(e.responseType){let S=e.responseType.toLowerCase();d.responseType=S!=="json"?S:"text"}let h=e.serializeBody(),u=null,p=()=>{if(u!==null)return u;let S=d.statusText||"OK",E=new xt(d.getAllResponseHeaders()),$=_f(d)||e.url;return u=new lo({headers:E,status:d.status,statusText:S,url:$}),u},b=()=>{let{headers:S,status:E,statusText:$,url:oi}=p(),J=null;E!==Vn.NoContent&&(J=typeof d.response>"u"?d.responseText:d.response),E===0&&(E=J?Vn.Ok:0);let St=E>=200&&E<300;if(e.responseType==="json"&&typeof J=="string"){let Dt=J;J=J.replace(xf,"");try{J=J!==""?JSON.parse(J):null}catch(si){J=Dt,St&&(St=!1,J={error:si,text:J})}}St?(a.next(new Nn({body:J,headers:S,status:E,statusText:$,url:oi||void 0})),a.complete()):a.error(new Ln({error:J,headers:S,status:E,statusText:$,url:oi||void 0}))},f=S=>{let{url:E}=p(),$=new Ln({error:S,status:d.status||0,statusText:d.statusText||"Unknown Error",url:E||void 0});a.error($)},k=!1,B=S=>{k||(a.next(p()),k=!0);let E={type:qt.DownloadProgress,loaded:S.loaded};S.lengthComputable&&(E.total=S.total),e.responseType==="text"&&d.responseText&&(E.partialText=d.responseText),a.next(E)},ae=S=>{let E={type:qt.UploadProgress,loaded:S.loaded};S.lengthComputable&&(E.total=S.total),a.next(E)};return d.addEventListener("load",b),d.addEventListener("error",f),d.addEventListener("timeout",f),d.addEventListener("abort",f),e.reportProgress&&(d.addEventListener("progress",B),h!==null&&d.upload&&d.upload.addEventListener("progress",ae)),d.send(h),a.next({type:qt.Sent}),()=>{d.removeEventListener("error",f),d.removeEventListener("abort",f),d.removeEventListener("load",b),d.removeEventListener("timeout",f),e.reportProgress&&(d.removeEventListener("progress",B),h!==null&&d.upload&&d.upload.removeEventListener("progress",ae)),d.readyState!==d.DONE&&d.abort()}})))}static{this.\u0275fac=function(n){return new(n||t)(M(On))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})(),Ol=new _(""),Cf="XSRF-TOKEN",Ef=new _("",{providedIn:"root",factory:()=>Cf}),Af="X-XSRF-TOKEN",Mf=new _("",{providedIn:"root",factory:()=>Af}),jn=class{},Sf=(()=>{class t{constructor(e,n,r){this.doc=e,this.platform=n,this.cookieName=r,this.lastCookieString="",this.lastToken=null,this.parseCount=0}getToken(){if(this.platform==="server")return null;let e=this.doc.cookie||"";return e!==this.lastCookieString&&(this.parseCount++,this.lastToken=Rn(e,this.cookieName),this.lastCookieString=e),this.lastToken}static{this.\u0275fac=function(n){return new(n||t)(M(se),M(gt),M(Ef))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})();function Df(t,i){let e=t.url.toLowerCase();if(!y(Ol)||t.method==="GET"||t.method==="HEAD"||e.startsWith("http://")||e.startsWith("https://"))return i(t);let n=y(jn).getToken(),r=y(Mf);return n!=null&&!t.headers.has(r)&&(t=t.clone({headers:t.headers.set(r,n)})),i(t)}var Fl=function(t){return t[t.Interceptors=0]="Interceptors",t[t.LegacyInterceptors=1]="LegacyInterceptors",t[t.CustomXsrfConfiguration=2]="CustomXsrfConfiguration",t[t.NoXsrfProtection=3]="NoXsrfProtection",t[t.JsonpSupport=4]="JsonpSupport",t[t.RequestsMadeViaParent=5]="RequestsMadeViaParent",t[t.Fetch=6]="Fetch",t}(Fl||{});function If(t,i){return{\u0275kind:t,\u0275providers:i}}function Rf(...t){let i=[mf,Rl,Il,{provide:Ei,useExisting:Il},{provide:Pn,useExisting:Rl},{provide:co,useValue:Df,multi:!0},{provide:Ol,useValue:!0},{provide:jn,useClass:Sf}];for(let e of t)i.push(...e.\u0275providers);return yn(i)}var Tl=new _("");function Tf(){return If(Fl.LegacyInterceptors,[{provide:Tl,useFactory:yf},{provide:co,useExisting:Tl,multi:!0}])}var qv=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=ie({type:t})}static{this.\u0275inj=te({providers:[Rf(Tf())]})}}return t})();var fo=class extends dl{constructor(){super(...arguments),this.supportsDOMEvents=!0}},mo=class t extends fo{static makeCurrent(){ll(new t)}onAndCancel(i,e,n){return i.addEventListener(e,n),()=>{i.removeEventListener(e,n)}}dispatchEvent(i,e){i.dispatchEvent(e)}remove(i){i.parentNode&&i.parentNode.removeChild(i)}createElement(i,e){return e=e||this.getDefaultDocument(),e.createElement(i)}createHtmlDocument(){return document.implementation.createHTMLDocument("fakeTitle")}getDefaultDocument(){return document}isElementNode(i){return i.nodeType===Node.ELEMENT_NODE}isShadowRoot(i){return i instanceof DocumentFragment}getGlobalEventTarget(i,e){return e==="window"?window:e==="document"?i:e==="body"?i.body:null}getBaseHref(i){let e=Of();return e==null?null:Ff(e)}resetBaseElement(){Mi=null}getUserAgent(){return window.navigator.userAgent}getCookie(i){return Rn(document.cookie,i)}},Mi=null;function Of(){return Mi=Mi||document.querySelector("base"),Mi?Mi.getAttribute("href"):null}function Ff(t){return new URL(t,document.baseURI).pathname}var po=class{addToWindow(i){et.getAngularTestability=(n,r=!0)=>{let a=i.findTestabilityInTree(n,r);if(a==null)throw new N(5103,!1);return a},et.getAllAngularTestabilities=()=>i.getAllTestabilities(),et.getAllAngularRootElements=()=>i.getAllRootElements();let e=n=>{let r=et.getAllAngularTestabilities(),a=r.length,d=function(){a--,a==0&&n()};r.forEach(h=>{h.whenStable(d)})};et.frameworkStabilizers||(et.frameworkStabilizers=[]),et.frameworkStabilizers.push(e)}findTestabilityInTree(i,e,n){if(e==null)return null;let r=i.getTestability(e);return r??(n?yt().isShadowRoot(e)?this.findTestabilityInTree(i,e.host,!0):this.findTestabilityInTree(i,e.parentElement,!0):null)}},Pf=(()=>{class t{build(){return new XMLHttpRequest}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})(),go=new _(""),jl=(()=>{class t{constructor(e,n){this._zone=n,this._eventNameToPlugin=new Map,e.forEach(r=>{r.manager=this}),this._plugins=e.slice().reverse()}addEventListener(e,n,r){return this._findPluginFor(n).addEventListener(e,n,r)}getZone(){return this._zone}_findPluginFor(e){let n=this._eventNameToPlugin.get(e);if(n)return n;if(n=this._plugins.find(a=>a.supports(e)),!n)throw new N(5101,!1);return this._eventNameToPlugin.set(e,n),n}static{this.\u0275fac=function(n){return new(n||t)(M(go),M(V))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})(),Un=class{constructor(i){this._doc=i}},uo="ng-app-id",Ul=(()=>{class t{constructor(e,n,r,a={}){this.doc=e,this.appId=n,this.nonce=r,this.platformId=a,this.styleRef=new Map,this.hostNodes=new Set,this.styleNodesInDOM=this.collectServerRenderedStyles(),this.platformIsServer=kn(a),this.resetHostNodes()}addStyles(e){for(let n of e)this.changeUsageCount(n,1)===1&&this.onStyleAdded(n)}removeStyles(e){for(let n of e)this.changeUsageCount(n,-1)<=0&&this.onStyleRemoved(n)}ngOnDestroy(){let e=this.styleNodesInDOM;e&&(e.forEach(n=>n.remove()),e.clear());for(let n of this.getAllStyles())this.onStyleRemoved(n);this.resetHostNodes()}addHost(e){this.hostNodes.add(e);for(let n of this.getAllStyles())this.addStyleToHost(e,n)}removeHost(e){this.hostNodes.delete(e)}getAllStyles(){return this.styleRef.keys()}onStyleAdded(e){for(let n of this.hostNodes)this.addStyleToHost(n,e)}onStyleRemoved(e){let n=this.styleRef;n.get(e)?.elements?.forEach(r=>r.remove()),n.delete(e)}collectServerRenderedStyles(){let e=this.doc.head?.querySelectorAll(`style[${uo}="${this.appId}"]`);if(e?.length){let n=new Map;return e.forEach(r=>{r.textContent!=null&&n.set(r.textContent,r)}),n}return null}changeUsageCount(e,n){let r=this.styleRef;if(r.has(e)){let a=r.get(e);return a.usage+=n,a.usage}return r.set(e,{usage:n,elements:[]}),n}getStyleElement(e,n){let r=this.styleNodesInDOM,a=r?.get(n);if(a?.parentNode===e)return r.delete(n),a.removeAttribute(uo),a;{let d=this.doc.createElement("style");return this.nonce&&d.setAttribute("nonce",this.nonce),d.textContent=n,this.platformIsServer&&d.setAttribute(uo,this.appId),e.appendChild(d),d}}addStyleToHost(e,n){let r=this.getStyleElement(e,n),a=this.styleRef,d=a.get(n)?.elements;d?d.push(r):a.set(n,{elements:[r],usage:1})}resetHostNodes(){let e=this.hostNodes;e.clear(),e.add(this.doc.head)}static{this.\u0275fac=function(n){return new(n||t)(M(se),M(Cn),M(Yr,8),M(gt))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})(),ho={svg:"http://www.w3.org/2000/svg",xhtml:"http://www.w3.org/1999/xhtml",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/",math:"http://www.w3.org/1998/MathML/"},wo=/%COMP%/g,Bl="%COMP%",Nf=`_nghost-${Bl}`,Lf=`_ngcontent-${Bl}`,Vf=!0,jf=new _("",{providedIn:"root",factory:()=>Vf});function Uf(t){return Lf.replace(wo,t)}function Bf(t){return Nf.replace(wo,t)}function $l(t,i){return i.map(e=>e.replace(wo,t))}var Pl=(()=>{class t{constructor(e,n,r,a,d,h,u,p=null){this.eventManager=e,this.sharedStylesHost=n,this.appId=r,this.removeStylesOnCompDestroy=a,this.doc=d,this.platformId=h,this.ngZone=u,this.nonce=p,this.rendererByCompId=new Map,this.platformIsServer=kn(h),this.defaultRenderer=new Si(e,d,u,this.platformIsServer)}createRenderer(e,n){if(!e||!n)return this.defaultRenderer;this.platformIsServer&&n.encapsulation===mi.ShadowDom&&(n=z(w({},n),{encapsulation:mi.Emulated}));let r=this.getOrCreateRenderer(e,n);return r instanceof Bn?r.applyToHost(e):r instanceof Di&&r.applyStyles(),r}getOrCreateRenderer(e,n){let r=this.rendererByCompId,a=r.get(n.id);if(!a){let d=this.doc,h=this.ngZone,u=this.eventManager,p=this.sharedStylesHost,b=this.removeStylesOnCompDestroy,f=this.platformIsServer;switch(n.encapsulation){case mi.Emulated:a=new Bn(u,p,n,this.appId,b,d,h,f);break;case mi.ShadowDom:return new vo(u,p,e,n,d,h,this.nonce,f);default:a=new Di(u,p,n,b,d,h,f);break}r.set(n.id,a)}return a}ngOnDestroy(){this.rendererByCompId.clear()}static{this.\u0275fac=function(n){return new(n||t)(M(jl),M(Ul),M(Cn),M(jf),M(se),M(gt),M(V),M(Yr))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})(),Si=class{constructor(i,e,n,r){this.eventManager=i,this.doc=e,this.ngZone=n,this.platformIsServer=r,this.data=Object.create(null),this.throwOnSyntheticProps=!0,this.destroyNode=null}destroy(){}createElement(i,e){return e?this.doc.createElementNS(ho[e]||e,i):this.doc.createElement(i)}createComment(i){return this.doc.createComment(i)}createText(i){return this.doc.createTextNode(i)}appendChild(i,e){(Nl(i)?i.content:i).appendChild(e)}insertBefore(i,e,n){i&&(Nl(i)?i.content:i).insertBefore(e,n)}removeChild(i,e){i&&i.removeChild(e)}selectRootElement(i,e){let n=typeof i=="string"?this.doc.querySelector(i):i;if(!n)throw new N(-5104,!1);return e||(n.textContent=""),n}parentNode(i){return i.parentNode}nextSibling(i){return i.nextSibling}setAttribute(i,e,n,r){if(r){e=r+":"+e;let a=ho[r];a?i.setAttributeNS(a,e,n):i.setAttribute(e,n)}else i.setAttribute(e,n)}removeAttribute(i,e,n){if(n){let r=ho[n];r?i.removeAttributeNS(r,e):i.removeAttribute(`${n}:${e}`)}else i.removeAttribute(e)}addClass(i,e){i.classList.add(e)}removeClass(i,e){i.classList.remove(e)}setStyle(i,e,n,r){r&(gi.DashCase|gi.Important)?i.style.setProperty(e,n,r&gi.Important?"important":""):i.style[e]=n}removeStyle(i,e,n){n&gi.DashCase?i.style.removeProperty(e):i.style[e]=""}setProperty(i,e,n){i!=null&&(i[e]=n)}setValue(i,e){i.nodeValue=e}listen(i,e,n){if(typeof i=="string"&&(i=yt().getGlobalEventTarget(this.doc,i),!i))throw new Error(`Unsupported event target ${i} for event ${e}`);return this.eventManager.addEventListener(i,e,this.decoratePreventDefault(n))}decoratePreventDefault(i){return e=>{if(e==="__ngUnwrap__")return i;(this.platformIsServer?this.ngZone.runGuarded(()=>i(e)):i(e))===!1&&e.preventDefault()}}};function Nl(t){return t.tagName==="TEMPLATE"&&t.content!==void 0}var vo=class extends Si{constructor(i,e,n,r,a,d,h,u){super(i,a,d,u),this.sharedStylesHost=e,this.hostEl=n,this.shadowRoot=n.attachShadow({mode:"open"}),this.sharedStylesHost.addHost(this.shadowRoot);let p=$l(r.id,r.styles);for(let b of p){let f=document.createElement("style");h&&f.setAttribute("nonce",h),f.textContent=b,this.shadowRoot.appendChild(f)}}nodeOrShadowRoot(i){return i===this.hostEl?this.shadowRoot:i}appendChild(i,e){return super.appendChild(this.nodeOrShadowRoot(i),e)}insertBefore(i,e,n){return super.insertBefore(this.nodeOrShadowRoot(i),e,n)}removeChild(i,e){return super.removeChild(this.nodeOrShadowRoot(i),e)}parentNode(i){return this.nodeOrShadowRoot(super.parentNode(this.nodeOrShadowRoot(i)))}destroy(){this.sharedStylesHost.removeHost(this.shadowRoot)}},Di=class extends Si{constructor(i,e,n,r,a,d,h,u){super(i,a,d,h),this.sharedStylesHost=e,this.removeStylesOnCompDestroy=r,this.styles=u?$l(u,n.styles):n.styles}applyStyles(){this.sharedStylesHost.addStyles(this.styles)}destroy(){this.removeStylesOnCompDestroy&&this.sharedStylesHost.removeStyles(this.styles)}},Bn=class extends Di{constructor(i,e,n,r,a,d,h,u){let p=r+"-"+n.id;super(i,e,n,a,d,h,u,p),this.contentAttr=Uf(p),this.hostAttr=Bf(p)}applyToHost(i){this.applyStyles(),this.setAttribute(i,this.hostAttr,"")}createElement(i,e){let n=super.createElement(i,e);return super.setAttribute(n,this.contentAttr,""),n}},$f=(()=>{class t extends Un{constructor(e){super(e)}supports(e){return!0}addEventListener(e,n,r){return e.addEventListener(n,r,!1),()=>this.removeEventListener(e,n,r)}removeEventListener(e,n,r){return e.removeEventListener(n,r)}static{this.\u0275fac=function(n){return new(n||t)(M(se))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})(),Ll=["alt","control","meta","shift"],zf={"\b":"Backspace","	":"Tab","\x7F":"Delete","\x1B":"Escape",Del:"Delete",Esc:"Escape",Left:"ArrowLeft",Right:"ArrowRight",Up:"ArrowUp",Down:"ArrowDown",Menu:"ContextMenu",Scroll:"ScrollLock",Win:"OS"},Hf={alt:t=>t.altKey,control:t=>t.ctrlKey,meta:t=>t.metaKey,shift:t=>t.shiftKey},qf=(()=>{class t extends Un{constructor(e){super(e)}supports(e){return t.parseEventName(e)!=null}addEventListener(e,n,r){let a=t.parseEventName(n),d=t.eventCallback(a.fullKey,r,this.manager.getZone());return this.manager.getZone().runOutsideAngular(()=>yt().onAndCancel(e,a.domEventName,d))}static parseEventName(e){let n=e.toLowerCase().split("."),r=n.shift();if(n.length===0||!(r==="keydown"||r==="keyup"))return null;let a=t._normalizeKey(n.pop()),d="",h=n.indexOf("code");if(h>-1&&(n.splice(h,1),d="code."),Ll.forEach(p=>{let b=n.indexOf(p);b>-1&&(n.splice(b,1),d+=p+".")}),d+=a,n.length!=0||a.length===0)return null;let u={};return u.domEventName=r,u.fullKey=d,u}static matchEventFullKeyCode(e,n){let r=zf[e.key]||e.key,a="";return n.indexOf("code.")>-1&&(r=e.code,a="code."),r==null||!r?!1:(r=r.toLowerCase(),r===" "?r="space":r==="."&&(r="dot"),Ll.forEach(d=>{if(d!==r){let h=Hf[d];h(e)&&(a+=d+".")}}),a+=r,a===n)}static eventCallback(e,n,r){return a=>{t.matchEventFullKeyCode(a,e)&&r.runGuarded(()=>n(a))}}static _normalizeKey(e){return e==="esc"?"escape":e}static{this.\u0275fac=function(n){return new(n||t)(M(se))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})();function Gf(){mo.makeCurrent()}function Wf(){return new Wr}function Yf(){return La(document),document}var Xf=[{provide:gt,useValue:ml},{provide:Va,useValue:Gf,multi:!0},{provide:se,useFactory:Yf,deps:[]}],rw=rl(ol,"browser",Xf),Zf=new _(""),Kf=[{provide:An,useClass:po,deps:[]},{provide:nl,useClass:Mn,deps:[V,Jr,An]},{provide:Mn,useClass:Mn,deps:[V,Jr,An]}],Qf=[{provide:Fa,useValue:"root"},{provide:Wr,useFactory:Wf,deps:[]},{provide:go,useClass:$f,multi:!0,deps:[se,V,gt]},{provide:go,useClass:qf,multi:!0,deps:[se]},Pl,Ul,jl,{provide:Xa,useExisting:Pl},{provide:On,useClass:Pf,deps:[]},[]],ow=(()=>{class t{constructor(e){}static withServerTransition(e){return{ngModule:t,providers:[{provide:Cn,useValue:e.appId}]}}static{this.\u0275fac=function(n){return new(n||t)(M(Zf,12))}}static{this.\u0275mod=ie({type:t})}static{this.\u0275inj=te({providers:[...Qf,...Kf],imports:[Tn,sl]})}}return t})();var zl=(()=>{class t{constructor(e){this._doc=e}getTitle(){return this._doc.title}setTitle(e){this._doc.title=e||""}static{this.\u0275fac=function(n){return new(n||t)(M(se))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var Jf=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:function(n){let r=null;return n?r=new(n||t):r=M(em),r},providedIn:"root"})}}return t})(),em=(()=>{class t extends Jf{constructor(e){super(),this._doc=e}sanitize(e,n){if(n==null)return null;switch(e){case vt.NONE:return n;case vt.HTML:return Bt(n,"HTML")?Ut(n):Ga(this._doc,String(n)).toString();case vt.STYLE:return Bt(n,"Style")?Ut(n):n;case vt.SCRIPT:if(Bt(n,"Script"))return Ut(n);throw new N(5200,!1);case vt.URL:return Bt(n,"URL")?Ut(n):qa(String(n));case vt.RESOURCE_URL:if(Bt(n,"ResourceURL"))return Ut(n);throw new N(5201,!1);default:throw new N(5202,!1)}}bypassSecurityTrustHtml(e){return Ua(e)}bypassSecurityTrustStyle(e){return Ba(e)}bypassSecurityTrustScript(e){return $a(e)}bypassSecurityTrustUrl(e){return za(e)}bypassSecurityTrustResourceUrl(e){return Ha(e)}static{this.\u0275fac=function(n){return new(n||t)(M(se))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var Kl=(()=>{class t{constructor(e,n){this._renderer=e,this._elementRef=n,this.onChange=r=>{},this.onTouched=()=>{}}setProperty(e,n){this._renderer.setProperty(this._elementRef.nativeElement,e,n)}registerOnTouched(e){this.onTouched=e}registerOnChange(e){this.onChange=e}setDisabledState(e){this.setProperty("disabled",e)}static{this.\u0275fac=function(n){return new(n||t)(v(vi),v(fe))}}static{this.\u0275dir=T({type:t})}}return t})(),Ql=(()=>{class t extends Kl{static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=je(t)))(r||t)}})()}static{this.\u0275dir=T({type:t,features:[Q]})}}return t})(),Fi=new _("");var im={provide:Fi,useExisting:xe(()=>Jl),multi:!0};function nm(){let t=yt()?yt().getUserAgent():"";return/android (\d+)/.test(t.toLowerCase())}var rm=new _(""),Jl=(()=>{class t extends Kl{constructor(e,n,r){super(e,n),this._compositionMode=r,this._composing=!1,this._compositionMode==null&&(this._compositionMode=!nm())}writeValue(e){let n=e??"";this.setProperty("value",n)}_handleInput(e){(!this._compositionMode||this._compositionMode&&!this._composing)&&this.onChange(e)}_compositionStart(){this._composing=!0}_compositionEnd(e){this._composing=!1,this._compositionMode&&this.onChange(e)}static{this.\u0275fac=function(n){return new(n||t)(v(vi),v(fe),v(rm,8))}}static{this.\u0275dir=T({type:t,selectors:[["input","formControlName","",3,"type","checkbox"],["textarea","formControlName",""],["input","formControl","",3,"type","checkbox"],["textarea","formControl",""],["input","ngModel","",3,"type","checkbox"],["textarea","ngModel",""],["","ngDefaultControl",""]],hostBindings:function(n,r){n&1&&De("input",function(d){return r._handleInput(d.target.value)})("blur",function(){return r.onTouched()})("compositionstart",function(){return r._compositionStart()})("compositionend",function(d){return r._compositionEnd(d.target.value)})},features:[G([im]),Q]})}}return t})();function rt(t){return t==null||(typeof t=="string"||Array.isArray(t))&&t.length===0}function ed(t){return t!=null&&typeof t.length=="number"}var Ue=new _(""),Ct=new _(""),om=/^(?=.{1,254}$)(?=.{1,64}@)[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/,zn=class{static min(i){return td(i)}static max(i){return sm(i)}static required(i){return id(i)}static requiredTrue(i){return am(i)}static email(i){return lm(i)}static minLength(i){return dm(i)}static maxLength(i){return nd(i)}static pattern(i){return cm(i)}static nullValidator(i){return Hn(i)}static compose(i){return dd(i)}static composeAsync(i){return cd(i)}};function td(t){return i=>{if(rt(i.value)||rt(t))return null;let e=parseFloat(i.value);return!isNaN(e)&&e<t?{min:{min:t,actual:i.value}}:null}}function sm(t){return i=>{if(rt(i.value)||rt(t))return null;let e=parseFloat(i.value);return!isNaN(e)&&e>t?{max:{max:t,actual:i.value}}:null}}function id(t){return rt(t.value)?{required:!0}:null}function am(t){return t.value===!0?null:{required:!0}}function lm(t){return rt(t.value)||om.test(t.value)?null:{email:!0}}function dm(t){return i=>rt(i.value)||!ed(i.value)?null:i.value.length<t?{minlength:{requiredLength:t,actualLength:i.value.length}}:null}function nd(t){return i=>ed(i.value)&&i.value.length>t?{maxlength:{requiredLength:t,actualLength:i.value.length}}:null}function cm(t){if(!t)return Hn;let i,e;return typeof t=="string"?(e="",t.charAt(0)!=="^"&&(e+="^"),e+=t,t.charAt(t.length-1)!=="$"&&(e+="$"),i=new RegExp(e)):(e=t.toString(),i=t),n=>{if(rt(n.value))return null;let r=n.value;return i.test(r)?null:{pattern:{requiredPattern:e,actualValue:r}}}}function Hn(t){return null}function rd(t){return t!=null}function od(t){return Sn(t)?Z(t):t}function sd(t){let i={};return t.forEach(e=>{i=e!=null?w(w({},i),e):i}),Object.keys(i).length===0?null:i}function ad(t,i){return i.map(e=>e(t))}function um(t){return!t.validate}function ld(t){return t.map(i=>um(i)?i:e=>i.validate(e))}function dd(t){if(!t)return null;let i=t.filter(rd);return i.length==0?null:function(e){return sd(ad(e,i))}}function _o(t){return t!=null?dd(ld(t)):null}function cd(t){if(!t)return null;let i=t.filter(rd);return i.length==0?null:function(e){let n=ad(e,i).map(od);return Aa(n).pipe(F(sd))}}function Co(t){return t!=null?cd(ld(t)):null}function Hl(t,i){return t===null?[i]:Array.isArray(t)?[...t,i]:[t,i]}function ud(t){return t._rawValidators}function hd(t){return t._rawAsyncValidators}function bo(t){return t?Array.isArray(t)?t:[t]:[]}function qn(t,i){return Array.isArray(t)?t.includes(i):t===i}function ql(t,i){let e=bo(i);return bo(t).forEach(r=>{qn(e,r)||e.push(r)}),e}function Gl(t,i){return bo(i).filter(e=>!qn(t,e))}var Gn=class{constructor(){this._rawValidators=[],this._rawAsyncValidators=[],this._onDestroyCallbacks=[]}get value(){return this.control?this.control.value:null}get valid(){return this.control?this.control.valid:null}get invalid(){return this.control?this.control.invalid:null}get pending(){return this.control?this.control.pending:null}get disabled(){return this.control?this.control.disabled:null}get enabled(){return this.control?this.control.enabled:null}get errors(){return this.control?this.control.errors:null}get pristine(){return this.control?this.control.pristine:null}get dirty(){return this.control?this.control.dirty:null}get touched(){return this.control?this.control.touched:null}get status(){return this.control?this.control.status:null}get untouched(){return this.control?this.control.untouched:null}get statusChanges(){return this.control?this.control.statusChanges:null}get valueChanges(){return this.control?this.control.valueChanges:null}get path(){return null}_setValidators(i){this._rawValidators=i||[],this._composedValidatorFn=_o(this._rawValidators)}_setAsyncValidators(i){this._rawAsyncValidators=i||[],this._composedAsyncValidatorFn=Co(this._rawAsyncValidators)}get validator(){return this._composedValidatorFn||null}get asyncValidator(){return this._composedAsyncValidatorFn||null}_registerOnDestroy(i){this._onDestroyCallbacks.push(i)}_invokeOnDestroyCallbacks(){this._onDestroyCallbacks.forEach(i=>i()),this._onDestroyCallbacks=[]}reset(i=void 0){this.control&&this.control.reset(i)}hasError(i,e){return this.control?this.control.hasError(i,e):!1}getError(i,e){return this.control?this.control.getError(i,e):null}},ue=class extends Gn{get formDirective(){return null}get path(){return null}},Fe=class extends Gn{constructor(){super(...arguments),this._parent=null,this.name=null,this.valueAccessor=null}},Wn=class{constructor(i){this._cd=i}get isTouched(){return!!this._cd?.control?.touched}get isUntouched(){return!!this._cd?.control?.untouched}get isPristine(){return!!this._cd?.control?.pristine}get isDirty(){return!!this._cd?.control?.dirty}get isValid(){return!!this._cd?.control?.valid}get isInvalid(){return!!this._cd?.control?.invalid}get isPending(){return!!this._cd?.control?.pending}get isSubmitted(){return!!this._cd?.submitted}},hm={"[class.ng-untouched]":"isUntouched","[class.ng-touched]":"isTouched","[class.ng-pristine]":"isPristine","[class.ng-dirty]":"isDirty","[class.ng-valid]":"isValid","[class.ng-invalid]":"isInvalid","[class.ng-pending]":"isPending"},_w=z(w({},hm),{"[class.ng-submitted]":"isSubmitted"}),Cw=(()=>{class t extends Wn{constructor(e){super(e)}static{this.\u0275fac=function(n){return new(n||t)(v(Fe,2))}}static{this.\u0275dir=T({type:t,selectors:[["","formControlName",""],["","ngModel",""],["","formControl",""]],hostVars:14,hostBindings:function(n,r){n&2&&Se("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)},features:[Q]})}}return t})(),Ew=(()=>{class t extends Wn{constructor(e){super(e)}static{this.\u0275fac=function(n){return new(n||t)(v(ue,10))}}static{this.\u0275dir=T({type:t,selectors:[["","formGroupName",""],["","formArrayName",""],["","ngModelGroup",""],["","formGroup",""],["form",3,"ngNoForm",""],["","ngForm",""]],hostVars:16,hostBindings:function(n,r){n&2&&Se("ng-untouched",r.isUntouched)("ng-touched",r.isTouched)("ng-pristine",r.isPristine)("ng-dirty",r.isDirty)("ng-valid",r.isValid)("ng-invalid",r.isInvalid)("ng-pending",r.isPending)("ng-submitted",r.isSubmitted)},features:[Q]})}}return t})();var Ii="VALID",$n="INVALID",Gt="PENDING",Ri="DISABLED";function Eo(t){return(Kn(t)?t.validators:t)||null}function fm(t){return Array.isArray(t)?_o(t):t||null}function Ao(t,i){return(Kn(i)?i.asyncValidators:t)||null}function mm(t){return Array.isArray(t)?Co(t):t||null}function Kn(t){return t!=null&&!Array.isArray(t)&&typeof t=="object"}function fd(t,i,e){let n=t.controls;if(!(i?Object.keys(n):n).length)throw new N(1e3,"");if(!n[e])throw new N(1001,"")}function md(t,i,e){t._forEachChild((n,r)=>{if(e[r]===void 0)throw new N(1002,"")})}var Wt=class{constructor(i,e){this._pendingDirty=!1,this._hasOwnPendingAsyncValidator=!1,this._pendingTouched=!1,this._onCollectionChange=()=>{},this._parent=null,this.pristine=!0,this.touched=!1,this._onDisabledChange=[],this._assignValidators(i),this._assignAsyncValidators(e)}get validator(){return this._composedValidatorFn}set validator(i){this._rawValidators=this._composedValidatorFn=i}get asyncValidator(){return this._composedAsyncValidatorFn}set asyncValidator(i){this._rawAsyncValidators=this._composedAsyncValidatorFn=i}get parent(){return this._parent}get valid(){return this.status===Ii}get invalid(){return this.status===$n}get pending(){return this.status==Gt}get disabled(){return this.status===Ri}get enabled(){return this.status!==Ri}get dirty(){return!this.pristine}get untouched(){return!this.touched}get updateOn(){return this._updateOn?this._updateOn:this.parent?this.parent.updateOn:"change"}setValidators(i){this._assignValidators(i)}setAsyncValidators(i){this._assignAsyncValidators(i)}addValidators(i){this.setValidators(ql(i,this._rawValidators))}addAsyncValidators(i){this.setAsyncValidators(ql(i,this._rawAsyncValidators))}removeValidators(i){this.setValidators(Gl(i,this._rawValidators))}removeAsyncValidators(i){this.setAsyncValidators(Gl(i,this._rawAsyncValidators))}hasValidator(i){return qn(this._rawValidators,i)}hasAsyncValidator(i){return qn(this._rawAsyncValidators,i)}clearValidators(){this.validator=null}clearAsyncValidators(){this.asyncValidator=null}markAsTouched(i={}){this.touched=!0,this._parent&&!i.onlySelf&&this._parent.markAsTouched(i)}markAllAsTouched(){this.markAsTouched({onlySelf:!0}),this._forEachChild(i=>i.markAllAsTouched())}markAsUntouched(i={}){this.touched=!1,this._pendingTouched=!1,this._forEachChild(e=>{e.markAsUntouched({onlySelf:!0})}),this._parent&&!i.onlySelf&&this._parent._updateTouched(i)}markAsDirty(i={}){this.pristine=!1,this._parent&&!i.onlySelf&&this._parent.markAsDirty(i)}markAsPristine(i={}){this.pristine=!0,this._pendingDirty=!1,this._forEachChild(e=>{e.markAsPristine({onlySelf:!0})}),this._parent&&!i.onlySelf&&this._parent._updatePristine(i)}markAsPending(i={}){this.status=Gt,i.emitEvent!==!1&&this.statusChanges.emit(this.status),this._parent&&!i.onlySelf&&this._parent.markAsPending(i)}disable(i={}){let e=this._parentMarkedDirty(i.onlySelf);this.status=Ri,this.errors=null,this._forEachChild(n=>{n.disable(z(w({},i),{onlySelf:!0}))}),this._updateValue(),i.emitEvent!==!1&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._updateAncestors(z(w({},i),{skipPristineCheck:e})),this._onDisabledChange.forEach(n=>n(!0))}enable(i={}){let e=this._parentMarkedDirty(i.onlySelf);this.status=Ii,this._forEachChild(n=>{n.enable(z(w({},i),{onlySelf:!0}))}),this.updateValueAndValidity({onlySelf:!0,emitEvent:i.emitEvent}),this._updateAncestors(z(w({},i),{skipPristineCheck:e})),this._onDisabledChange.forEach(n=>n(!1))}_updateAncestors(i){this._parent&&!i.onlySelf&&(this._parent.updateValueAndValidity(i),i.skipPristineCheck||this._parent._updatePristine(),this._parent._updateTouched())}setParent(i){this._parent=i}getRawValue(){return this.value}updateValueAndValidity(i={}){this._setInitialStatus(),this._updateValue(),this.enabled&&(this._cancelExistingSubscription(),this.errors=this._runValidator(),this.status=this._calculateStatus(),(this.status===Ii||this.status===Gt)&&this._runAsyncValidator(i.emitEvent)),i.emitEvent!==!1&&(this.valueChanges.emit(this.value),this.statusChanges.emit(this.status)),this._parent&&!i.onlySelf&&this._parent.updateValueAndValidity(i)}_updateTreeValidity(i={emitEvent:!0}){this._forEachChild(e=>e._updateTreeValidity(i)),this.updateValueAndValidity({onlySelf:!0,emitEvent:i.emitEvent})}_setInitialStatus(){this.status=this._allControlsDisabled()?Ri:Ii}_runValidator(){return this.validator?this.validator(this):null}_runAsyncValidator(i){if(this.asyncValidator){this.status=Gt,this._hasOwnPendingAsyncValidator=!0;let e=od(this.asyncValidator(this));this._asyncValidationSubscription=e.subscribe(n=>{this._hasOwnPendingAsyncValidator=!1,this.setErrors(n,{emitEvent:i})})}}_cancelExistingSubscription(){this._asyncValidationSubscription&&(this._asyncValidationSubscription.unsubscribe(),this._hasOwnPendingAsyncValidator=!1)}setErrors(i,e={}){this.errors=i,this._updateControlsErrors(e.emitEvent!==!1)}get(i){let e=i;return e==null||(Array.isArray(e)||(e=e.split(".")),e.length===0)?null:e.reduce((n,r)=>n&&n._find(r),this)}getError(i,e){let n=e?this.get(e):this;return n&&n.errors?n.errors[i]:null}hasError(i,e){return!!this.getError(i,e)}get root(){let i=this;for(;i._parent;)i=i._parent;return i}_updateControlsErrors(i){this.status=this._calculateStatus(),i&&this.statusChanges.emit(this.status),this._parent&&this._parent._updateControlsErrors(i)}_initObservables(){this.valueChanges=new _e,this.statusChanges=new _e}_calculateStatus(){return this._allControlsDisabled()?Ri:this.errors?$n:this._hasOwnPendingAsyncValidator||this._anyControlsHaveStatus(Gt)?Gt:this._anyControlsHaveStatus($n)?$n:Ii}_anyControlsHaveStatus(i){return this._anyControls(e=>e.status===i)}_anyControlsDirty(){return this._anyControls(i=>i.dirty)}_anyControlsTouched(){return this._anyControls(i=>i.touched)}_updatePristine(i={}){this.pristine=!this._anyControlsDirty(),this._parent&&!i.onlySelf&&this._parent._updatePristine(i)}_updateTouched(i={}){this.touched=this._anyControlsTouched(),this._parent&&!i.onlySelf&&this._parent._updateTouched(i)}_registerOnCollectionChange(i){this._onCollectionChange=i}_setUpdateStrategy(i){Kn(i)&&i.updateOn!=null&&(this._updateOn=i.updateOn)}_parentMarkedDirty(i){let e=this._parent&&this._parent.dirty;return!i&&!!e&&!this._parent._anyControlsDirty()}_find(i){return null}_assignValidators(i){this._rawValidators=Array.isArray(i)?i.slice():i,this._composedValidatorFn=fm(this._rawValidators)}_assignAsyncValidators(i){this._rawAsyncValidators=Array.isArray(i)?i.slice():i,this._composedAsyncValidatorFn=mm(this._rawAsyncValidators)}},Yt=class extends Wt{constructor(i,e,n){super(Eo(e),Ao(n,e)),this.controls=i,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}registerControl(i,e){return this.controls[i]?this.controls[i]:(this.controls[i]=e,e.setParent(this),e._registerOnCollectionChange(this._onCollectionChange),e)}addControl(i,e,n={}){this.registerControl(i,e),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}removeControl(i,e={}){this.controls[i]&&this.controls[i]._registerOnCollectionChange(()=>{}),delete this.controls[i],this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}setControl(i,e,n={}){this.controls[i]&&this.controls[i]._registerOnCollectionChange(()=>{}),delete this.controls[i],e&&this.registerControl(i,e),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}contains(i){return this.controls.hasOwnProperty(i)&&this.controls[i].enabled}setValue(i,e={}){md(this,!0,i),Object.keys(i).forEach(n=>{fd(this,!0,n),this.controls[n].setValue(i[n],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(i,e={}){i!=null&&(Object.keys(i).forEach(n=>{let r=this.controls[n];r&&r.patchValue(i[n],{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(i={},e={}){this._forEachChild((n,r)=>{n.reset(i?i[r]:null,{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e),this._updateTouched(e),this.updateValueAndValidity(e)}getRawValue(){return this._reduceChildren({},(i,e,n)=>(i[n]=e.getRawValue(),i))}_syncPendingControls(){let i=this._reduceChildren(!1,(e,n)=>n._syncPendingControls()?!0:e);return i&&this.updateValueAndValidity({onlySelf:!0}),i}_forEachChild(i){Object.keys(this.controls).forEach(e=>{let n=this.controls[e];n&&i(n,e)})}_setUpControls(){this._forEachChild(i=>{i.setParent(this),i._registerOnCollectionChange(this._onCollectionChange)})}_updateValue(){this.value=this._reduceValue()}_anyControls(i){for(let[e,n]of Object.entries(this.controls))if(this.contains(e)&&i(n))return!0;return!1}_reduceValue(){let i={};return this._reduceChildren(i,(e,n,r)=>((n.enabled||this.disabled)&&(e[r]=n.value),e))}_reduceChildren(i,e){let n=i;return this._forEachChild((r,a)=>{n=e(n,r,a)}),n}_allControlsDisabled(){for(let i of Object.keys(this.controls))if(this.controls[i].enabled)return!1;return Object.keys(this.controls).length>0||this.disabled}_find(i){return this.controls.hasOwnProperty(i)?this.controls[i]:null}};var yo=class extends Yt{};var Xt=new _("CallSetDisabledState",{providedIn:"root",factory:()=>Qn}),Qn="always";function Jn(t,i){return[...i.path,t]}function Oi(t,i,e=Qn){Mo(t,i),i.valueAccessor.writeValue(t.value),(t.disabled||e==="always")&&i.valueAccessor.setDisabledState?.(t.disabled),gm(t,i),wm(t,i),vm(t,i),pm(t,i)}function Yn(t,i,e=!0){let n=()=>{};i.valueAccessor&&(i.valueAccessor.registerOnChange(n),i.valueAccessor.registerOnTouched(n)),Zn(t,i),t&&(i._invokeOnDestroyCallbacks(),t._registerOnCollectionChange(()=>{}))}function Xn(t,i){t.forEach(e=>{e.registerOnValidatorChange&&e.registerOnValidatorChange(i)})}function pm(t,i){if(i.valueAccessor.setDisabledState){let e=n=>{i.valueAccessor.setDisabledState(n)};t.registerOnDisabledChange(e),i._registerOnDestroy(()=>{t._unregisterOnDisabledChange(e)})}}function Mo(t,i){let e=ud(t);i.validator!==null?t.setValidators(Hl(e,i.validator)):typeof e=="function"&&t.setValidators([e]);let n=hd(t);i.asyncValidator!==null?t.setAsyncValidators(Hl(n,i.asyncValidator)):typeof n=="function"&&t.setAsyncValidators([n]);let r=()=>t.updateValueAndValidity();Xn(i._rawValidators,r),Xn(i._rawAsyncValidators,r)}function Zn(t,i){let e=!1;if(t!==null){if(i.validator!==null){let r=ud(t);if(Array.isArray(r)&&r.length>0){let a=r.filter(d=>d!==i.validator);a.length!==r.length&&(e=!0,t.setValidators(a))}}if(i.asyncValidator!==null){let r=hd(t);if(Array.isArray(r)&&r.length>0){let a=r.filter(d=>d!==i.asyncValidator);a.length!==r.length&&(e=!0,t.setAsyncValidators(a))}}}let n=()=>{};return Xn(i._rawValidators,n),Xn(i._rawAsyncValidators,n),e}function gm(t,i){i.valueAccessor.registerOnChange(e=>{t._pendingValue=e,t._pendingChange=!0,t._pendingDirty=!0,t.updateOn==="change"&&pd(t,i)})}function vm(t,i){i.valueAccessor.registerOnTouched(()=>{t._pendingTouched=!0,t.updateOn==="blur"&&t._pendingChange&&pd(t,i),t.updateOn!=="submit"&&t.markAsTouched()})}function pd(t,i){t._pendingDirty&&t.markAsDirty(),t.setValue(t._pendingValue,{emitModelToViewChange:!1}),i.viewToModelUpdate(t._pendingValue),t._pendingChange=!1}function wm(t,i){let e=(n,r)=>{i.valueAccessor.writeValue(n),r&&i.viewToModelUpdate(n)};t.registerOnChange(e),i._registerOnDestroy(()=>{t._unregisterOnChange(e)})}function gd(t,i){t==null,Mo(t,i)}function bm(t,i){return Zn(t,i)}function So(t,i){if(!t.hasOwnProperty("model"))return!1;let e=t.model;return e.isFirstChange()?!0:!Object.is(i,e.currentValue)}function ym(t){return Object.getPrototypeOf(t.constructor)===Ql}function vd(t,i){t._syncPendingControls(),i.forEach(e=>{let n=e.control;n.updateOn==="submit"&&n._pendingChange&&(e.viewToModelUpdate(n._pendingValue),n._pendingChange=!1)})}function Do(t,i){if(!i)return null;Array.isArray(i);let e,n,r;return i.forEach(a=>{a.constructor===Jl?e=a:ym(a)?n=a:r=a}),r||n||e||null}function xm(t,i){let e=t.indexOf(i);e>-1&&t.splice(e,1)}var _m={provide:ue,useExisting:xe(()=>Io)},Ti=Promise.resolve(),Io=(()=>{class t extends ue{constructor(e,n,r){super(),this.callSetDisabledState=r,this.submitted=!1,this._directives=new Set,this.ngSubmit=new _e,this.form=new Yt({},_o(e),Co(n))}ngAfterViewInit(){this._setUpdateStrategy()}get formDirective(){return this}get control(){return this.form}get path(){return[]}get controls(){return this.form.controls}addControl(e){Ti.then(()=>{let n=this._findContainer(e.path);e.control=n.registerControl(e.name,e.control),Oi(e.control,e,this.callSetDisabledState),e.control.updateValueAndValidity({emitEvent:!1}),this._directives.add(e)})}getControl(e){return this.form.get(e.path)}removeControl(e){Ti.then(()=>{let n=this._findContainer(e.path);n&&n.removeControl(e.name),this._directives.delete(e)})}addFormGroup(e){Ti.then(()=>{let n=this._findContainer(e.path),r=new Yt({});gd(r,e),n.registerControl(e.name,r),r.updateValueAndValidity({emitEvent:!1})})}removeFormGroup(e){Ti.then(()=>{let n=this._findContainer(e.path);n&&n.removeControl(e.name)})}getFormGroup(e){return this.form.get(e.path)}updateModel(e,n){Ti.then(()=>{this.form.get(e.path).setValue(n)})}setValue(e){this.control.setValue(e)}onSubmit(e){return this.submitted=!0,vd(this.form,this._directives),this.ngSubmit.emit(e),e?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this.submitted=!1}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.form._updateOn=this.options.updateOn)}_findContainer(e){return e.pop(),e.length?this.form.get(e):this.form}static{this.\u0275fac=function(n){return new(n||t)(v(Ue,10),v(Ct,10),v(Xt,8))}}static{this.\u0275dir=T({type:t,selectors:[["form",3,"ngNoForm","",3,"formGroup",""],["ng-form"],["","ngForm",""]],hostBindings:function(n,r){n&1&&De("submit",function(d){return r.onSubmit(d)})("reset",function(){return r.onReset()})},inputs:{options:[j.None,"ngFormOptions","options"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[G([_m]),Q]})}}return t})();function Wl(t,i){let e=t.indexOf(i);e>-1&&t.splice(e,1)}function Yl(t){return typeof t=="object"&&t!==null&&Object.keys(t).length===2&&"value"in t&&"disabled"in t}var ki=class extends Wt{constructor(i=null,e,n){super(Eo(e),Ao(n,e)),this.defaultValue=null,this._onChange=[],this._pendingChange=!1,this._applyFormState(i),this._setUpdateStrategy(e),this._initObservables(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator}),Kn(e)&&(e.nonNullable||e.initialValueIsDefault)&&(Yl(i)?this.defaultValue=i.value:this.defaultValue=i)}setValue(i,e={}){this.value=this._pendingValue=i,this._onChange.length&&e.emitModelToViewChange!==!1&&this._onChange.forEach(n=>n(this.value,e.emitViewToModelChange!==!1)),this.updateValueAndValidity(e)}patchValue(i,e={}){this.setValue(i,e)}reset(i=this.defaultValue,e={}){this._applyFormState(i),this.markAsPristine(e),this.markAsUntouched(e),this.setValue(this.value,e),this._pendingChange=!1}_updateValue(){}_anyControls(i){return!1}_allControlsDisabled(){return this.disabled}registerOnChange(i){this._onChange.push(i)}_unregisterOnChange(i){Wl(this._onChange,i)}registerOnDisabledChange(i){this._onDisabledChange.push(i)}_unregisterOnDisabledChange(i){Wl(this._onDisabledChange,i)}_forEachChild(i){}_syncPendingControls(){return this.updateOn==="submit"&&(this._pendingDirty&&this.markAsDirty(),this._pendingTouched&&this.markAsTouched(),this._pendingChange)?(this.setValue(this._pendingValue,{onlySelf:!0,emitModelToViewChange:!1}),!0):!1}_applyFormState(i){Yl(i)?(this.value=this._pendingValue=i.value,i.disabled?this.disable({onlySelf:!0,emitEvent:!1}):this.enable({onlySelf:!0,emitEvent:!1})):this.value=this._pendingValue=i}};var Cm=t=>t instanceof ki,Em=(()=>{class t extends ue{ngOnInit(){this._checkParentType(),this.formDirective.addFormGroup(this)}ngOnDestroy(){this.formDirective&&this.formDirective.removeFormGroup(this)}get control(){return this.formDirective.getFormGroup(this)}get path(){return Jn(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=je(t)))(r||t)}})()}static{this.\u0275dir=T({type:t,features:[Q]})}}return t})();var Am={provide:Fe,useExisting:xe(()=>Mm)},Xl=Promise.resolve(),Mm=(()=>{class t extends Fe{constructor(e,n,r,a,d,h){super(),this._changeDetectorRef=d,this.callSetDisabledState=h,this.control=new ki,this._registered=!1,this.name="",this.update=new _e,this._parent=e,this._setValidators(n),this._setAsyncValidators(r),this.valueAccessor=Do(this,a)}ngOnChanges(e){if(this._checkForErrors(),!this._registered||"name"in e){if(this._registered&&(this._checkName(),this.formDirective)){let n=e.name.previousValue;this.formDirective.removeControl({name:n,path:this._getPath(n)})}this._setUpControl()}"isDisabled"in e&&this._updateDisabled(e),So(e,this.viewModel)&&(this._updateValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}get path(){return this._getPath(this.name)}get formDirective(){return this._parent?this._parent.formDirective:null}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_setUpControl(){this._setUpdateStrategy(),this._isStandalone()?this._setUpStandalone():this.formDirective.addControl(this),this._registered=!0}_setUpdateStrategy(){this.options&&this.options.updateOn!=null&&(this.control._updateOn=this.options.updateOn)}_isStandalone(){return!this._parent||!!(this.options&&this.options.standalone)}_setUpStandalone(){Oi(this.control,this,this.callSetDisabledState),this.control.updateValueAndValidity({emitEvent:!1})}_checkForErrors(){this._isStandalone()||this._checkParentType(),this._checkName()}_checkParentType(){}_checkName(){this.options&&this.options.name&&(this.name=this.options.name),!this._isStandalone()&&this.name}_updateValue(e){Xl.then(()=>{this.control.setValue(e,{emitViewToModelChange:!1}),this._changeDetectorRef?.markForCheck()})}_updateDisabled(e){let n=e.isDisabled.currentValue,r=n!==0&&bt(n);Xl.then(()=>{r&&!this.control.disabled?this.control.disable():!r&&this.control.disabled&&this.control.enable(),this._changeDetectorRef?.markForCheck()})}_getPath(e){return this._parent?Jn(e,this._parent):[e]}static{this.\u0275fac=function(n){return new(n||t)(v(ue,9),v(Ue,10),v(Ct,10),v(Fi,10),v(wt,8),v(Xt,8))}}static{this.\u0275dir=T({type:t,selectors:[["","ngModel","",3,"formControlName","",3,"formControl",""]],inputs:{name:"name",isDisabled:[j.None,"disabled","isDisabled"],model:[j.None,"ngModel","model"],options:[j.None,"ngModelOptions","options"]},outputs:{update:"ngModelChange"},exportAs:["ngModel"],features:[G([Am]),Q,Te]})}}return t})(),Mw=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275dir=T({type:t,selectors:[["form",3,"ngNoForm","",3,"ngNativeValidate",""]],hostAttrs:["novalidate",""]})}}return t})(),Sm={provide:Fi,useExisting:xe(()=>Dm),multi:!0},Dm=(()=>{class t extends Ql{writeValue(e){let n=e??"";this.setProperty("value",n)}registerOnChange(e){this.onChange=n=>{e(n==""?null:parseFloat(n))}}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=je(t)))(r||t)}})()}static{this.\u0275dir=T({type:t,selectors:[["input","type","number","formControlName",""],["input","type","number","formControl",""],["input","type","number","ngModel",""]],hostBindings:function(n,r){n&1&&De("input",function(d){return r.onChange(d.target.value)})("blur",function(){return r.onTouched()})},features:[G([Sm]),Q]})}}return t})();var Ro=new _(""),Im={provide:Fe,useExisting:xe(()=>Rm)},Rm=(()=>{class t extends Fe{set isDisabled(e){}static{this._ngModelWarningSentOnce=!1}constructor(e,n,r,a,d){super(),this._ngModelWarningConfig=a,this.callSetDisabledState=d,this.update=new _e,this._ngModelWarningSent=!1,this._setValidators(e),this._setAsyncValidators(n),this.valueAccessor=Do(this,r)}ngOnChanges(e){if(this._isControlChanged(e)){let n=e.form.previousValue;n&&Yn(n,this,!1),Oi(this.form,this,this.callSetDisabledState),this.form.updateValueAndValidity({emitEvent:!1})}So(e,this.viewModel)&&(this.form.setValue(this.model),this.viewModel=this.model)}ngOnDestroy(){this.form&&Yn(this.form,this,!1)}get path(){return[]}get control(){return this.form}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}_isControlChanged(e){return e.hasOwnProperty("form")}static{this.\u0275fac=function(n){return new(n||t)(v(Ue,10),v(Ct,10),v(Fi,10),v(Ro,8),v(Xt,8))}}static{this.\u0275dir=T({type:t,selectors:[["","formControl",""]],inputs:{form:[j.None,"formControl","form"],isDisabled:[j.None,"disabled","isDisabled"],model:[j.None,"ngModel","model"]},outputs:{update:"ngModelChange"},exportAs:["ngForm"],features:[G([Im]),Q,Te]})}}return t})(),Tm={provide:ue,useExisting:xe(()=>er)},er=(()=>{class t extends ue{constructor(e,n,r){super(),this.callSetDisabledState=r,this.submitted=!1,this._onCollectionChange=()=>this._updateDomValue(),this.directives=[],this.form=null,this.ngSubmit=new _e,this._setValidators(e),this._setAsyncValidators(n)}ngOnChanges(e){this._checkFormPresent(),e.hasOwnProperty("form")&&(this._updateValidators(),this._updateDomValue(),this._updateRegistrations(),this._oldForm=this.form)}ngOnDestroy(){this.form&&(Zn(this.form,this),this.form._onCollectionChange===this._onCollectionChange&&this.form._registerOnCollectionChange(()=>{}))}get formDirective(){return this}get control(){return this.form}get path(){return[]}addControl(e){let n=this.form.get(e.path);return Oi(n,e,this.callSetDisabledState),n.updateValueAndValidity({emitEvent:!1}),this.directives.push(e),n}getControl(e){return this.form.get(e.path)}removeControl(e){Yn(e.control||null,e,!1),xm(this.directives,e)}addFormGroup(e){this._setUpFormContainer(e)}removeFormGroup(e){this._cleanUpFormContainer(e)}getFormGroup(e){return this.form.get(e.path)}addFormArray(e){this._setUpFormContainer(e)}removeFormArray(e){this._cleanUpFormContainer(e)}getFormArray(e){return this.form.get(e.path)}updateModel(e,n){this.form.get(e.path).setValue(n)}onSubmit(e){return this.submitted=!0,vd(this.form,this.directives),this.ngSubmit.emit(e),e?.target?.method==="dialog"}onReset(){this.resetForm()}resetForm(e=void 0){this.form.reset(e),this.submitted=!1}_updateDomValue(){this.directives.forEach(e=>{let n=e.control,r=this.form.get(e.path);n!==r&&(Yn(n||null,e),Cm(r)&&(Oi(r,e,this.callSetDisabledState),e.control=r))}),this.form._updateTreeValidity({emitEvent:!1})}_setUpFormContainer(e){let n=this.form.get(e.path);gd(n,e),n.updateValueAndValidity({emitEvent:!1})}_cleanUpFormContainer(e){if(this.form){let n=this.form.get(e.path);n&&bm(n,e)&&n.updateValueAndValidity({emitEvent:!1})}}_updateRegistrations(){this.form._registerOnCollectionChange(this._onCollectionChange),this._oldForm&&this._oldForm._registerOnCollectionChange(()=>{})}_updateValidators(){Mo(this.form,this),this._oldForm&&Zn(this._oldForm,this)}_checkFormPresent(){this.form}static{this.\u0275fac=function(n){return new(n||t)(v(Ue,10),v(Ct,10),v(Xt,8))}}static{this.\u0275dir=T({type:t,selectors:[["","formGroup",""]],hostBindings:function(n,r){n&1&&De("submit",function(d){return r.onSubmit(d)})("reset",function(){return r.onReset()})},inputs:{form:[j.None,"formGroup","form"]},outputs:{ngSubmit:"ngSubmit"},exportAs:["ngForm"],features:[G([Tm]),Q,Te]})}}return t})(),km={provide:ue,useExisting:xe(()=>wd)},wd=(()=>{class t extends Em{constructor(e,n,r){super(),this.name=null,this._parent=e,this._setValidators(n),this._setAsyncValidators(r)}_checkParentType(){yd(this._parent)}static{this.\u0275fac=function(n){return new(n||t)(v(ue,13),v(Ue,10),v(Ct,10))}}static{this.\u0275dir=T({type:t,selectors:[["","formGroupName",""]],inputs:{name:[j.None,"formGroupName","name"]},features:[G([km]),Q]})}}return t})(),Om={provide:ue,useExisting:xe(()=>bd)},bd=(()=>{class t extends ue{constructor(e,n,r){super(),this.name=null,this._parent=e,this._setValidators(n),this._setAsyncValidators(r)}ngOnInit(){this._checkParentType(),this.formDirective.addFormArray(this)}ngOnDestroy(){this.formDirective&&this.formDirective.removeFormArray(this)}get control(){return this.formDirective.getFormArray(this)}get formDirective(){return this._parent?this._parent.formDirective:null}get path(){return Jn(this.name==null?this.name:this.name.toString(),this._parent)}_checkParentType(){yd(this._parent)}static{this.\u0275fac=function(n){return new(n||t)(v(ue,13),v(Ue,10),v(Ct,10))}}static{this.\u0275dir=T({type:t,selectors:[["","formArrayName",""]],inputs:{name:[j.None,"formArrayName","name"]},features:[G([Om]),Q]})}}return t})();function yd(t){return!(t instanceof wd)&&!(t instanceof er)&&!(t instanceof bd)}var Fm={provide:Fe,useExisting:xe(()=>Pm)},Pm=(()=>{class t extends Fe{set isDisabled(e){}static{this._ngModelWarningSentOnce=!1}constructor(e,n,r,a,d){super(),this._ngModelWarningConfig=d,this._added=!1,this.name=null,this.update=new _e,this._ngModelWarningSent=!1,this._parent=e,this._setValidators(n),this._setAsyncValidators(r),this.valueAccessor=Do(this,a)}ngOnChanges(e){this._added||this._setUpControl(),So(e,this.viewModel)&&(this.viewModel=this.model,this.formDirective.updateModel(this,this.model))}ngOnDestroy(){this.formDirective&&this.formDirective.removeControl(this)}viewToModelUpdate(e){this.viewModel=e,this.update.emit(e)}get path(){return Jn(this.name==null?this.name:this.name.toString(),this._parent)}get formDirective(){return this._parent?this._parent.formDirective:null}_checkParentType(){}_setUpControl(){this._checkParentType(),this.control=this.formDirective.addControl(this),this._added=!0}static{this.\u0275fac=function(n){return new(n||t)(v(ue,13),v(Ue,10),v(Ct,10),v(Fi,10),v(Ro,8))}}static{this.\u0275dir=T({type:t,selectors:[["","formControlName",""]],inputs:{name:[j.None,"formControlName","name"],isDisabled:[j.None,"disabled","isDisabled"],model:[j.None,"ngModel","model"]},outputs:{update:"ngModelChange"},features:[G([Fm]),Q,Te]})}}return t})();function Nm(t){return typeof t=="number"?t:parseInt(t,10)}function Lm(t){return typeof t=="number"?t:parseFloat(t)}var To=(()=>{class t{constructor(){this._validator=Hn}ngOnChanges(e){if(this.inputName in e){let n=this.normalizeInput(e[this.inputName].currentValue);this._enabled=this.enabled(n),this._validator=this._enabled?this.createValidator(n):Hn,this._onChange&&this._onChange()}}validate(e){return this._validator(e)}registerOnValidatorChange(e){this._onChange=e}enabled(e){return e!=null}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275dir=T({type:t,features:[Te]})}}return t})();var Vm={provide:Ue,useExisting:xe(()=>jm),multi:!0},jm=(()=>{class t extends To{constructor(){super(...arguments),this.inputName="min",this.normalizeInput=e=>Lm(e),this.createValidator=e=>td(e)}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=je(t)))(r||t)}})()}static{this.\u0275dir=T({type:t,selectors:[["input","type","number","min","","formControlName",""],["input","type","number","min","","formControl",""],["input","type","number","min","","ngModel",""]],hostVars:1,hostBindings:function(n,r){n&2&&ke("min",r._enabled?r.min:null)},inputs:{min:"min"},features:[G([Vm]),Q]})}}return t})(),Um={provide:Ue,useExisting:xe(()=>Bm),multi:!0};var Bm=(()=>{class t extends To{constructor(){super(...arguments),this.inputName="required",this.normalizeInput=bt,this.createValidator=e=>id}enabled(e){return e}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=je(t)))(r||t)}})()}static{this.\u0275dir=T({type:t,selectors:[["","required","","formControlName","",3,"type","checkbox"],["","required","","formControl","",3,"type","checkbox"],["","required","","ngModel","",3,"type","checkbox"]],hostVars:1,hostBindings:function(n,r){n&2&&ke("required",r._enabled?"":null)},inputs:{required:"required"},features:[G([Um]),Q]})}}return t})();var $m={provide:Ue,useExisting:xe(()=>zm),multi:!0},zm=(()=>{class t extends To{constructor(){super(...arguments),this.inputName="maxlength",this.normalizeInput=e=>Nm(e),this.createValidator=e=>nd(e)}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=je(t)))(r||t)}})()}static{this.\u0275dir=T({type:t,selectors:[["","maxlength","","formControlName",""],["","maxlength","","formControl",""],["","maxlength","","ngModel",""]],hostVars:1,hostBindings:function(n,r){n&2&&ke("maxlength",r._enabled?r.maxlength:null)},inputs:{maxlength:"maxlength"},features:[G([$m]),Q]})}}return t})();var xd=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=ie({type:t})}static{this.\u0275inj=te({})}}return t})(),xo=class extends Wt{constructor(i,e,n){super(Eo(e),Ao(n,e)),this.controls=i,this._initObservables(),this._setUpdateStrategy(e),this._setUpControls(),this.updateValueAndValidity({onlySelf:!0,emitEvent:!!this.asyncValidator})}at(i){return this.controls[this._adjustIndex(i)]}push(i,e={}){this.controls.push(i),this._registerControl(i),this.updateValueAndValidity({emitEvent:e.emitEvent}),this._onCollectionChange()}insert(i,e,n={}){this.controls.splice(i,0,e),this._registerControl(e),this.updateValueAndValidity({emitEvent:n.emitEvent})}removeAt(i,e={}){let n=this._adjustIndex(i);n<0&&(n=0),this.controls[n]&&this.controls[n]._registerOnCollectionChange(()=>{}),this.controls.splice(n,1),this.updateValueAndValidity({emitEvent:e.emitEvent})}setControl(i,e,n={}){let r=this._adjustIndex(i);r<0&&(r=0),this.controls[r]&&this.controls[r]._registerOnCollectionChange(()=>{}),this.controls.splice(r,1),e&&(this.controls.splice(r,0,e),this._registerControl(e)),this.updateValueAndValidity({emitEvent:n.emitEvent}),this._onCollectionChange()}get length(){return this.controls.length}setValue(i,e={}){md(this,!1,i),i.forEach((n,r)=>{fd(this,!1,r),this.at(r).setValue(n,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e)}patchValue(i,e={}){i!=null&&(i.forEach((n,r)=>{this.at(r)&&this.at(r).patchValue(n,{onlySelf:!0,emitEvent:e.emitEvent})}),this.updateValueAndValidity(e))}reset(i=[],e={}){this._forEachChild((n,r)=>{n.reset(i[r],{onlySelf:!0,emitEvent:e.emitEvent})}),this._updatePristine(e),this._updateTouched(e),this.updateValueAndValidity(e)}getRawValue(){return this.controls.map(i=>i.getRawValue())}clear(i={}){this.controls.length<1||(this._forEachChild(e=>e._registerOnCollectionChange(()=>{})),this.controls.splice(0),this.updateValueAndValidity({emitEvent:i.emitEvent}))}_adjustIndex(i){return i<0?i+this.length:i}_syncPendingControls(){let i=this.controls.reduce((e,n)=>n._syncPendingControls()?!0:e,!1);return i&&this.updateValueAndValidity({onlySelf:!0}),i}_forEachChild(i){this.controls.forEach((e,n)=>{i(e,n)})}_updateValue(){this.value=this.controls.filter(i=>i.enabled||this.disabled).map(i=>i.value)}_anyControls(i){return this.controls.some(e=>e.enabled&&i(e))}_setUpControls(){this._forEachChild(i=>this._registerControl(i))}_allControlsDisabled(){for(let i of this.controls)if(i.enabled)return!1;return this.controls.length>0||this.disabled}_registerControl(i){i.setParent(this),i._registerOnCollectionChange(this._onCollectionChange)}_find(i){return this.at(i)??null}};function Zl(t){return!!t&&(t.asyncValidators!==void 0||t.validators!==void 0||t.updateOn!==void 0)}var Sw=(()=>{class t{constructor(){this.useNonNullable=!1}get nonNullable(){let e=new t;return e.useNonNullable=!0,e}group(e,n=null){let r=this._reduceControls(e),a={};return Zl(n)?a=n:n!==null&&(a.validators=n.validator,a.asyncValidators=n.asyncValidator),new Yt(r,a)}record(e,n=null){let r=this._reduceControls(e);return new yo(r,n)}control(e,n,r){let a={};return this.useNonNullable?(Zl(n)?a=n:(a.validators=n,a.asyncValidators=r),new ki(e,z(w({},a),{nonNullable:!0}))):new ki(e,n,r)}array(e,n,r){let a=e.map(d=>this._createControl(d));return new xo(a,n,r)}_reduceControls(e){let n={};return Object.keys(e).forEach(r=>{n[r]=this._createControl(e[r])}),n}_createControl(e){if(e instanceof ki)return e;if(e instanceof Wt)return e;if(Array.isArray(e)){let n=e[0],r=e.length>1?e[1]:null,a=e.length>2?e[2]:null;return this.control(n,r,a)}else return this.control(e)}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var Dw=(()=>{class t{static withConfig(e){return{ngModule:t,providers:[{provide:Xt,useValue:e.callSetDisabledState??Qn}]}}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=ie({type:t})}static{this.\u0275inj=te({imports:[xd]})}}return t})(),Iw=(()=>{class t{static withConfig(e){return{ngModule:t,providers:[{provide:Ro,useValue:e.warnOnNgModelWithFormControl??"always"},{provide:Xt,useValue:e.callSetDisabledState??Qn}]}}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=ie({type:t})}static{this.\u0275inj=te({imports:[xd]})}}return t})();var ko=class{constructor(i){this._box=i,this._destroyed=new X,this._resizeSubject=new X,this._elementObservables=new Map,typeof ResizeObserver<"u"&&(this._resizeObserver=new ResizeObserver(e=>this._resizeSubject.next(e)))}observe(i){return this._elementObservables.has(i)||this._elementObservables.set(i,new wn(e=>{let n=this._resizeSubject.subscribe(e);return this._resizeObserver?.observe(i,{box:this._box}),()=>{this._resizeObserver?.unobserve(i),n.unsubscribe(),this._elementObservables.delete(i)}}).pipe(Re(e=>e.some(n=>n.target===i)),Ra({bufferSize:1,refCount:!0}),Je(this._destroyed))),this._elementObservables.get(i)}destroy(){this._destroyed.next(),this._destroyed.complete(),this._resizeSubject.complete(),this._elementObservables.clear()}},_d=(()=>{class t{constructor(){this._observers=new Map,this._ngZone=y(V),typeof ResizeObserver<"u"}ngOnDestroy(){for(let[,e]of this._observers)e.destroy();this._observers.clear(),typeof ResizeObserver<"u"}observe(e,n){let r=n?.box||"content-box";return this._observers.has(r)||this._observers.set(r,new ko(r)),this._observers.get(r).observe(e)}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var qm=["notch"],Gm=["matFormFieldNotchedOutline",""],Wm=["*"],Ym=["textField"],Xm=["iconPrefixContainer"],Zm=["textPrefixContainer"],Km=["*",[["mat-label"]],[["","matPrefix",""],["","matIconPrefix",""]],[["","matTextPrefix",""]],[["","matTextSuffix",""]],[["","matSuffix",""],["","matIconSuffix",""]],[["mat-error"],["","matError",""]],[["mat-hint",3,"align","end"]],[["mat-hint","align","end"]]],Qm=["*","mat-label","[matPrefix], [matIconPrefix]","[matTextPrefix]","[matTextSuffix]","[matSuffix], [matIconSuffix]","mat-error, [matError]","mat-hint:not([align='end'])","mat-hint[align='end']"];function Jm(t,i){t&1&&Ge(0,"span",17)}function ep(t,i){if(t&1&&(de(0,"label",16),Oe(1,1),me(2,Jm,1,0,"span",17),ce()),t&2){let e=We(2);tt("floating",e._shouldLabelFloat())("monitorResize",e._hasOutline())("id",e._labelId),ke("for",e._control.id),ne(2),pe(2,!e.hideRequiredMarker&&e._control.required?2:-1)}}function tp(t,i){if(t&1&&me(0,ep,3,5,"label",16),t&2){let e=We();pe(0,e._hasFloatingLabel()?0:-1)}}function ip(t,i){t&1&&Ge(0,"div",5)}function np(t,i){}function rp(t,i){if(t&1&&me(0,np,0,0,"ng-template",11),t&2){We(2);let e=Qr(1);tt("ngTemplateOutlet",e)}}function op(t,i){if(t&1&&(de(0,"div",7),me(1,rp,1,1,null,11),ce()),t&2){let e=We();tt("matFormFieldNotchedOutlineOpen",e._shouldLabelFloat()),ne(),pe(1,e._forceDisplayInfixLabel()?-1:1)}}function sp(t,i){t&1&&(de(0,"div",8,2),Oe(2,2),ce())}function ap(t,i){t&1&&(de(0,"div",9,3),Oe(2,3),ce())}function lp(t,i){}function dp(t,i){if(t&1&&me(0,lp,0,0,"ng-template",11),t&2){We();let e=Qr(1);tt("ngTemplateOutlet",e)}}function cp(t,i){t&1&&(de(0,"div",12),Oe(1,4),ce())}function up(t,i){t&1&&(de(0,"div",13),Oe(1,5),ce())}function hp(t,i){t&1&&Ge(0,"div",14)}function fp(t,i){if(t&1&&(de(0,"div",18),Oe(1,6),ce()),t&2){let e=We();tt("@transitionMessages",e._subscriptAnimationState)}}function mp(t,i){if(t&1&&(de(0,"mat-hint",20),el(1),ce()),t&2){let e=We(2);tt("id",e._hintLabelId),ne(),tl(e.hintLabel)}}function pp(t,i){if(t&1&&(de(0,"div",19),me(1,mp,2,2,"mat-hint",20),Oe(2,7),Ge(3,"div",21),Oe(4,8),ce()),t&2){let e=We();tt("@transitionMessages",e._subscriptAnimationState),ne(),pe(1,e.hintLabel?1:-1)}}var Cd=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275dir=T({type:t,selectors:[["mat-label"]],standalone:!0})}}return t})(),gp=0,Od=new _("MatError"),rb=(()=>{class t{constructor(e,n){this.id=`mat-mdc-error-${gp++}`,e||n.nativeElement.setAttribute("aria-live","polite")}static{this.\u0275fac=function(n){return new(n||t)(xn("aria-live"),v(fe))}}static{this.\u0275dir=T({type:t,selectors:[["mat-error"],["","matError",""]],hostAttrs:["aria-atomic","true",1,"mat-mdc-form-field-error","mat-mdc-form-field-bottom-align"],hostVars:1,hostBindings:function(n,r){n&2&&bi("id",r.id)},inputs:{id:"id"},standalone:!0,features:[G([{provide:Od,useExisting:t}])]})}}return t})(),vp=0,Ed=(()=>{class t{constructor(){this.align="start",this.id=`mat-mdc-hint-${vp++}`}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275dir=T({type:t,selectors:[["mat-hint"]],hostAttrs:[1,"mat-mdc-form-field-hint","mat-mdc-form-field-bottom-align"],hostVars:4,hostBindings:function(n,r){n&2&&(bi("id",r.id),ke("align",null),Se("mat-mdc-form-field-hint-end",r.align==="end"))},inputs:{align:"align",id:"id"},standalone:!0})}}return t})(),wp=new _("MatPrefix");var Fd=new _("MatSuffix"),ob=(()=>{class t{constructor(){this._isText=!1}set _isTextSelector(e){this._isText=!0}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275dir=T({type:t,selectors:[["","matSuffix",""],["","matIconSuffix",""],["","matTextSuffix",""]],inputs:{_isTextSelector:[j.None,"matTextSuffix","_isTextSelector"]},standalone:!0,features:[G([{provide:Fd,useExisting:t}])]})}}return t})(),Pd=new _("FloatingLabelParent"),Ad=(()=>{class t{get floating(){return this._floating}set floating(e){this._floating=e,this.monitorResize&&this._handleResize()}get monitorResize(){return this._monitorResize}set monitorResize(e){this._monitorResize=e,this._monitorResize?this._subscribeToResize():this._resizeSubscription.unsubscribe()}constructor(e){this._elementRef=e,this._floating=!1,this._monitorResize=!1,this._resizeObserver=y(_d),this._ngZone=y(V),this._parent=y(Pd),this._resizeSubscription=new vn}ngOnDestroy(){this._resizeSubscription.unsubscribe()}getWidth(){return bp(this._elementRef.nativeElement)}get element(){return this._elementRef.nativeElement}_handleResize(){setTimeout(()=>this._parent._handleLabelResized())}_subscribeToResize(){this._resizeSubscription.unsubscribe(),this._ngZone.runOutsideAngular(()=>{this._resizeSubscription=this._resizeObserver.observe(this._elementRef.nativeElement,{box:"border-box"}).subscribe(()=>this._handleResize())})}static{this.\u0275fac=function(n){return new(n||t)(v(fe))}}static{this.\u0275dir=T({type:t,selectors:[["label","matFormFieldFloatingLabel",""]],hostAttrs:[1,"mdc-floating-label","mat-mdc-floating-label"],hostVars:2,hostBindings:function(n,r){n&2&&Se("mdc-floating-label--float-above",r.floating)},inputs:{floating:"floating",monitorResize:"monitorResize"},standalone:!0})}}return t})();function bp(t){let i=t;if(i.offsetParent!==null)return i.scrollWidth;let e=i.cloneNode(!0);e.style.setProperty("position","absolute"),e.style.setProperty("transform","translate(-9999px, -9999px)"),document.documentElement.appendChild(e);let n=e.scrollWidth;return e.remove(),n}var Md="mdc-line-ripple--active",tr="mdc-line-ripple--deactivating",Sd=(()=>{class t{constructor(e,n){this._elementRef=e,this._handleTransitionEnd=r=>{let a=this._elementRef.nativeElement.classList,d=a.contains(tr);r.propertyName==="opacity"&&d&&a.remove(Md,tr)},n.runOutsideAngular(()=>{e.nativeElement.addEventListener("transitionend",this._handleTransitionEnd)})}activate(){let e=this._elementRef.nativeElement.classList;e.remove(tr),e.add(Md)}deactivate(){this._elementRef.nativeElement.classList.add(tr)}ngOnDestroy(){this._elementRef.nativeElement.removeEventListener("transitionend",this._handleTransitionEnd)}static{this.\u0275fac=function(n){return new(n||t)(v(fe),v(V))}}static{this.\u0275dir=T({type:t,selectors:[["div","matFormFieldLineRipple",""]],hostAttrs:[1,"mdc-line-ripple"],standalone:!0})}}return t})(),Dd=(()=>{class t{constructor(e,n){this._elementRef=e,this._ngZone=n,this.open=!1}ngAfterViewInit(){let e=this._elementRef.nativeElement.querySelector(".mdc-floating-label");e?(this._elementRef.nativeElement.classList.add("mdc-notched-outline--upgraded"),typeof requestAnimationFrame=="function"&&(e.style.transitionDuration="0s",this._ngZone.runOutsideAngular(()=>{requestAnimationFrame(()=>e.style.transitionDuration="")}))):this._elementRef.nativeElement.classList.add("mdc-notched-outline--no-label")}_setNotchWidth(e){!this.open||!e?this._notch.nativeElement.style.width="":this._notch.nativeElement.style.width=`calc(${e}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + 9px)`}static{this.\u0275fac=function(n){return new(n||t)(v(fe),v(V))}}static{this.\u0275cmp=pi({type:t,selectors:[["div","matFormFieldNotchedOutline",""]],viewQuery:function(n,r){if(n&1&&it(qm,5),n&2){let a;re(a=oe())&&(r._notch=a.first)}},hostAttrs:[1,"mdc-notched-outline"],hostVars:2,hostBindings:function(n,r){n&2&&Se("mdc-notched-outline--notched",r.open)},inputs:{open:[j.None,"matFormFieldNotchedOutlineOpen","open"]},standalone:!0,features:[yi],attrs:Gm,ngContentSelectors:Wm,decls:5,vars:0,consts:[["notch",""],[1,"mdc-notched-outline__leading"],[1,"mdc-notched-outline__notch"],[1,"mdc-notched-outline__trailing"]],template:function(n,r){n&1&&(Kr(),Ge(0,"div",1),de(1,"div",2,0),Oe(3),ce(),Ge(4,"div",3))},encapsulation:2,changeDetection:0})}}return t})(),yp={transitionMessages:xl("transitionMessages",[Cl("enter",ro({opacity:1,transform:"translateY(0%)"})),El("void => enter",[ro({opacity:0,transform:"translateY(-5px)"}),_l("300ms cubic-bezier(0.55, 0, 0.55, 0.2)")])])},Oo=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275dir=T({type:t})}}return t})();var Fo=new _("MatFormField"),xp=new _("MAT_FORM_FIELD_DEFAULT_OPTIONS"),Id=0,Rd="fill",_p="auto",Td="fixed",Cp="translateY(-50%)",sb=(()=>{class t{get hideRequiredMarker(){return this._hideRequiredMarker}set hideRequiredMarker(e){this._hideRequiredMarker=zt(e)}get floatLabel(){return this._floatLabel||this._defaults?.floatLabel||_p}set floatLabel(e){e!==this._floatLabel&&(this._floatLabel=e,this._changeDetectorRef.markForCheck())}get appearance(){return this._appearance}set appearance(e){let n=this._appearance,r=e||this._defaults?.appearance||Rd;this._appearance=r,this._appearance==="outline"&&this._appearance!==n&&(this._needsOutlineLabelOffsetUpdateOnStable=!0)}get subscriptSizing(){return this._subscriptSizing||this._defaults?.subscriptSizing||Td}set subscriptSizing(e){this._subscriptSizing=e||this._defaults?.subscriptSizing||Td}get hintLabel(){return this._hintLabel}set hintLabel(e){this._hintLabel=e,this._processHints()}get _control(){return this._explicitFormFieldControl||this._formFieldControl}set _control(e){this._explicitFormFieldControl=e}constructor(e,n,r,a,d,h,u,p){this._elementRef=e,this._changeDetectorRef=n,this._ngZone=r,this._dir=a,this._platform=d,this._defaults=h,this._animationMode=u,this._hideRequiredMarker=!1,this.color="primary",this._appearance=Rd,this._subscriptSizing=null,this._hintLabel="",this._hasIconPrefix=!1,this._hasTextPrefix=!1,this._hasIconSuffix=!1,this._hasTextSuffix=!1,this._labelId=`mat-mdc-form-field-label-${Id++}`,this._hintLabelId=`mat-mdc-hint-${Id++}`,this._subscriptAnimationState="",this._destroyed=new X,this._isFocused=null,this._needsOutlineLabelOffsetUpdateOnStable=!1,h&&(h.appearance&&(this.appearance=h.appearance),this._hideRequiredMarker=!!h?.hideRequiredMarker,h.color&&(this.color=h.color))}ngAfterViewInit(){this._updateFocusState(),this._subscriptAnimationState="enter",this._changeDetectorRef.detectChanges()}ngAfterContentInit(){this._assertFormFieldControl(),this._initializeControl(),this._initializeSubscript(),this._initializePrefixAndSuffix(),this._initializeOutlineLabelOffsetSubscriptions()}ngAfterContentChecked(){this._assertFormFieldControl()}ngOnDestroy(){this._destroyed.next(),this._destroyed.complete()}getLabelId(){return this._hasFloatingLabel()?this._labelId:null}getConnectedOverlayOrigin(){return this._textField||this._elementRef}_animateAndLockLabel(){this._hasFloatingLabel()&&(this.floatLabel="always")}_initializeControl(){let e=this._control;e.controlType&&this._elementRef.nativeElement.classList.add(`mat-mdc-form-field-type-${e.controlType}`),e.stateChanges.subscribe(()=>{this._updateFocusState(),this._syncDescribedByIds(),this._changeDetectorRef.markForCheck()}),e.ngControl&&e.ngControl.valueChanges&&e.ngControl.valueChanges.pipe(Je(this._destroyed)).subscribe(()=>this._changeDetectorRef.markForCheck())}_checkPrefixAndSuffixTypes(){this._hasIconPrefix=!!this._prefixChildren.find(e=>!e._isText),this._hasTextPrefix=!!this._prefixChildren.find(e=>e._isText),this._hasIconSuffix=!!this._suffixChildren.find(e=>!e._isText),this._hasTextSuffix=!!this._suffixChildren.find(e=>e._isText)}_initializePrefixAndSuffix(){this._checkPrefixAndSuffixTypes(),Ma(this._prefixChildren.changes,this._suffixChildren.changes).subscribe(()=>{this._checkPrefixAndSuffixTypes(),this._changeDetectorRef.markForCheck()})}_initializeSubscript(){this._hintChildren.changes.subscribe(()=>{this._processHints(),this._changeDetectorRef.markForCheck()}),this._errorChildren.changes.subscribe(()=>{this._syncDescribedByIds(),this._changeDetectorRef.markForCheck()}),this._validateHints(),this._syncDescribedByIds()}_assertFormFieldControl(){this._control}_updateFocusState(){this._control.focused&&!this._isFocused?(this._isFocused=!0,this._lineRipple?.activate()):!this._control.focused&&(this._isFocused||this._isFocused===null)&&(this._isFocused=!1,this._lineRipple?.deactivate()),this._textField?.nativeElement.classList.toggle("mdc-text-field--focused",this._control.focused)}_initializeOutlineLabelOffsetSubscriptions(){this._prefixChildren.changes.subscribe(()=>this._needsOutlineLabelOffsetUpdateOnStable=!0),this._ngZone.runOutsideAngular(()=>{this._ngZone.onStable.pipe(Je(this._destroyed)).subscribe(()=>{this._needsOutlineLabelOffsetUpdateOnStable&&(this._needsOutlineLabelOffsetUpdateOnStable=!1,this._updateOutlineLabelOffset())})}),this._dir.change.pipe(Je(this._destroyed)).subscribe(()=>this._needsOutlineLabelOffsetUpdateOnStable=!0)}_shouldAlwaysFloat(){return this.floatLabel==="always"}_hasOutline(){return this.appearance==="outline"}_forceDisplayInfixLabel(){return!this._platform.isBrowser&&this._prefixChildren.length&&!this._shouldLabelFloat()}_hasFloatingLabel(){return!!this._labelChildNonStatic||!!this._labelChildStatic}_shouldLabelFloat(){return this._control.shouldLabelFloat||this._shouldAlwaysFloat()}_shouldForward(e){let n=this._control?this._control.ngControl:null;return n&&n[e]}_getDisplayedMessages(){return this._errorChildren&&this._errorChildren.length>0&&this._control.errorState?"error":"hint"}_handleLabelResized(){this._refreshOutlineNotchWidth()}_refreshOutlineNotchWidth(){!this._hasOutline()||!this._floatingLabel||!this._shouldLabelFloat()?this._notchedOutline?._setNotchWidth(0):this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth())}_processHints(){this._validateHints(),this._syncDescribedByIds()}_validateHints(){this._hintChildren}_syncDescribedByIds(){if(this._control){let e=[];if(this._control.userAriaDescribedBy&&typeof this._control.userAriaDescribedBy=="string"&&e.push(...this._control.userAriaDescribedBy.split(" ")),this._getDisplayedMessages()==="hint"){let n=this._hintChildren?this._hintChildren.find(a=>a.align==="start"):null,r=this._hintChildren?this._hintChildren.find(a=>a.align==="end"):null;n?e.push(n.id):this._hintLabel&&e.push(this._hintLabelId),r&&e.push(r.id)}else this._errorChildren&&e.push(...this._errorChildren.map(n=>n.id));this._control.setDescribedByIds(e)}}_updateOutlineLabelOffset(){if(!this._platform.isBrowser||!this._hasOutline()||!this._floatingLabel)return;let e=this._floatingLabel.element;if(!(this._iconPrefixContainer||this._textPrefixContainer)){e.style.transform="";return}if(!this._isAttachedToDom()){this._needsOutlineLabelOffsetUpdateOnStable=!0;return}let n=this._iconPrefixContainer?.nativeElement,r=this._textPrefixContainer?.nativeElement,a=n?.getBoundingClientRect().width??0,d=r?.getBoundingClientRect().width??0,h=this._dir.value==="rtl"?"-1":"1",u=`${a+d}px`,b=`calc(${h} * (${u} + var(--mat-mdc-form-field-label-offset-x, 0px)))`;e.style.transform=`var(
        --mat-mdc-form-field-label-transform,
        ${Cp} translateX(${b})
    )`}_isAttachedToDom(){let e=this._elementRef.nativeElement;if(e.getRootNode){let n=e.getRootNode();return n&&n!==e}return document.documentElement.contains(e)}static{this.\u0275fac=function(n){return new(n||t)(v(fe),v(wt),v(V),v(wl),v($t),v(xp,8),v(ja,8),v(se))}}static{this.\u0275cmp=pi({type:t,selectors:[["mat-form-field"]],contentQueries:function(n,r,a){if(n&1&&(Ye(a,Cd,5),Ye(a,Cd,7),Ye(a,Oo,5),Ye(a,wp,5),Ye(a,Fd,5),Ye(a,Od,5),Ye(a,Ed,5)),n&2){let d;re(d=oe())&&(r._labelChildNonStatic=d.first),re(d=oe())&&(r._labelChildStatic=d.first),re(d=oe())&&(r._formFieldControl=d.first),re(d=oe())&&(r._prefixChildren=d),re(d=oe())&&(r._suffixChildren=d),re(d=oe())&&(r._errorChildren=d),re(d=oe())&&(r._hintChildren=d)}},viewQuery:function(n,r){if(n&1&&(it(Ym,5),it(Xm,5),it(Zm,5),it(Ad,5),it(Dd,5),it(Sd,5)),n&2){let a;re(a=oe())&&(r._textField=a.first),re(a=oe())&&(r._iconPrefixContainer=a.first),re(a=oe())&&(r._textPrefixContainer=a.first),re(a=oe())&&(r._floatingLabel=a.first),re(a=oe())&&(r._notchedOutline=a.first),re(a=oe())&&(r._lineRipple=a.first)}},hostAttrs:[1,"mat-mdc-form-field"],hostVars:42,hostBindings:function(n,r){n&2&&Se("mat-mdc-form-field-label-always-float",r._shouldAlwaysFloat())("mat-mdc-form-field-has-icon-prefix",r._hasIconPrefix)("mat-mdc-form-field-has-icon-suffix",r._hasIconSuffix)("mat-form-field-invalid",r._control.errorState)("mat-form-field-disabled",r._control.disabled)("mat-form-field-autofilled",r._control.autofilled)("mat-form-field-no-animations",r._animationMode==="NoopAnimations")("mat-form-field-appearance-fill",r.appearance=="fill")("mat-form-field-appearance-outline",r.appearance=="outline")("mat-form-field-hide-placeholder",r._hasFloatingLabel()&&!r._shouldLabelFloat())("mat-focused",r._control.focused)("mat-primary",r.color!=="accent"&&r.color!=="warn")("mat-accent",r.color==="accent")("mat-warn",r.color==="warn")("ng-untouched",r._shouldForward("untouched"))("ng-touched",r._shouldForward("touched"))("ng-pristine",r._shouldForward("pristine"))("ng-dirty",r._shouldForward("dirty"))("ng-valid",r._shouldForward("valid"))("ng-invalid",r._shouldForward("invalid"))("ng-pending",r._shouldForward("pending"))},inputs:{hideRequiredMarker:"hideRequiredMarker",color:"color",floatLabel:"floatLabel",appearance:"appearance",subscriptSizing:"subscriptSizing",hintLabel:"hintLabel"},exportAs:["matFormField"],standalone:!0,features:[G([{provide:Fo,useExisting:t},{provide:Pd,useExisting:t}]),yi],ngContentSelectors:Qm,decls:18,vars:21,consts:[["labelTemplate",""],["textField",""],["iconPrefixContainer",""],["textPrefixContainer",""],[1,"mat-mdc-text-field-wrapper","mdc-text-field",3,"click"],[1,"mat-mdc-form-field-focus-overlay"],[1,"mat-mdc-form-field-flex"],["matFormFieldNotchedOutline","",3,"matFormFieldNotchedOutlineOpen"],[1,"mat-mdc-form-field-icon-prefix"],[1,"mat-mdc-form-field-text-prefix"],[1,"mat-mdc-form-field-infix"],[3,"ngTemplateOutlet"],[1,"mat-mdc-form-field-text-suffix"],[1,"mat-mdc-form-field-icon-suffix"],["matFormFieldLineRipple",""],[1,"mat-mdc-form-field-subscript-wrapper","mat-mdc-form-field-bottom-align"],["matFormFieldFloatingLabel","",3,"floating","monitorResize","id"],["aria-hidden","true",1,"mat-mdc-form-field-required-marker","mdc-floating-label--required"],[1,"mat-mdc-form-field-error-wrapper"],[1,"mat-mdc-form-field-hint-wrapper"],[3,"id"],[1,"mat-mdc-form-field-hint-spacer"]],template:function(n,r){if(n&1){let a=Ja();Kr(Km),me(0,tp,1,1,"ng-template",null,0,il),de(2,"div",4,1),De("click",function(h){return Pa(a),Na(r._control.onContainerClick(h))}),me(4,ip,1,0,"div",5),de(5,"div",6),me(6,op,2,2,"div",7)(7,sp,3,0,"div",8)(8,ap,3,0,"div",9),de(9,"div",10),me(10,dp,1,1,null,11),Oe(11),ce(),me(12,cp,2,0,"div",12)(13,up,2,0,"div",13),ce(),me(14,hp,1,0,"div",14),ce(),de(15,"div",15),me(16,fp,2,1)(17,pp,5,2),ce()}if(n&2){let a;ne(2),Se("mdc-text-field--filled",!r._hasOutline())("mdc-text-field--outlined",r._hasOutline())("mdc-text-field--no-label",!r._hasFloatingLabel())("mdc-text-field--disabled",r._control.disabled)("mdc-text-field--invalid",r._control.errorState),ne(2),pe(4,!r._hasOutline()&&!r._control.disabled?4:-1),ne(2),pe(6,r._hasOutline()?6:-1),ne(),pe(7,r._hasIconPrefix?7:-1),ne(),pe(8,r._hasTextPrefix?8:-1),ne(2),pe(10,!r._hasOutline()||r._forceDisplayInfixLabel()?10:-1),ne(2),pe(12,r._hasTextSuffix?12:-1),ne(),pe(13,r._hasIconSuffix?13:-1),ne(),pe(14,r._hasOutline()?-1:14),ne(),Se("mat-mdc-form-field-subscript-dynamic-size",r.subscriptSizing==="dynamic"),ne(),pe(16,(a=r._getDisplayedMessages())==="error"?16:a==="hint"?17:-1)}},dependencies:[Ad,Dd,fl,Sd,Ed],styles:['.mdc-text-field{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:0;border-bottom-left-radius:0;display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-text-field__input{height:28px;width:100%;min-width:0;border:none;border-radius:0;background:none;appearance:none;padding:0}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}@media all{.mdc-text-field__input::placeholder{opacity:0}}@media all{.mdc-text-field__input:-ms-input-placeholder{opacity:0}}@media all{.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}}@media all{.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}}.mdc-text-field__affix{height:28px;opacity:0;white-space:nowrap}.mdc-text-field--label-floating .mdc-text-field__affix,.mdc-text-field--no-label .mdc-text-field__affix{opacity:1}@supports(-webkit-hyphens: none){.mdc-text-field--outlined .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field__affix--prefix,.mdc-text-field__affix--prefix[dir=rtl]{padding-left:2px;padding-right:0}.mdc-text-field--end-aligned .mdc-text-field__affix--prefix{padding-left:0;padding-right:12px}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--end-aligned .mdc-text-field__affix--prefix[dir=rtl]{padding-left:12px;padding-right:0}.mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field__affix--suffix,.mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:12px}.mdc-text-field--end-aligned .mdc-text-field__affix--suffix{padding-left:2px;padding-right:0}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--end-aligned .mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:2px}.mdc-text-field--filled{height:56px}.mdc-text-field--filled::before{display:inline-block;width:0;height:40px;content:"";vertical-align:0}.mdc-text-field--filled .mdc-floating-label{left:16px;right:initial}[dir=rtl] .mdc-text-field--filled .mdc-floating-label,.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:16px}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled.mdc-text-field--no-label::before{display:none}@supports(-webkit-hyphens: none){.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field--outlined{height:56px;overflow:visible}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--outlined .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px,var(--mdc-shape-small, 4px))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px,var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px,var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px,var(--mdc-shape-small, 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px,var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:initial}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:4px}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mdc-text-field--textarea{flex-direction:column;align-items:center;width:auto;height:auto;padding:0}.mdc-text-field--textarea .mdc-floating-label{top:19px}.mdc-text-field--textarea .mdc-floating-label:not(.mdc-floating-label--float-above){transform:none}.mdc-text-field--textarea .mdc-text-field__input{flex-grow:1;height:auto;min-height:1.5rem;overflow-x:hidden;overflow-y:auto;box-sizing:border-box;resize:none;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--filled::before{display:none}.mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-10.25px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--filled .mdc-text-field__input{margin-top:23px;margin-bottom:9px}.mdc-text-field--textarea.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-27.25px) scale(1)}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-24.75px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label{top:18px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field__input{margin-bottom:2px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter{align-self:flex-end;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::after{display:inline-block;width:0;height:16px;content:"";vertical-align:-16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::before{display:none}.mdc-text-field__resizer{align-self:stretch;display:inline-flex;flex-direction:column;flex-grow:1;max-height:100%;max-width:100%;min-height:56px;min-width:fit-content;min-width:-moz-available;min-width:-webkit-fill-available;overflow:hidden;resize:both}.mdc-text-field--filled .mdc-text-field__resizer{transform:translateY(-1px)}.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateY(1px)}.mdc-text-field--outlined .mdc-text-field__resizer{transform:translateX(-1px) translateY(-1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer,.mdc-text-field--outlined .mdc-text-field__resizer[dir=rtl]{transform:translateX(1px) translateY(-1px)}.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateX(1px) translateY(1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input[dir=rtl],.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter[dir=rtl]{transform:translateX(-1px) translateY(1px)}.mdc-text-field--with-leading-icon{padding-left:0;padding-right:16px}[dir=rtl] .mdc-text-field--with-leading-icon,.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:16px;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 48px);left:48px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:48px}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 64px/0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label{left:36px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:36px}.mdc-text-field--with-leading-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) translateX(-32px) scale(1)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-37.25px) translateX(32px) scale(1)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) translateX(-32px) scale(0.75)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl],.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-34.75px) translateX(32px) scale(0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--with-trailing-icon{padding-left:16px;padding-right:0}[dir=rtl] .mdc-text-field--with-trailing-icon,.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0;padding-right:16px}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 64px)}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 64px/0.75)}.mdc-text-field--with-trailing-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 96px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 96px/0.75)}.mdc-text-field-helper-line{display:flex;justify-content:space-between;box-sizing:border-box}.mdc-text-field+.mdc-text-field-helper-line{padding-right:16px;padding-left:16px}.mdc-form-field>.mdc-text-field+label{align-self:flex-start}.mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--focused .mdc-notched-outline__trailing{border-width:2px}.mdc-text-field--focused+.mdc-text-field-helper-line .mdc-text-field-helper-text:not(.mdc-text-field-helper-text--validation-msg){opacity:1}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-text-field--focused.mdc-text-field--outlined.mdc-text-field--textarea .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--invalid+.mdc-text-field-helper-line .mdc-text-field-helper-text--validation-msg{opacity:1}.mdc-text-field--disabled{pointer-events:none}@media screen and (forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--disabled .mdc-floating-label{cursor:default}.mdc-text-field--disabled.mdc-text-field--filled .mdc-text-field__ripple{display:none}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--end-aligned .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--end-aligned .mdc-text-field__input[dir=rtl]{text-align:left}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix{direction:ltr}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--leading,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--leading{order:1}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{order:2}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input{order:3}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{order:4}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--trailing,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--trailing{order:5}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--prefix{padding-right:12px}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--suffix{padding-left:2px}.mdc-floating-label{position:absolute;left:0;-webkit-transform-origin:left top;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label,.mdc-floating-label[dir=rtl]{right:0;left:auto;-webkit-transform-origin:right top;transform-origin:right top;text-align:right}.mdc-floating-label--float-above{cursor:auto}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0px;content:"*"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after,.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)[dir=rtl]::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline,.mdc-notched-outline[dir=rtl]{text-align:right}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{box-sizing:border-box;height:100%;pointer-events:none}.mdc-notched-outline__trailing{flex-grow:1}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch,.mdc-notched-outline--notched .mdc-notched-outline__notch[dir=rtl]{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:""}.mdc-line-ripple::before{z-index:1}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{border-top:1px solid;border-bottom:1px solid}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;width:12px}[dir=rtl] .mdc-notched-outline__leading,.mdc-notched-outline__leading[dir=rtl]{border-left:none;border-right:1px solid}.mdc-notched-outline__trailing{border-left:none;border-right:1px solid}[dir=rtl] .mdc-notched-outline__trailing,.mdc-notched-outline__trailing[dir=rtl]{border-left:1px solid;border-right:none}.mdc-notched-outline__notch{max-width:calc(100% - 12px*2)}.mdc-line-ripple::before{border-bottom-width:1px}.mdc-line-ripple::after{border-bottom-width:2px}.mdc-text-field--filled{border-top-left-radius:var(--mdc-filled-text-field-container-shape);border-top-right-radius:var(--mdc-filled-text-field-container-shape);border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-caret-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-label-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-focus-label-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-hover-label-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-disabled-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-focus-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-hover-label-text-color)}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font);font-size:var(--mdc-filled-text-field-label-text-size);font-weight:var(--mdc-filled-text-field-label-text-weight);letter-spacing:var(--mdc-filled-text-field-label-text-tracking)}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color)}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color)}.mdc-text-field--filled .mdc-line-ripple::before{border-bottom-width:var(--mdc-filled-text-field-active-indicator-height)}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-caret-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-label-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-focus-label-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-hover-label-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-disabled-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-focus-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-hover-label-text-color)}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font);font-size:var(--mdc-outlined-text-field-label-text-size);font-weight:var(--mdc-outlined-text-field-label-text-weight);letter-spacing:var(--mdc-outlined-text-field-label-text-tracking)}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(.75*var(--mdc-outlined-text-field-label-text-size))}.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mdc-outlined-text-field-label-text-size)}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px,var(--mdc-outlined-text-field-container-shape))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px,var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-hover-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-focus-outline-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-disabled-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-hover-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-focus-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-outline-width)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-focus-outline-width)}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-text-field-wrapper::before{content:none}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height);padding-top:var(--mat-form-field-filled-with-label-container-padding-top);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding);padding-bottom:var(--mat-form-field-container-vertical-padding)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:"";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color)}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font);line-height:var(--mat-form-field-subscript-text-line-height);font-size:var(--mat-form-field-subscript-text-size);letter-spacing:var(--mat-form-field-subscript-text-tracking);font-weight:var(--mat-form-field-subscript-text-weight)}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color)}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity)}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color)}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color)}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:"";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color)}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color)}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color)}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}.cdk-high-contrast-active .mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font);line-height:var(--mat-form-field-container-text-line-height);font-size:var(--mat-form-field-container-text-size);letter-spacing:var(--mat-form-field-container-text-tracking);font-weight:var(--mat-form-field-container-text-weight)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color)}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color)}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color)}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color)}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color)}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color)}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color)}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__affix{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled.mdc-ripple-upgraded--background-focused .mdc-text-field__ripple::before,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea{transition:none}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-filled 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-filled{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon{0%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}[dir=rtl] .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined[dir=rtl] .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl{0%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-standard 250ms 1}@keyframes mdc-floating-label-shake-float-above-standard{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}'],encapsulation:2,data:{animation:[yp.transitionMessages]},changeDetection:0})}}return t})(),Po=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=ie({type:t})}static{this.\u0275inj=te({imports:[Ht,Tn,vl,Ht]})}}return t})();var Nd=gl({passive:!0}),Ld=(()=>{class t{constructor(e,n){this._platform=e,this._ngZone=n,this._monitoredElements=new Map}monitor(e){if(!this._platform.isBrowser)return Le;let n=no(e),r=this._monitoredElements.get(n);if(r)return r.subject;let a=new X,d="cdk-text-field-autofilled",h=u=>{u.animationName==="cdk-text-field-autofill-start"&&!n.classList.contains(d)?(n.classList.add(d),this._ngZone.run(()=>a.next({target:u.target,isAutofilled:!0}))):u.animationName==="cdk-text-field-autofill-end"&&n.classList.contains(d)&&(n.classList.remove(d),this._ngZone.run(()=>a.next({target:u.target,isAutofilled:!1})))};return this._ngZone.runOutsideAngular(()=>{n.addEventListener("animationstart",h,Nd),n.classList.add("cdk-text-field-autofill-monitored")}),this._monitoredElements.set(n,{subject:a,unlisten:()=>{n.removeEventListener("animationstart",h,Nd)}}),a}stopMonitoring(e){let n=no(e),r=this._monitoredElements.get(n);r&&(r.unlisten(),r.subject.complete(),n.classList.remove("cdk-text-field-autofill-monitored"),n.classList.remove("cdk-text-field-autofilled"),this._monitoredElements.delete(n))}ngOnDestroy(){this._monitoredElements.forEach((e,n)=>this.stopMonitoring(n))}static{this.\u0275fac=function(n){return new(n||t)(M($t),M(V))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();var Vd=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=ie({type:t})}static{this.\u0275inj=te({})}}return t})();var Ap=new _("MAT_INPUT_VALUE_ACCESSOR"),Mp=["button","checkbox","file","hidden","image","radio","range","reset","submit"],Sp=0,Db=(()=>{class t{get disabled(){return this._disabled}set disabled(e){this._disabled=zt(e),this.focused&&(this.focused=!1,this.stateChanges.next())}get id(){return this._id}set id(e){this._id=e||this._uid}get required(){return this._required??this.ngControl?.control?.hasValidator(zn.required)??!1}set required(e){this._required=zt(e)}get type(){return this._type}set type(e){this._type=e||"text",this._validateType(),!this._isTextarea&&io().has(this._type)&&(this._elementRef.nativeElement.type=this._type)}get errorStateMatcher(){return this._errorStateTracker.matcher}set errorStateMatcher(e){this._errorStateTracker.matcher=e}get value(){return this._inputValueAccessor.value}set value(e){e!==this.value&&(this._inputValueAccessor.value=e,this.stateChanges.next())}get readonly(){return this._readonly}set readonly(e){this._readonly=zt(e)}get errorState(){return this._errorStateTracker.errorState}set errorState(e){this._errorStateTracker.errorState=e}constructor(e,n,r,a,d,h,u,p,b,f){this._elementRef=e,this._platform=n,this.ngControl=r,this._autofillMonitor=p,this._formField=f,this._uid=`mat-input-${Sp++}`,this.focused=!1,this.stateChanges=new X,this.controlType="mat-input",this.autofilled=!1,this._disabled=!1,this._type="text",this._readonly=!1,this._neverEmptyInputTypes=["date","datetime","datetime-local","month","time","week"].filter(ae=>io().has(ae)),this._iOSKeyupListener=ae=>{let S=ae.target;!S.value&&S.selectionStart===0&&S.selectionEnd===0&&(S.setSelectionRange(1,1),S.setSelectionRange(0,0))};let k=this._elementRef.nativeElement,B=k.nodeName.toLowerCase();this._inputValueAccessor=u||k,this._previousNativeValue=this.value,this.id=this.id,n.IOS&&b.runOutsideAngular(()=>{e.nativeElement.addEventListener("keyup",this._iOSKeyupListener)}),this._errorStateTracker=new bl(h,r,d,a,this.stateChanges),this._isServer=!this._platform.isBrowser,this._isNativeSelect=B==="select",this._isTextarea=B==="textarea",this._isInFormField=!!f,this._isNativeSelect&&(this.controlType=k.multiple?"mat-native-select-multiple":"mat-native-select")}ngAfterViewInit(){this._platform.isBrowser&&this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(e=>{this.autofilled=e.isAutofilled,this.stateChanges.next()})}ngOnChanges(){this.stateChanges.next()}ngOnDestroy(){this.stateChanges.complete(),this._platform.isBrowser&&this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement),this._platform.IOS&&this._elementRef.nativeElement.removeEventListener("keyup",this._iOSKeyupListener)}ngDoCheck(){this.ngControl&&(this.updateErrorState(),this.ngControl.disabled!==null&&this.ngControl.disabled!==this.disabled&&(this.disabled=this.ngControl.disabled,this.stateChanges.next())),this._dirtyCheckNativeValue(),this._dirtyCheckPlaceholder()}focus(e){this._elementRef.nativeElement.focus(e)}updateErrorState(){this._errorStateTracker.updateErrorState()}_focusChanged(e){e!==this.focused&&(this.focused=e,this.stateChanges.next())}_onInput(){}_dirtyCheckNativeValue(){let e=this._elementRef.nativeElement.value;this._previousNativeValue!==e&&(this._previousNativeValue=e,this.stateChanges.next())}_dirtyCheckPlaceholder(){let e=this._getPlaceholder();if(e!==this._previousPlaceholder){let n=this._elementRef.nativeElement;this._previousPlaceholder=e,e?n.setAttribute("placeholder",e):n.removeAttribute("placeholder")}}_getPlaceholder(){return this.placeholder||null}_validateType(){Mp.indexOf(this._type)>-1}_isNeverEmpty(){return this._neverEmptyInputTypes.indexOf(this._type)>-1}_isBadInput(){let e=this._elementRef.nativeElement.validity;return e&&e.badInput}get empty(){return!this._isNeverEmpty()&&!this._elementRef.nativeElement.value&&!this._isBadInput()&&!this.autofilled}get shouldLabelFloat(){if(this._isNativeSelect){let e=this._elementRef.nativeElement,n=e.options[0];return this.focused||e.multiple||!this.empty||!!(e.selectedIndex>-1&&n&&n.label)}else return this.focused||!this.empty}setDescribedByIds(e){e.length?this._elementRef.nativeElement.setAttribute("aria-describedby",e.join(" ")):this._elementRef.nativeElement.removeAttribute("aria-describedby")}onContainerClick(){this.focused||this.focus()}_isInlineSelect(){let e=this._elementRef.nativeElement;return this._isNativeSelect&&(e.multiple||e.size>1)}static{this.\u0275fac=function(n){return new(n||t)(v(fe),v($t),v(Fe,10),v(Io,8),v(er,8),v(yl),v(Ap,10),v(Ld),v(V),v(Fo,8))}}static{this.\u0275dir=T({type:t,selectors:[["input","matInput",""],["textarea","matInput",""],["select","matNativeControl",""],["input","matNativeControl",""],["textarea","matNativeControl",""]],hostAttrs:[1,"mat-mdc-input-element"],hostVars:18,hostBindings:function(n,r){n&1&&De("focus",function(){return r._focusChanged(!0)})("blur",function(){return r._focusChanged(!1)})("input",function(){return r._onInput()}),n&2&&(bi("id",r.id)("disabled",r.disabled)("required",r.required),ke("name",r.name||null)("readonly",r.readonly&&!r._isNativeSelect||null)("aria-invalid",r.empty&&r.required?null:r.errorState)("aria-required",r.required)("id",r.id),Se("mat-input-server",r._isServer)("mat-mdc-form-field-textarea-control",r._isInFormField&&r._isTextarea)("mat-mdc-form-field-input-control",r._isInFormField)("mdc-text-field__input",r._isInFormField)("mat-mdc-native-select-inline",r._isInlineSelect()))},inputs:{disabled:"disabled",id:"id",placeholder:"placeholder",name:"name",required:"required",type:"type",errorStateMatcher:"errorStateMatcher",userAriaDescribedBy:[j.None,"aria-describedby","userAriaDescribedBy"],value:"value",readonly:"readonly"},exportAs:["matInput"],standalone:!0,features:[G([{provide:Oo,useExisting:t}]),Te]})}}return t})(),Ib=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275mod=ie({type:t})}static{this.\u0275inj=te({imports:[Ht,Po,Po,Vd,Ht]})}}return t})();var C="primary",Xi=Symbol("RouteTitle"),Uo=class{constructor(i){this.params=i||{}}has(i){return Object.prototype.hasOwnProperty.call(this.params,i)}get(i){if(this.has(i)){let e=this.params[i];return Array.isArray(e)?e[0]:e}return null}getAll(i){if(this.has(i)){let e=this.params[i];return Array.isArray(e)?e:[e]}return[]}get keys(){return Object.keys(this.params)}};function ei(t){return new Uo(t)}function Dp(t,i,e){let n=e.path.split("/");if(n.length>t.length||e.pathMatch==="full"&&(i.hasChildren()||n.length<t.length))return null;let r={};for(let a=0;a<n.length;a++){let d=n[a],h=t[a];if(d.startsWith(":"))r[d.substring(1)]=h;else if(d!==h.path)return null}return{consumed:t.slice(0,n.length),posParams:r}}function Ip(t,i){if(t.length!==i.length)return!1;for(let e=0;e<t.length;++e)if(!Be(t[e],i[e]))return!1;return!0}function Be(t,i){let e=t?Bo(t):void 0,n=i?Bo(i):void 0;if(!e||!n||e.length!=n.length)return!1;let r;for(let a=0;a<e.length;a++)if(r=e[a],!Wd(t[r],i[r]))return!1;return!0}function Bo(t){return[...Object.keys(t),...Object.getOwnPropertySymbols(t)]}function Wd(t,i){if(Array.isArray(t)&&Array.isArray(i)){if(t.length!==i.length)return!1;let e=[...t].sort(),n=[...i].sort();return e.every((r,a)=>n[a]===r)}else return t===i}function Yd(t){return t.length>0?t[t.length-1]:null}function dt(t){return _a(t)?t:Sn(t)?Z(Promise.resolve(t)):x(t)}var Rp={exact:Zd,subset:Kd},Xd={exact:Tp,subset:kp,ignored:()=>!0};function jd(t,i,e){return Rp[e.paths](t.root,i.root,e.matrixParams)&&Xd[e.queryParams](t.queryParams,i.queryParams)&&!(e.fragment==="exact"&&t.fragment!==i.fragment)}function Tp(t,i){return Be(t,i)}function Zd(t,i,e){if(!At(t.segments,i.segments)||!rr(t.segments,i.segments,e)||t.numberOfChildren!==i.numberOfChildren)return!1;for(let n in i.children)if(!t.children[n]||!Zd(t.children[n],i.children[n],e))return!1;return!0}function kp(t,i){return Object.keys(i).length<=Object.keys(t).length&&Object.keys(i).every(e=>Wd(t[e],i[e]))}function Kd(t,i,e){return Qd(t,i,i.segments,e)}function Qd(t,i,e,n){if(t.segments.length>e.length){let r=t.segments.slice(0,e.length);return!(!At(r,e)||i.hasChildren()||!rr(r,e,n))}else if(t.segments.length===e.length){if(!At(t.segments,e)||!rr(t.segments,e,n))return!1;for(let r in i.children)if(!t.children[r]||!Kd(t.children[r],i.children[r],n))return!1;return!0}else{let r=e.slice(0,t.segments.length),a=e.slice(t.segments.length);return!At(t.segments,r)||!rr(t.segments,r,n)||!t.children[C]?!1:Qd(t.children[C],i,a,n)}}function rr(t,i,e){return i.every((n,r)=>Xd[e](t[r].parameters,n.parameters))}var ot=class{constructor(i=new P([],{}),e={},n=null){this.root=i,this.queryParams=e,this.fragment=n}get queryParamMap(){return this._queryParamMap??=ei(this.queryParams),this._queryParamMap}toString(){return Pp.serialize(this)}},P=class{constructor(i,e){this.segments=i,this.children=e,this.parent=null,Object.values(e).forEach(n=>n.parent=this)}hasChildren(){return this.numberOfChildren>0}get numberOfChildren(){return Object.keys(this.children).length}toString(){return or(this)}},Et=class{constructor(i,e){this.path=i,this.parameters=e}get parameterMap(){return this._parameterMap??=ei(this.parameters),this._parameterMap}toString(){return ec(this)}};function Op(t,i){return At(t,i)&&t.every((e,n)=>Be(e.parameters,i[n].parameters))}function At(t,i){return t.length!==i.length?!1:t.every((e,n)=>e.path===i[n].path)}function Fp(t,i){let e=[];return Object.entries(t.children).forEach(([n,r])=>{n===C&&(e=e.concat(i(r,n)))}),Object.entries(t.children).forEach(([n,r])=>{n!==C&&(e=e.concat(i(r,n)))}),e}var Zi=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:()=>new Bi,providedIn:"root"})}}return t})(),Bi=class{parse(i){let e=new zo(i);return new ot(e.parseRootSegment(),e.parseQueryParams(),e.parseFragment())}serialize(i){let e=`/${Pi(i.root,!0)}`,n=Vp(i.queryParams),r=typeof i.fragment=="string"?`#${Np(i.fragment)}`:"";return`${e}${n}${r}`}},Pp=new Bi;function or(t){return t.segments.map(i=>ec(i)).join("/")}function Pi(t,i){if(!t.hasChildren())return or(t);if(i){let e=t.children[C]?Pi(t.children[C],!1):"",n=[];return Object.entries(t.children).forEach(([r,a])=>{r!==C&&n.push(`${r}:${Pi(a,!1)}`)}),n.length>0?`${e}(${n.join("//")})`:e}else{let e=Fp(t,(n,r)=>r===C?[Pi(t.children[C],!1)]:[`${r}:${Pi(n,!1)}`]);return Object.keys(t.children).length===1&&t.children[C]!=null?`${or(t)}/${e[0]}`:`${or(t)}/(${e.join("//")})`}}function Jd(t){return encodeURIComponent(t).replace(/%40/g,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",")}function ir(t){return Jd(t).replace(/%3B/gi,";")}function Np(t){return encodeURI(t)}function $o(t){return Jd(t).replace(/\(/g,"%28").replace(/\)/g,"%29").replace(/%26/gi,"&")}function sr(t){return decodeURIComponent(t)}function Ud(t){return sr(t.replace(/\+/g,"%20"))}function ec(t){return`${$o(t.path)}${Lp(t.parameters)}`}function Lp(t){return Object.entries(t).map(([i,e])=>`;${$o(i)}=${$o(e)}`).join("")}function Vp(t){let i=Object.entries(t).map(([e,n])=>Array.isArray(n)?n.map(r=>`${ir(e)}=${ir(r)}`).join("&"):`${ir(e)}=${ir(n)}`).filter(e=>e);return i.length?`?${i.join("&")}`:""}var jp=/^[^\/()?;#]+/;function No(t){let i=t.match(jp);return i?i[0]:""}var Up=/^[^\/()?;=#]+/;function Bp(t){let i=t.match(Up);return i?i[0]:""}var $p=/^[^=?&#]+/;function zp(t){let i=t.match($p);return i?i[0]:""}var Hp=/^[^&#]+/;function qp(t){let i=t.match(Hp);return i?i[0]:""}var zo=class{constructor(i){this.url=i,this.remaining=i}parseRootSegment(){return this.consumeOptional("/"),this.remaining===""||this.peekStartsWith("?")||this.peekStartsWith("#")?new P([],{}):new P([],this.parseChildren())}parseQueryParams(){let i={};if(this.consumeOptional("?"))do this.parseQueryParam(i);while(this.consumeOptional("&"));return i}parseFragment(){return this.consumeOptional("#")?decodeURIComponent(this.remaining):null}parseChildren(){if(this.remaining==="")return{};this.consumeOptional("/");let i=[];for(this.peekStartsWith("(")||i.push(this.parseSegment());this.peekStartsWith("/")&&!this.peekStartsWith("//")&&!this.peekStartsWith("/(");)this.capture("/"),i.push(this.parseSegment());let e={};this.peekStartsWith("/(")&&(this.capture("/"),e=this.parseParens(!0));let n={};return this.peekStartsWith("(")&&(n=this.parseParens(!1)),(i.length>0||Object.keys(e).length>0)&&(n[C]=new P(i,e)),n}parseSegment(){let i=No(this.remaining);if(i===""&&this.peekStartsWith(";"))throw new N(4009,!1);return this.capture(i),new Et(sr(i),this.parseMatrixParams())}parseMatrixParams(){let i={};for(;this.consumeOptional(";");)this.parseParam(i);return i}parseParam(i){let e=Bp(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){let r=No(this.remaining);r&&(n=r,this.capture(n))}i[sr(e)]=sr(n)}parseQueryParam(i){let e=zp(this.remaining);if(!e)return;this.capture(e);let n="";if(this.consumeOptional("=")){let d=qp(this.remaining);d&&(n=d,this.capture(n))}let r=Ud(e),a=Ud(n);if(i.hasOwnProperty(r)){let d=i[r];Array.isArray(d)||(d=[d],i[r]=d),d.push(a)}else i[r]=a}parseParens(i){let e={};for(this.capture("(");!this.consumeOptional(")")&&this.remaining.length>0;){let n=No(this.remaining),r=this.remaining[n.length];if(r!=="/"&&r!==")"&&r!==";")throw new N(4010,!1);let a;n.indexOf(":")>-1?(a=n.slice(0,n.indexOf(":")),this.capture(a),this.capture(":")):i&&(a=C);let d=this.parseChildren();e[a]=Object.keys(d).length===1?d[C]:new P([],d),this.consumeOptional("//")}return e}peekStartsWith(i){return this.remaining.startsWith(i)}consumeOptional(i){return this.peekStartsWith(i)?(this.remaining=this.remaining.substring(i.length),!0):!1}capture(i){if(!this.consumeOptional(i))throw new N(4011,!1)}};function tc(t){return t.segments.length>0?new P([],{[C]:t}):t}function ic(t){let i={};for(let[n,r]of Object.entries(t.children)){let a=ic(r);if(n===C&&a.segments.length===0&&a.hasChildren())for(let[d,h]of Object.entries(a.children))i[d]=h;else(a.segments.length>0||a.hasChildren())&&(i[n]=a)}let e=new P(t.segments,i);return Gp(e)}function Gp(t){if(t.numberOfChildren===1&&t.children[C]){let i=t.children[C];return new P(t.segments.concat(i.segments),i.children)}return t}function ti(t){return t instanceof ot}function Wp(t,i,e=null,n=null){let r=nc(t);return rc(r,i,e,n)}function nc(t){let i;function e(a){let d={};for(let u of a.children){let p=e(u);d[u.outlet]=p}let h=new P(a.url,d);return a===t&&(i=h),h}let n=e(t.root),r=tc(n);return i??r}function rc(t,i,e,n){let r=t;for(;r.parent;)r=r.parent;if(i.length===0)return Lo(r,r,r,e,n);let a=Yp(i);if(a.toRoot())return Lo(r,r,new P([],{}),e,n);let d=Xp(a,r,t),h=d.processChildren?Vi(d.segmentGroup,d.index,a.commands):sc(d.segmentGroup,d.index,a.commands);return Lo(r,d.segmentGroup,h,e,n)}function ar(t){return typeof t=="object"&&t!=null&&!t.outlets&&!t.segmentPath}function $i(t){return typeof t=="object"&&t!=null&&t.outlets}function Lo(t,i,e,n,r){let a={};n&&Object.entries(n).forEach(([u,p])=>{a[u]=Array.isArray(p)?p.map(b=>`${b}`):`${p}`});let d;t===i?d=e:d=oc(t,i,e);let h=tc(ic(d));return new ot(h,a,r)}function oc(t,i,e){let n={};return Object.entries(t.children).forEach(([r,a])=>{a===i?n[r]=e:n[r]=oc(a,i,e)}),new P(t.segments,n)}var lr=class{constructor(i,e,n){if(this.isAbsolute=i,this.numberOfDoubleDots=e,this.commands=n,i&&n.length>0&&ar(n[0]))throw new N(4003,!1);let r=n.find($i);if(r&&r!==Yd(n))throw new N(4004,!1)}toRoot(){return this.isAbsolute&&this.commands.length===1&&this.commands[0]=="/"}};function Yp(t){if(typeof t[0]=="string"&&t.length===1&&t[0]==="/")return new lr(!0,0,t);let i=0,e=!1,n=t.reduce((r,a,d)=>{if(typeof a=="object"&&a!=null){if(a.outlets){let h={};return Object.entries(a.outlets).forEach(([u,p])=>{h[u]=typeof p=="string"?p.split("/"):p}),[...r,{outlets:h}]}if(a.segmentPath)return[...r,a.segmentPath]}return typeof a!="string"?[...r,a]:d===0?(a.split("/").forEach((h,u)=>{u==0&&h==="."||(u==0&&h===""?e=!0:h===".."?i++:h!=""&&r.push(h))}),r):[...r,a]},[]);return new lr(e,i,n)}var Qt=class{constructor(i,e,n){this.segmentGroup=i,this.processChildren=e,this.index=n}};function Xp(t,i,e){if(t.isAbsolute)return new Qt(i,!0,0);if(!e)return new Qt(i,!1,NaN);if(e.parent===null)return new Qt(e,!0,0);let n=ar(t.commands[0])?0:1,r=e.segments.length-1+n;return Zp(e,r,t.numberOfDoubleDots)}function Zp(t,i,e){let n=t,r=i,a=e;for(;a>r;){if(a-=r,n=n.parent,!n)throw new N(4005,!1);r=n.segments.length}return new Qt(n,!1,r-a)}function Kp(t){return $i(t[0])?t[0].outlets:{[C]:t}}function sc(t,i,e){if(t??=new P([],{}),t.segments.length===0&&t.hasChildren())return Vi(t,i,e);let n=Qp(t,i,e),r=e.slice(n.commandIndex);if(n.match&&n.pathIndex<t.segments.length){let a=new P(t.segments.slice(0,n.pathIndex),{});return a.children[C]=new P(t.segments.slice(n.pathIndex),t.children),Vi(a,0,r)}else return n.match&&r.length===0?new P(t.segments,{}):n.match&&!t.hasChildren()?Ho(t,i,e):n.match?Vi(t,0,r):Ho(t,i,e)}function Vi(t,i,e){if(e.length===0)return new P(t.segments,{});{let n=Kp(e),r={};if(Object.keys(n).some(a=>a!==C)&&t.children[C]&&t.numberOfChildren===1&&t.children[C].segments.length===0){let a=Vi(t.children[C],i,e);return new P(t.segments,a.children)}return Object.entries(n).forEach(([a,d])=>{typeof d=="string"&&(d=[d]),d!==null&&(r[a]=sc(t.children[a],i,d))}),Object.entries(t.children).forEach(([a,d])=>{n[a]===void 0&&(r[a]=d)}),new P(t.segments,r)}}function Qp(t,i,e){let n=0,r=i,a={match:!1,pathIndex:0,commandIndex:0};for(;r<t.segments.length;){if(n>=e.length)return a;let d=t.segments[r],h=e[n];if($i(h))break;let u=`${h}`,p=n<e.length-1?e[n+1]:null;if(r>0&&u===void 0)break;if(u&&p&&typeof p=="object"&&p.outlets===void 0){if(!$d(u,p,d))return a;n+=2}else{if(!$d(u,{},d))return a;n++}r++}return{match:!0,pathIndex:r,commandIndex:n}}function Ho(t,i,e){let n=t.segments.slice(0,i),r=0;for(;r<e.length;){let a=e[r];if($i(a)){let u=Jp(a.outlets);return new P(n,u)}if(r===0&&ar(e[0])){let u=t.segments[i];n.push(new Et(u.path,Bd(e[0]))),r++;continue}let d=$i(a)?a.outlets[C]:`${a}`,h=r<e.length-1?e[r+1]:null;d&&h&&ar(h)?(n.push(new Et(d,Bd(h))),r+=2):(n.push(new Et(d,{})),r++)}return new P(n,{})}function Jp(t){let i={};return Object.entries(t).forEach(([e,n])=>{typeof n=="string"&&(n=[n]),n!==null&&(i[e]=Ho(new P([],{}),0,n))}),i}function Bd(t){let i={};return Object.entries(t).forEach(([e,n])=>i[e]=`${n}`),i}function $d(t,i,e){return t==e.path&&Be(i,e.parameters)}var ji="imperative",Y=function(t){return t[t.NavigationStart=0]="NavigationStart",t[t.NavigationEnd=1]="NavigationEnd",t[t.NavigationCancel=2]="NavigationCancel",t[t.NavigationError=3]="NavigationError",t[t.RoutesRecognized=4]="RoutesRecognized",t[t.ResolveStart=5]="ResolveStart",t[t.ResolveEnd=6]="ResolveEnd",t[t.GuardsCheckStart=7]="GuardsCheckStart",t[t.GuardsCheckEnd=8]="GuardsCheckEnd",t[t.RouteConfigLoadStart=9]="RouteConfigLoadStart",t[t.RouteConfigLoadEnd=10]="RouteConfigLoadEnd",t[t.ChildActivationStart=11]="ChildActivationStart",t[t.ChildActivationEnd=12]="ChildActivationEnd",t[t.ActivationStart=13]="ActivationStart",t[t.ActivationEnd=14]="ActivationEnd",t[t.Scroll=15]="Scroll",t[t.NavigationSkipped=16]="NavigationSkipped",t}(Y||{}),Ie=class{constructor(i,e){this.id=i,this.url=e}},ii=class extends Ie{constructor(i,e,n="imperative",r=null){super(i,e),this.type=Y.NavigationStart,this.navigationTrigger=n,this.restoredState=r}toString(){return`NavigationStart(id: ${this.id}, url: '${this.url}')`}},$e=class extends Ie{constructor(i,e,n){super(i,e),this.urlAfterRedirects=n,this.type=Y.NavigationEnd}toString(){return`NavigationEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}')`}},Ee=function(t){return t[t.Redirect=0]="Redirect",t[t.SupersededByNewNavigation=1]="SupersededByNewNavigation",t[t.NoDataFromResolver=2]="NoDataFromResolver",t[t.GuardRejected=3]="GuardRejected",t}(Ee||{}),dr=function(t){return t[t.IgnoredSameUrlNavigation=0]="IgnoredSameUrlNavigation",t[t.IgnoredByUrlHandlingStrategy=1]="IgnoredByUrlHandlingStrategy",t}(dr||{}),st=class extends Ie{constructor(i,e,n,r){super(i,e),this.reason=n,this.code=r,this.type=Y.NavigationCancel}toString(){return`NavigationCancel(id: ${this.id}, url: '${this.url}')`}},at=class extends Ie{constructor(i,e,n,r){super(i,e),this.reason=n,this.code=r,this.type=Y.NavigationSkipped}},zi=class extends Ie{constructor(i,e,n,r){super(i,e),this.error=n,this.target=r,this.type=Y.NavigationError}toString(){return`NavigationError(id: ${this.id}, url: '${this.url}', error: ${this.error})`}},cr=class extends Ie{constructor(i,e,n,r){super(i,e),this.urlAfterRedirects=n,this.state=r,this.type=Y.RoutesRecognized}toString(){return`RoutesRecognized(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},qo=class extends Ie{constructor(i,e,n,r){super(i,e),this.urlAfterRedirects=n,this.state=r,this.type=Y.GuardsCheckStart}toString(){return`GuardsCheckStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Go=class extends Ie{constructor(i,e,n,r,a){super(i,e),this.urlAfterRedirects=n,this.state=r,this.shouldActivate=a,this.type=Y.GuardsCheckEnd}toString(){return`GuardsCheckEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state}, shouldActivate: ${this.shouldActivate})`}},Wo=class extends Ie{constructor(i,e,n,r){super(i,e),this.urlAfterRedirects=n,this.state=r,this.type=Y.ResolveStart}toString(){return`ResolveStart(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Yo=class extends Ie{constructor(i,e,n,r){super(i,e),this.urlAfterRedirects=n,this.state=r,this.type=Y.ResolveEnd}toString(){return`ResolveEnd(id: ${this.id}, url: '${this.url}', urlAfterRedirects: '${this.urlAfterRedirects}', state: ${this.state})`}},Xo=class{constructor(i){this.route=i,this.type=Y.RouteConfigLoadStart}toString(){return`RouteConfigLoadStart(path: ${this.route.path})`}},Zo=class{constructor(i){this.route=i,this.type=Y.RouteConfigLoadEnd}toString(){return`RouteConfigLoadEnd(path: ${this.route.path})`}},Ko=class{constructor(i){this.snapshot=i,this.type=Y.ChildActivationStart}toString(){return`ChildActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Qo=class{constructor(i){this.snapshot=i,this.type=Y.ChildActivationEnd}toString(){return`ChildActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},Jo=class{constructor(i){this.snapshot=i,this.type=Y.ActivationStart}toString(){return`ActivationStart(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},es=class{constructor(i){this.snapshot=i,this.type=Y.ActivationEnd}toString(){return`ActivationEnd(path: '${this.snapshot.routeConfig&&this.snapshot.routeConfig.path||""}')`}},ur=class{constructor(i,e,n){this.routerEvent=i,this.position=e,this.anchor=n,this.type=Y.Scroll}toString(){let i=this.position?`${this.position[0]}, ${this.position[1]}`:null;return`Scroll(anchor: '${this.anchor}', position: '${i}')`}},Hi=class{},qi=class{constructor(i){this.url=i}};var ts=class{constructor(){this.outlet=null,this.route=null,this.injector=null,this.children=new Ki,this.attachRef=null}},Ki=(()=>{class t{constructor(){this.contexts=new Map}onChildOutletCreated(e,n){let r=this.getOrCreateContext(e);r.outlet=n,this.contexts.set(e,r)}onChildOutletDestroyed(e){let n=this.getContext(e);n&&(n.outlet=null,n.attachRef=null)}onOutletDeactivated(){let e=this.contexts;return this.contexts=new Map,e}onOutletReAttached(e){this.contexts=e}getOrCreateContext(e){let n=this.getContext(e);return n||(n=new ts,this.contexts.set(e,n)),n}getContext(e){return this.contexts.get(e)||null}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),hr=class{constructor(i){this._root=i}get root(){return this._root.value}parent(i){let e=this.pathFromRoot(i);return e.length>1?e[e.length-2]:null}children(i){let e=is(i,this._root);return e?e.children.map(n=>n.value):[]}firstChild(i){let e=is(i,this._root);return e&&e.children.length>0?e.children[0].value:null}siblings(i){let e=ns(i,this._root);return e.length<2?[]:e[e.length-2].children.map(r=>r.value).filter(r=>r!==i)}pathFromRoot(i){return ns(i,this._root).map(e=>e.value)}};function is(t,i){if(t===i.value)return i;for(let e of i.children){let n=is(t,e);if(n)return n}return null}function ns(t,i){if(t===i.value)return[i];for(let e of i.children){let n=ns(t,e);if(n.length)return n.unshift(i),n}return[]}var Ce=class{constructor(i,e){this.value=i,this.children=e}toString(){return`TreeNode(${this.value})`}};function Kt(t){let i={};return t&&t.children.forEach(e=>i[e.value.outlet]=e),i}var fr=class extends hr{constructor(i,e){super(i),this.snapshot=e,fs(this,i)}toString(){return this.snapshot.toString()}};function ac(t){let i=eg(t),e=new Me([new Et("",{})]),n=new Me({}),r=new Me({}),a=new Me({}),d=new Me(""),h=new Mt(e,n,a,d,r,C,t,i.root);return h.snapshot=i.root,new fr(new Ce(h,[]),i)}function eg(t){let i={},e={},n={},r="",a=new Gi([],i,n,r,e,C,t,null,{});return new mr("",new Ce(a,[]))}var Mt=class{constructor(i,e,n,r,a,d,h,u){this.urlSubject=i,this.paramsSubject=e,this.queryParamsSubject=n,this.fragmentSubject=r,this.dataSubject=a,this.outlet=d,this.component=h,this._futureSnapshot=u,this.title=this.dataSubject?.pipe(F(p=>p[Xi]))??x(void 0),this.url=i,this.params=e,this.queryParams=n,this.fragment=r,this.data=a}get routeConfig(){return this._futureSnapshot.routeConfig}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=this.params.pipe(F(i=>ei(i))),this._paramMap}get queryParamMap(){return this._queryParamMap??=this.queryParams.pipe(F(i=>ei(i))),this._queryParamMap}toString(){return this.snapshot?this.snapshot.toString():`Future(${this._futureSnapshot})`}};function hs(t,i,e="emptyOnly"){let n,{routeConfig:r}=t;return i!==null&&(e==="always"||r?.path===""||!i.component&&!i.routeConfig?.loadComponent)?n={params:w(w({},i.params),t.params),data:w(w({},i.data),t.data),resolve:w(w(w(w({},t.data),i.data),r?.data),t._resolvedData)}:n={params:w({},t.params),data:w({},t.data),resolve:w(w({},t.data),t._resolvedData??{})},r&&dc(r)&&(n.resolve[Xi]=r.title),n}var Gi=class{get title(){return this.data?.[Xi]}constructor(i,e,n,r,a,d,h,u,p){this.url=i,this.params=e,this.queryParams=n,this.fragment=r,this.data=a,this.outlet=d,this.component=h,this.routeConfig=u,this._resolve=p}get root(){return this._routerState.root}get parent(){return this._routerState.parent(this)}get firstChild(){return this._routerState.firstChild(this)}get children(){return this._routerState.children(this)}get pathFromRoot(){return this._routerState.pathFromRoot(this)}get paramMap(){return this._paramMap??=ei(this.params),this._paramMap}get queryParamMap(){return this._queryParamMap??=ei(this.queryParams),this._queryParamMap}toString(){let i=this.url.map(n=>n.toString()).join("/"),e=this.routeConfig?this.routeConfig.path:"";return`Route(url:'${i}', path:'${e}')`}},mr=class extends hr{constructor(i,e){super(e),this.url=i,fs(this,e)}toString(){return lc(this._root)}};function fs(t,i){i.value._routerState=t,i.children.forEach(e=>fs(t,e))}function lc(t){let i=t.children.length>0?` { ${t.children.map(lc).join(", ")} } `:"";return`${t.value}${i}`}function Vo(t){if(t.snapshot){let i=t.snapshot,e=t._futureSnapshot;t.snapshot=e,Be(i.queryParams,e.queryParams)||t.queryParamsSubject.next(e.queryParams),i.fragment!==e.fragment&&t.fragmentSubject.next(e.fragment),Be(i.params,e.params)||t.paramsSubject.next(e.params),Ip(i.url,e.url)||t.urlSubject.next(e.url),Be(i.data,e.data)||t.dataSubject.next(e.data)}else t.snapshot=t._futureSnapshot,t.dataSubject.next(t._futureSnapshot.data)}function rs(t,i){let e=Be(t.params,i.params)&&Op(t.url,i.url),n=!t.parent!=!i.parent;return e&&!n&&(!t.parent||rs(t.parent,i.parent))}function dc(t){return typeof t.title=="string"||t.title===null}var tg=(()=>{class t{constructor(){this.activated=null,this._activatedRoute=null,this.name=C,this.activateEvents=new _e,this.deactivateEvents=new _e,this.attachEvents=new _e,this.detachEvents=new _e,this.parentContexts=y(Ki),this.location=y(Ka),this.changeDetector=y(wt),this.environmentInjector=y(pt),this.inputBinder=y(br,{optional:!0}),this.supportsBindingToComponentInputs=!0}get activatedComponentRef(){return this.activated}ngOnChanges(e){if(e.name){let{firstChange:n,previousValue:r}=e.name;if(n)return;this.isTrackedInParentContexts(r)&&(this.deactivate(),this.parentContexts.onChildOutletDestroyed(r)),this.initializeOutletWithName()}}ngOnDestroy(){this.isTrackedInParentContexts(this.name)&&this.parentContexts.onChildOutletDestroyed(this.name),this.inputBinder?.unsubscribeFromRouteData(this)}isTrackedInParentContexts(e){return this.parentContexts.getContext(e)?.outlet===this}ngOnInit(){this.initializeOutletWithName()}initializeOutletWithName(){if(this.parentContexts.onChildOutletCreated(this.name,this),this.activated)return;let e=this.parentContexts.getContext(this.name);e?.route&&(e.attachRef?this.attach(e.attachRef,e.route):this.activateWith(e.route,e.injector))}get isActivated(){return!!this.activated}get component(){if(!this.activated)throw new N(4012,!1);return this.activated.instance}get activatedRoute(){if(!this.activated)throw new N(4012,!1);return this._activatedRoute}get activatedRouteData(){return this._activatedRoute?this._activatedRoute.snapshot.data:{}}detach(){if(!this.activated)throw new N(4012,!1);this.location.detach();let e=this.activated;return this.activated=null,this._activatedRoute=null,this.detachEvents.emit(e.instance),e}attach(e,n){this.activated=e,this._activatedRoute=n,this.location.insert(e.hostView),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.attachEvents.emit(e.instance)}deactivate(){if(this.activated){let e=this.component;this.activated.destroy(),this.activated=null,this._activatedRoute=null,this.deactivateEvents.emit(e)}}activateWith(e,n){if(this.isActivated)throw new N(4013,!1);this._activatedRoute=e;let r=this.location,d=e.snapshot.component,h=this.parentContexts.getOrCreateContext(this.name).children,u=new os(e,h,r.injector);this.activated=r.createComponent(d,{index:r.length,injector:u,environmentInjector:n??this.environmentInjector}),this.changeDetector.markForCheck(),this.inputBinder?.bindActivatedRouteToOutletComponent(this),this.activateEvents.emit(this.activated.instance)}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275dir=T({type:t,selectors:[["router-outlet"]],inputs:{name:"name"},outputs:{activateEvents:"activate",deactivateEvents:"deactivate",attachEvents:"attach",detachEvents:"detach"},exportAs:["outlet"],standalone:!0,features:[Te]})}}return t})(),os=class{constructor(i,e,n){this.route=i,this.childContexts=e,this.parent=n}get(i,e){return i===Mt?this.route:i===Ki?this.childContexts:this.parent.get(i,e)}},br=new _(""),zd=(()=>{class t{constructor(){this.outletDataSubscriptions=new Map}bindActivatedRouteToOutletComponent(e){this.unsubscribeFromRouteData(e),this.subscribeToRouteData(e)}unsubscribeFromRouteData(e){this.outletDataSubscriptions.get(e)?.unsubscribe(),this.outletDataSubscriptions.delete(e)}subscribeToRouteData(e){let{activatedRoute:n}=e,r=bn([n.queryParams,n.params,n.data]).pipe(ye(([a,d,h],u)=>(h=w(w(w({},a),d),h),u===0?x(h):Promise.resolve(h)))).subscribe(a=>{if(!e.isActivated||!e.activatedComponentRef||e.activatedRoute!==n||n.component===null){this.unsubscribeFromRouteData(e);return}let d=al(n.component);if(!d){this.unsubscribeFromRouteData(e);return}for(let{templateName:h}of d.inputs)e.activatedComponentRef.setInput(h,a[h])});this.outletDataSubscriptions.set(e,r)}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})();function ig(t,i,e){let n=Wi(t,i._root,e?e._root:void 0);return new fr(n,i)}function Wi(t,i,e){if(e&&t.shouldReuseRoute(i.value,e.value.snapshot)){let n=e.value;n._futureSnapshot=i.value;let r=ng(t,i,e);return new Ce(n,r)}else{if(t.shouldAttach(i.value)){let a=t.retrieve(i.value);if(a!==null){let d=a.route;return d.value._futureSnapshot=i.value,d.children=i.children.map(h=>Wi(t,h)),d}}let n=rg(i.value),r=i.children.map(a=>Wi(t,a));return new Ce(n,r)}}function ng(t,i,e){return i.children.map(n=>{for(let r of e.children)if(t.shouldReuseRoute(n.value,r.value.snapshot))return Wi(t,n,r);return Wi(t,n)})}function rg(t){return new Mt(new Me(t.url),new Me(t.params),new Me(t.queryParams),new Me(t.fragment),new Me(t.data),t.outlet,t.component,t)}var cc="ngNavigationCancelingError";function uc(t,i){let{redirectTo:e,navigationBehaviorOptions:n}=ti(i)?{redirectTo:i,navigationBehaviorOptions:void 0}:i,r=hc(!1,Ee.Redirect);return r.url=e,r.navigationBehaviorOptions=n,r}function hc(t,i){let e=new Error(`NavigationCancelingError: ${t||""}`);return e[cc]=!0,e.cancellationCode=i,e}function og(t){return fc(t)&&ti(t.url)}function fc(t){return!!t&&t[cc]}var sg=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275cmp=pi({type:t,selectors:[["ng-component"]],standalone:!0,features:[yi],decls:1,vars:0,template:function(n,r){n&1&&Ge(0,"router-outlet")},dependencies:[tg],encapsulation:2})}}return t})();function ag(t,i){return t.providers&&!t._injector&&(t._injector=Zr(t.providers,i,`Route: ${t.path}`)),t._injector??i}function ms(t){let i=t.children&&t.children.map(ms),e=i?z(w({},t),{children:i}):w({},t);return!e.component&&!e.loadComponent&&(i||e.loadChildren)&&e.outlet&&e.outlet!==C&&(e.component=sg),e}function ze(t){return t.outlet||C}function lg(t,i){let e=t.filter(n=>ze(n)===i);return e.push(...t.filter(n=>ze(n)!==i)),e}function Qi(t){if(!t)return null;if(t.routeConfig?._injector)return t.routeConfig._injector;for(let i=t.parent;i;i=i.parent){let e=i.routeConfig;if(e?._loadedInjector)return e._loadedInjector;if(e?._injector)return e._injector}return null}var dg=(t,i,e,n)=>F(r=>(new ss(i,r.targetRouterState,r.currentRouterState,e,n).activate(t),r)),ss=class{constructor(i,e,n,r,a){this.routeReuseStrategy=i,this.futureState=e,this.currState=n,this.forwardEvent=r,this.inputBindingEnabled=a}activate(i){let e=this.futureState._root,n=this.currState?this.currState._root:null;this.deactivateChildRoutes(e,n,i),Vo(this.futureState.root),this.activateChildRoutes(e,n,i)}deactivateChildRoutes(i,e,n){let r=Kt(e);i.children.forEach(a=>{let d=a.value.outlet;this.deactivateRoutes(a,r[d],n),delete r[d]}),Object.values(r).forEach(a=>{this.deactivateRouteAndItsChildren(a,n)})}deactivateRoutes(i,e,n){let r=i.value,a=e?e.value:null;if(r===a)if(r.component){let d=n.getContext(r.outlet);d&&this.deactivateChildRoutes(i,e,d.children)}else this.deactivateChildRoutes(i,e,n);else a&&this.deactivateRouteAndItsChildren(e,n)}deactivateRouteAndItsChildren(i,e){i.value.component&&this.routeReuseStrategy.shouldDetach(i.value.snapshot)?this.detachAndStoreRouteSubtree(i,e):this.deactivateRouteAndOutlet(i,e)}detachAndStoreRouteSubtree(i,e){let n=e.getContext(i.value.outlet),r=n&&i.value.component?n.children:e,a=Kt(i);for(let d of Object.values(a))this.deactivateRouteAndItsChildren(d,r);if(n&&n.outlet){let d=n.outlet.detach(),h=n.children.onOutletDeactivated();this.routeReuseStrategy.store(i.value.snapshot,{componentRef:d,route:i,contexts:h})}}deactivateRouteAndOutlet(i,e){let n=e.getContext(i.value.outlet),r=n&&i.value.component?n.children:e,a=Kt(i);for(let d of Object.values(a))this.deactivateRouteAndItsChildren(d,r);n&&(n.outlet&&(n.outlet.deactivate(),n.children.onOutletDeactivated()),n.attachRef=null,n.route=null)}activateChildRoutes(i,e,n){let r=Kt(e);i.children.forEach(a=>{this.activateRoutes(a,r[a.value.outlet],n),this.forwardEvent(new es(a.value.snapshot))}),i.children.length&&this.forwardEvent(new Qo(i.value.snapshot))}activateRoutes(i,e,n){let r=i.value,a=e?e.value:null;if(Vo(r),r===a)if(r.component){let d=n.getOrCreateContext(r.outlet);this.activateChildRoutes(i,e,d.children)}else this.activateChildRoutes(i,e,n);else if(r.component){let d=n.getOrCreateContext(r.outlet);if(this.routeReuseStrategy.shouldAttach(r.snapshot)){let h=this.routeReuseStrategy.retrieve(r.snapshot);this.routeReuseStrategy.store(r.snapshot,null),d.children.onOutletReAttached(h.contexts),d.attachRef=h.componentRef,d.route=h.route.value,d.outlet&&d.outlet.attach(h.componentRef,h.route.value),Vo(h.route.value),this.activateChildRoutes(i,null,d.children)}else{let h=Qi(r.snapshot);d.attachRef=null,d.route=r,d.injector=h,d.outlet&&d.outlet.activateWith(r,d.injector),this.activateChildRoutes(i,null,d.children)}}else this.activateChildRoutes(i,null,n)}},pr=class{constructor(i){this.path=i,this.route=this.path[this.path.length-1]}},Jt=class{constructor(i,e){this.component=i,this.route=e}};function cg(t,i,e){let n=t._root,r=i?i._root:null;return Ni(n,r,e,[n.value])}function ug(t){let i=t.routeConfig?t.routeConfig.canActivateChild:null;return!i||i.length===0?null:{node:t,guards:i}}function ri(t,i){let e=Symbol(),n=i.get(t,e);return n===e?typeof t=="function"&&!ka(t)?t:i.get(t):n}function Ni(t,i,e,n,r={canDeactivateChecks:[],canActivateChecks:[]}){let a=Kt(i);return t.children.forEach(d=>{hg(d,a[d.value.outlet],e,n.concat([d.value]),r),delete a[d.value.outlet]}),Object.entries(a).forEach(([d,h])=>Ui(h,e.getContext(d),r)),r}function hg(t,i,e,n,r={canDeactivateChecks:[],canActivateChecks:[]}){let a=t.value,d=i?i.value:null,h=e?e.getContext(t.value.outlet):null;if(d&&a.routeConfig===d.routeConfig){let u=fg(d,a,a.routeConfig.runGuardsAndResolvers);u?r.canActivateChecks.push(new pr(n)):(a.data=d.data,a._resolvedData=d._resolvedData),a.component?Ni(t,i,h?h.children:null,n,r):Ni(t,i,e,n,r),u&&h&&h.outlet&&h.outlet.isActivated&&r.canDeactivateChecks.push(new Jt(h.outlet.component,d))}else d&&Ui(i,h,r),r.canActivateChecks.push(new pr(n)),a.component?Ni(t,null,h?h.children:null,n,r):Ni(t,null,e,n,r);return r}function fg(t,i,e){if(typeof e=="function")return e(t,i);switch(e){case"pathParamsChange":return!At(t.url,i.url);case"pathParamsOrQueryParamsChange":return!At(t.url,i.url)||!Be(t.queryParams,i.queryParams);case"always":return!0;case"paramsOrQueryParamsChange":return!rs(t,i)||!Be(t.queryParams,i.queryParams);case"paramsChange":default:return!rs(t,i)}}function Ui(t,i,e){let n=Kt(t),r=t.value;Object.entries(n).forEach(([a,d])=>{r.component?i?Ui(d,i.children.getContext(a),e):Ui(d,null,e):Ui(d,i,e)}),r.component?i&&i.outlet&&i.outlet.isActivated?e.canDeactivateChecks.push(new Jt(i.outlet.component,r)):e.canDeactivateChecks.push(new Jt(null,r)):e.canDeactivateChecks.push(new Jt(null,r))}function Ji(t){return typeof t=="function"}function mg(t){return typeof t=="boolean"}function pg(t){return t&&Ji(t.canLoad)}function gg(t){return t&&Ji(t.canActivate)}function vg(t){return t&&Ji(t.canActivateChild)}function wg(t){return t&&Ji(t.canDeactivate)}function bg(t){return t&&Ji(t.canMatch)}function mc(t){return t instanceof Ca||t?.name==="EmptyError"}var nr=Symbol("INITIAL_VALUE");function ni(){return ye(t=>bn(t.map(i=>i.pipe(jt(1),Ta(nr)))).pipe(F(i=>{for(let e of i)if(e!==!0){if(e===nr)return nr;if(e===!1||e instanceof ot)return e}return!0}),Re(i=>i!==nr),jt(1)))}function yg(t,i){return be(e=>{let{targetSnapshot:n,currentSnapshot:r,guards:{canActivateChecks:a,canDeactivateChecks:d}}=e;return d.length===0&&a.length===0?x(z(w({},e),{guardsResult:!0})):xg(d,n,r,t).pipe(be(h=>h&&mg(h)?_g(n,a,t,i):x(h)),F(h=>z(w({},e),{guardsResult:h})))})}function xg(t,i,e,n){return Z(t).pipe(be(r=>Sg(r.component,r.route,e,i,n)),Qe(r=>r!==!0,!0))}function _g(t,i,e,n){return Z(i).pipe(Ke(r=>Ea(Eg(r.route.parent,n),Cg(r.route,n),Mg(t,r.path,e),Ag(t,r.route,e))),Qe(r=>r!==!0,!0))}function Cg(t,i){return t!==null&&i&&i(new Jo(t)),x(!0)}function Eg(t,i){return t!==null&&i&&i(new Ko(t)),x(!0)}function Ag(t,i,e){let n=i.routeConfig?i.routeConfig.canActivate:null;if(!n||n.length===0)return x(!0);let r=n.map(a=>$r(()=>{let d=Qi(i)??e,h=ri(a,d),u=gg(h)?h.canActivate(i,t):Ve(d,()=>h(i,t));return dt(u).pipe(Qe())}));return x(r).pipe(ni())}function Mg(t,i,e){let n=i[i.length-1],a=i.slice(0,i.length-1).reverse().map(d=>ug(d)).filter(d=>d!==null).map(d=>$r(()=>{let h=d.guards.map(u=>{let p=Qi(d.node)??e,b=ri(u,p),f=vg(b)?b.canActivateChild(n,t):Ve(p,()=>b(n,t));return dt(f).pipe(Qe())});return x(h).pipe(ni())}));return x(a).pipe(ni())}function Sg(t,i,e,n,r){let a=i&&i.routeConfig?i.routeConfig.canDeactivate:null;if(!a||a.length===0)return x(!0);let d=a.map(h=>{let u=Qi(i)??r,p=ri(h,u),b=wg(p)?p.canDeactivate(t,i,e,n):Ve(u,()=>p(t,i,e,n));return dt(b).pipe(Qe())});return x(d).pipe(ni())}function Dg(t,i,e,n){let r=i.canLoad;if(r===void 0||r.length===0)return x(!0);let a=r.map(d=>{let h=ri(d,t),u=pg(h)?h.canLoad(i,e):Ve(t,()=>h(i,e));return dt(u)});return x(a).pipe(ni(),pc(n))}function pc(t){return xa(K(i=>{if(ti(i))throw uc(t,i)}),F(i=>i===!0))}function Ig(t,i,e,n){let r=i.canMatch;if(!r||r.length===0)return x(!0);let a=r.map(d=>{let h=ri(d,t),u=bg(h)?h.canMatch(i,e):Ve(t,()=>h(i,e));return dt(u)});return x(a).pipe(ni(),pc(n))}var Yi=class{constructor(i){this.segmentGroup=i||null}},gr=class extends Error{constructor(i){super(),this.urlTree=i}};function Zt(t){return fi(new Yi(t))}function Rg(t){return fi(new N(4e3,!1))}function Tg(t){return fi(hc(!1,Ee.GuardRejected))}var as=class{constructor(i,e){this.urlSerializer=i,this.urlTree=e}lineralizeSegments(i,e){let n=[],r=e.root;for(;;){if(n=n.concat(r.segments),r.numberOfChildren===0)return x(n);if(r.numberOfChildren>1||!r.children[C])return Rg(i.redirectTo);r=r.children[C]}}applyRedirectCommands(i,e,n){let r=this.applyRedirectCreateUrlTree(e,this.urlSerializer.parse(e),i,n);if(e.startsWith("/"))throw new gr(r);return r}applyRedirectCreateUrlTree(i,e,n,r){let a=this.createSegmentGroup(i,e.root,n,r);return new ot(a,this.createQueryParams(e.queryParams,this.urlTree.queryParams),e.fragment)}createQueryParams(i,e){let n={};return Object.entries(i).forEach(([r,a])=>{if(typeof a=="string"&&a.startsWith(":")){let h=a.substring(1);n[r]=e[h]}else n[r]=a}),n}createSegmentGroup(i,e,n,r){let a=this.createSegments(i,e.segments,n,r),d={};return Object.entries(e.children).forEach(([h,u])=>{d[h]=this.createSegmentGroup(i,u,n,r)}),new P(a,d)}createSegments(i,e,n,r){return e.map(a=>a.path.startsWith(":")?this.findPosParam(i,a,r):this.findOrReturn(a,n))}findPosParam(i,e,n){let r=n[e.path.substring(1)];if(!r)throw new N(4001,!1);return r}findOrReturn(i,e){let n=0;for(let r of e){if(r.path===i.path)return e.splice(n),r;n++}return i}},ls={matched:!1,consumedSegments:[],remainingSegments:[],parameters:{},positionalParamSegments:{}};function kg(t,i,e,n,r){let a=ps(t,i,e);return a.matched?(n=ag(i,n),Ig(n,i,e,r).pipe(F(d=>d===!0?a:w({},ls)))):x(a)}function ps(t,i,e){if(i.path==="**")return Og(e);if(i.path==="")return i.pathMatch==="full"&&(t.hasChildren()||e.length>0)?w({},ls):{matched:!0,consumedSegments:[],remainingSegments:e,parameters:{},positionalParamSegments:{}};let r=(i.matcher||Dp)(e,t,i);if(!r)return w({},ls);let a={};Object.entries(r.posParams??{}).forEach(([h,u])=>{a[h]=u.path});let d=r.consumed.length>0?w(w({},a),r.consumed[r.consumed.length-1].parameters):a;return{matched:!0,consumedSegments:r.consumed,remainingSegments:e.slice(r.consumed.length),parameters:d,positionalParamSegments:r.posParams??{}}}function Og(t){return{matched:!0,parameters:t.length>0?Yd(t).parameters:{},consumedSegments:t,remainingSegments:[],positionalParamSegments:{}}}function Hd(t,i,e,n){return e.length>0&&Ng(t,e,n)?{segmentGroup:new P(i,Pg(n,new P(e,t.children))),slicedSegments:[]}:e.length===0&&Lg(t,e,n)?{segmentGroup:new P(t.segments,Fg(t,e,n,t.children)),slicedSegments:e}:{segmentGroup:new P(t.segments,t.children),slicedSegments:e}}function Fg(t,i,e,n){let r={};for(let a of e)if(yr(t,i,a)&&!n[ze(a)]){let d=new P([],{});r[ze(a)]=d}return w(w({},n),r)}function Pg(t,i){let e={};e[C]=i;for(let n of t)if(n.path===""&&ze(n)!==C){let r=new P([],{});e[ze(n)]=r}return e}function Ng(t,i,e){return e.some(n=>yr(t,i,n)&&ze(n)!==C)}function Lg(t,i,e){return e.some(n=>yr(t,i,n))}function yr(t,i,e){return(t.hasChildren()||i.length>0)&&e.pathMatch==="full"?!1:e.path===""}function Vg(t,i,e,n){return ze(t)!==n&&(n===C||!yr(i,e,t))?!1:ps(i,t,e).matched}function jg(t,i,e){return i.length===0&&!t.children[e]}var ds=class{};function Ug(t,i,e,n,r,a,d="emptyOnly"){return new cs(t,i,e,n,r,d,a).recognize()}var Bg=31,cs=class{constructor(i,e,n,r,a,d,h){this.injector=i,this.configLoader=e,this.rootComponentType=n,this.config=r,this.urlTree=a,this.paramsInheritanceStrategy=d,this.urlSerializer=h,this.applyRedirects=new as(this.urlSerializer,this.urlTree),this.absoluteRedirectCount=0,this.allowRedirects=!0}noMatchError(i){return new N(4002,`'${i.segmentGroup}'`)}recognize(){let i=Hd(this.urlTree.root,[],[],this.config).segmentGroup;return this.match(i).pipe(F(e=>{let n=new Gi([],Object.freeze({}),Object.freeze(w({},this.urlTree.queryParams)),this.urlTree.fragment,{},C,this.rootComponentType,null,{}),r=new Ce(n,e),a=new mr("",r),d=Wp(n,[],this.urlTree.queryParams,this.urlTree.fragment);return d.queryParams=this.urlTree.queryParams,a.url=this.urlSerializer.serialize(d),this.inheritParamsAndData(a._root,null),{state:a,tree:d}}))}match(i){return this.processSegmentGroup(this.injector,this.config,i,C).pipe(Vt(n=>{if(n instanceof gr)return this.urlTree=n.urlTree,this.match(n.urlTree.root);throw n instanceof Yi?this.noMatchError(n):n}))}inheritParamsAndData(i,e){let n=i.value,r=hs(n,e,this.paramsInheritanceStrategy);n.params=Object.freeze(r.params),n.data=Object.freeze(r.data),i.children.forEach(a=>this.inheritParamsAndData(a,n))}processSegmentGroup(i,e,n,r){return n.segments.length===0&&n.hasChildren()?this.processChildren(i,e,n):this.processSegment(i,e,n,n.segments,r,!0).pipe(F(a=>a instanceof Ce?[a]:[]))}processChildren(i,e,n){let r=[];for(let a of Object.keys(n.children))a==="primary"?r.unshift(a):r.push(a);return Z(r).pipe(Ke(a=>{let d=n.children[a],h=lg(e,a);return this.processSegmentGroup(i,h,d,a)}),Ia((a,d)=>(a.push(...d),a)),zr(null),Da(),be(a=>{if(a===null)return Zt(n);let d=gc(a);return $g(d),x(d)}))}processSegment(i,e,n,r,a,d){return Z(e).pipe(Ke(h=>this.processSegmentAgainstRoute(h._injector??i,e,h,n,r,a,d).pipe(Vt(u=>{if(u instanceof Yi)return x(null);throw u}))),Qe(h=>!!h),Vt(h=>{if(mc(h))return jg(n,r,a)?x(new ds):Zt(n);throw h}))}processSegmentAgainstRoute(i,e,n,r,a,d,h){return Vg(n,r,a,d)?n.redirectTo===void 0?this.matchSegmentAgainstRoute(i,r,n,a,d):this.allowRedirects&&h?this.expandSegmentAgainstRouteUsingRedirect(i,r,e,n,a,d):Zt(r):Zt(r)}expandSegmentAgainstRouteUsingRedirect(i,e,n,r,a,d){let{matched:h,consumedSegments:u,positionalParamSegments:p,remainingSegments:b}=ps(e,r,a);if(!h)return Zt(e);r.redirectTo.startsWith("/")&&(this.absoluteRedirectCount++,this.absoluteRedirectCount>Bg&&(this.allowRedirects=!1));let f=this.applyRedirects.applyRedirectCommands(u,r.redirectTo,p);return this.applyRedirects.lineralizeSegments(r,f).pipe(be(k=>this.processSegment(i,n,e,k.concat(b),d,!1)))}matchSegmentAgainstRoute(i,e,n,r,a){let d=kg(e,n,r,i,this.urlSerializer);return n.path==="**"&&(e.children={}),d.pipe(ye(h=>h.matched?(i=n._injector??i,this.getChildConfig(i,n,r).pipe(ye(({routes:u})=>{let p=n._loadedInjector??i,{consumedSegments:b,remainingSegments:f,parameters:k}=h,B=new Gi(b,k,Object.freeze(w({},this.urlTree.queryParams)),this.urlTree.fragment,Hg(n),ze(n),n.component??n._loadedComponent??null,n,qg(n)),{segmentGroup:ae,slicedSegments:S}=Hd(e,b,f,u);if(S.length===0&&ae.hasChildren())return this.processChildren(p,u,ae).pipe(F($=>$===null?null:new Ce(B,$)));if(u.length===0&&S.length===0)return x(new Ce(B,[]));let E=ze(n)===a;return this.processSegment(p,u,ae,S,E?C:a,!0).pipe(F($=>new Ce(B,$ instanceof Ce?[$]:[])))}))):Zt(e)))}getChildConfig(i,e,n){return e.children?x({routes:e.children,injector:i}):e.loadChildren?e._loadedRoutes!==void 0?x({routes:e._loadedRoutes,injector:e._loadedInjector}):Dg(i,e,n,this.urlSerializer).pipe(be(r=>r?this.configLoader.loadChildren(i,e).pipe(K(a=>{e._loadedRoutes=a.routes,e._loadedInjector=a.injector})):Tg(e))):x({routes:[],injector:i})}};function $g(t){t.sort((i,e)=>i.value.outlet===C?-1:e.value.outlet===C?1:i.value.outlet.localeCompare(e.value.outlet))}function zg(t){let i=t.value.routeConfig;return i&&i.path===""}function gc(t){let i=[],e=new Set;for(let n of t){if(!zg(n)){i.push(n);continue}let r=i.find(a=>n.value.routeConfig===a.value.routeConfig);r!==void 0?(r.children.push(...n.children),e.add(r)):i.push(n)}for(let n of e){let r=gc(n.children);i.push(new Ce(n.value,r))}return i.filter(n=>!e.has(n))}function Hg(t){return t.data||{}}function qg(t){return t.resolve||{}}function Gg(t,i,e,n,r,a){return be(d=>Ug(t,i,e,n,d.extractedUrl,r,a).pipe(F(({state:h,tree:u})=>z(w({},d),{targetSnapshot:h,urlAfterRedirects:u}))))}function Wg(t,i){return be(e=>{let{targetSnapshot:n,guards:{canActivateChecks:r}}=e;if(!r.length)return x(e);let a=new Set(r.map(u=>u.route)),d=new Set;for(let u of a)if(!d.has(u))for(let p of vc(u))d.add(p);let h=0;return Z(d).pipe(Ke(u=>a.has(u)?Yg(u,n,t,i):(u.data=hs(u,u.parent,t).resolve,x(void 0))),K(()=>h++),Hr(1),be(u=>h===d.size?x(e):Le))})}function vc(t){let i=t.children.map(e=>vc(e)).flat();return[t,...i]}function Yg(t,i,e,n){let r=t.routeConfig,a=t._resolve;return r?.title!==void 0&&!dc(r)&&(a[Xi]=r.title),Xg(a,t,i,n).pipe(F(d=>(t._resolvedData=d,t.data=hs(t,t.parent,e).resolve,null)))}function Xg(t,i,e,n){let r=Bo(t);if(r.length===0)return x({});let a={};return Z(r).pipe(be(d=>Zg(t[d],i,e,n).pipe(Qe(),K(h=>{a[d]=h}))),Hr(1),Sa(a),Vt(d=>mc(d)?Le:fi(d)))}function Zg(t,i,e,n){let r=Qi(i)??n,a=ri(t,r),d=a.resolve?a.resolve(i,e):Ve(r,()=>a(i,e));return dt(d)}function jo(t){return ye(i=>{let e=t(i);return e?Z(e).pipe(F(()=>i)):x(i)})}var wc=(()=>{class t{buildTitle(e){let n,r=e.root;for(;r!==void 0;)n=this.getResolvedTitleForRoute(r)??n,r=r.children.find(a=>a.outlet===C);return n}getResolvedTitleForRoute(e){return e.data[Xi]}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:()=>y(Kg),providedIn:"root"})}}return t})(),Kg=(()=>{class t extends wc{constructor(e){super(),this.title=e}updateTitle(e){let n=this.buildTitle(e);n!==void 0&&this.title.setTitle(n)}static{this.\u0275fac=function(n){return new(n||t)(M(zl))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),en=new _("",{providedIn:"root",factory:()=>({})}),vr=new _(""),gs=(()=>{class t{constructor(){this.componentLoaders=new WeakMap,this.childrenLoaders=new WeakMap,this.compiler=y(In)}loadComponent(e){if(this.componentLoaders.get(e))return this.componentLoaders.get(e);if(e._loadedComponent)return x(e._loadedComponent);this.onLoadStartListener&&this.onLoadStartListener(e);let n=dt(e.loadComponent()).pipe(F(bc),K(a=>{this.onLoadEndListener&&this.onLoadEndListener(e),e._loadedComponent=a}),mt(()=>{this.componentLoaders.delete(e)})),r=new Ur(n,()=>new X).pipe(jr());return this.componentLoaders.set(e,r),r}loadChildren(e,n){if(this.childrenLoaders.get(n))return this.childrenLoaders.get(n);if(n._loadedRoutes)return x({routes:n._loadedRoutes,injector:n._loadedInjector});this.onLoadStartListener&&this.onLoadStartListener(n);let a=Qg(n,this.compiler,e,this.onLoadEndListener).pipe(mt(()=>{this.childrenLoaders.delete(n)})),d=new Ur(a,()=>new X).pipe(jr());return this.childrenLoaders.set(n,d),d}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function Qg(t,i,e,n){return dt(t.loadChildren()).pipe(F(bc),be(r=>r instanceof Qa||Array.isArray(r)?x(r):Z(i.compileModuleAsync(r))),F(r=>{n&&n(t);let a,d,h=!1;return Array.isArray(r)?(d=r,h=!0):(a=r.create(e).injector,d=a.get(vr,[],{optional:!0,self:!0}).flat()),{routes:d.map(ms),injector:a}}))}function Jg(t){return t&&typeof t=="object"&&"default"in t}function bc(t){return Jg(t)?t.default:t}var vs=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:()=>y(ev),providedIn:"root"})}}return t})(),ev=(()=>{class t{shouldProcessUrl(e){return!0}extract(e){return e}merge(e,n){return e}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),yc=new _(""),xc=new _("");function tv(t,i,e){let n=t.get(xc),r=t.get(se);return t.get(V).runOutsideAngular(()=>{if(!r.startViewTransition||n.skipNextTransition)return n.skipNextTransition=!1,Promise.resolve();let a,d=new Promise(p=>{a=p}),h=r.startViewTransition(()=>(a(),iv(t))),{onViewTransitionCreated:u}=n;return u&&Ve(t,()=>u({transition:h,from:i,to:e})),d})}function iv(t){return new Promise(i=>{Za(i,{injector:t})})}var ws=(()=>{class t{get hasRequestedNavigation(){return this.navigationId!==0}constructor(){this.currentNavigation=null,this.currentTransition=null,this.lastSuccessfulNavigation=null,this.events=new X,this.transitionAbortSubject=new X,this.configLoader=y(gs),this.environmentInjector=y(pt),this.urlSerializer=y(Zi),this.rootContexts=y(Ki),this.location=y(_i),this.inputBindingEnabled=y(br,{optional:!0})!==null,this.titleStrategy=y(wc),this.options=y(en,{optional:!0})||{},this.paramsInheritanceStrategy=this.options.paramsInheritanceStrategy||"emptyOnly",this.urlHandlingStrategy=y(vs),this.createViewTransition=y(yc,{optional:!0}),this.navigationId=0,this.afterPreactivation=()=>x(void 0),this.rootComponentType=null;let e=r=>this.events.next(new Xo(r)),n=r=>this.events.next(new Zo(r));this.configLoader.onLoadEndListener=n,this.configLoader.onLoadStartListener=e}complete(){this.transitions?.complete()}handleNavigationRequest(e){let n=++this.navigationId;this.transitions?.next(z(w(w({},this.transitions.value),e),{id:n}))}setupNavigations(e,n,r){return this.transitions=new Me({id:0,currentUrlTree:n,currentRawUrl:n,extractedUrl:this.urlHandlingStrategy.extract(n),urlAfterRedirects:this.urlHandlingStrategy.extract(n),rawUrl:n,extras:{},resolve:null,reject:null,promise:Promise.resolve(!0),source:ji,restoredState:null,currentSnapshot:r.snapshot,targetSnapshot:null,currentRouterState:r,targetRouterState:null,guards:{canActivateChecks:[],canDeactivateChecks:[]},guardsResult:null}),this.transitions.pipe(Re(a=>a.id!==0),F(a=>z(w({},a),{extractedUrl:this.urlHandlingStrategy.extract(a.rawUrl)})),ye(a=>{let d=!1,h=!1;return x(a).pipe(ye(u=>{if(this.navigationId>a.id)return this.cancelNavigationTransition(a,"",Ee.SupersededByNewNavigation),Le;this.currentTransition=a,this.currentNavigation={id:u.id,initialUrl:u.rawUrl,extractedUrl:u.extractedUrl,trigger:u.source,extras:u.extras,previousNavigation:this.lastSuccessfulNavigation?z(w({},this.lastSuccessfulNavigation),{previousNavigation:null}):null};let p=!e.navigated||this.isUpdatingInternalState()||this.isUpdatedBrowserUrl(),b=u.extras.onSameUrlNavigation??e.onSameUrlNavigation;if(!p&&b!=="reload"){let f="";return this.events.next(new at(u.id,this.urlSerializer.serialize(u.rawUrl),f,dr.IgnoredSameUrlNavigation)),u.resolve(null),Le}if(this.urlHandlingStrategy.shouldProcessUrl(u.rawUrl))return x(u).pipe(ye(f=>{let k=this.transitions?.getValue();return this.events.next(new ii(f.id,this.urlSerializer.serialize(f.extractedUrl),f.source,f.restoredState)),k!==this.transitions?.getValue()?Le:Promise.resolve(f)}),Gg(this.environmentInjector,this.configLoader,this.rootComponentType,e.config,this.urlSerializer,this.paramsInheritanceStrategy),K(f=>{a.targetSnapshot=f.targetSnapshot,a.urlAfterRedirects=f.urlAfterRedirects,this.currentNavigation=z(w({},this.currentNavigation),{finalUrl:f.urlAfterRedirects});let k=new cr(f.id,this.urlSerializer.serialize(f.extractedUrl),this.urlSerializer.serialize(f.urlAfterRedirects),f.targetSnapshot);this.events.next(k)}));if(p&&this.urlHandlingStrategy.shouldProcessUrl(u.currentRawUrl)){let{id:f,extractedUrl:k,source:B,restoredState:ae,extras:S}=u,E=new ii(f,this.urlSerializer.serialize(k),B,ae);this.events.next(E);let $=ac(this.rootComponentType).snapshot;return this.currentTransition=a=z(w({},u),{targetSnapshot:$,urlAfterRedirects:k,extras:z(w({},S),{skipLocationChange:!1,replaceUrl:!1})}),this.currentNavigation.finalUrl=k,x(a)}else{let f="";return this.events.next(new at(u.id,this.urlSerializer.serialize(u.extractedUrl),f,dr.IgnoredByUrlHandlingStrategy)),u.resolve(null),Le}}),K(u=>{let p=new qo(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot);this.events.next(p)}),F(u=>(this.currentTransition=a=z(w({},u),{guards:cg(u.targetSnapshot,u.currentSnapshot,this.rootContexts)}),a)),yg(this.environmentInjector,u=>this.events.next(u)),K(u=>{if(a.guardsResult=u.guardsResult,ti(u.guardsResult))throw uc(this.urlSerializer,u.guardsResult);let p=new Go(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects),u.targetSnapshot,!!u.guardsResult);this.events.next(p)}),Re(u=>u.guardsResult?!0:(this.cancelNavigationTransition(u,"",Ee.GuardRejected),!1)),jo(u=>{if(u.guards.canActivateChecks.length)return x(u).pipe(K(p=>{let b=new Wo(p.id,this.urlSerializer.serialize(p.extractedUrl),this.urlSerializer.serialize(p.urlAfterRedirects),p.targetSnapshot);this.events.next(b)}),ye(p=>{let b=!1;return x(p).pipe(Wg(this.paramsInheritanceStrategy,this.environmentInjector),K({next:()=>b=!0,complete:()=>{b||this.cancelNavigationTransition(p,"",Ee.NoDataFromResolver)}}))}),K(p=>{let b=new Yo(p.id,this.urlSerializer.serialize(p.extractedUrl),this.urlSerializer.serialize(p.urlAfterRedirects),p.targetSnapshot);this.events.next(b)}))}),jo(u=>{let p=b=>{let f=[];b.routeConfig?.loadComponent&&!b.routeConfig._loadedComponent&&f.push(this.configLoader.loadComponent(b.routeConfig).pipe(K(k=>{b.component=k}),F(()=>{})));for(let k of b.children)f.push(...p(k));return f};return bn(p(u.targetSnapshot.root)).pipe(zr(null),jt(1))}),jo(()=>this.afterPreactivation()),ye(()=>{let{currentSnapshot:u,targetSnapshot:p}=a,b=this.createViewTransition?.(this.environmentInjector,u.root,p.root);return b?Z(b).pipe(F(()=>a)):x(a)}),F(u=>{let p=ig(e.routeReuseStrategy,u.targetSnapshot,u.currentRouterState);return this.currentTransition=a=z(w({},u),{targetRouterState:p}),this.currentNavigation.targetRouterState=p,a}),K(()=>{this.events.next(new Hi)}),dg(this.rootContexts,e.routeReuseStrategy,u=>this.events.next(u),this.inputBindingEnabled),jt(1),K({next:u=>{d=!0,this.lastSuccessfulNavigation=this.currentNavigation,this.events.next(new $e(u.id,this.urlSerializer.serialize(u.extractedUrl),this.urlSerializer.serialize(u.urlAfterRedirects))),this.titleStrategy?.updateTitle(u.targetRouterState.snapshot),u.resolve(!0)},complete:()=>{d=!0}}),Je(this.transitionAbortSubject.pipe(K(u=>{throw u}))),mt(()=>{!d&&!h&&this.cancelNavigationTransition(a,"",Ee.SupersededByNewNavigation),this.currentTransition?.id===a.id&&(this.currentNavigation=null,this.currentTransition=null)}),Vt(u=>{if(h=!0,fc(u))this.events.next(new st(a.id,this.urlSerializer.serialize(a.extractedUrl),u.message,u.cancellationCode)),og(u)?this.events.next(new qi(u.url)):a.resolve(!1);else{this.events.next(new zi(a.id,this.urlSerializer.serialize(a.extractedUrl),u,a.targetSnapshot??void 0));try{a.resolve(e.errorHandler(u))}catch(p){this.options.resolveNavigationPromiseOnError?a.resolve(!1):a.reject(p)}}return Le}))}))}cancelNavigationTransition(e,n,r){let a=new st(e.id,this.urlSerializer.serialize(e.extractedUrl),n,r);this.events.next(a),e.resolve(!1)}isUpdatingInternalState(){return this.currentTransition?.extractedUrl.toString()!==this.currentTransition?.currentUrlTree.toString()}isUpdatedBrowserUrl(){return this.urlHandlingStrategy.extract(this.urlSerializer.parse(this.location.path(!0))).toString()!==this.currentTransition?.extractedUrl.toString()&&!this.currentTransition?.extras.skipLocationChange}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function nv(t){return t!==ji}var rv=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:()=>y(ov),providedIn:"root"})}}return t})(),us=class{shouldDetach(i){return!1}store(i,e){}shouldAttach(i){return!1}retrieve(i){return null}shouldReuseRoute(i,e){return i.routeConfig===e.routeConfig}},ov=(()=>{class t extends us{static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=je(t)))(r||t)}})()}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),_c=(()=>{class t{static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:()=>y(sv),providedIn:"root"})}}return t})(),sv=(()=>{class t extends _c{constructor(){super(...arguments),this.location=y(_i),this.urlSerializer=y(Zi),this.options=y(en,{optional:!0})||{},this.canceledNavigationResolution=this.options.canceledNavigationResolution||"replace",this.urlHandlingStrategy=y(vs),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.currentUrlTree=new ot,this.rawUrlTree=this.currentUrlTree,this.currentPageId=0,this.lastSuccessfulId=-1,this.routerState=ac(null),this.stateMemento=this.createStateMemento()}getCurrentUrlTree(){return this.currentUrlTree}getRawUrlTree(){return this.rawUrlTree}restoredState(){return this.location.getState()}get browserPageId(){return this.canceledNavigationResolution!=="computed"?this.currentPageId:this.restoredState()?.\u0275routerPageId??this.currentPageId}getRouterState(){return this.routerState}createStateMemento(){return{rawUrlTree:this.rawUrlTree,currentUrlTree:this.currentUrlTree,routerState:this.routerState}}registerNonRouterCurrentEntryChangeListener(e){return this.location.subscribe(n=>{n.type==="popstate"&&e(n.url,n.state)})}handleRouterEvent(e,n){if(e instanceof ii)this.stateMemento=this.createStateMemento();else if(e instanceof at)this.rawUrlTree=n.initialUrl;else if(e instanceof cr){if(this.urlUpdateStrategy==="eager"&&!n.extras.skipLocationChange){let r=this.urlHandlingStrategy.merge(n.finalUrl,n.initialUrl);this.setBrowserUrl(r,n)}}else e instanceof Hi?(this.currentUrlTree=n.finalUrl,this.rawUrlTree=this.urlHandlingStrategy.merge(n.finalUrl,n.initialUrl),this.routerState=n.targetRouterState,this.urlUpdateStrategy==="deferred"&&(n.extras.skipLocationChange||this.setBrowserUrl(this.rawUrlTree,n))):e instanceof st&&(e.code===Ee.GuardRejected||e.code===Ee.NoDataFromResolver)?this.restoreHistory(n):e instanceof zi?this.restoreHistory(n,!0):e instanceof $e&&(this.lastSuccessfulId=e.id,this.currentPageId=this.browserPageId)}setBrowserUrl(e,n){let r=this.urlSerializer.serialize(e);if(this.location.isCurrentPathEqualTo(r)||n.extras.replaceUrl){let a=this.browserPageId,d=w(w({},n.extras.state),this.generateNgRouterState(n.id,a));this.location.replaceState(r,"",d)}else{let a=w(w({},n.extras.state),this.generateNgRouterState(n.id,this.browserPageId+1));this.location.go(r,"",a)}}restoreHistory(e,n=!1){if(this.canceledNavigationResolution==="computed"){let r=this.browserPageId,a=this.currentPageId-r;a!==0?this.location.historyGo(a):this.currentUrlTree===e.finalUrl&&a===0&&(this.resetState(e),this.resetUrlToCurrentUrlTree())}else this.canceledNavigationResolution==="replace"&&(n&&this.resetState(e),this.resetUrlToCurrentUrlTree())}resetState(e){this.routerState=this.stateMemento.routerState,this.currentUrlTree=this.stateMemento.currentUrlTree,this.rawUrlTree=this.urlHandlingStrategy.merge(this.currentUrlTree,e.finalUrl??this.rawUrlTree)}resetUrlToCurrentUrlTree(){this.location.replaceState(this.urlSerializer.serialize(this.rawUrlTree),"",this.generateNgRouterState(this.lastSuccessfulId,this.currentPageId))}generateNgRouterState(e,n){return this.canceledNavigationResolution==="computed"?{navigationId:e,\u0275routerPageId:n}:{navigationId:e}}static{this.\u0275fac=(()=>{let e;return function(r){return(e||(e=je(t)))(r||t)}})()}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),Li=function(t){return t[t.COMPLETE=0]="COMPLETE",t[t.FAILED=1]="FAILED",t[t.REDIRECTING=2]="REDIRECTING",t}(Li||{});function Cc(t,i){t.events.pipe(Re(e=>e instanceof $e||e instanceof st||e instanceof zi||e instanceof at),F(e=>e instanceof $e||e instanceof at?Li.COMPLETE:(e instanceof st?e.code===Ee.Redirect||e.code===Ee.SupersededByNewNavigation:!1)?Li.REDIRECTING:Li.FAILED),Re(e=>e!==Li.REDIRECTING),jt(1)).subscribe(()=>{i()})}function av(t){throw t}var lv={paths:"exact",fragment:"ignored",matrixParams:"ignored",queryParams:"exact"},dv={paths:"subset",fragment:"ignored",matrixParams:"ignored",queryParams:"subset"},lt=(()=>{class t{get currentUrlTree(){return this.stateManager.getCurrentUrlTree()}get rawUrlTree(){return this.stateManager.getRawUrlTree()}get events(){return this._events}get routerState(){return this.stateManager.getRouterState()}constructor(){this.disposed=!1,this.isNgZoneEnabled=!1,this.console=y(En),this.stateManager=y(_c),this.options=y(en,{optional:!0})||{},this.pendingTasks=y(wi),this.urlUpdateStrategy=this.options.urlUpdateStrategy||"deferred",this.navigationTransitions=y(ws),this.urlSerializer=y(Zi),this.location=y(_i),this.urlHandlingStrategy=y(vs),this._events=new X,this.errorHandler=this.options.errorHandler||av,this.navigated=!1,this.routeReuseStrategy=y(rv),this.onSameUrlNavigation=this.options.onSameUrlNavigation||"ignore",this.config=y(vr,{optional:!0})?.flat()??[],this.componentInputBindingEnabled=!!y(br,{optional:!0}),this.eventsSubscription=new vn,this.isNgZoneEnabled=y(V)instanceof V&&V.isInAngularZone(),this.resetConfig(this.config),this.navigationTransitions.setupNavigations(this,this.currentUrlTree,this.routerState).subscribe({error:e=>{this.console.warn(e)}}),this.subscribeToNavigationEvents()}subscribeToNavigationEvents(){let e=this.navigationTransitions.events.subscribe(n=>{try{let r=this.navigationTransitions.currentTransition,a=this.navigationTransitions.currentNavigation;if(r!==null&&a!==null){if(this.stateManager.handleRouterEvent(n,a),n instanceof st&&n.code!==Ee.Redirect&&n.code!==Ee.SupersededByNewNavigation)this.navigated=!0;else if(n instanceof $e)this.navigated=!0;else if(n instanceof qi){let d=this.urlHandlingStrategy.merge(n.url,r.currentRawUrl),h={info:r.extras.info,skipLocationChange:r.extras.skipLocationChange,replaceUrl:this.urlUpdateStrategy==="eager"||nv(r.source)};this.scheduleNavigation(d,ji,null,h,{resolve:r.resolve,reject:r.reject,promise:r.promise})}}uv(n)&&this._events.next(n)}catch(r){this.navigationTransitions.transitionAbortSubject.next(r)}});this.eventsSubscription.add(e)}resetRootComponentType(e){this.routerState.root.component=e,this.navigationTransitions.rootComponentType=e}initialNavigation(){this.setUpLocationChangeListener(),this.navigationTransitions.hasRequestedNavigation||this.navigateToSyncWithBrowser(this.location.path(!0),ji,this.stateManager.restoredState())}setUpLocationChangeListener(){this.nonRouterCurrentEntryChangeSubscription??=this.stateManager.registerNonRouterCurrentEntryChangeListener((e,n)=>{setTimeout(()=>{this.navigateToSyncWithBrowser(e,"popstate",n)},0)})}navigateToSyncWithBrowser(e,n,r){let a={replaceUrl:!0},d=r?.navigationId?r:null;if(r){let u=w({},r);delete u.navigationId,delete u.\u0275routerPageId,Object.keys(u).length!==0&&(a.state=u)}let h=this.parseUrl(e);this.scheduleNavigation(h,n,d,a)}get url(){return this.serializeUrl(this.currentUrlTree)}getCurrentNavigation(){return this.navigationTransitions.currentNavigation}get lastSuccessfulNavigation(){return this.navigationTransitions.lastSuccessfulNavigation}resetConfig(e){this.config=e.map(ms),this.navigated=!1}ngOnDestroy(){this.dispose()}dispose(){this.navigationTransitions.complete(),this.nonRouterCurrentEntryChangeSubscription&&(this.nonRouterCurrentEntryChangeSubscription.unsubscribe(),this.nonRouterCurrentEntryChangeSubscription=void 0),this.disposed=!0,this.eventsSubscription.unsubscribe()}createUrlTree(e,n={}){let{relativeTo:r,queryParams:a,fragment:d,queryParamsHandling:h,preserveFragment:u}=n,p=u?this.currentUrlTree.fragment:d,b=null;switch(h){case"merge":b=w(w({},this.currentUrlTree.queryParams),a);break;case"preserve":b=this.currentUrlTree.queryParams;break;default:b=a||null}b!==null&&(b=this.removeEmptyProps(b));let f;try{let k=r?r.snapshot:this.routerState.snapshot.root;f=nc(k)}catch{(typeof e[0]!="string"||!e[0].startsWith("/"))&&(e=[]),f=this.currentUrlTree.root}return rc(f,e,b,p??null)}navigateByUrl(e,n={skipLocationChange:!1}){let r=ti(e)?e:this.parseUrl(e),a=this.urlHandlingStrategy.merge(r,this.rawUrlTree);return this.scheduleNavigation(a,ji,null,n)}navigate(e,n={skipLocationChange:!1}){return cv(e),this.navigateByUrl(this.createUrlTree(e,n),n)}serializeUrl(e){return this.urlSerializer.serialize(e)}parseUrl(e){try{return this.urlSerializer.parse(e)}catch{return this.urlSerializer.parse("/")}}isActive(e,n){let r;if(n===!0?r=w({},lv):n===!1?r=w({},dv):r=n,ti(e))return jd(this.currentUrlTree,e,r);let a=this.parseUrl(e);return jd(this.currentUrlTree,a,r)}removeEmptyProps(e){return Object.entries(e).reduce((n,[r,a])=>(a!=null&&(n[r]=a),n),{})}scheduleNavigation(e,n,r,a,d){if(this.disposed)return Promise.resolve(!1);let h,u,p;d?(h=d.resolve,u=d.reject,p=d.promise):p=new Promise((f,k)=>{h=f,u=k});let b=this.pendingTasks.add();return Cc(this,()=>{queueMicrotask(()=>this.pendingTasks.remove(b))}),this.navigationTransitions.handleNavigationRequest({source:n,restoredState:r,currentUrlTree:this.currentUrlTree,currentRawUrl:this.currentUrlTree,rawUrl:e,extras:a,resolve:h,reject:u,promise:p,currentSnapshot:this.routerState.snapshot,currentRouterState:this.routerState}),p.catch(f=>Promise.reject(f))}static{this.\u0275fac=function(n){return new(n||t)}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})();function cv(t){for(let i=0;i<t.length;i++)if(t[i]==null)throw new N(4008,!1)}function uv(t){return!(t instanceof Hi)&&!(t instanceof qi)}var Yb=(()=>{class t{constructor(e,n,r,a,d,h){this.router=e,this.route=n,this.tabIndexAttribute=r,this.renderer=a,this.el=d,this.locationStrategy=h,this.href=null,this.commands=null,this.onChanges=new X,this.preserveFragment=!1,this.skipLocationChange=!1,this.replaceUrl=!1;let u=d.nativeElement.tagName?.toLowerCase();this.isAnchorElement=u==="a"||u==="area",this.isAnchorElement?this.subscription=e.events.subscribe(p=>{p instanceof $e&&this.updateHref()}):this.setTabIndexIfNotOnNativeEl("0")}setTabIndexIfNotOnNativeEl(e){this.tabIndexAttribute!=null||this.isAnchorElement||this.applyAttributeValue("tabindex",e)}ngOnChanges(e){this.isAnchorElement&&this.updateHref(),this.onChanges.next(this)}set routerLink(e){e!=null?(this.commands=Array.isArray(e)?e:[e],this.setTabIndexIfNotOnNativeEl("0")):(this.commands=null,this.setTabIndexIfNotOnNativeEl(null))}onClick(e,n,r,a,d){let h=this.urlTree;if(h===null||this.isAnchorElement&&(e!==0||n||r||a||d||typeof this.target=="string"&&this.target!="_self"))return!0;let u={skipLocationChange:this.skipLocationChange,replaceUrl:this.replaceUrl,state:this.state,info:this.info};return this.router.navigateByUrl(h,u),!this.isAnchorElement}ngOnDestroy(){this.subscription?.unsubscribe()}updateHref(){let e=this.urlTree;this.href=e!==null&&this.locationStrategy?this.locationStrategy?.prepareExternalUrl(this.router.serializeUrl(e)):null;let n=this.href===null?null:Wa(this.href,this.el.nativeElement.tagName.toLowerCase(),"href");this.applyAttributeValue("href",n)}applyAttributeValue(e,n){let r=this.renderer,a=this.el.nativeElement;n!==null?r.setAttribute(a,e,n):r.removeAttribute(a,e)}get urlTree(){return this.commands===null?null:this.router.createUrlTree(this.commands,{relativeTo:this.relativeTo!==void 0?this.relativeTo:this.route,queryParams:this.queryParams,fragment:this.fragment,queryParamsHandling:this.queryParamsHandling,preserveFragment:this.preserveFragment})}static{this.\u0275fac=function(n){return new(n||t)(v(lt),v(Mt),xn("tabindex"),v(vi),v(fe),v(xi))}}static{this.\u0275dir=T({type:t,selectors:[["","routerLink",""]],hostVars:1,hostBindings:function(n,r){n&1&&De("click",function(d){return r.onClick(d.button,d.ctrlKey,d.shiftKey,d.altKey,d.metaKey)}),n&2&&ke("target",r.target)},inputs:{target:"target",queryParams:"queryParams",fragment:"fragment",queryParamsHandling:"queryParamsHandling",state:"state",info:"info",relativeTo:"relativeTo",preserveFragment:[j.HasDecoratorInputTransform,"preserveFragment","preserveFragment",bt],skipLocationChange:[j.HasDecoratorInputTransform,"skipLocationChange","skipLocationChange",bt],replaceUrl:[j.HasDecoratorInputTransform,"replaceUrl","replaceUrl",bt],routerLink:"routerLink"},standalone:!0,features:[Xr,Te]})}}return t})();var wr=class{};var hv=(()=>{class t{constructor(e,n,r,a,d){this.router=e,this.injector=r,this.preloadingStrategy=a,this.loader=d}setUpPreloading(){this.subscription=this.router.events.pipe(Re(e=>e instanceof $e),Ke(()=>this.preload())).subscribe(()=>{})}preload(){return this.processRoutes(this.injector,this.router.config)}ngOnDestroy(){this.subscription&&this.subscription.unsubscribe()}processRoutes(e,n){let r=[];for(let a of n){a.providers&&!a._injector&&(a._injector=Zr(a.providers,e,`Route: ${a.path}`));let d=a._injector??e,h=a._loadedInjector??d;(a.loadChildren&&!a._loadedRoutes&&a.canLoad===void 0||a.loadComponent&&!a._loadedComponent)&&r.push(this.preloadConfig(d,a)),(a.children||a._loadedRoutes)&&r.push(this.processRoutes(h,a.children??a._loadedRoutes))}return Z(r).pipe(Br())}preloadConfig(e,n){return this.preloadingStrategy.preload(n,()=>{let r;n.loadChildren&&n.canLoad===void 0?r=this.loader.loadChildren(e,n):r=x(null);let a=r.pipe(be(d=>d===null?x(void 0):(n._loadedRoutes=d.routes,n._loadedInjector=d.injector,this.processRoutes(d.injector??e,d.routes))));if(n.loadComponent&&!n._loadedComponent){let d=this.loader.loadComponent(n);return Z([a,d]).pipe(Br())}else return a})}static{this.\u0275fac=function(n){return new(n||t)(M(lt),M(In),M(pt),M(wr),M(gs))}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac,providedIn:"root"})}}return t})(),Ec=new _(""),fv=(()=>{class t{constructor(e,n,r,a,d={}){this.urlSerializer=e,this.transitions=n,this.viewportScroller=r,this.zone=a,this.options=d,this.lastId=0,this.lastSource="imperative",this.restoredId=0,this.store={},d.scrollPositionRestoration||="disabled",d.anchorScrolling||="disabled"}init(){this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.setHistoryScrollRestoration("manual"),this.routerEventsSubscription=this.createScrollEvents(),this.scrollEventsSubscription=this.consumeScrollEvents()}createScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof ii?(this.store[this.lastId]=this.viewportScroller.getScrollPosition(),this.lastSource=e.navigationTrigger,this.restoredId=e.restoredState?e.restoredState.navigationId:0):e instanceof $e?(this.lastId=e.id,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.urlAfterRedirects).fragment)):e instanceof at&&e.code===dr.IgnoredSameUrlNavigation&&(this.lastSource=void 0,this.restoredId=0,this.scheduleScrollEvent(e,this.urlSerializer.parse(e.url).fragment))})}consumeScrollEvents(){return this.transitions.events.subscribe(e=>{e instanceof ur&&(e.position?this.options.scrollPositionRestoration==="top"?this.viewportScroller.scrollToPosition([0,0]):this.options.scrollPositionRestoration==="enabled"&&this.viewportScroller.scrollToPosition(e.position):e.anchor&&this.options.anchorScrolling==="enabled"?this.viewportScroller.scrollToAnchor(e.anchor):this.options.scrollPositionRestoration!=="disabled"&&this.viewportScroller.scrollToPosition([0,0]))})}scheduleScrollEvent(e,n){this.zone.runOutsideAngular(()=>{setTimeout(()=>{this.zone.run(()=>{this.transitions.events.next(new ur(e,this.lastSource==="popstate"?this.store[this.restoredId]:null,n))})},0)})}ngOnDestroy(){this.routerEventsSubscription?.unsubscribe(),this.scrollEventsSubscription?.unsubscribe()}static{this.\u0275fac=function(n){Ya()}}static{this.\u0275prov=D({token:t,factory:t.\u0275fac})}}return t})();function mv(t){return t.routerState.root}function tn(t,i){return{\u0275kind:t,\u0275providers:i}}function pv(){let t=y(_n);return i=>{let e=t.get(Dn);if(i!==e.components[0])return;let n=t.get(lt),r=t.get(Ac);t.get(bs)===1&&n.initialNavigation(),t.get(Mc,null,qr.Optional)?.setUpPreloading(),t.get(Ec,null,qr.Optional)?.init(),n.resetRootComponentType(e.componentTypes[0]),r.closed||(r.next(),r.complete(),r.unsubscribe())}}var Ac=new _("",{factory:()=>new X}),bs=new _("",{providedIn:"root",factory:()=>1});function gv(){return tn(2,[{provide:bs,useValue:0},{provide:eo,multi:!0,deps:[_n],useFactory:i=>{let e=i.get(cl,Promise.resolve());return()=>e.then(()=>new Promise(n=>{let r=i.get(lt),a=i.get(Ac);Cc(r,()=>{n(!0)}),i.get(ws).afterPreactivation=()=>(n(!0),a.closed?x(void 0):a),r.initialNavigation()}))}}])}function vv(){return tn(3,[{provide:eo,multi:!0,useFactory:()=>{let i=y(lt);return()=>{i.setUpLocationChangeListener()}}},{provide:bs,useValue:2}])}var Mc=new _("");function wv(t){return tn(0,[{provide:Mc,useExisting:hv},{provide:wr,useExisting:t}])}function bv(){return tn(8,[zd,{provide:br,useExisting:zd}])}function yv(t){let i=[{provide:yc,useValue:tv},{provide:xc,useValue:w({skipNextTransition:!!t?.skipInitialTransition},t)}];return tn(9,i)}var qd=new _("ROUTER_FORROOT_GUARD"),xv=[_i,{provide:Zi,useClass:Bi},lt,Ki,{provide:Mt,useFactory:mv,deps:[lt]},gs,[]],Xb=(()=>{class t{constructor(e){}static forRoot(e,n){return{ngModule:t,providers:[xv,[],{provide:vr,multi:!0,useValue:e},{provide:qd,useFactory:Av,deps:[[lt,new Gr,new Oa]]},{provide:en,useValue:n||{}},n?.useHash?Cv():Ev(),_v(),n?.preloadingStrategy?wv(n.preloadingStrategy).\u0275providers:[],n?.initialNavigation?Mv(n):[],n?.bindToComponentInputs?bv().\u0275providers:[],n?.enableViewTransitions?yv().\u0275providers:[],Sv()]}}static forChild(e){return{ngModule:t,providers:[{provide:vr,multi:!0,useValue:e}]}}static{this.\u0275fac=function(n){return new(n||t)(M(qd,8))}}static{this.\u0275mod=ie({type:t})}static{this.\u0275inj=te({})}}return t})();function _v(){return{provide:Ec,useFactory:()=>{let t=y(pl),i=y(V),e=y(en),n=y(ws),r=y(Zi);return e.scrollOffset&&t.setOffset(e.scrollOffset),new fv(r,n,t,i,e)}}}function Cv(){return{provide:xi,useClass:hl}}function Ev(){return{provide:xi,useClass:ul}}function Av(t){return"guarded"}function Mv(t){return[t.initialNavigation==="disabled"?vv().\u0275providers:[],t.initialNavigation==="enabledBlocking"?gv().\u0275providers:[]]}var Gd=new _("");function Sv(){return[{provide:Gd,useFactory:pv},{provide:to,multi:!0,useExisting:Gd}]}var Qb={production:!1,apiUrl:"http://localhost:2000/api",imageUrl:"http://localhost:2000/public/all-files/"};export{nt as a,mf as b,qv as c,Pl as d,rw as e,ow as f,Jf as g,$e as h,Mt as i,tg as j,lt as k,Yb as l,Xb as m,Fi as n,Jl as o,Ue as p,zn as q,Fe as r,Cw as s,Ew as t,Io as u,ki as v,Mm as w,Mw as x,Dm as y,Rm as z,er as A,wd as B,bd as C,Pm as D,jm as E,Bm as F,zm as G,Sw as H,Dw as I,Iw as J,Qb as K,Dv as L,Cd as M,rb as N,Ed as O,ob as P,Oo as Q,Fo as R,sb as S,Po as T,Ap as U,Db as V,Ib as W};
